<6>[    0.000000,0] Booting Linux on physical CPU 0x0
<6>[    0.000000,0] Initializing cgroup subsys cpu
<6>[    0.000000,0] Initializing cgroup subsys cpuacct
<5>[    0.000000,0] Linux version 3.18.71-perf-g099018b (hudsoncm@ilclbld31) (gcc version 4.9.x 20150123 (prerelease) (GCC) ) #1 SMP PREEMPT Wed Feb 6 03:43:34 CST 2019
<6>[    0.000000,0] CPU: ARMv7 Processor [410fd034] revision 4 (ARMv7), cr=10c0383d
<6>[    0.000000,0] CPU: PIPT / VIPT nonaliasing data cache, VIPT aliasing instruction cache
<6>[    0.000000,0] Machine model: sanders
<6>[    0.000000,0] Reserved memory: reserved region for node 'other_ext_region@0': base 0x84300000, size 37 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'modem_region@0': base 0x86c00000, size 106 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'adsp_fw_region@0': base 0x8d600000, size 17 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'wcnss_fw_region@0': base 0x8e700000, size 7 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'dfps_data_mem@90000000': base 0x90000000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'splash_region@0x90001000': base 0x90001000, size 19 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'ramoops_mem_region': base 0xef000000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'tzlog_bck_region': base 0xeefe4000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'wdog_cpuctx_region': base 0xeefe6000, size 0 MiB
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x84300000, size 37 MiB
<6>[    0.000000,0] Reserved memory: initialized node other_ext_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x86c00000, size 106 MiB
<6>[    0.000000,0] Reserved memory: initialized node modem_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x8d600000, size 17 MiB
<6>[    0.000000,0] Reserved memory: initialized node adsp_fw_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x8e700000, size 7 MiB
<6>[    0.000000,0] Reserved memory: initialized node wcnss_fw_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'venus_region@0' node: base 0x8f800000, size 8 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0x8f800000, size 8 MiB
<6>[    0.000000,0] Reserved memory: initialized node venus_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'secure_region@0' node: base 0xf6400000, size 152 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf6400000, size 152 MiB
<6>[    0.000000,0] Reserved memory: initialized node secure_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'qseecom_region@0' node: base 0xf5400000, size 16 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf5400000, size 16 MiB
<6>[    0.000000,0] Reserved memory: initialized node qseecom_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'adsp_region@0' node: base 0xf5000000, size 4 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf5000000, size 4 MiB
<6>[    0.000000,0] Reserved memory: initialized node adsp_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'gpu_region@0' node: base 0x8f000000, size 8 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0x8f000000, size 8 MiB
<6>[    0.000000,0] Reserved memory: initialized node gpu_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] cma: Reserved 16 MiB at 0xf4000000
<6>[    0.000000,0] Memory policy: Data cache writealloc
<7>[    0.000000,0] On node 0 totalpages: 940131
<7>[    0.000000,0] free_area_init_node: node 0, pgdat c15fef80, node_mem_map e73f9000
<7>[    0.000000,0]   Normal zone: 1316 pages used for memmap
<7>[    0.000000,0]   Normal zone: 0 pages reserved
<7>[    0.000000,0]   Normal zone: 168448 pages, LIFO batch:31
<7>[    0.000000,0]   HighMem zone: 6364 pages used for memmap
<7>[    0.000000,0]   HighMem zone: 771683 pages, LIFO batch:31
<6>[    0.000000,0] psci: probing for conduit method from DT.
<6>[    0.000000,0] psci: PSCIv1.0 detected in firmware.
<6>[    0.000000,0] psci: Using standard PSCI v0.2 function IDs
<4>[    0.000000,0] PERCPU: max_distance=0xb000 too large for vmalloc space 0x0
<6>[    0.000000,0] PERCPU: Embedded 11 pages/cpu @e72ee000 s14912 r8192 d21952 u45056
<7>[    0.000000,0] pcpu-alloc: s14912 r8192 d21952 u45056 alloc=11*4096
<7>[    0.000000,0] pcpu-alloc: [0] 0 [0] 1 [0] 2 [0] 3 [0] 4 [0] 5 [0] 6 [0] 7 
<4>[    0.000000,0] Built 1 zonelists in Zone order, mobility grouping on.  Total pages: 938815
<5>[    0.000000,0] Kernel command line: sched_enable_hmp=1 sched_enable_power_aware=1 console=null androidboot.hardware=qcom user_debug=30 msm_rtb.filter=0x237 ehci-hcd.park=3 androidboot.bootdevice=7824900.sdhci lpm_levels.sleep_disabled=1 vmalloc=350M buildvariant=user androidboot.emmc=true androidboot.serialno=ZY32286WPB androidboot.baseband=msm androidboot.mode=normal androidboot.device=sanders androidboot.hwrev=0x8400 androidboot.radio=INDIA androidboot.powerup_reason=0x00004000 androidboot.bootreason=reboot msm_poweroff.download_mode=0 androidboot.fsg-id= androidboot.wifimacaddr=A8:96:75:05:41:09,A8:96:75:05:41:0A androidboot.btmacaddr=A8:96:75:05:41:08 mdss_mdp.panel=1:dsi:0:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0 androidboot.bootloader=0xC212 androidboot.carrier=retin androidboot.poweroff_alarm=0 androidboot.hardware.sku=XT1804 androidboot.secure_hardware=1 androidboot.bl_state=1 androidboot.cid=0x32 androidboot.uid=C035992300000000000000000000 androidboot.write_protect=1 androidboot.ve<6>[    0.000000,0] PID hash table entries: 4096 (order: 2, 16384 bytes)
<6>[    0.000000,0] Dentry cache hash table entries: 131072 (order: 7, 524288 bytes)
<6>[    0.000000,0] Inode-cache hash table entries: 65536 (order: 6, 262144 bytes)
<4>[    0.000000,0] Memory: 3469276K/3760524K available (13312K kernel code, 1076K rwdata, 5704K rodata, 506K init, 1902K bss, 82352K reserved, 208896K cma-reserved, 2857356K highmem)
<5>[    0.000000,0] Virtual kernel memory layout:
<5>[    0.000000,0]     vector  : 0xffff0000 - 0xffff1000   (   4 kB)
<5>[    0.000000,0]     fixmap  : 0xffc00000 - 0xfff00000   (3072 kB)
<5>[    0.000000,0] 	   vmalloc : 0xe9200000 - 0xff000000   ( 350 MB)
<5>[    0.000000,0] 	   lowmem  : 0xc0000000 - 0xe9200000   ( 658 MB)
<5>[    0.000000,0]     pkmap   : 0xbfe00000 - 0xc0000000   (   2 MB)
<5>[    0.000000,0]     modules : 0xbf000000 - 0xbfe00000   (  14 MB)
<5>[    0.000000,0]       .text : 0xc0008000 - 0xc0e00000   (14304 kB)
<5>[    0.000000,0]       .init : 0xc1400000 - 0xc147ea40   ( 507 kB)
<5>[    0.000000,0]       .data : 0xc1500000 - 0xc160d2fc   (1077 kB)
<5>[    0.000000,0]        .bss : 0xc160d2fc - 0xc17e8c28   (1903 kB)
<6>[    0.000000,0] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=8, Nodes=1
<6>[    0.000000,0] HMP scheduling enabled.
<6>[    0.000000,0] Preemptible hierarchical RCU implementation.
<6>[    0.000000,0] 	RCU dyntick-idle grace-period acceleration is enabled.
<4>[    0.000000,0] 
<4>[    0.000000,0] **********************************************************
<4>[    0.000000,0] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** trace_printk() being used. Allocating extra memory.  **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** This means that this is a DEBUG kernel and it is     **
<4>[    0.000000,0] ** unsafe for produciton use.                           **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** If you see this message and you are not debugging    **
<4>[    0.000000,0] ** the kernel, report this immediately to your vendor!  **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
<4>[    0.000000,0] **********************************************************
<6>[    0.000000,0] NR_IRQS:16 nr_irqs:16 16
<4>[    0.000000,0] mpm_init_irq_domain(): Cannot find irq controller for qcom,gpio-parent
<3>[    0.000000,0] MPM 1 irq mapping errored -517
<6>[    0.000000,0] 	Offload RCU callbacks from all CPUs
<6>[    0.000000,0] 	Offload RCU callbacks from CPUs: 0-7.
<6>[    0.000000,0] Architected cp15 and mmio timer(s) running at 19.20MHz (virt/virt).
<6>[    0.000006,0] sched_clock: 56 bits at 19MHz, resolution 52ns, wraps every 3579139424256ns
<6>[    0.000020,0] Switching to timer-based delay loop, resolution 52ns
<6>[    0.000035,0] Switched to clocksource arch_sys_counter
<6>[    0.000934,0] Calibrating delay loop (skipped), value calculated using timer frequency.. 38.00 BogoMIPS (lpj=64000)
<6>[    0.000949,0] pid_max: default: 32768 minimum: 301
<6>[    0.001032,0] Security Framework initialized
<6>[    0.001046,0] SELinux:  Initializing.
<7>[    0.001084,0] SELinux:  Starting in permissive mode
<6>[    0.001126,0] Mount-cache hash table entries: 2048 (order: 1, 8192 bytes)
<6>[    0.001138,0] Mountpoint-cache hash table entries: 2048 (order: 1, 8192 bytes)
<6>[    0.001868,0] Initializing cgroup subsys freezer
<6>[    0.001914,0] CPU: Testing write buffer coherency: ok
<3>[    0.002486,0] /cpus/cpu@0 missing clock-frequency property
<3>[    0.002501,0] /cpus/cpu@1 missing clock-frequency property
<3>[    0.002516,0] /cpus/cpu@2 missing clock-frequency property
<3>[    0.002533,0] /cpus/cpu@3 missing clock-frequency property
<3>[    0.002551,0] /cpus/cpu@100 missing clock-frequency property
<3>[    0.002570,0] /cpus/cpu@101 missing clock-frequency property
<3>[    0.002593,0] /cpus/cpu@102 missing clock-frequency property
<3>[    0.002616,0] /cpus/cpu@103 missing clock-frequency property
<6>[    0.002690,0] Setting up static identity map for 0x10d2e260 - 0x10d2e2b8
<4>[    0.002978,0] NOHZ: local_softirq_pending 02
<4>[    0.003388,0] NOHZ: local_softirq_pending 02
<6>[    0.011120,0] MSM Memory Dump base table set up
<6>[    0.011152,0] MSM Memory Dump apps data table set up
<6>[    0.011215,0] Configuring XPU violations to be fatal errors
<6>[    0.012436,0] cpu_clock_pwr_init: Power clocks configured
<4>[    0.017486,1] CPU1: Booted secondary processor
<4>[    0.022355,2] CPU2: Booted secondary processor
<4>[    0.027218,3] CPU3: Booted secondary processor
<4>[    0.032160,4] CPU4: Booted secondary processor
<4>[    0.037097,5] CPU5: Booted secondary processor
<4>[    0.041942,6] CPU6: Booted secondary processor
<4>[    0.046908,7] CPU7: Booted secondary processor
<6>[    0.047125,0] Brought up 8 CPUs
<6>[    0.047168,0] SMP: Total of 8 processors activated (307.00 BogoMIPS).
<6>[    0.047177,0] CPU: All CPU(s) started in SVC mode.
<6>[    0.056557,1] VFP support v0.3: implementor 41 architecture 3 part 40 variant 3 rev 4
<6>[    0.066033,1] pinctrl core: initialized pinctrl subsystem
<6>[    0.066478,1] regulator-dummy: no parameters
<6>[    0.143809,1] NET: Registered protocol family 16
<6>[    0.149946,1] DMA: preallocated 256 KiB pool for atomic coherent allocations
<4>[    0.150807,1] msm_pm_tz_boot_init: set warmboot address failed
<3>[    0.150833,1] scm_call failed: func id 0x2000101, ret: -1, syscall returns: 0x0, 0x0, 0x0
<6>[    0.163520,2] cpuidle: using governor ladder
<6>[    0.176804,2] cpuidle: using governor menu
<6>[    0.190132,2] cpuidle: using governor qcom
<6>[    0.196744,2] platform soc:qcom,kgsl-hyp: assigned reserved memory node gpu_region@0
<6>[    0.222178,2] msm_watchdog b017000.qcom,wdt: wdog absent resource not present
<6>[    0.222632,2] msm_watchdog b017000.qcom,wdt: MSM Watchdog Initialized
<6>[    0.227911,2] platform soc:qcom,adsprpc-mem: assigned reserved memory node adsp_region@0
<4>[    0.230164,2] irq: no irq domain found for /soc/pinctrl@1000000 !
<3>[    0.230707,2] spmi_pmic_arb 200f000.qcom,spmi: PMIC Arb Version-2 0x20010000
<3>[    0.231462,2] spmi_pmic_arb 200f000.qcom,spmi: non-zero irq-accumulator[0]:0x20000000
<3>[    0.238928,2] spmi spmi-0: of_spmi_register_devices: invalid sid on /soc/qcom,spmi@200f000/qcom,pm8950@0
<6>[    0.239402,2] platform 4080000.qcom,mss: assigned reserved memory node modem_region@0
<6>[    0.239831,2] platform c200000.qcom,lpass: assigned reserved memory node adsp_fw_region@0
<6>[    0.240096,2] platform 1de0000.qcom,venus: assigned reserved memory node venus_region@0
<6>[    0.240643,2] platform a21b000.qcom,pronto: assigned reserved memory node wcnss_fw_region@0
<6>[    0.242338,2] apc_mem_acc_corner: 0 <--> 0 mV 
<6>[    0.243165,2] gfx_mem_acc_corner: 0 <--> 0 mV 
<6>[    0.255728,2] persistent_ram: persistent_ram: paddr: ef000000, vaddr: e9280000, buf size = 0x1fff4
<6>[    0.255753,2] persistent_ram: persistent_ram: paddr: ef020000, vaddr: e9300000, buf size = 0x3fff4
<6>[    0.259244,2] persistent_ram: persistent_ram: paddr: ef060000, vaddr: e9262000, buf size = 0x7f4
<6>[    0.260284,2] console [pstore-1] enabled
<6>[    0.260294,2] pstore: Registered ramoops as persistent store backend
<6>[    0.260308,2] ramoops: attached 0x80000@0xef000000, ecc: 0/0
<6>[    0.261782,2] hw-breakpoint: found 5 (+1 reserved) breakpoint and 4 watchpoint registers.
<6>[    0.261795,2] hw-breakpoint: maximum watchpoint size is 8 bytes.
<4>[    0.263868,2] __of_mpm_init(): MPM driver mapping exists
<4>[    0.265166,2] msm_rpm_glink_dt_parse: qcom,rpm-glink compatible not matches
<6>[    0.265181,2] msm_rpm_dev_probe: APSS-RPM communication over SMD
<6>[    0.265194,2] smd_open() before smd_init()
<3>[    0.266966,2] msm_mpm_dev_probe(): Cannot get clk resource for XO: -517
<3>[    0.272645,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.272823,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.272983,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.279013,2] GFX_LDO: msm_gfx_ldo_parse_dt: Unable to parse CX parameters rc=-517
<3>[    0.279034,2] GFX_LDO: msm_gfx_ldo_probe: Unable to pasrse dt rc=-517
<6>[    0.280578,2] pm8953_s5: 400 <--> 1140 mV at 870 mV normal idle 
<6>[    0.280914,2] pm8953_s5_avs_limit: 400 <--> 1140 mV 
<6>[    0.281082,2] spm_regulator_probe: name=pm8953_s5, range=LV, voltage=870000 uV, mode=AUTO, step rate=1200 uV/us
<6>[    0.289243,2] msm_thermal:vdd_restriction_reg_init Defer regulator vdd-dig probe
<3>[    0.289265,2] msm_thermal:probe_vdd_rstr Err regulator init. err:-517. KTM continues.
<6>[    0.289284,2] msm-thermal soc:qcom,msm-thermal: probe_vdd_rstr:Failed reading node=/soc/qcom,msm-thermal, key=qcom,max-freq-level. err=-517. KTM continues
<3>[    0.289299,2] msm_thermal:msm_thermal_dev_probe Failed reading node=/soc/qcom,msm-thermal, key=qcom,online-hotplug-core. err:-517
<6>[    0.290798,2] sps:sps is ready.
<6>[    0.294402,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Speed bin: 2 PVS Version: 0
<3>[    0.294645,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Get vdd-mx regulator!!!
<4>[    0.295241,0] msm_rpm_glink_dt_parse: qcom,rpm-glink compatible not matches
<6>[    0.295257,0] msm_rpm_dev_probe: APSS-RPM communication over SMD
<6>[    0.296167,0] pm8953_s1: 870 <--> 1156 mV at 1000 mV normal idle 
<6>[    0.297012,0] pm8953_s2_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.297552,0] pm8953_s2_floor_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.298051,0] pm8953_s2_level_ao: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.298745,0] pm8953_s3: 1225 mV normal idle 
<6>[    0.299444,0] pm8953_s4: 1900 <--> 2050 mV at 1900 mV normal idle 
<6>[    0.300178,0] pm8953_s7_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.300700,0] pm8953_s7_level_ao: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.301218,0] pm8953_s7_level_so: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.301912,0] pm8953_l1: 1000 <--> 1100 mV at 1000 mV normal idle 
<6>[    0.302616,0] pm8953_l2: 1200 mV normal idle 
<6>[    0.303321,0] pm8953_l3: 925 mV normal idle 
<6>[    0.304069,0] pm8953_l5: 1800 mV normal idle 
<6>[    0.305144,1] pm8953_l6: 1800 mV normal idle 
<6>[    0.305878,1] pm8953_l7: 1800 <--> 1900 mV at 1800 mV normal idle 
<6>[    0.306413,1] pm8953_l7_ao: 1800 <--> 1900 mV at 1800 mV normal idle 
<6>[    0.307184,1] pm8953_l8: 2900 mV normal idle 
<6>[    0.307889,1] pm8953_l9: 3000 <--> 3300 mV at 3000 mV normal idle 
<6>[    0.309324,1] pm8953_l10: 2850 mV normal idle 
<6>[    0.310085,1] pm8953_l11: 2950 mV normal idle 
<6>[    0.310825,1] pm8953_l12: 1800 <--> 2950 mV at 1800 mV normal idle 
<6>[    0.311556,1] pm8953_l13: 3125 mV normal idle 
<6>[    0.312284,1] pm8953_l16: 1800 mV normal idle 
<6>[    0.312990,1] pm8953_l17: 2800 mV normal idle 
<6>[    0.313722,1] pm8953_l19: 1200 <--> 1350 mV at 1200 mV normal idle 
<6>[    0.314405,1] pm8953_l22: 2800 mV normal idle 
<6>[    0.315099,1] pm8953_l23: 1200 mV normal idle 
<3>[    0.315587,1] msm_mpm_dev_probe(): Cannot get clk resource for XO: -517
<6>[    0.315923,1] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 1: target-volt = 580000 uV
<6>[    0.315938,1] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 2: target-volt = 650000 uV
<6>[    0.315952,1] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 3: target-volt = 720000 uV
<6>[    0.315969,1] GFX_LDO: msm_gfx_ldo_adjust_init_voltage: adjusted the open-loop voltage[1] 580000 -> 615000
<6>[    0.315982,1] GFX_LDO: msm_gfx_ldo_adjust_init_voltage: adjusted the open-loop voltage[2] 650000 -> 675000
<6>[    0.315996,1] GFX_LDO: msm_gfx_ldo_voltage_init: LDO-mode fuse disabled by default
<6>[    0.316309,1] msm_gfx_ldo: 0 <--> 0 mV at 0 mV 
<6>[    0.317170,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: speed bin = 2
<6>[    0.317186,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: CPR fusing revision = 3
<6>[    0.317199,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: foundry id = 2
<6>[    0.317212,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: CPR misc fuse value = 0
<6>[    0.317252,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: Voltage boost fuse config = 0 boost = disable
<6>[    0.317394,1] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused   LowSVS: open-loop= 625000 uV
<6>[    0.317407,1] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused      SVS: open-loop= 700000 uV
<6>[    0.317420,1] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused      NOM: open-loop= 815000 uV
<6>[    0.317432,1] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused TURBO_L1: open-loop= 915000 uV
<6>[    0.317512,1] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused   LowSVS: quot[ 7]= 442
<6>[    0.317527,1] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused      SVS: quot[ 7]= 567, quot_offset[ 7]= 120
<6>[    0.317541,1] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused      NOM: quot[ 7]= 791, quot_offset[ 7]= 220
<6>[    0.317555,1] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused TURBO_L1: quot[ 7]= 978, quot_offset[ 7]= 185
<6>[    0.317922,1] cpr4_apss_init_aging: apc: sensor 6 aging init quotient diff = 12, aging RO scale = 2800 QUOT/V
<6>[    0.318103,1] cpr3_regulator_init_ctrl: apc: Default CPR mode = HW closed-loop
<6>[    0.318274,1] apc_corner: 0 <--> 0 mV at 0 mV 
<6>[    0.319813,1] msm_thermal:sensor_mgr_init_threshold threshold id already initialized
<6>[    0.320507,1] msm_thermal:vdd_restriction_reg_init Defer vdd rstr freq init.
<6>[    0.323662,1] qcom,gcc-8953 1800000.qcom,gcc: Venus speed bin: 2
<4>[    0.345298,1] branch_clk_handoff: gcc_usb_phy_cfg_ahb_clk clock is enabled in HW
<4>[    0.345319,1] branch_clk_handoff: even though ENABLE_BIT is not set
<6>[    0.347314,1] qcom,gcc-8953 1800000.qcom,gcc: Registered GCC clocks
<6>[    0.347519,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Speed bin: 2 PVS Version: 0
<3>[    0.350093,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,dfs-sid-c0
<3>[    0.350111,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No DFS SID tables found for Cluster-0
<3>[    0.350127,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,link-sid-c0
<3>[    0.350142,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No Link SID tables found for Cluster-0
<3>[    0.350157,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,lmh-sid-c0
<3>[    0.350172,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No LMH SID tables found for Cluster-0
<3>[    0.350182,1] ramp_lmh_sid: Use Default LMH SID
<3>[    0.350192,1] ramp_dfs_sid: Use Default DFS SID
<3>[    0.350202,1] ramp_link_sid: Use Default Link SID
<3>[    0.350255,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,dfs-sid-c1
<3>[    0.350270,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No DFS SID tables found for Cluster-1
<3>[    0.350286,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,link-sid-c1
<3>[    0.350299,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No Link SID tables found for Cluster-1
<3>[    0.350315,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,lmh-sid-c1
<3>[    0.350329,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No LMH SID tables found for Cluster-1
<3>[    0.350339,1] ramp_lmh_sid: Use Default LMH SID
<3>[    0.350349,1] ramp_dfs_sid: Use Default DFS SID
<3>[    0.350359,1] ramp_link_sid: Use Default Link SID
<6>[    0.350421,1] clock_rcgwr_init: RCGwR  Init Completed
<6>[    0.350835,1] populate_opp_table: clock-cpu-8953: OPP tables populated (cpu 3 and 7)
<6>[    0.350849,1] print_opp_table: clock_cpu: a53 C0: OPP voltage for 652800000: 1
<6>[    0.350860,1] print_opp_table: clock_cpu: a53 C0: OPP voltage for 2016000000: 7
<6>[    0.350871,1] print_opp_table: clock_cpu: a53 C1: OPP voltage for 652800000: 1
<6>[    0.350881,1] print_opp_table: clock_cpu: a53 C2: OPP voltage for 2016000000: 7
<6>[    0.353003,0] gcc-gfx-8953 1800000.qcom,gcc-gfx: Registered GCC GFX clocks.
<3>[    0.413468,0] msm_cpuss_dump soc:cpuss_dump: Couldn't get memory for dumping
<3>[    0.413497,0] msm_cpuss_dump soc:cpuss_dump: Couldn't get memory for dumping
<6>[    0.417237,0] KPI: Bootloader start count = 122030
<6>[    0.417255,0] KPI: Bootloader end count = 151000
<6>[    0.417265,0] KPI: Bootloader display count = 3078157587
<6>[    0.417275,0] KPI: Bootloader load kernel count = 1804
<6>[    0.417285,0] KPI: Kernel MPM timestamp = 214677
<6>[    0.417294,0] KPI: Kernel MPM Clock frequency = 32768
<6>[    0.417321,0] socinfo_print: v0.10, id=293, ver=1.1, raw_id=70, raw_ver=1, hw_plat=8, hw_plat_ver=65536
<6>[    0.417321,0]  accessory_chip=0, hw_plat_subtype=0, pmic_model=65558, pmic_die_revision=65536 foundry_id=3 serial_number=597243328
<6>[    0.418556,0] dummy_vreg: no parameters
<6>[    0.418862,0] vci_fci: no parameters
<5>[    0.420292,0] SCSI subsystem initialized
<6>[    0.421163,0] usbcore: registered new interface driver usbfs
<6>[    0.421238,0] usbcore: registered new interface driver hub
<6>[    0.421505,2] usbcore: registered new device driver usb
<6>[    0.422670,2] i2c-msm-v2 78b6000.i2c: probing driver i2c-msm-v2
<3>[    0.423007,2] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.423023,2] i2c-msm-v2 78b6000.i2c: msm_bus_scale_register_client(mstr-id:86):0 (not a problem)
<6>[    0.424562,2] i2c-msm-v2 78b7000.i2c: probing driver i2c-msm-v2
<3>[    0.424810,2] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.424824,2] i2c-msm-v2 78b7000.i2c: msm_bus_scale_register_client(mstr-id:86):0 (not a problem)
<6>[    0.425050,0] i2c-msm-v2 78b7000.i2c: irq:50 when no active transfer
<6>[    0.425786,1] i2c-msm-v2 7af5000.i2c: probing driver i2c-msm-v2
<3>[    0.425994,1] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.426008,1] i2c-msm-v2 7af5000.i2c: msm_bus_scale_register_client(mstr-id:84):0 (not a problem)
<6>[    0.427543,0] i2c-msm-v2 7af7000.i2c: probing driver i2c-msm-v2
<3>[    0.427779,0] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.427793,0] i2c-msm-v2 7af7000.i2c: msm_bus_scale_register_client(mstr-id:84):0 (not a problem)
<6>[    0.429290,0] media: Linux media interface: v0.10
<6>[    0.429365,0] Linux video capture interface: v2.00
<6>[    0.429455,0] EDAC MC: Ver: 3.0.0
<6>[    0.516931,0] cpufreq: driver msm up and running
<6>[    0.517357,0] platform soc:qcom,ion:qcom,ion-heap@8: assigned reserved memory node secure_region@0
<6>[    0.517527,0] platform soc:qcom,ion:qcom,ion-heap@27: assigned reserved memory node qseecom_region@0
<6>[    0.517713,0] ION heap system created
<6>[    0.517826,0] ION heap mm created at 0xf6400000 with size 9800000
<6>[    0.517836,0] ION heap qsecom created at 0xf5400000 with size 1000000
<3>[    0.518408,0] msm_bus_fabric_init_driver
<6>[    0.527788,0] qcom,qpnp-power-on qpnp-power-on-1: PMIC@SID0 Power-on reason: Triggered from Hard Reset and 'warm' boot
<6>[    0.527812,0] qcom,qpnp-power-on qpnp-power-on-1: PMIC@SID0: Power-off reason: Triggered from PS_HOLD (PS_HOLD/MSM controlled shutdown)
<6>[    0.527963,0] input: qpnp_pon as /devices/virtual/input/input0
<6>[    0.528309,0] pon_spare_reg: no parameters
<6>[    0.528380,0] qcom,qpnp-power-on qpnp-power-on-13: No PON config. specified
<6>[    0.528429,0] qcom,qpnp-power-on qpnp-power-on-13: PMIC@SID2 Power-on reason: Triggered from PON1 (secondary PMIC) and 'warm' boot
<6>[    0.528445,0] qcom,qpnp-power-on qpnp-power-on-13: PMIC@SID2: Power-off reason: Triggered from PS_HOLD (PS_HOLD/MSM controlled shutdown)
<6>[    0.528607,0] PMIC@SID0: (null) v1.0 options: 2, 2, 0, 0
<6>[    0.528698,0] PMIC@SID2: PMI8950 v2.0 options: 0, 0, 0, 0
<3>[    0.529564,0] ipa ipa2_uc_state_check:296 uC interface not initialized
<3>[    0.529578,0] ipa ipa_sps_irq_control_all:942 EP (2) not allocated.
<3>[    0.529585,0] ipa ipa_sps_irq_control_all:942 EP (5) not allocated.
<6>[    0.530920,1] sps:BAM 0x07904000 is registered.
<6>[    0.531389,1] sps:BAM 0x07904000 (va:0xe97c0000) enabled: ver:0x27, number of pipes:20
<6>[    0.534345,5] IPA driver initialization was successful.
<6>[    0.535505,5] gdsc_venus: no parameters
<6>[    0.535727,5] gdsc_mdss: no parameters
<6>[    0.536027,5] gdsc_jpeg: no parameters
<6>[    0.536388,5] gdsc_vfe: no parameters
<6>[    0.536755,5] gdsc_vfe1: no parameters
<6>[    0.536963,5] gdsc_cpp: no parameters
<6>[    0.537111,5] gdsc_oxili_gx: no parameters
<6>[    0.537160,5] gdsc_oxili_gx: supplied by msm_gfx_ldo
<6>[    0.537328,5] gdsc_venus_core0: fast normal 
<6>[    0.537485,5] gdsc_oxili_cx: no parameters
<6>[    0.537616,5] gdsc_usb30: no parameters
<6>[    0.538530,5] mdss_pll_probe: MDSS pll label = MDSS DSI 0 PLL
<6>[    0.538537,5] mdss_pll_probe: mdss_pll_probe: label=MDSS DSI 0 PLL PLL SSC enabled
<4>[    0.538553,5] mdss_pll_util_parse_dt_supply: : error reading ulp load. rc=-22
<6>[    0.539041,5] dsi_pll_clock_register_8996: Registered DSI PLL ndx=0 clocks successfully
<6>[    0.539062,5] mdss_pll_probe: MDSS pll label = MDSS DSI 1 PLL
<6>[    0.539068,5] mdss_pll_probe: mdss_pll_probe: label=MDSS DSI 1 PLL PLL SSC enabled
<4>[    0.539081,5] mdss_pll_util_parse_dt_supply: : error reading ulp load. rc=-22
<3>[    0.540170,5] pll_is_pll_locked_8996: DSI PLL ndx=1 status=0 failed to Lock
<6>[    0.540505,5] dsi_pll_clock_register_8996: Registered DSI PLL ndx=1 clocks successfully
<6>[    0.540942,5] msm_iommu 1e00000.qcom,iommu: device apps_iommu (model: 500) mapped at e9b80000, with 21 ctx banks
<6>[    0.545720,5] msm_iommu_ctx 1e20000.qcom,iommu-ctx: context adsp_elf using bank 0
<6>[    0.545842,5] msm_iommu_ctx 1e21000.qcom,iommu-ctx: context adsp_sec_pixel using bank 1
<6>[    0.545960,5] msm_iommu_ctx 1e22000.qcom,iommu-ctx: context mdp_1 using bank 2
<6>[    0.546078,5] msm_iommu_ctx 1e23000.qcom,iommu-ctx: context venus_fw using bank 3
<6>[    0.546198,5] msm_iommu_ctx 1e24000.qcom,iommu-ctx: context venus_sec_non_pixel using bank 4
<6>[    0.546317,5] msm_iommu_ctx 1e25000.qcom,iommu-ctx: context venus_sec_bitstream using bank 5
<6>[    0.546435,5] msm_iommu_ctx 1e26000.qcom,iommu-ctx: context venus_sec_pixel using bank 6
<6>[    0.546587,5] msm_iommu_ctx 1e28000.qcom,iommu-ctx: context pronto_pil using bank 8
<6>[    0.546742,5] msm_iommu_ctx 1e29000.qcom,iommu-ctx: context q6 using bank 9
<6>[    0.546884,5] msm_iommu_ctx 1e2a000.qcom,iommu-ctx: context periph_rpm using bank 10
<6>[    0.547027,5] msm_iommu_ctx 1e2b000.qcom,iommu-ctx: context lpass using bank 11
<6>[    0.547174,5] msm_iommu_ctx 1e2f000.qcom,iommu-ctx: context adsp_io using bank 15
<6>[    0.547315,5] msm_iommu_ctx 1e30000.qcom,iommu-ctx: context adsp_opendsp using bank 16
<6>[    0.547456,5] msm_iommu_ctx 1e31000.qcom,iommu-ctx: context adsp_shared using bank 17
<6>[    0.547597,5] msm_iommu_ctx 1e32000.qcom,iommu-ctx: context cpp using bank 18
<6>[    0.547738,5] msm_iommu_ctx 1e33000.qcom,iommu-ctx: context jpeg_enc0 using bank 19
<6>[    0.547883,5] msm_iommu_ctx 1e34000.qcom,iommu-ctx: context vfe using bank 20
<6>[    0.548027,5] msm_iommu_ctx 1e35000.qcom,iommu-ctx: context mdp_0 using bank 21
<6>[    0.548170,5] msm_iommu_ctx 1e36000.qcom,iommu-ctx: context venus_ns using bank 22
<6>[    0.548309,5] msm_iommu_ctx 1e38000.qcom,iommu-ctx: context ipa using bank 24
<6>[    0.548450,5] msm_iommu_ctx 1e37000.qcom,iommu-ctx: context access_control using bank 23
<6>[    0.550215,5] arm-smmu 1c40000.arm,smmu-kgsl: regulator defer delay 80
<6>[    0.551808,5] Advanced Linux Sound Architecture Driver Initialized.
<6>[    0.552458,5] Bluetooth: e6e05ed8
<6>[    0.552477,5] NET: Registered protocol family 31
<6>[    0.552482,5] Bluetooth: e6e05ed8
<6>[    0.552490,5] Bluetooth: e6e05ed0Bluetooth: e6e05ec0
<6>[    0.552521,5] Bluetooth: e6e05ec0<6>[    0.552755,5] cfg80211: Calling CRDA to update world regulatory domain
<6>[    0.552771,5] cfg80211: World regulatory domain updated:
<6>[    0.552776,5] cfg80211:  DFS Master region: unset
<6>[    0.552781,5] cfg80211:   (start_freq - end_freq @ bandwidth), (max_antenna_gain, max_eirp), (dfs_cac_time)
<6>[    0.552788,5] cfg80211:   (2402000 KHz - 2472000 KHz @ 40000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.552794,5] cfg80211:   (2457000 KHz - 2482000 KHz @ 40000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.552800,5] cfg80211:   (2474000 KHz - 2494000 KHz @ 20000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.552805,5] cfg80211:   (5170000 KHz - 5250000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.552811,5] cfg80211:   (5250000 KHz - 5330000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.552817,5] cfg80211:   (5490000 KHz - 5710000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.552822,5] cfg80211:   (5735000 KHz - 5835000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.552828,5] cfg80211:   (57240000 KHz - 63720000 KHz @ 2160000 KHz), (N/A, 0 mBm), (N/A)
<6>[    0.553161,1] ibb_reg: 4600 <--> 6000 mV at 5500 mV 
<6>[    0.553407,1] lab_reg: 4600 <--> 6000 mV at 5500 mV 
<6>[    0.555119,5] Switched to clocksource arch_sys_counter
<6>[    0.581278,5] NET: Registered protocol family 2
<6>[    0.581617,5] TCP established hash table entries: 8192 (order: 3, 32768 bytes)
<6>[    0.581655,5] TCP bind hash table entries: 8192 (order: 4, 65536 bytes)
<6>[    0.581714,5] TCP: Hash tables configured (established 8192 bind 8192)
<6>[    0.581740,5] TCP: reno registered
<6>[    0.581748,5] UDP hash table entries: 512 (order: 2, 16384 bytes)
<6>[    0.581766,5] UDP-Lite hash table entries: 512 (order: 2, 16384 bytes)
<6>[    0.581870,5] NET: Registered protocol family 1
<6>[    0.582922,5] gcc-mdss-8953 1800000.qcom,gcc-mdss: Registered GCC MDSS clocks.
<6>[    0.583409,5] Trying to unpack rootfs image as initramfs...
<6>[    0.715634,5] Freeing initrd memory: 6880K
<6>[    0.717946,5] hw perfevents: enabled with ARMv8 Cortex-A53 PMU driver, 7 counters available
<6>[    0.721022,5] futex hash table entries: 2048 (order: 5, 131072 bytes)
<6>[    0.721090,5] audit: initializing netlink subsys (disabled)
<5>[    0.721122,5] audit: type=2000 audit(0.720:1): initialized
<4>[    0.721419,5] vmscan: error setting kswapd cpu affinity mask
<5>[    0.724771,5] VFS: Disk quotas dquot_6.5.2
<4>[    0.724852,5] Dquot-cache hash table entries: 1024 (order 0, 4096 bytes)
<6>[    0.725665,5] exFAT: Version 1.2.9
<6>[    0.726097,5] Registering sdcardfs 0.1
<6>[    0.726208,5] fuse init (API version 7.23)
<7>[    0.726506,5] SELinux:  Registering netfilter hooks
<6>[    0.728080,5] bounce: pool size: 64 pages
<6>[    0.728163,5] Block layer SCSI generic (bsg) driver version 0.4 loaded (major 246)
<6>[    0.728172,5] io scheduler noop registered
<6>[    0.728180,5] io scheduler deadline registered
<6>[    0.728197,5] io scheduler cfq registered (default)
<3>[    0.731241,5] msm_dss_get_res_byname: 'vbif_nrt_phys' resource not found
<3>[    0.731250,5] mdss_mdp_probe+0x1a0/0x10d8->msm_dss_ioremap_byname: 'vbif_nrt_phys' msm_dss_get_res_byname failed
<3>[    0.731672,5] mdss_mdp_irq_clk_register: unable to get clk: lut_clk
<3>[    0.732171,5] No change in context(0==0), skip
<6>[    0.732874,5] mdss_mdp_pipe_addr_setup: type:0 ftchid:-1 xinid:0 num:0 rect:0 ndx:0x1 prio:0
<6>[    0.732892,5] mdss_mdp_pipe_addr_setup: type:1 ftchid:-1 xinid:1 num:3 rect:0 ndx:0x8 prio:1
<6>[    0.732898,5] mdss_mdp_pipe_addr_setup: type:1 ftchid:-1 xinid:5 num:4 rect:0 ndx:0x10 prio:2
<6>[    0.732914,5] mdss_mdp_pipe_addr_setup: type:2 ftchid:-1 xinid:2 num:6 rect:0 ndx:0x40 prio:3
<6>[    0.732929,5] mdss_mdp_pipe_addr_setup: type:3 ftchid:-1 xinid:7 num:10 rect:0 ndx:0x400 prio:0
<3>[    0.732940,5] mdss_mdp_parse_dt_handler: Error from prop qcom,mdss-pipe-sw-reset-off : u32 array read
<3>[    0.733037,5] mdss_mdp_parse_dt_handler: Error from prop qcom,mdss-ib-factor-overlap : u32 array read
<6>[    0.733260,5] xlog_status: enable:0, panic:1, dump:2
<6>[    0.733797,5] mdss_mdp_probe: mdss version = 0x10100000, bootloader display is on, num 1, intf_sel=0x00000100
<3>[    0.735220,5] mdss_smmu_util_parse_dt_clock: clocks are not defined
<6>[    0.735244,5] mdss_smmu_probe: iommu v2 domain[0] mapping and clk register successful!
<3>[    0.735263,5] mdss_smmu_util_parse_dt_clock: clocks are not defined
<6>[    0.735272,5] mdss_smmu_probe: iommu v2 domain[2] mapping and clk register successful!
<4>[    0.736278,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.736291,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.736303,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<6>[    0.736784,5] mdss_dsi_ctrl_probe: DSI Ctrl name = MDSS DSI CTRL->0
<6>[    0.737170,5] mdss_panel_parse_panel_config_dt: BL: panel=mipi_mot_vid_djn_1080p_550, manufacture_id(0xDA)= 0x1A controller_ver(0xDB)= 0xD5 controller_drv_ver(0XDC)= 0x45, full=0x000000000045D51A
<6>[    0.737180,5] mdss_dsi_find_panel_of_node: cmdline:0:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0 panel_name:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0
<6>[    0.737228,5] mdss_dsi_panel_init: Panel Name = mipi_mot_vid_djn_1080p_550
<6>[    0.737385,5] mdss_dsi_panel_timing_from_dt: found new timing "qcom,mdss_dsi_mot_djn_550_1080p_vid_v0" (e6e05788)
<3>[    0.737404,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-post-panel-on-command
<3>[    0.737413,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-timing-switch-command
<4>[    0.737418,5] mdss_dsi_panel_get_dsc_cfg_np: cannot find dsc config node:
<3>[    0.737529,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-idle-on-command
<3>[    0.737538,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-idle-off-command
<6>[    0.737567,5] mdss_dsi_parse_panel_features: ulps feature disabled
<6>[    0.737574,5] mdss_dsi_parse_panel_features: ulps during suspend feature disabled
<6>[    0.737581,5] mdss_dsi_parse_dms_config: dynamic switch feature enabled: 0
<3>[    0.737663,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-on
<3>[    0.737672,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-off
<6>[    0.737714,5] mdss_panel_parse_param_prop: HBM feature enabled with 2 dt cmds
<6>[    0.737719,5] mdss_panel_parse_param_prop: HBM type = 1
<6>[    0.737754,5] mdss_panel_parse_param_prop: CABC feature enabled with 3 dt cmds
<3>[    0.737763,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-on
<3>[    0.737772,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-off
<4>[    0.737790,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.737801,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.737811,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<3>[    0.737975,5] mdss_dsi_parse_gpio_params:4125, TE gpio not specified
<6>[    0.737981,5] mdss_dsi_parse_gpio_params: bklt_en gpio not specified
<3>[    0.738015,5] msm_dss_get_res_byname: 'dsi_phy_regulator' resource not found
<3>[    0.738024,5] mdss_dsi_retrieve_ctrl_resources+0x124/0x1b8->msm_dss_ioremap_byname: 'dsi_phy_regulator' msm_dss_get_res_byname failed
<6>[    0.738031,5] mdss_dsi_retrieve_ctrl_resources: ctrl_base=e9782000 ctrl_size=400 phy_base=e9790400 phy_size=580
<6>[    0.738102,5] dsi_panel_device_register: Continuous splash enabled
<6>[    0.738281,5] mdss_register_panel: adding framebuffer device 1a94000.qcom,mdss_dsi_ctrl0
<6>[    0.739664,5] mdss_dsi_ctrl_probe: Dsi Ctrl->0 initialized, DSI rev:0x10040002, PHY rev:0x2
<6>[    0.739782,5] mdss_dsi_status_init: DSI status check interval:8000
<6>[    0.740441,5] mdss_register_panel: adding framebuffer device soc:qcom,mdss_wb_panel
<6>[    0.740860,5] mdss_fb_probe: fb0: split_mode:0 left:0 right:0
<6>[    0.741277,5] mdss_fb_register: FrameBuffer[0] 1080x1920 registered successfully!
<6>[    0.741548,5] mdss_fb_probe: fb1: split_mode:0 left:0 right:0
<6>[    0.741624,5] mdss_fb_register: FrameBuffer[1] 640x640 registered successfully!
<3>[    0.741706,5] mdss_mdp_splash_parse_dt: splash mem child node is not present
<6>[    0.741727,5] anx7805 anx7805_init: anx7805_init
<6>[    0.741752,1] anx7805 anx7805_init_async: anx7805_init_async
<3>[    0.743716,5] IPC_RTR: msm_ipc_router_smd_driver_register Already driver registered IPCRTR
<3>[    0.743736,5] IPC_RTR: msm_ipc_router_smd_driver_register Already driver registered IPCRTR
<6>[    0.747214,5] In memshare_probe, Memshare probe success
<5>[    0.748633,5] msm_rpm_log_probe: OK
<6>[    0.749469,5] subsys-pil-tz soc:qcom,kgsl-hyp: for a506_zap segments only will be dumped.
<6>[    0.751020,5] subsys-pil-tz 1de0000.qcom,venus: for venus segments only will be dumped.
<6>[    0.752919,5] mmi_unit_info (SMEM) for modem: version = 0x03, device = 'sanders', radio = 0x0, radio_str = 'INDIA', system_rev = 0x8400, system_serial = 0xc035992300000000, machine = 'Qualcomm Technologies, Inc. MSM ', barcode = 'ZY32286WPB', baseband = '', carrier = 'retin', pu_reason = 0x00004000
<3>[    0.752949,5] ACPU Bin is not available.
<6>[    0.752993,5] mmi_storage_info :eMMC: 64GB SAMSUNG RC14MB FV=0000000000000007
<6>[    0.753385,5] msm_serial_hs module loaded
<6>[    0.761597,5] platform 1c40000.qcom,kgsl-iommu:gfx3d_secure: assigned reserved memory node secure_region@0
<6>[    0.766388,5] brd: module loaded
<6>[    0.767891,5] loop: module loaded
<6>[    0.768153,5] zram: Added device: zram0
<6>[    0.768449,5] QSEECOM: qseecom_probe: qseecom.qsee_version = 0x1001000
<4>[    0.768480,5] QSEECOM: qseecom_retrieve_ce_data: Device does not support PFE
<6>[    0.768489,5] QSEECOM: qseecom_probe: qseecom clocks handled by other subsystem
<4>[    0.768495,5] QSEECOM: qseecom_probe: qsee reentrancy support phase is not defined, setting to default 0
<4>[    0.768957,5] QSEECOM: qseecom_probe: qseecom.whitelist_support = 1
<6>[    0.770356,5] alsa-to-h2w soc:alsa_to_h2w: alsa_to_h2w_probe success
<4>[    0.770976,5] i2c-core: driver [tabla-i2c-core] using legacy suspend method
<4>[    0.770981,5] i2c-core: driver [tabla-i2c-core] using legacy resume method
<4>[    0.771044,5] i2c-core: driver [wcd9xxx-i2c-core] using legacy suspend method
<4>[    0.771049,5] i2c-core: driver [wcd9xxx-i2c-core] using legacy resume method
<4>[    0.771119,5] i2c-core: driver [tasha-i2c-core] using legacy suspend method
<4>[    0.771123,5] i2c-core: driver [tasha-i2c-core] using legacy resume method
<6>[    0.771353,5] Loading pn544 driver
<6>[    0.771463,5] nfc: succeed in obtaining nfc_clk from msm pmic
<4>[    0.771627,5] 5-0028 supply vdd not found, using dummy regulator
<6>[    0.771928,5] QCE50: __qce_get_device_tree_data: BAM Apps EE is not defined, setting to default 1
<6>[    0.772459,5] qce 720000.qcedev: Qualcomm Crypto 5.3.3 device found @0x720000
<6>[    0.772468,5] qce 720000.qcedev: CE device = 0x0
<6>[    0.772468,5] IO base, CE = 0xe9b40000
<6>[    0.772468,5] Consumer (IN) PIPE 2,    Producer (OUT) PIPE 3
<6>[    0.772468,5] IO base BAM = 0x00000000
<6>[    0.772468,5] BAM IRQ 59
<6>[    0.772468,5] Engines Availability = 0x2010853
<6>[    0.772620,5] sps:BAM 0x00704000 is registered.
<6>[    0.772789,5] sps:BAM 0x00704000 (va:0xea840000) enabled: ver:0x27, number of pipes:8
<6>[    0.772985,5] QCE50: qce_sps_init:  Qualcomm MSM CE-BAM at 0x0000000000704000 irq 59
<6>[    0.775697,5] QCE50: __qce_get_device_tree_data: BAM Apps EE is not defined, setting to default 1
<6>[    0.776551,5] qcrypto 720000.qcrypto: Qualcomm Crypto 5.3.3 device found @0x720000
<6>[    0.776559,5] qcrypto 720000.qcrypto: CE device = 0x0
<6>[    0.776559,5] IO base, CE = 0xea880000
<6>[    0.776559,5] Consumer (IN) PIPE 4,    Producer (OUT) PIPE 5
<6>[    0.776559,5] IO base BAM = 0x00000000
<6>[    0.776559,5] BAM IRQ 59
<6>[    0.776559,5] Engines Availability = 0x2010853
<6>[    0.776829,5] QCE50: qce_sps_init:  Qualcomm MSM CE-BAM at 0x0000000000704000 irq 59
<6>[    0.778983,5] qcrypto 720000.qcrypto: qcrypto-ecb-aes
<6>[    0.779056,5] qcrypto 720000.qcrypto: qcrypto-cbc-aes
<6>[    0.779129,5] qcrypto 720000.qcrypto: qcrypto-ctr-aes
<6>[    0.779199,5] qcrypto 720000.qcrypto: qcrypto-ecb-des
<6>[    0.779269,5] qcrypto 720000.qcrypto: qcrypto-cbc-des
<6>[    0.779340,5] qcrypto 720000.qcrypto: qcrypto-ecb-3des
<6>[    0.779410,5] qcrypto 720000.qcrypto: qcrypto-cbc-3des
<6>[    0.779480,5] qcrypto 720000.qcrypto: qcrypto-xts-aes
<6>[    0.779550,5] qcrypto 720000.qcrypto: qcrypto-sha1
<6>[    0.779626,5] qcrypto 720000.qcrypto: qcrypto-sha256
<6>[    0.779697,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-aes
<6>[    0.779768,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-des
<6>[    0.779838,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-3des
<6>[    0.779909,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-aes
<6>[    0.779981,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-des
<6>[    0.780066,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-3des
<6>[    0.780139,5] qcrypto 720000.qcrypto: qcrypto-hmac-sha1
<6>[    0.780210,5] qcrypto 720000.qcrypto: qcrypto-hmac-sha256
<6>[    0.780280,5] qcrypto 720000.qcrypto: qcrypto-aes-ccm
<6>[    0.780350,5] qcrypto 720000.qcrypto: qcrypto-rfc4309-aes-ccm
<3>[    0.781083,5] qcom_ice_get_device_tree_data: No vdd-hba-supply regulator, assuming not needed
<6>[    0.781187,5] ICE IRQ = 60
<6>[    0.781892,5] SCSI Media Changer driver v0.25 
<3>[    0.783292,5] spi_qsd 7af8000.spi: init_resources: unable to get core_clk
<3>[    0.784065,5] sps: BAM device 0x07884000 is not registered yet.
<6>[    0.784207,5] sps:BAM 0x07884000 is registered.
<6>[    0.784710,5] sps:BAM 0x07884000 (va:0xe9b20000) enabled: ver:0x19, number of pipes:12
<6>[    0.785361,5] tun: Universal TUN/TAP device driver, 1.6
<6>[    0.785367,5] tun: (C) 1999-2004 Max Krasnyansky <<EMAIL>>
<6>[    0.785422,5] PPP generic driver version 2.4.2
<6>[    0.785493,5] PPP BSD Compression module registered
<6>[    0.785500,5] PPP Deflate Compression module registered
<6>[    0.785520,5] PPP MPPE Compression module registered
<6>[    0.785529,5] NET: Registered protocol family 24
<6>[    0.786128,5] wcnss_wlan probed in built-in mode
<6>[    0.786782,5] pegasus: v0.9.3 (2013/04/25), Pegasus/Pegasus II USB Ethernet driver
<6>[    0.786847,5] usbcore: registered new interface driver pegasus
<6>[    0.786888,5] usbcore: registered new interface driver asix
<6>[    0.786918,5] usbcore: registered new interface driver ax88179_178a
<6>[    0.786948,5] usbcore: registered new interface driver cdc_ether
<6>[    0.786978,5] usbcore: registered new interface driver net1080
<6>[    0.787008,5] usbcore: registered new interface driver cdc_subset
<6>[    0.787037,5] usbcore: registered new interface driver zaurus
<6>[    0.787069,5] usbcore: registered new interface driver MOSCHIP usb-ethernet driver
<6>[    0.787204,5] usbcore: registered new interface driver cdc_ncm
<3>[    0.788539,5] scm_call failed: func id 0x2000c16, ret: -1, syscall returns: 0x0, 0x0, 0x0
<6>[    0.788545,5] hyp_assign_table: Failed to assign memory protection, ret = -5
<3>[    0.788553,5] msm_sharedmem: setup_shared_ram_perms: hyp_assign_phys failed IPA=0x0160xf4500000 size=1572864 err=-5
<6>[    0.788636,5] msm_sharedmem: msm_sharedmem_probe: Device created for client 'rmtfs'
<6>[    0.790371,5] msm_sharedmem: sharedmem_register_qmi: qmi init successful
<3>[    0.792245,5] msm-dwc3 7000000.ssusb: unable to get dbm device
<6>[    0.793234,5] ehci_hcd: USB 2.0 'Enhanced' Host Controller (EHCI) Driver
<6>[    0.793241,5] ehci-msm: Qualcomm On-Chip EHCI Host Controller
<6>[    0.793531,5] usbcore: registered new interface driver cdc_acm
<6>[    0.793536,5] cdc_acm: USB Abstract Control Model driver for USB modems and ISDN adapters
<6>[    0.793576,5] usbcore: registered new interface driver usb-storage
<6>[    0.793603,5] usbcore: registered new interface driver ums-alauda
<6>[    0.793630,5] usbcore: registered new interface driver ums-cypress
<6>[    0.793655,5] usbcore: registered new interface driver ums-datafab
<6>[    0.793682,5] usbcore: registered new interface driver ums-freecom
<6>[    0.793708,5] usbcore: registered new interface driver ums-isd200
<6>[    0.793735,5] usbcore: registered new interface driver ums-jumpshot
<6>[    0.793760,5] usbcore: registered new interface driver ums-karma
<6>[    0.793787,5] usbcore: registered new interface driver ums-onetouch
<6>[    0.793813,5] usbcore: registered new interface driver ums-sddr09
<6>[    0.793842,5] usbcore: registered new interface driver ums-sddr55
<6>[    0.793869,5] usbcore: registered new interface driver ums-usbat
<6>[    0.793937,5] usbcore: registered new interface driver usbserial
<6>[    0.793969,5] usbcore: registered new interface driver usb_ehset_test
<6>[    0.794436,5] gbridge_init: gbridge_init successs.
<6>[    0.794660,5] mousedev: PS/2 mouse device common for all mice
<6>[    0.794800,5] usbcore: registered new interface driver xpad
<6>[    0.794887,5] ft5x06_ts 3-0038: processing modifier config_modifier-charger[0]
<5>[    0.794892,5] using charger detection
<6>[    0.794993,5] ft5x06_ts 3-0038: processing modifier config_modifier-fps[1]
<5>[    0.794998,5] sing fingerprint sensor detection
<5>[    0.795004,5] using touch clip area in fps-active
<6>[    0.795130,5] input: ft5x06_ts as /devices/soc/78b7000.i2c/i2c-3/3-0038/input/input1
<3>[    1.020248,5] i2c-msm-v2 78b7000.i2c: msm_bus_scale_register_client(mstr-id:86):0xe (ok)
<6>[    1.020453,5] ft5x06_ts 3-0038: Device ID = 0x54
<6>[    1.020566,5] assigned minor 56
<6>[    1.020678,5] ft5x06_ts 3-0038: Create proc entry success
<6>[    1.020832,5] ft5x06_ts 3-0038: report rate = 110Hz
<6>[    1.021430,5] ft5x06_ts 3-0038: Firmware version = 6.0.0
<6>[    1.021582,5] vendor id 0x04 panel supplier is biel
<6>[    1.021757,5] ft5x06_ts 3-0038: Firmware id = 0x0001
<3>[    1.021837,5] ft5x06_ts 3-0038: Failed to register fps_notifier: -19
<3>[    1.022298,5] [NVT-ts] nvt_driver_init 1865: start
<6>[    1.022326,5] nvt_driver_init: finished
<6>[    1.022855,5] input: hbtp_vm as /devices/virtual/input/input2
<3>[    1.023628,5] fpc1020 spi8.0: Unable to read wakelock time
<6>[    1.023739,5] input: fpc1020 as /devices/virtual/input/input3
<6>[    1.023789,5] fpc1020 spi8.0: fpc1020_probe: ok
<6>[    1.023813,5] Driver ltr559 init.
<3>[    1.156897,0] i2c-msm-v2 7af7000.i2c: msm_bus_scale_register_client(mstr-id:84):0xf (ok)
<4>[    1.157103,0] ltr559_check_chip_id read the  LTR559_MANUFAC_ID is 0x5
<6>[    1.171308,0] ltr559_gpio_irq: INT No. 254
<6>[    1.171419,0] input: ltr559-ps as /devices/soc/7af7000.i2c/i2c-7/7-0023/input/input4
<4>[    1.171470,0] ltr559_probe input device success.
<6>[    1.172093,0] qcom,qpnp-rtc qpnp-rtc-8: rtc core: registered qpnp_rtc as rtc0
<6>[    1.172226,0] i2c /dev entries driver
<3>[    1.178179,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.180091,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.180731,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.181310,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.183155,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.184286,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.185378,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<5>[    1.185901,4] msm_actuator_platform_probe:2086 No valid actuator GPIOs data
<5>[    1.185986,4] msm_actuator_platform_probe:2086 No valid actuator GPIOs data
<3>[    1.186585,4] msm_eeprom_platform_probe failed 2029
<3>[    1.186973,4] msm_eeprom_platform_probe failed 2029
<3>[    1.187306,4] msm_eeprom_platform_probe failed 2029
<3>[    1.187948,4] msm_flash_get_pmic_source_info:1000 alternate current: read failed
<3>[    1.187955,4] msm_flash_get_pmic_source_info:1020 alternate max-current: read failed
<3>[    1.187961,4] msm_flash_get_pmic_source_info:1040 alternate duration: read failed
<3>[    1.187997,4] msm_flash_get_pmic_source_info:1000 alternate current: read failed
<3>[    1.188003,4] msm_flash_get_pmic_source_info:1020 alternate max-current: read failed
<3>[    1.188008,4] msm_flash_get_pmic_source_info:1040 alternate duration: read failed
<3>[    1.188042,4] msm_flash_get_pmic_source_info:1110 alternate current: read failed
<3>[    1.188048,4] msm_flash_get_pmic_source_info:1130 alternate current: read failed
<3>[    1.188081,4] msm_flash_get_pmic_source_info:1110 alternate current: read failed
<3>[    1.188087,4] msm_flash_get_pmic_source_info:1130 alternate current: read failed
<5>[    1.188094,4] msm_flash_get_dt_data:1203 No valid flash GPIOs data
<3>[    1.188099,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.188802,4] adp1660 i2c_add_driver success
<6>[    1.194710,4] MSM-CPP cpp_init_hardware:1005 CPP HW Version: 0x40030003
<3>[    1.194719,4] MSM-CPP cpp_init_hardware:1023 stream_cnt:0
<3>[    1.195970,5] CAM-SOC msm_camera_get_reg_base:787 err: mem resource vfe_fuse not found
<3>[    1.195975,5] CAM-SOC msm_camera_get_res_size:830 err: mem resource vfe_fuse not found
<3>[    1.197052,5] CAM-SOC msm_camera_get_reg_base:787 err: mem resource vfe_fuse not found
<3>[    1.197058,5] CAM-SOC msm_camera_get_res_size:830 err: mem resource vfe_fuse not found
<3>[    1.204078,5] __msm_jpeg_init:1537] Jpeg Device id 0
<6>[    1.205701,5] usbcore: registered new interface driver uvcvideo
<6>[    1.205707,5] USB Video Class driver (1.1.1)
<6>[    1.206284,5] FG: fg_check_ima_exception: Initial ima_err_sts=0 ima_exp_sts=0 ima_hw_sts=88
<6>[    1.206510,5] FG: fg_empty_soc_irq_handler: triggered 0x21
<3>[    1.207555,5] FG: fg_power_get_property: ESR Bad: -22 mOhm
<3>[    1.207800,5] FG: fg_power_get_property: ESR Bad: -22 mOhm
<6>[    1.207847,5] FG: fg_probe: FG Probe success - FG Revision DIG:3.1 ANA:1.2 PMIC subtype=17
<3>[    1.209352,5] unable to find DT imem DLOAD mode node
<3>[    1.209739,5] unable to find DT imem EDLOAD mode node
<4>[    1.210813,6] thermal thermal_zone1: failed to read out thermal zone 1
<4>[    1.210986,6] thermal thermal_zone2: failed to read out thermal zone 2
<4>[    1.211137,6] thermal thermal_zone3: failed to read out thermal zone 3
<4>[    1.211286,6] thermal thermal_zone4: failed to read out thermal zone 4
<3>[    1.211725,6] qpnp_vadc_read: no vadc_chg_vote found
<3>[    1.211731,6] qpnp_vadc_get_temp: VADC read error with -22
<4>[    1.211738,6] thermal thermal_zone5: failed to read out thermal zone 5
<6>[    1.233611,6] device-mapper: uevent: version 1.0.3
<6>[    1.233746,6] device-mapper: ioctl: 4.28.0-ioctl (2014-09-17) initialised: <EMAIL>
<6>[    1.233825,6] device-mapper: req-crypt: dm-req-crypt successfully initalized.
<6>[    1.233825,6] 
<6>[    1.234481,6] sdhci: Secure Digital Host Controller Interface driver
<6>[    1.234485,6] sdhci: Copyright(c) Pierre Ossman
<6>[    1.234492,6] sdhci-pltfm: SDHCI platform and OF driver helper
<6>[    1.236578,1] qcom_ice_get_pdevice: found ice device c3b259c0
<6>[    1.236585,1] qcom_ice_get_pdevice: matching platform device e5823800
<6>[    1.240408,1] qcom_ice 7803000.sdcc1ice: QC ICE 2.1.44 device found @0xe99a0000
<6>[    1.240768,1] sdhci_msm 7824900.sdhci: No vmmc regulator found
<6>[    1.240774,1] sdhci_msm 7824900.sdhci: No vqmmc regulator found
<6>[    1.241070,1] mmc0: SDHCI controller on 7824900.sdhci [7824900.sdhci] using 32-bit ADMA in CMDQ mode
<4>[    1.273742,1] sdhci_msm 7864900.sdhci: sdhci_msm_probe: ICE device is not enabled
<6>[    1.288071,1] sdhci_msm 7864900.sdhci: No vmmc regulator found
<6>[    1.288078,1] sdhci_msm 7864900.sdhci: No vqmmc regulator found
<6>[    1.288391,1] mmc1: SDHCI controller on 7864900.sdhci [7864900.sdhci] using 32-bit ADMA in legacy mode
<6>[    1.309466,2] mmc0: Out-of-interrupt timeout is 50[ms]
<6>[    1.309472,2] mmc0: BKOPS_EN equals 0x2
<6>[    1.309477,2] mmc0: eMMC FW version: 0x07
<6>[    1.309482,2] mmc0: CMDQ supported: depth: 16
<6>[    1.309486,2] mmc0: cache barrier support 0 flush policy 0
<6>[    1.319094,2] cmdq_host_alloc_tdl: desc_size: 512 data_sz: 126976 slot-sz: 16
<6>[    1.319281,2] mmc0: CMDQ enabled on card
<6>[    1.319290,2] mmc0: new HS400 MMC card at address 0001
<6>[    1.319544,2] sdhci_msm_pm_qos_cpu_init (): voted for group #0 (mask=0xf) latency=2
<6>[    1.319553,2] sdhci_msm_pm_qos_cpu_init (): voted for group #1 (mask=0xf0) latency=2
<6>[    1.319655,2] mmcblk0: mmc0:0001 RC14MB 58.2 GiB 
<6>[    1.319741,2] mmcblk0rpmb: mmc0:0001 RC14MB partition 3 4.00 MiB
<6>[    1.320701,0] qcom,leds-atc leds-atc-20: atc_leds_probe success
<6>[    1.320843,0] hidraw: raw HID events driver (C) Jiri Kosina
<6>[    1.321001,1] tz_log 8600720.tz-log: Hyp log service is not supported
<6>[    1.321246,0] usbcore: registered new interface driver usbhid
<6>[    1.321251,0] usbhid: USB HID core driver
<6>[    1.321350,0]  mmcblk0: p1 p2 p3 p4 p5 p6 p7 p8 p9 p10 p11 p12 p13 p14 p15 p16 p17 p18 p19 p20 p21 p22 p23 p24 p25 p26 p27 p28 p29 p30 p31 p32 p33 p34 p35 p36 p37 p38 p39 p40 p41 p42 p43 p44 p45 p46 p47 p48 p49 p50 p51 p52 p53 p54
<6>[    1.321735,3] ashmem: initialized
<6>[    1.322055,3] qpnp_coincell_charger_show_state: enabled=Y, voltage=3200 mV, resistance=2100 ohm
<6>[    1.324803,3] bimc-bwmon 408000.qcom,cpu-bwmon: BW HWmon governor registered.
<3>[    1.326499,3] devfreq soc:qcom,cpubw: Couldn't update frequency transition information.
<3>[    1.326628,3] devfreq soc:qcom,mincpubw: Couldn't update frequency transition information.
<3>[    1.328236,3] sensors-ssc soc:qcom,msm-ssc-sensors: msm_ssc_sensors_dt_parse: get qdsp timer cntpct hi offset fail
<6>[    1.328245,3] sensors-ssc soc:qcom,msm-ssc-sensors: slpi_loader_init_sysfs: Could not parse dt
<6>[    1.328589,3] usbcore: registered new interface driver snd-usb-audio
<6>[    1.332013,3] cs35l35 7-0040: Cirrus Logic CS35L35 (35a35), Revision: 00
<6>[    1.343340,4] msm-pcm-lpa soc:qcom,msm-pcm-lpa: msm_pcm_probe: dev name soc:qcom,msm-pcm-lpa
<6>[    1.347649,4] u32 classifier
<6>[    1.347654,4]     Actions configured
<6>[    1.347681,4] Netfilter messages via NETLINK v0.30.
<6>[    1.347718,4] nf_conntrack version 0.5.0 (16384 buckets, 65536 max)
<6>[    1.347972,4] ctnetlink v0.93: registering with nfnetlink.
<6>[    1.348427,4] xt_time: kernel timezone is -0000
<6>[    1.348649,4] ip_tables: (C) 2000-2006 Netfilter Core Team
<6>[    1.348761,4] arp_tables: (C) 2002 David S. Miller
<6>[    1.348799,4] TCP: cubic registered
<6>[    1.348805,4] Initializing XFRM netlink socket
<6>[    1.349041,4] NET: Registered protocol family 10
<6>[    1.349692,5] mip6: Mobile IPv6
<6>[    1.349713,5] ip6_tables: (C) 2000-2006 Netfilter Core Team
<6>[    1.349814,5] sit: IPv6 over IPv4 tunneling driver
<6>[    1.350164,5] NET: Registered protocol family 17
<6>[    1.350181,5] NET: Registered protocol family 15
<6>[    1.350211,5] bridge: automatic filtering via arp/ip/ip6tables has been deprecated. Update your scripts to load br_netfilter if you need this.
<6>[    1.350220,5] Ebtables v2.0 registered
<6>[    1.350319,5] Bluetooth: e6e05eb0
<6>[    1.350330,5] Bluetooth: e6e05ea8Bluetooth: e6e05ec0
<6>[    1.350356,5] Bluetooth: e6e05ea0Bluetooth: e6e05ea0
<6>[    1.350368,5] Bluetooth: e6e05e98Bluetooth: e6e05ed8
<6>[    1.350382,5] Bluetooth: e6e05ed8<6>[    1.350416,5] l2tp_core: L2TP core driver, V2.0
<6>[    1.350429,5] l2tp_ppp: PPPoL2TP kernel driver, V2.0
<6>[    1.350436,5] l2tp_ip: L2TP IP encapsulation support (L2TPv3)
<6>[    1.350452,5] l2tp_netlink: L2TP netlink interface
<6>[    1.350473,5] l2tp_eth: L2TP ethernet pseudowire support (L2TPv3)
<6>[    1.350488,5] l2tp_debugfs: L2TP debugfs support
<6>[    1.350496,5] l2tp_ip6: L2TP IP encapsulation support for IPv6 (L2TPv3)
<6>[    1.351052,5] NET: Registered protocol family 27
<6>[    1.354596,6] subsys-pil-tz a21b000.qcom,pronto: for wcnss segments only will be dumped.
<6>[    1.356253,6] pil-q6v5-mss 4080000.qcom,mss: for modem segments only will be dumped.
<6>[    1.357671,6] msm-dwc3 7000000.ssusb: unable to read dcp-max-current, using define value
<6>[    1.358015,6] ft5x06_ts 3-0038: unset chg state
<6>[    1.358034,6] ft5x06_ts 3-0038: ps present state not change
<6>[    1.359129,6] sps:BAM 0x07104000 is registered.
<3>[    1.362092,6] qpnp-smbcharger qpnp-smbcharger-17: node /soc/qcom,spmi@200f000/qcom,pmi8950@2/qcom,qpnp-smbcharger IO resource absent!
<3>[    1.362216,6] qpnp-smbcharger qpnp-smbcharger-17: length=8
<3>[    1.362223,6] qpnp-smbcharger qpnp-smbcharger-17: num parallel charge entries=8
<6>[    1.362307,6] smbcharger_charger_otg: no parameters
<6>[    1.362937,6] FG: fg_vbat_est_check: vbat(4221471),est-vbat(4209112),diff(12359),threshold(300000)
<6>[    1.384844,6] FG: fg_vbat_est_check: vbat(4221471),est-vbat(4209112),diff(12359),threshold(300000)
<3>[    1.387252,7] qpnp-smbcharger qpnp-smbcharger-17: node /soc/qcom,spmi@200f000/qcom,pmi8950@2/qcom,qpnp-smbcharger IO resource absent!
<6>[    1.387364,7] ft5x06_ts 3-0038: ps present state not change
<6>[    1.388078,7] qpnp-smbcharger qpnp-smbcharger-17: SMBCHG successfully probe Charger version=SCHG_LITE Revision DIG:0.0 ANA:0.1 batt=1 dc=0 usb=0
<5>[    1.391581,5] Registering SWP/SWPB emulation handler
<6>[    1.391894,5] registered taskstats version 1
<3>[    1.392600,7] qpnp-smbcharger qpnp-smbcharger-17: Battery Temp State: Unknown -> Cool at -2C
<3>[    1.392607,7] qpnp-smbcharger qpnp-smbcharger-17: Ext High = High
<6>[    1.392761,7] ft5x06_ts 3-0038: ps present state not change
<6>[    1.396038,5] fastrpc soc:qcom,adsprpc-mem: for adsp_rh segments only will be dumped.
<1>[    1.397405,5] drv260x: drv260x_init success
<6>[    1.397825,5] utags (utags_probe): Done [config]
<6>[    1.397849,5] utags (utags_dt_init): backup storage path not provided
<6>[    1.398444,5] utags (utags_probe): Done [hw]
<6>[    1.399034,5] RNDIS_IPA module is loaded.
<6>[    1.399392,5] file system registered
<6>[    1.399435,5] mbim_init: initialize 1 instances
<6>[    1.399482,5] mbim_init: Initialized 1 ports
<6>[    1.400523,5] rndis_qc_init: initialize rndis QC instance
<6>[    1.400693,5] Number of LUNs=8
<6>[    1.400701,5] Mass Storage Function, version: 2009/09/11
<6>[    1.400707,5] LUN: removable file: (no medium)
<6>[    1.400719,5] Number of LUNs=1
<6>[    1.400760,5] LUN: removable file: (no medium)
<6>[    1.400765,5] Number of LUNs=1
<6>[    1.401435,5] android_usb gadget: android_usb ready
<6>[    1.402654,5] input: gpio-keys as /devices/soc/soc:gpio_keys/input/input5
<4>[    1.402978,5] i2c-core: driver [stmvl53l0] using legacy resume method
<6>[    1.403482,5] qcom,qpnp-rtc qpnp-rtc-8: setting system clock to 1970-11-09 12:01:12 UTC (27000072)
<6>[    1.406026,4] msm-core initialized without polling period
<3>[    1.408574,5] parse_cpu_levels: idx 1 276
<3>[    1.408584,5] calculate_residency: residency < 0 for LPM
<3>[    1.408700,5] parse_cpu_levels: idx 1 286
<3>[    1.408706,5] calculate_residency: residency < 0 for LPM
<3>[    1.411699,5] qcom,qpnp-flash-led qpnp-flash-led-23: Unable to acquire pinctrl
<6>[    1.413413,5] rmnet_ipa started initialization
<6>[    1.413420,5] IPA SSR support = True
<6>[    1.413424,5] IPA ipa-loaduC = True
<6>[    1.413428,5] IPA SG support = True
<3>[    1.415258,5] ipa ipa_sps_irq_control_all:942 EP (5) not allocated.
<3>[    1.415266,5] ipa ipa2_uc_state_check:301 uC is not loaded
<6>[    1.416214,0] msm-dwc3 7000000.ssusb: DWC3 in low power mode
<6>[    1.417192,5] rmnet_ipa completed initialization
<6>[    1.419621,5] qcom,cc-debug-8953 1874000.qcom,cc-debug: Registered Debug Mux successfully
<6>[    1.428175,5] msm8952-asoc-wcd c051000.sound: default codec configured
<3>[    1.431441,5] msm8952-asoc-wcd c051000.sound: ASoC: platform (null) not registered
<3>[    1.431482,5] msm8952-asoc-wcd c051000.sound: snd_soc_register_card failed (-517)
<6>[    1.432532,5] apc_mem_acc_corner: disabling
<6>[    1.432538,5] gfx_mem_acc_corner: disabling
<6>[    1.432577,5] vci_fci: disabling
<6>[    1.432615,5] regulator_proxy_consumer_remove_all: removing regulator proxy consumer requests
<6>[    1.432654,5] clock_late_init: Removing enables held for handed-off clocks
<6>[    1.436360,5] ALSA device list:
<6>[    1.436365,5]   No soundcards found.
<3>[    1.436437,5] Warning: unable to open an initial console.
<6>[    1.471975,5] Freeing unused kernel memory: 504K
<14>[    1.473527,5] init: init first stage started!
<14>[    1.473564,5] init: First stage mount skipped (recovery mode)
<14>[    1.473775,5] init: Using Android DT directory /proc/device-tree/firmware/android/
<14>[    1.473838,5] init: Skipped setting INIT_AVB_VERSION (not vbmeta compatible)
<14>[    1.473856,5] init: Loading SELinux policy
<7>[    1.478892,5] SELinux: 2048 avtab hash slots, 29508 rules.
<7>[    1.491089,5] SELinux: 2048 avtab hash slots, 29508 rules.
<7>[    1.491111,5] SELinux:  1 users, 2 roles, 2214 types, 0 bools, 1 sens, 1024 cats
<7>[    1.491117,5] SELinux:  93 classes, 29508 rules
<7>[    1.494414,5] SELinux:  Completing initialization.
<7>[    1.494420,5] SELinux:  Setting up existing superblocks.
<7>[    1.494435,5] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<7>[    1.494456,5] SELinux: initialized (dev rootfs, type rootfs), uses genfs_contexts
<7>[    1.494627,5] SELinux: initialized (dev bdev, type bdev), not configured for labeling
<7>[    1.494640,5] SELinux: initialized (dev proc, type proc), uses genfs_contexts
<7>[    1.494666,5] SELinux: initialized (dev debugfs, type debugfs), uses genfs_contexts
<4>[    1.503418,4] bcl_peripheral:bcl_poll_vbat_high Vbat reached high clear trip. vbat:4230720
<3>[    1.503445,4] bcl_peripheral:bcl_poll_ibat_low Invalid ibat state 1
<7>[    1.518717,5] SELinux: initialized (dev sockfs, type sockfs), uses task SIDs
<7>[    1.518734,5] SELinux: initialized (dev tracefs, type tracefs), uses genfs_contexts
<7>[    1.552255,5] SELinux: initialized (dev pipefs, type pipefs), uses task SIDs
<7>[    1.552269,5] SELinux: initialized (dev anon_inodefs, type anon_inodefs), not configured for labeling
<7>[    1.552275,5] SELinux: initialized (dev aio, type aio), not configured for labeling
<7>[    1.552285,5] SELinux: initialized (dev devpts, type devpts), uses transition SIDs
<7>[    1.552302,5] SELinux: initialized (dev configfs, type configfs), uses genfs_contexts
<7>[    1.552315,5] SELinux: initialized (dev selinuxfs, type selinuxfs), uses genfs_contexts
<7>[    1.552373,5] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<7>[    1.552407,5] SELinux: initialized (dev sysfs, type sysfs), uses genfs_contexts
<5>[    1.562001,5] audit: type=1403 audit(27000072.653:2): policy loaded auid=4294967295 ses=4294967295
<14>[    1.562238,5] selinux: SELinux: Loaded policy from /sepolicy
<14>[    1.562238,5] 
<5>[    1.562454,5] audit: type=1404 audit(27000072.653:3): enforcing=1 old_enforcing=0 auid=4294967295 ses=4294967295
<14>[    1.585969,5] selinux: SELinux: Loaded file_contexts
<14>[    1.585969,5] 
<5>[    1.586972,5] random: init urandom read with 86 bits of entropy available
<14>[    1.587794,5] init: init second stage started!
<14>[    1.596464,5] init: Using Android DT directory /proc/device-tree/firmware/android/
<14>[    1.602770,5] selinux: SELinux: Loaded file_contexts
<14>[    1.602770,5] 
<14>[    1.604549,5] selinux: SELinux: Loaded property_contexts from /plat_property_contexts & /nonplat_property_contexts.
<14>[    1.604549,5] 
<14>[    1.604568,5] init: Running restorecon...
<11>[    1.611993,5] selinux: SELinux:  Could not stat /dev/block: No such file or directory.
<11>[    1.611993,5] 
<11>[    1.612361,5] init: waitid failed: No child processes
<12>[    1.612405,5] init: Couldn't load property file: Unable to open '/system/etc/prop.default': No such file or directory: No such file or directory
<12>[    1.612853,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.612877,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.612901,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.612924,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.612947,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.612970,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.612993,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613016,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613039,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613064,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613088,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613111,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613134,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613157,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613180,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613204,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613227,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613250,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613272,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613295,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613318,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613341,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613364,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613409,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613432,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613455,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613478,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613500,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613523,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613546,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613569,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613592,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613615,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613638,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613661,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613684,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613709,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613732,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613755,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613779,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613802,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613825,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.613848,5] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<11>[    1.615638,5] init: property_set("ro.cutoff_voltage_mv", "3400") failed: property already set
<11>[    1.616361,5] init: property_set("ro.opengles.version", "196610") failed: property already set
<11>[    1.616898,5] init: property_set("ro.carrier", "unknown") failed: property already set
<12>[    1.617373,5] init: Couldn't load property file: Unable to open '/odm/default.prop': No such file or directory: No such file or directory
<12>[    1.617433,5] init: Couldn't load property file: Unable to open '/vendor/default.prop': No such file or directory: No such file or directory
<14>[    1.617929,5] init: Created socket '/dev/socket/property_service', mode 666, user 0, group 0
<14>[    1.618047,5] init: Parsing file /init.rc...
<14>[    1.618148,5] init: Added '/init.recovery.qcom.rc' to import list
<14>[    1.618488,5] init: Parsing file /init.recovery.qcom.rc...
<14>[    1.618617,5] init: Parsing file /system/etc/init...
<11>[    1.618640,5] init: Unable to open '/system/etc/init': No such file or directory
<14>[    1.618661,5] init: Parsing file /vendor/etc/init...
<11>[    1.618683,5] init: Unable to open '/vendor/etc/init': No such file or directory
<14>[    1.618701,5] init: Parsing file /odm/etc/init...
<11>[    1.618721,5] init: Unable to open '/odm/etc/init': No such file or directory
<14>[    1.618815,5] init: processing action (early-init) from (/init.rc:3)
<14>[    1.618874,5] init: starting service 'ueventd'...
<5>[    1.619329,5] audit: type=1400 audit(27000072.710:4): avc:  denied  { create } for  uid=0 pid=1 comm="init" name="cgroup.procs" scontext=u:r:init:s0 tcontext=u:object_r:rootfs:s0 tclass=file permissive=0
<11>[    1.619396,5] init: Failed to write '415' to /acct/uid_0/pid_415/cgroup.procs: Permission denied
<11>[    1.619415,5] init: createProcessGroup(0, 415) failed for service 'ueventd': Permission denied
<14>[    1.619487,5] init: processing action (wait_for_coldboot_done) from (<Builtin Action>:0)
<14>[    1.621846,4] ueventd: ueventd started!
<14>[    1.621899,4] ueventd: Parsing file /ueventd.rc...
<11>[    1.622200,4] ueventd: /ueventd.rc: 66: invalid gid 'qcom_diag'
<14>[    1.622683,4] ueventd: Parsing file /vendor/ueventd.rc...
<11>[    1.622707,4] ueventd: Unable to open '/vendor/ueventd.rc': No such file or directory
<14>[    1.622724,4] ueventd: Parsing file /odm/ueventd.rc...
<11>[    1.622748,4] ueventd: Unable to open '/odm/ueventd.rc': No such file or directory
<14>[    1.622817,4] ueventd: Parsing file /ueventd.qcom.rc...
<11>[    1.622838,4] ueventd: Unable to open '/ueventd.qcom.rc': No such file or directory
<14>[    1.628191,4] selinux: SELinux: Loaded file_contexts
<14>[    1.628191,4] 
<14>[    1.759338,5] selinux: SELinux: Loaded file_contexts
<14>[    1.759338,5] 
<14>[    1.759411,7] selinux: SELinux: Loaded file_contexts
<14>[    1.759411,7] 
<14>[    1.759416,4] selinux: SELinux: Loaded file_contexts
<14>[    1.759416,4] 
<14>[    1.759672,1] selinux: SELinux: Loaded file_contexts
<14>[    1.759672,1] 
<14>[    1.759840,3] selinux: SELinux: Loaded file_contexts
<14>[    1.759840,3] 
<14>[    1.765198,6] selinux: SELinux: Loaded file_contexts
<14>[    1.765198,6] 
<14>[    1.766399,2] selinux: SELinux: Loaded file_contexts
<14>[    1.766399,2] 
<14>[    1.766400,0] selinux: SELinux: Loaded file_contexts
<14>[    1.766400,0] 
<14>[    1.767673,7] selinux: SELinux: Loaded file_contexts
<14>[    1.767673,7] 
<14>[    3.129071,7] ueventd: Coldboot took 1.5 seconds
<14>[    3.131993,1] init: Command 'wait_for_coldboot_done' action=wait_for_coldboot_done (<Builtin Action>:0) returned 0 took 1512ms.
<14>[    3.132031,1] init: processing action (mix_hwrng_into_linux_rng) from (<Builtin Action>:0)
<14>[    3.133141,1] init: Mixed 512 bytes from /dev/hw_random into /dev/urandom
<14>[    3.133170,1] init: processing action (set_mmap_rnd_bits) from (<Builtin Action>:0)
<14>[    3.133192,1] init: processing action (set_kptr_restrict) from (<Builtin Action>:0)
<14>[    3.133495,1] init: processing action (keychord_init) from (<Builtin Action>:0)
<14>[    3.133523,1] init: processing action (console_init) from (<Builtin Action>:0)
<14>[    3.133571,1] init: processing action (init) from (/init.rc:9)
<7>[    3.134139,1] SELinux: initialized (dev cgroup, type cgroup), uses genfs_contexts
<7>[    3.135934,1] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<14>[    3.136206,1] init: processing action (init) from (/init.recovery.qcom.rc:28)
<11>[    3.136249,1] init: Unable to open '/sys/class/backlight/panel0-backlight/brightness': No such file or directory
<14>[    3.137362,1] init: processing action (mix_hwrng_into_linux_rng) from (<Builtin Action>:0)
<14>[    3.138432,1] init: Mixed 512 bytes from /dev/hw_random into /dev/urandom
<14>[    3.138464,1] init: processing action (late-init) from (/init.rc:66)
<14>[    3.138508,1] init: processing action (queue_property_triggers) from (<Builtin Action>:0)
<14>[    3.138539,1] init: processing action (fs) from (/init.rc:36)
<7>[    3.139628,1] SELinux: initialized (dev functionfs, type functionfs), uses genfs_contexts
<3>[    3.139728,1] enable_store: android_usb: already disabled
<14>[    3.140272,0] init: processing action (load_system_props_action) from (/init.rc:59)
<12>[    3.140377,0] init: HW descriptor status=2
<6>[    3.140387,0] utags (reload_write): [init] (pid 1) [hw] 1
<12>[    3.252804,1] init: Sent HW descriptor reload command rc=2
<11>[    3.252878,1] init: File /vendor/etc/vhw.xml not found
<12>[    3.252926,1] init: Couldn't load property file: Unable to open '/system/build.prop': No such file or directory: No such file or directory
<12>[    3.252952,1] init: Couldn't load property file: Unable to open '/odm/build.prop': No such file or directory: No such file or directory
<12>[    3.252977,1] init: Couldn't load property file: Unable to open '/vendor/build.prop': No such file or directory: No such file or directory
<12>[    3.253000,1] init: Couldn't load property file: Unable to open '/factory/factory.prop': No such file or directory: No such file or directory
<14>[    3.254478,1] init: Command 'load_system_props' action=load_system_props_action (/init.rc:60) returned 0 took 114ms.
<14>[    3.254509,1] init: processing action (firmware_mounts_complete) from (/init.rc:62)
<14>[    3.254553,1] init: processing action (boot) from (/init.rc:51)
<14>[    3.254998,1] init: starting service 'charger'...
<14>[    3.255676,1] init: starting service 'recovery'...
<14>[    3.256275,1] init: processing action (enable_property_trigger) from (<Builtin Action>:0)
<12>[    3.259595,4] healthd: battery l=100 v=4194 t=31.7 h=7 st=3 c=649 fc=0 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<5>[    3.263356,5] audit: type=1400 audit(27000074.353:5): avc:  denied  { read } for  uid=0 pid=427 comm="recovery" name="u:object_r:sf_lcd_density_prop:s0" dev="tmpfs" ino=14318 scontext=u:r:recovery:s0 tcontext=u:object_r:sf_lcd_density_prop:s0 tclass=file permissive=0
<6>[    3.316816,0] input input5: gpio-keys report volume_up [0x73] type 0x1 state Off
<5>[    3.317106,0] audit: type=1400 audit(27000074.410:6): avc:  denied  { write } for  uid=0 pid=427 comm="recovery" name="brightness" dev="sysfs" ino=22073 scontext=u:r:recovery:s0 tcontext=u:object_r:sysfs_graphics:s0 tclass=file permissive=0
<4>[    3.336897,0] irq 21, desc: e5e2f000, depth: 0, count: 0, unhandled: 0
<4>[    3.336914,0] ->handle_irq():  c03f6d64, msm_gpio_irq_handler+0x0/0x118
<4>[    3.336921,0] ->irq_data.chip(): c1531158, gic_chip+0x0/0x74
<4>[    3.336922,0] ->action():   (null)
<4>[    3.336924,0]    IRQ_NOPROBE set
<4>[    3.336925,0]  IRQ_NOREQUEST set
<4>[    3.336926,0]   IRQ_NOTHREAD set
<6>[    3.337335,7] mdss_dsi_on[0]+.
<5>[    4.328513,6] audit: type=1400 audit(27000075.423:7): avc:  denied  { search } for  uid=0 pid=427 comm="recovery" name="usb" dev="sysfs" ino=31589 scontext=u:r:recovery:s0 tcontext=u:object_r:sysfs_usb_supply:s0 tclass=dir permissive=0
<6>[    4.377163,0] EXT4-fs (mmcblk0p52): mounted filesystem with ordered data mode. Opts: 
<7>[    4.377186,0] SELinux: initialized (dev mmcblk0p52, type ext4), uses xattr
<5>[    5.124031,0] random: nonblocking pool is initialized
<3>[    5.233487,4] FG: fg_get_mmi_battid: Battsn unused
<4>[    5.233504,4] qcom,qpnp-fg qpnp-fg-18: Default Serial Number SB18C15119
<4>[    5.233513,4] qcom,qpnp-fg qpnp-fg-18: Battery Match Found using default qcom,hg30-alt
<6>[    5.238308,4] FG: fg_batt_profile_init: Battery profiles same, using default
<3>[    5.238661,4] qpnp-smbcharger qpnp-smbcharger-17: Enable conflict! ext_high_temp: 1,temp_state: 10,step_chg_state 255
<3>[    5.238668,4] qpnp-smbcharger qpnp-smbcharger-17: Couldn't configure batt chg: 0x0 rc = -22
<6>[    5.241260,4] FG: populate_system_data: cutoff_voltage = 3199901, nom_cap_uah = 3021000 p1p2 = 33, p2p3 = 5
<6>[    5.241313,4] FG: fg_batt_profile_init: Battery SOC: 100, V: 4194311uV
<6>[    5.241351,4] FG: fg_vbat_est_check: vbat(4194311),est-vbat(4187139),diff(7172),threshold(300000)
<12>[    5.242272,6] healthd: battery l=100 v=4194 t=31.7 h=7 st=3 c=649 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[    5.242775,6] healthd: battery l=100 v=4194 t=31.7 h=7 st=3 c=649 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<6>[    6.046654,5] EXT4-fs (mmcblk0p51): mounted filesystem with ordered data mode. Opts: 
<7>[    6.046755,5] SELinux: initialized (dev mmcblk0p51, type ext4), uses mountpoint labeling
<6>[    6.355996,5] EXT4-fs (mmcblk0p19): mounted filesystem with ordered data mode. Opts: 
<7>[    6.356019,5] SELinux: initialized (dev mmcblk0p19, type ext4), uses xattr
<3>[   61.498154,7] qpnp-smbcharger qpnp-smbcharger-17: Battery Temp State: Cool -> Good at 32C
<3>[   61.498158,7] qpnp-smbcharger qpnp-smbcharger-17: Ext High = Low
<12>[   61.509581,0] healthd: battery l=100 v=4268 t=32.5 h=2 st=3 c=284 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[  121.550256,0] healthd: battery l=99 v=4256 t=32.7 h=2 st=3 c=346 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[  121.660538,0] healthd: battery l=99 v=4269 t=32.7 h=2 st=3 c=283 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[  181.680855,0] healthd: battery l=99 v=4262 t=32.7 h=2 st=3 c=300 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[  181.818544,0] healthd: battery l=99 v=4258 t=32.7 h=2 st=3 c=311 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<6>[  231.121823,4] update-binary (438): drop_caches: 3
<6>[  240.412178,2] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0x301c04c9)
<3>[  240.412182,2] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 1th superblock
<6>[  240.412325,2] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xc6a3ab26)
<3>[  240.412327,2] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 2th superblock
<6>[  240.412331,2] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0x301c04c9)
<3>[  240.412333,2] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 1th superblock
<6>[  240.412335,2] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xc6a3ab26)
<3>[  240.412337,2] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 2th superblock
<3>[  240.412679,2] EXT4-fs (mmcblk0p54): VFS: Can't find ext4 filesystem
<12>[  241.845124,0] healthd: battery l=99 v=4255 t=33.5 h=2 st=3 c=323 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[  241.978577,0] healthd: battery l=99 v=4226 t=33.5 h=2 st=3 c=425 fc=2202000 cc=110 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
