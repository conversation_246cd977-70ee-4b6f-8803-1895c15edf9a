<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- GoatGoat Webhook Configuration -->
        <record id="goatgoat_webhook_approval_url" model="ir.config_parameter">
            <field name="key">goatgoat_webhook.approval_url</field>
            <field name="value">https://oaynfzqjielnsipttzbs.supabase.co/functions/v1/odoo-approval-sync</field>
        </record>
        
        <record id="goatgoat_webhook_token" model="ir.config_parameter">
            <field name="key">goatgoat_webhook.token</field>
            <field name="value">odoo-goatgoat-sync-2024</field>
        </record>
        
        <!-- Seller approval webhook URL -->
        <record id="goatgoat_seller_webhook_approval_url" model="ir.config_parameter">
            <field name="key">goatgoat_webhook.seller_approval_url</field>
            <field name="value">https://oaynfzqjielnsipttzbs.supabase.co/functions/v1/seller-approval-webhook</field>
        </record>

        <!-- Enable webhook by default -->
        <record id="goatgoat_webhook_enabled" model="ir.config_parameter">
            <field name="key">goatgoat_webhook.enabled</field>
            <field name="value">True</field>
        </record>
    </data>
</odoo>
