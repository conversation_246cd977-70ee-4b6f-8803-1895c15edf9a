import 'package:flutter/material.dart';
import '../services/shopping_cart_service.dart';
import '../services/delivery_fee_service.dart';
import '../services/comprehensive_order_service.dart';
import '../services/delivery_address_state.dart';
import '../widgets/address_picker.dart';
import '../widgets/delivery_fee_breakdown_widget.dart';
import '../widgets/enhanced_address_picker.dart';
import '../config/feature_flags.dart';

/// CustomerCheckoutScreen - Complete checkout flow with delivery fee integration
///
/// This screen provides the final checkout experience for customers, including:
/// - Order summary with delivery fee breakdown
/// - Address confirmation and editing
/// - Payment method selection
/// - Order placement with delivery fees included
/// - Integration with existing PhonePe payment system
/// - Zero-risk implementation preserving all existing functionality
class CustomerCheckoutScreen extends StatefulWidget {
  final Map<String, dynamic> customer;
  final List<Map<String, dynamic>> cartItems;
  final Map<String, dynamic> cartSummary;

  const CustomerCheckoutScreen({
    super.key,
    required this.customer,
    required this.cartItems,
    required this.cartSummary,
  });

  @override
  State<CustomerCheckoutScreen> createState() => _CustomerCheckoutScreenState();
}

class _CustomerCheckoutScreenState extends State<CustomerCheckoutScreen> {
  final ShoppingCartService _cartService = ShoppingCartService();
  final DeliveryFeeService _deliveryFeeService = DeliveryFeeService();

  bool _isLoading = false;
  String? _errorMessage;
  String? _deliveryAddress;
  Map<String, dynamic>? _deliveryFeeDetails;
  double _deliveryFee = 0.0;

  // Payment method selection
  String _selectedPaymentMethod = 'phonepe'; // Default to PhonePe
  final List<Map<String, String>> _paymentMethods = [
    {'id': 'phonepe', 'name': 'PhonePe', 'icon': 'phonepe'},
    {'id': 'cod', 'name': 'Cash on Delivery', 'icon': 'cash'},
  ];

  @override
  void initState() {
    super.initState();
    _initializeCheckout();
  }

  /// Initialize checkout with delivery address and fee calculation
  Future<void> _initializeCheckout() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final customerId = widget.customer['id'] as String;

      // Initialize shared address state from customer data
      DeliveryAddressState.initializeFromCustomer(widget.customer);

      // Get delivery address from shared state (preserves cart selection)
      if (DeliveryAddressState.hasAddress() &&
          DeliveryAddressState.belongsToCustomer(customerId)) {
        _deliveryAddress = DeliveryAddressState.getCurrentAddress();
        print(
          '✅ CHECKOUT - Using shared state address: ${(_deliveryAddress?.length ?? 0) > 50 ? '${_deliveryAddress!.substring(0, 50)}...' : _deliveryAddress ?? 'None'}',
        );
      } else {
        // Fallback to customer profile address
        _deliveryAddress = widget.customer['address'] as String?;
        print('✅ CHECKOUT - Using customer profile address');
      }

      // Extract delivery fee details from cart summary
      if (widget.cartSummary['delivery_fee'] != null) {
        _deliveryFee = widget.cartSummary['delivery_fee'] as double;
        _deliveryFeeDetails =
            widget.cartSummary['delivery_fee_details'] as Map<String, dynamic>?;
      }

      print(
        '🛒 CHECKOUT - Initialized with delivery fee: ₹${_deliveryFee.toStringAsFixed(0)}',
      );
    } catch (e) {
      print('❌ CHECKOUT - Initialization error: $e');
      setState(() {
        _errorMessage = 'Failed to initialize checkout. Please try again.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Handle address change and recalculate delivery fee
  Future<void> _onAddressChanged(
    String address,
    Map<String, dynamic>? locationData,
  ) async {
    if (address.trim().isEmpty) return;

    setState(() {
      _deliveryAddress = address;
      _isLoading = true;
    });

    try {
      // Recalculate delivery fee for new address
      final subtotal =
          widget.cartSummary['subtotal'] as double? ??
          widget.cartSummary['total_price'] as double? ??
          0.0;

      // Phase 3B: Use multi-seller delivery fee calculation if enabled
      Map<String, dynamic> feeResult;
      if (kEnableMultiSellerDeliveryFee) {
        // Use multi-seller calculation for all scenarios
        feeResult = await _deliveryFeeService.calculateMultiSellerDeliveryFee(
          customerAddress: address,
          cartItems: widget.cartItems,
        );
      } else if (kEnableSellerLocationDeliveryFee) {
        // Phase 3A: Use seller-aware delivery fee calculation
        final sellerId = _extractSellerIdFromCartItems();
        if (sellerId != null) {
          feeResult = await _deliveryFeeService.calculateDeliveryFeeWithSeller(
            customerAddress: address,
            orderSubtotal: subtotal,
            sellerId: sellerId,
          );
        } else {
          // Fallback to original method if no seller ID found
          feeResult = await _deliveryFeeService.calculateDeliveryFee(
            customerAddress: address,
            orderSubtotal: subtotal,
          );
        }
      } else {
        // Use original method when feature flags are disabled
        feeResult = await _deliveryFeeService.calculateDeliveryFee(
          customerAddress: address,
          orderSubtotal: subtotal,
        );
      }

      if (feeResult['success']) {
        setState(() {
          // Handle both single-seller and multi-seller fee results
          if (feeResult.containsKey('total_delivery_fee')) {
            // Multi-seller result
            _deliveryFee = (feeResult['total_delivery_fee'] as num).toDouble();
            _deliveryFeeDetails = {
              'calculated': true,
              'is_multi_seller': feeResult['is_multi_seller'] ?? false,
              'seller_count': feeResult['seller_count'] ?? 1,
              'breakdown': feeResult['breakdown'] ?? [],
              'seller_delivery_fees': feeResult['seller_delivery_fees'] ?? {},
              'method': 'multi_seller_calculation',
            };
          } else {
            // Single-seller result
            _deliveryFee = feeResult['fee'] as double;
            _deliveryFeeDetails = {
              'calculated': true,
              'distance_km': feeResult['distance_km'],
              'tier': feeResult['tier'],
              'config_name': feeResult['config_name'],
              'is_multi_seller': false,
              'method': 'single_seller_calculation',
            };
          }
        });

        print(
          '✅ CHECKOUT - Delivery fee calculated: ₹${_deliveryFee.toStringAsFixed(0)} (Multi-seller: ${_deliveryFeeDetails?['is_multi_seller'] ?? false})',
        );
      } else {
        print(
          '⚠️ CHECKOUT - Delivery fee calculation failed: ${feeResult['error']}',
        );
        // Keep existing fee if recalculation fails
      }
    } catch (e) {
      print('❌ CHECKOUT - Address change error: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Calculate final order total
  double get _orderTotal {
    final subtotal =
        widget.cartSummary['subtotal'] as double? ??
        widget.cartSummary['total_price'] as double? ??
        0.0;
    return subtotal + _deliveryFee;
  }

  /// Handle order placement
  Future<void> _placeOrder() async {
    if (_deliveryAddress == null || _deliveryAddress!.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please set a delivery address'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create order with delivery fee included
      final orderData = {
        'customer_id': widget.customer['id'],
        'items': widget.cartItems,
        'subtotal':
            widget.cartSummary['subtotal'] ?? widget.cartSummary['total_price'],
        'delivery_fee': _deliveryFee,
        'delivery_address': _deliveryAddress,
        'delivery_fee_details': _deliveryFeeDetails,
        'total_amount': _orderTotal,
        'payment_method': _selectedPaymentMethod,
        'status': 'pending_payment',
      };

      print(
        '🛒 CHECKOUT - Creating order with total: ₹${_orderTotal.toStringAsFixed(0)}',
      );

      // Use the comprehensive order service for order creation
      final comprehensiveOrderService = ComprehensiveOrderService();

      final orderResult = await comprehensiveOrderService
          .createComprehensiveOrder(
            customerId: widget.customer['id'],
            items: widget.cartItems,
            subtotal:
                widget.cartSummary['subtotal'] ??
                widget.cartSummary['total_price'],
            deliveryFee: _deliveryFee,
            deliveryAddress: _deliveryAddress!,
            deliveryFeeDetails: _deliveryFeeDetails,
            specialInstructions: null, // TODO: Add special instructions field
            paymentMethod: _selectedPaymentMethod,
          );

      if (!orderResult['success']) {
        throw Exception(orderResult['error'] ?? 'Failed to create order');
      }

      // Store order result for payment processing
      final orderId = orderResult['order_id'];
      final orderNumber = orderResult['order_number'];

      print('✅ CHECKOUT - Order created successfully: $orderNumber');

      // Navigate to payment or success screen
      if (_selectedPaymentMethod == 'phonepe') {
        _processPhonePePayment();
      } else {
        _processCODOrder();
      }
    } catch (e) {
      print('❌ CHECKOUT - Order placement error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to place order: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Process PhonePe payment
  void _processPhonePePayment() {
    // TODO: Integrate with existing PhonePe payment system
    print(
      '💳 CHECKOUT - Processing PhonePe payment for ₹${_orderTotal.toStringAsFixed(0)}',
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Redirecting to PhonePe payment...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// Process Cash on Delivery order
  void _processCODOrder() {
    print(
      '💰 CHECKOUT - Processing COD order for ₹${_orderTotal.toStringAsFixed(0)}',
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Order placed successfully! Cash on Delivery'),
        backgroundColor: Colors.green,
      ),
    );

    // Navigate back to catalog or order confirmation
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildCheckoutButton(),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Checkout',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      ),
      backgroundColor: Colors.green[600],
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// Build main body
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF059669)),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeCheckout,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDeliveryAddressSection(),
          const SizedBox(height: 16),
          _buildOrderSummarySection(),
          const SizedBox(height: 16),
          _buildPaymentMethodSection(),
          const SizedBox(height: 100), // Space for bottom button
        ],
      ),
    );
  }

  /// Build delivery address section
  Widget _buildDeliveryAddressSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.green[600], size: 20),
              const SizedBox(width: 8),
              const Text(
                'Delivery Address',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 12),
          AddressPicker(
            initialAddress: _deliveryAddress,
            customerId: widget.customer['id'] as String,
            onAddressChanged: _onAddressChanged,
          ),
        ],
      ),
    );
  }

  /// Build order summary section
  Widget _buildOrderSummarySection() {
    final subtotal =
        widget.cartSummary['subtotal'] as double? ??
        widget.cartSummary['total_price'] as double? ??
        0.0;
    final itemCount = widget.cartSummary['total_items'] as int? ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long, color: Colors.green[600], size: 20),
              const SizedBox(width: 8),
              const Text(
                'Order Summary',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Items summary
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Items ($itemCount)', style: const TextStyle(fontSize: 16)),
              Text(
                '₹${subtotal.toStringAsFixed(0)}',
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Delivery fee
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Delivery Fee', style: TextStyle(fontSize: 16)),
                  if (_deliveryFeeDetails != null &&
                      _deliveryFeeDetails!['distance_km'] != null)
                    Text(
                      '${_deliveryFeeDetails!['distance_km'].toStringAsFixed(1)}km • ${_deliveryFeeDetails!['tier'] ?? 'Standard rate'}',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                ],
              ),
              _deliveryFee > 0
                  ? Text(
                      '₹${_deliveryFee.toStringAsFixed(0)}',
                      style: const TextStyle(fontSize: 16),
                    )
                  : Text(
                      'FREE',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.green[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ],
          ),

          const Divider(height: 24),

          // Total
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Text(
                '₹${_orderTotal.toStringAsFixed(0)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build payment method section
  Widget _buildPaymentMethodSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.payment, color: Colors.green[600], size: 20),
              const SizedBox(width: 8),
              const Text(
                'Payment Method',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Payment method options
          ..._paymentMethods.map((method) => _buildPaymentMethodTile(method)),
        ],
      ),
    );
  }

  /// Build payment method tile
  Widget _buildPaymentMethodTile(Map<String, String> method) {
    final isSelected = _selectedPaymentMethod == method['id'];

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedPaymentMethod = method['id']!;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.green[600]! : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected ? Colors.green[50] : Colors.transparent,
          ),
          child: Row(
            children: [
              Icon(
                method['id'] == 'phonepe' ? Icons.phone_android : Icons.money,
                color: isSelected ? Colors.green[600] : Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  method['name']!,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                    color: isSelected ? Colors.green[700] : Colors.black87,
                  ),
                ),
              ),
              if (isSelected)
                Icon(Icons.check_circle, color: Colors.green[600], size: 20),
            ],
          ),
        ),
      ),
    );
  }

  /// Build checkout button
  Widget _buildCheckoutButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 52,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _placeOrder,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    'Place Order • ₹${_orderTotal.toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  /// Extract seller ID from cart items for delivery fee calculation
  /// Phase 3A Implementation - Seller Location Integration
  String? _extractSellerIdFromCartItems() {
    try {
      // For now, assume single seller orders (Phase 3A)
      // Phase 3B will handle multi-seller scenarios
      if (widget.cartItems.isNotEmpty) {
        final firstItem = widget.cartItems.first;
        final product = firstItem['meat_products'];

        if (product != null) {
          // Try to get seller_id from product data
          // Note: This requires the cart query to include seller_id
          final sellerId = product['seller_id'];
          if (sellerId != null) {
            print('🛒 CHECKOUT - Using seller ID: $sellerId');
            return sellerId as String;
          }

          // Fallback: Try to get from sellers relationship
          final sellers = product['sellers'];
          if (sellers != null && sellers is Map) {
            final sellerId = sellers['id'];
            if (sellerId != null) {
              print(
                '🛒 CHECKOUT - Using seller ID from relationship: $sellerId',
              );
              return sellerId as String;
            }
          }
        }
      }

      print('⚠️ CHECKOUT - No seller ID found in cart items');
      return null;
    } catch (e) {
      print('❌ CHECKOUT - Error extracting seller ID: $e');
      return null;
    }
  }
}
