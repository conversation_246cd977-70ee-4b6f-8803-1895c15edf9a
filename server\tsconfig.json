{"compilerOptions": {"target": "es2020", "module": "node16", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node16", "resolveJsonModule": true, "allowJs": true, "typeRoots": ["./node_modules/@types", "./src/types"], "declaration": true, "declarationMap": true, "baseUrl": "./src", "preserveSymlinks": false, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.d.ts"], "exclude": ["node_modules", "dist"]}