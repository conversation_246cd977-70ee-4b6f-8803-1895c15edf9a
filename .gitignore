# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node_modules is gitignored by default, but some files under node_modules/ should be committed
# Comment the next line if you want to checkin your node_modules folder
node_modules

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# React Native
.expo/
.expo-shared/

# Android
android/app/build/
android/build/
android/.gradle/
android/app/.cxx/
android/app/build/
android/gradle/
*.keystore
!debug.keystore

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace
ios/*.xcuserdata

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp

# Firebase
firebase-service-account.json
*firebase-adminsdk*.json

# Build outputs
build/
dist/
out/

# Test coverage
coverage/

# Long path issues on Windows - exclude problematic directories
**/node_modules/**/.transforms/**
**/android/build/**
**/android/.gradle/**
**/android/app/.cxx/**
**/android/app/build/**
**/.cxx/**
**/build/.transforms/**
**/*.dex

# Exclude all node_modules to avoid path length issues
**/node_modules/

# Exclude build directories that cause path length issues
SellerApp/android/build/
SellerApp/android/.gradle/
SellerApp/android/app/.cxx/
SellerApp/android/app/build/
android/build/
android/.gradle/
android/app/.cxx/
android/app/build/

# Exclude specific problematic paths
**/.transforms/
**/bundleLibRuntimeToDirDebug/
