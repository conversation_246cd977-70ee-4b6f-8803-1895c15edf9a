import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../services/location_service.dart';
import '../services/auto_location_service.dart';
import '../services/delivery_address_state.dart';
import '../supabase_service.dart';
import '../widgets/address_picker.dart';
import 'location_selector_screen.dart';
import 'customer_address_form_screen.dart';

/// CustomerAddressManagementScreen - Zomato-inspired address management
///
/// This screen provides comprehensive address management functionality inspired by
/// Zomato's design but simplified for our use case. Features include:
/// - Current location detection and usage
/// - Saved addresses management (Home, Work, Other)
/// - Add new address with map integration
/// - Edit and delete existing addresses
/// - Set default delivery address
/// - Integration with delivery fee calculation system
class CustomerAddressManagementScreen extends StatefulWidget {
  final Map<String, dynamic> customer;

  const CustomerAddressManagementScreen({super.key, required this.customer});

  @override
  State<CustomerAddressManagementScreen> createState() =>
      _CustomerAddressManagementScreenState();
}

class _CustomerAddressManagementScreenState
    extends State<CustomerAddressManagementScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  final LocationService _locationService = LocationService();

  bool _isLoading = false;
  bool _isLoadingCurrentLocation = false;
  String? _currentLocationAddress;
  Map<String, dynamic>? _currentLocationData;
  List<Map<String, dynamic>> _savedAddresses = [];
  String? _defaultAddressId;

  @override
  void initState() {
    super.initState();
    _loadSavedAddresses();
    _detectCurrentLocation();
  }

  /// Load saved addresses from customer profile
  Future<void> _loadSavedAddresses() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final customerId = widget.customer['id'] as String;

      // Get customer data with delivery addresses
      final customerData = await _supabaseService.getCustomerById(customerId);

      if (customerData['success']) {
        final customer = customerData['customer'];
        final deliveryAddresses =
            customer['delivery_addresses'] as List<dynamic>? ?? [];

        setState(() {
          _savedAddresses = deliveryAddresses.cast<Map<String, dynamic>>();

          // Find default address
          for (final address in _savedAddresses) {
            if (address['is_default'] == true) {
              _defaultAddressId = address['id'] ?? address['address'];
              break;
            }
          }
        });
      }
    } catch (e) {
      print('❌ ADDRESS_MGMT - Error loading addresses: $e');
      _showErrorSnackBar('Failed to load saved addresses');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Detect current location for "Use Current Location" option
  Future<void> _detectCurrentLocation() async {
    setState(() {
      _isLoadingCurrentLocation = true;
    });

    try {
      final locationData = await AutoLocationService.getCurrentLocationOnce();

      if (locationData != null) {
        final address = await _locationService.reverseGeocode(
          locationData['latitude'],
          locationData['longitude'],
        );

        if (address != null && mounted) {
          setState(() {
            _currentLocationAddress = address;
            _currentLocationData = locationData;
          });
        }
      }
    } catch (e) {
      print('❌ ADDRESS_MGMT - Error detecting current location: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingCurrentLocation = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Select a location',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      backgroundColor: const Color(0xFF059669),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// Build main body
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF059669)),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search bar (simplified - just shows current search functionality)
          _buildSearchBar(),
          const SizedBox(height: 20),

          // Current location option
          _buildCurrentLocationOption(),
          const SizedBox(height: 16),

          // Add new address option
          _buildAddNewAddressOption(),
          const SizedBox(height: 24),

          // Saved addresses section
          if (_savedAddresses.isNotEmpty) ...[
            _buildSectionHeader('SAVED ADDRESSES'),
            const SizedBox(height: 12),
            ..._savedAddresses.map(
              (address) => _buildSavedAddressItem(address),
            ),
          ],
        ],
      ),
    );
  }

  /// Build search bar
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(Icons.search, color: Colors.grey.shade600, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Search for area, street name...',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// Build current location option
  Widget _buildCurrentLocationOption() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(Icons.my_location, color: Colors.red.shade600, size: 20),
        ),
        title: const Text(
          'Use current location',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
        ),
        subtitle: _isLoadingCurrentLocation
            ? const Text('Detecting location...')
            : _currentLocationAddress != null
            ? Text(
                _currentLocationAddress!,
                style: TextStyle(color: Colors.grey.shade600, fontSize: 13),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              )
            : Text(
                'Enable location access to use this feature',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 13),
              ),
        trailing: _isLoadingCurrentLocation
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF059669)),
                ),
              )
            : Icon(Icons.chevron_right, color: Colors.grey.shade400),
        onTap: _currentLocationAddress != null
            ? _useCurrentLocation
            : _requestLocationPermission,
      ),
    );
  }

  /// Build add new address option
  Widget _buildAddNewAddressOption() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(Icons.add, color: Colors.green.shade600, size: 20),
        ),
        title: const Text(
          'Add Address',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
        ),
        trailing: Icon(Icons.chevron_right, color: Colors.grey.shade400),
        onTap: _addNewAddress,
      ),
    );
  }

  /// Build section header
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
        color: Colors.grey.shade600,
        letterSpacing: 0.5,
      ),
    );
  }

  /// Build saved address item
  Widget _buildSavedAddressItem(Map<String, dynamic> address) {
    final addressText = address['address'] as String? ?? '';
    final addressType = address['type'] as String? ?? 'Other';
    final isDefault = address['is_default'] == true;
    final addressId = address['id'] ?? address['address'];

    IconData typeIcon;
    Color typeColor;

    switch (addressType.toLowerCase()) {
      case 'home':
        typeIcon = Icons.home;
        typeColor = Colors.blue.shade600;
        break;
      case 'work':
      case 'office':
        typeIcon = Icons.work;
        typeColor = Colors.orange.shade600;
        break;
      default:
        typeIcon = Icons.location_on;
        typeColor = Colors.grey.shade600;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDefault ? const Color(0xFF059669) : Colors.grey.shade200,
          width: isDefault ? 2 : 1,
        ),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: typeColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(typeIcon, color: typeColor, size: 20),
        ),
        title: Row(
          children: [
            Text(
              addressType,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
            ),
            if (isDefault) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFF059669),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'DEFAULT',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
        subtitle: Text(
          addressText,
          style: TextStyle(color: Colors.grey.shade600, fontSize: 13),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
          onSelected: (value) => _handleAddressAction(value, address),
          itemBuilder: (context) => [
            const PopupMenuItem(value: 'select', child: Text('Select')),
            if (!isDefault)
              const PopupMenuItem(
                value: 'set_default',
                child: Text('Set as Default'),
              ),
            const PopupMenuItem(value: 'edit', child: Text('Edit')),
            const PopupMenuItem(value: 'delete', child: Text('Delete')),
          ],
        ),
        onTap: () => _selectAddress(address),
      ),
    );
  }

  /// Use current location
  void _useCurrentLocation() {
    if (_currentLocationAddress != null) {
      _selectAddressAndReturn(_currentLocationAddress!, _currentLocationData);
    }
  }

  /// Request location permission
  Future<void> _requestLocationPermission() async {
    await _detectCurrentLocation();
  }

  /// Add new address
  Future<void> _addNewAddress() async {
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerAddressFormScreen(
          customerId: widget.customer['id'] as String,
          initialLocation: const LatLng(
            12.9716,
            77.5946,
          ), // Default to Bangalore
        ),
      ),
    );

    if (result != null) {
      await _saveNewAddressFromForm(result);
    }
  }

  /// Save new address from detailed form
  Future<void> _saveNewAddressFromForm(Map<String, dynamic> formData) async {
    try {
      final newAddress = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'address': formData['address'],
        'house_details': formData['house_details'],
        'area': formData['area'],
        'landmark': formData['landmark'],
        'receiver_name': formData['receiver_name'],
        'receiver_phone': formData['receiver_phone'],
        'type': formData['type'],
        'latitude': formData['latitude'],
        'longitude': formData['longitude'],
        'is_default': _savedAddresses.isEmpty, // First address is default
        'created_at': DateTime.now().toIso8601String(),
      };

      // Update customer profile with new address
      final updatedAddresses = [..._savedAddresses, newAddress];

      final result = await _supabaseService.updateCustomer(
        widget.customer['id'] as String,
        {'delivery_addresses': updatedAddresses},
      );

      if (result['success']) {
        setState(() {
          _savedAddresses = updatedAddresses;
        });

        _showSuccessSnackBar('Address saved successfully');
      } else {
        _showErrorSnackBar('Failed to save address');
      }
    } catch (e) {
      print('❌ ADDRESS_MGMT - Error saving address: $e');
      _showErrorSnackBar('Failed to save address');
    }
  }

  /// Save new address to customer profile (legacy method for simple location data)
  Future<void> _saveNewAddress(Map<String, dynamic> locationData) async {
    try {
      final address = locationData['address'] as String? ?? '';
      if (address.isEmpty) return;

      // Show address type selection dialog
      final addressType = await _showAddressTypeDialog();
      if (addressType == null) return;

      final newAddress = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'address': address,
        'type': addressType,
        'latitude': locationData['latitude'],
        'longitude': locationData['longitude'],
        'is_default': _savedAddresses.isEmpty, // First address is default
        'created_at': DateTime.now().toIso8601String(),
      };

      // Update customer profile with new address
      final updatedAddresses = [..._savedAddresses, newAddress];

      final result = await _supabaseService.updateCustomer(
        widget.customer['id'] as String,
        {'delivery_addresses': updatedAddresses},
      );

      if (result['success']) {
        setState(() {
          _savedAddresses = updatedAddresses;
        });

        _showSuccessSnackBar('Address saved successfully');
      } else {
        _showErrorSnackBar('Failed to save address');
      }
    } catch (e) {
      print('❌ ADDRESS_MGMT - Error saving address: $e');
      _showErrorSnackBar('Failed to save address');
    }
  }

  /// Handle address actions (edit, delete, set default)
  void _handleAddressAction(String action, Map<String, dynamic> address) {
    switch (action) {
      case 'select':
        _selectAddress(address);
        break;
      case 'set_default':
        _setDefaultAddress(address);
        break;
      case 'edit':
        _editAddress(address);
        break;
      case 'delete':
        _deleteAddress(address);
        break;
    }
  }

  /// Show address type selection dialog
  Future<String?> _showAddressTypeDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Save address as'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.home, color: Colors.blue),
              title: const Text('Home'),
              onTap: () => Navigator.pop(context, 'Home'),
            ),
            ListTile(
              leading: const Icon(Icons.work, color: Colors.orange),
              title: const Text('Work'),
              onTap: () => Navigator.pop(context, 'Work'),
            ),
            ListTile(
              leading: const Icon(Icons.location_on, color: Colors.grey),
              title: const Text('Other'),
              onTap: () => Navigator.pop(context, 'Other'),
            ),
          ],
        ),
      ),
    );
  }

  /// Set address as default
  Future<void> _setDefaultAddress(Map<String, dynamic> address) async {
    try {
      final updatedAddresses = _savedAddresses.map((addr) {
        return {...addr, 'is_default': addr['id'] == address['id']};
      }).toList();

      final result = await _supabaseService.updateCustomer(
        widget.customer['id'] as String,
        {'delivery_addresses': updatedAddresses},
      );

      if (result['success']) {
        setState(() {
          _savedAddresses = updatedAddresses;
        });
        _showSuccessSnackBar('Default address updated');
      } else {
        _showErrorSnackBar('Failed to update default address');
      }
    } catch (e) {
      print('❌ ADDRESS_MGMT - Error setting default address: $e');
      _showErrorSnackBar('Failed to update default address');
    }
  }

  /// Edit address
  Future<void> _editAddress(Map<String, dynamic> address) async {
    // Navigate to location selector with current address
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) => LocationSelectorScreen(
          customerId: widget.customer['id'] as String,
          initialLocation: LatLng(
            address['latitude']?.toDouble() ?? 12.9716,
            address['longitude']?.toDouble() ?? 77.5946,
          ),
          initialAddress: address['address'] as String?,
        ),
      ),
    );

    if (result != null) {
      await _updateAddress(address, result);
    }
  }

  /// Update existing address
  Future<void> _updateAddress(
    Map<String, dynamic> oldAddress,
    Map<String, dynamic> newLocationData,
  ) async {
    try {
      final updatedAddresses = _savedAddresses.map((addr) {
        if (addr['id'] == oldAddress['id']) {
          return {
            ...addr,
            'address': newLocationData['address'],
            'latitude': newLocationData['latitude'],
            'longitude': newLocationData['longitude'],
            'updated_at': DateTime.now().toIso8601String(),
          };
        }
        return addr;
      }).toList();

      final result = await _supabaseService.updateCustomer(
        widget.customer['id'] as String,
        {'delivery_addresses': updatedAddresses},
      );

      if (result['success']) {
        setState(() {
          _savedAddresses = updatedAddresses;
        });
        _showSuccessSnackBar('Address updated successfully');
      } else {
        _showErrorSnackBar('Failed to update address');
      }
    } catch (e) {
      print('❌ ADDRESS_MGMT - Error updating address: $e');
      _showErrorSnackBar('Failed to update address');
    }
  }

  /// Delete address
  Future<void> _deleteAddress(Map<String, dynamic> address) async {
    final confirmed = await _showDeleteConfirmationDialog();
    if (!confirmed) return;

    try {
      final updatedAddresses = _savedAddresses
          .where((addr) => addr['id'] != address['id'])
          .toList();

      final result = await _supabaseService.updateCustomer(
        widget.customer['id'] as String,
        {'delivery_addresses': updatedAddresses},
      );

      if (result['success']) {
        setState(() {
          _savedAddresses = updatedAddresses;
        });
        _showSuccessSnackBar('Address deleted successfully');
      } else {
        _showErrorSnackBar('Failed to delete address');
      }
    } catch (e) {
      print('❌ ADDRESS_MGMT - Error deleting address: $e');
      _showErrorSnackBar('Failed to delete address');
    }
  }

  /// Show delete confirmation dialog
  Future<bool> _showDeleteConfirmationDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Address'),
        content: const Text('Are you sure you want to delete this address?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// Show success snack bar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green.shade600),
    );
  }

  /// Select address and return to previous screen
  void _selectAddress(Map<String, dynamic> address) {
    final addressText = address['address'] as String? ?? '';
    _selectAddressAndReturn(addressText, address);
  }

  /// Select address and return with result
  void _selectAddressAndReturn(
    String address,
    Map<String, dynamic>? locationData,
  ) {
    // Update shared state
    DeliveryAddressState.setAddress(
      address,
      locationData: locationData,
      customerId: widget.customer['id'] as String,
    );

    // Return to previous screen with selected address
    Navigator.pop(context, {'address': address, 'location_data': locationData});
  }

  /// Show error snack bar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red.shade600),
    );
  }
}
