import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/delivery_fee_config.dart';
import 'location_service.dart';
import 'delivery_fee_setup_service.dart';
import 'seller_location_service.dart';
import '../config/feature_flags.dart';

/// DeliveryFeeService - Mobile app service for calculating delivery fees
///
/// This service integrates with the admin panel delivery fee configurations
/// to provide real-time delivery fee calculations for customers during checkout.
///
/// Features:
/// - Fetches active delivery configurations from admin panel
/// - Calculates distance-based delivery fees using Google Maps
/// - Applies tier-based pricing with dynamic multipliers
/// - Handles free delivery thresholds and service area limits
/// - Provides caching for performance optimization
/// - Zero-risk implementation - extends existing functionality only
class DeliveryFeeService {
  static final DeliveryFeeService _instance = DeliveryFeeService._internal();
  factory DeliveryFeeService() => _instance;
  DeliveryFeeService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final LocationService _locationService = LocationService();
  final SellerLocationService _sellerLocationService = SellerLocationService();

  // Cache for delivery configurations
  DeliveryFeeConfig? _cachedConfig;
  DateTime? _cacheTimestamp;
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Get active delivery fee configuration
  /// Returns cached config if available and not expired
  Future<DeliveryFeeConfig?> getActiveConfig() async {
    try {
      // Check cache first
      if (_cachedConfig != null &&
          _cacheTimestamp != null &&
          DateTime.now().difference(_cacheTimestamp!) < _cacheExpiry) {
        print('📦 DELIVERY FEE - Using cached configuration');
        return _cachedConfig;
      }

      print('🔄 DELIVERY FEE - Fetching active configuration from database');

      // Fetch active global configuration
      final response = await _supabase
          .from('delivery_fee_configs')
          .select()
          .eq('is_active', true)
          .eq('scope', 'GLOBAL')
          .maybeSingle();

      if (response != null) {
        _cachedConfig = DeliveryFeeConfig.fromJson(response);
        _cacheTimestamp = DateTime.now();

        print(
          '✅ DELIVERY FEE - Active configuration loaded: ${_cachedConfig!.configName}',
        );
        return _cachedConfig;
      } else {
        print('⚠️ DELIVERY FEE - No active configuration found');

        // Auto-create default configuration if none exists
        print('🔧 DELIVERY FEE - Auto-creating default configuration...');
        final setupSuccess =
            await DeliveryFeeSetupService.ensureDefaultConfigExists();

        if (setupSuccess) {
          // Try fetching again after creating default config
          final retryResponse = await _supabase
              .from('delivery_fee_configs')
              .select()
              .eq('is_active', true)
              .eq('scope', 'GLOBAL')
              .maybeSingle();

          if (retryResponse != null) {
            _cachedConfig = DeliveryFeeConfig.fromJson(retryResponse);
            _cacheTimestamp = DateTime.now();
            print('✅ DELIVERY FEE - Default configuration created and loaded');
            return _cachedConfig;
          }
        }

        print('❌ DELIVERY FEE - Failed to create default configuration');
        return null;
      }
    } catch (e) {
      print('❌ DELIVERY FEE - Error fetching configuration: $e');
      return _cachedConfig; // Return cached config if available
    }
  }

  /// Calculate delivery fee for given customer address
  /// Returns delivery fee amount or null if location not serviceable
  Future<Map<String, dynamic>> calculateDeliveryFee({
    required String customerAddress,
    required double orderSubtotal,
    String? sellerAddress,
  }) async {
    try {
      print('🧮 DELIVERY FEE - Calculating fee for address: $customerAddress');

      // Get active configuration
      final config = await getActiveConfig();
      if (config == null) {
        return {
          'success': false,
          'error': 'No delivery configuration available',
          'fee': 0.0,
        };
      }

      // Check free delivery threshold
      if (config.freeDeliveryThreshold != null &&
          orderSubtotal >= config.freeDeliveryThreshold!) {
        print(
          '🎉 DELIVERY FEE - Free delivery threshold met (₹${config.freeDeliveryThreshold})',
        );
        return {
          'success': true,
          'fee': 0.0,
          'reason': 'free_delivery_threshold',
          'threshold': config.freeDeliveryThreshold,
          'distance_km': 0.0,
        };
      }

      // Calculate distance
      final distanceResult = await _locationService.calculateDistance(
        origin:
            sellerAddress ??
            'Bangalore, Karnataka, India', // Default seller location
        destination: customerAddress,
        useRouting: config.useRouting,
      );

      if (!distanceResult['success']) {
        return {'success': false, 'error': distanceResult['error'], 'fee': 0.0};
      }

      double distanceKm = distanceResult['distance_km'];

      // Apply calibration multiplier if using straight-line distance
      if (!config.useRouting) {
        distanceKm *= config.calibrationMultiplier;
      }

      print('📏 DELIVERY FEE - Distance: ${distanceKm.toStringAsFixed(2)}km');

      // Check if location is serviceable
      if (distanceKm > config.maxServiceableDistanceKm) {
        print(
          '❌ DELIVERY FEE - Location not serviceable (${distanceKm.toStringAsFixed(2)}km > ${config.maxServiceableDistanceKm}km)',
        );
        return {
          'success': false,
          'error': 'Location not serviceable',
          'fee': 0.0,
          'distance_km': distanceKm,
          'max_distance': config.maxServiceableDistanceKm,
        };
      }

      // Find applicable tier and calculate fee
      final tier = _findApplicableTier(config.tierRates, distanceKm);
      if (tier == null) {
        return {
          'success': false,
          'error': 'No applicable pricing tier found',
          'fee': 0.0,
        };
      }

      double baseFee = tier.calculateFee(distanceKm);

      // Apply dynamic multipliers (peak hours, weather, demand)
      double finalFee = _applyDynamicMultipliers(
        baseFee,
        config.dynamicMultipliers,
      );

      // Apply min/max fee constraints
      finalFee = finalFee.clamp(config.minFee, config.maxFee);

      print(
        '💰 DELIVERY FEE - Calculated fee: ₹${finalFee.toStringAsFixed(0)} (base: ₹${baseFee.toStringAsFixed(0)})',
      );

      return {
        'success': true,
        'fee': finalFee,
        'distance_km': distanceKm,
        'tier': tier.displayRange,
        'base_fee': baseFee,
        'applied_multipliers': _getAppliedMultipliers(
          config.dynamicMultipliers,
        ),
        'config_name': config.configName,
      };
    } catch (e) {
      print('❌ DELIVERY FEE - Calculation error: $e');
      return {
        'success': false,
        'error': 'Failed to calculate delivery fee: $e',
        'fee': 0.0,
      };
    }
  }

  /// Calculate delivery fee using seller ID for accurate location-based pricing
  /// This is the enhanced method that uses actual seller coordinates
  ///
  /// Phase 3A Implementation - Seller Location Integration
  Future<Map<String, dynamic>> calculateDeliveryFeeWithSeller({
    required String customerAddress,
    required double orderSubtotal,
    required String sellerId,
  }) async {
    try {
      print(
        '🧮 DELIVERY FEE - Calculating fee with seller location for: $customerAddress',
      );

      // Get seller location data
      final sellerLocationResult = await _sellerLocationService
          .getSellerLocation(sellerId);

      String? sellerOrigin;
      if (sellerLocationResult['success'] &&
          sellerLocationResult['has_location']) {
        final locationData = sellerLocationResult['location_data'];
        final lat = locationData['latitude'];
        final lng = locationData['longitude'];

        if (lat != null && lng != null) {
          sellerOrigin = '$lat,$lng';
          print('📍 DELIVERY FEE - Using seller coordinates: $sellerOrigin');
        }
      }

      // Fallback to seller's business address or city if coordinates not available
      if (sellerOrigin == null) {
        final locationData = sellerLocationResult['location_data'];
        sellerOrigin =
            locationData['business_address'] ??
            locationData['business_city'] ??
            'Bangalore, Karnataka, India';
        print('⚠️ DELIVERY FEE - Using fallback address: $sellerOrigin');
      }

      // Use the existing calculateDeliveryFee method with seller location
      return await calculateDeliveryFee(
        customerAddress: customerAddress,
        orderSubtotal: orderSubtotal,
        sellerAddress: sellerOrigin,
      );
    } catch (e) {
      print('❌ DELIVERY FEE - Error with seller location: $e');

      // Fallback to original method with default location
      return await calculateDeliveryFee(
        customerAddress: customerAddress,
        orderSubtotal: orderSubtotal,
      );
    }
  }

  /// Calculate delivery fees for multi-seller orders (Phase 3B)
  /// This method handles orders with products from multiple sellers
  Future<Map<String, dynamic>> calculateMultiSellerDeliveryFee({
    required String customerAddress,
    required List<Map<String, dynamic>> cartItems,
  }) async {
    try {
      print(
        '🧮 MULTI-SELLER DELIVERY FEE - Calculating fees for ${cartItems.length} cart items',
      );

      // Group cart items by seller
      final sellerGroups = <String, List<Map<String, dynamic>>>{};
      final sellerInfo = <String, Map<String, dynamic>>{};

      for (final item in cartItems) {
        final product = item['meat_products'];
        if (product != null) {
          final sellerId = product['seller_id'] as String?;
          if (sellerId != null) {
            sellerGroups.putIfAbsent(sellerId, () => []).add(item);

            // Store seller info for later use
            if (!sellerInfo.containsKey(sellerId)) {
              final sellers = product['sellers'];
              sellerInfo[sellerId] = {
                'id': sellerId,
                'name': sellers?['seller_name'] ?? 'Unknown Seller',
              };
            }
          }
        }
      }

      print('📊 MULTI-SELLER - Found ${sellerGroups.length} sellers in cart');

      // Calculate delivery fee for each seller group
      final sellerDeliveryFees = <String, Map<String, dynamic>>{};
      double totalDeliveryFee = 0.0;
      double totalSubtotal = 0.0;

      for (final entry in sellerGroups.entries) {
        final sellerId = entry.key;
        final sellerItems = entry.value;

        // Calculate subtotal for this seller
        double sellerSubtotal = 0.0;
        for (final item in sellerItems) {
          final quantity = item['quantity'] as int;
          final unitPrice = (item['unit_price'] as num).toDouble();
          sellerSubtotal += quantity * unitPrice;
        }

        print(
          '💰 MULTI-SELLER - Seller $sellerId subtotal: ₹${sellerSubtotal.toStringAsFixed(2)}',
        );

        // Calculate delivery fee for this seller
        Map<String, dynamic> sellerFeeResult;
        if (kEnableSellerLocationDeliveryFee) {
          sellerFeeResult = await calculateDeliveryFeeWithSeller(
            customerAddress: customerAddress,
            orderSubtotal: sellerSubtotal,
            sellerId: sellerId,
          );
        } else {
          sellerFeeResult = await calculateDeliveryFee(
            customerAddress: customerAddress,
            orderSubtotal: sellerSubtotal,
          );
        }

        if (sellerFeeResult['success']) {
          final sellerDeliveryFee = (sellerFeeResult['fee'] as num).toDouble();
          totalDeliveryFee += sellerDeliveryFee;
          totalSubtotal += sellerSubtotal;

          sellerDeliveryFees[sellerId] = {
            'seller_info': sellerInfo[sellerId],
            'subtotal': sellerSubtotal,
            'delivery_fee': sellerDeliveryFee,
            'items_count': sellerItems.length,
            'fee_details': sellerFeeResult,
          };

          print(
            '✅ MULTI-SELLER - Seller $sellerId delivery fee: ₹${sellerDeliveryFee.toStringAsFixed(2)}',
          );
        } else {
          print(
            '❌ MULTI-SELLER - Failed to calculate fee for seller $sellerId',
          );
          return {
            'success': false,
            'error':
                'Failed to calculate delivery fee for seller: ${sellerInfo[sellerId]?['name']}',
          };
        }
      }

      print(
        '🎯 MULTI-SELLER - Total delivery fee: ₹${totalDeliveryFee.toStringAsFixed(2)}',
      );

      return {
        'success': true,
        'total_delivery_fee': totalDeliveryFee,
        'total_subtotal': totalSubtotal,
        'seller_count': sellerGroups.length,
        'seller_delivery_fees': sellerDeliveryFees,
        'is_multi_seller': sellerGroups.length > 1,
        'breakdown': sellerDeliveryFees.values
            .map(
              (seller) => {
                'seller_name': seller['seller_info']['name'],
                'subtotal': seller['subtotal'],
                'delivery_fee': seller['delivery_fee'],
                'items_count': seller['items_count'],
              },
            )
            .toList(),
      };
    } catch (e) {
      print('❌ MULTI-SELLER DELIVERY FEE - Error: $e');
      return {
        'success': false,
        'error': 'Failed to calculate multi-seller delivery fees: $e',
      };
    }
  }

  /// Check if location is serviceable
  Future<bool> isLocationServiceable(String address) async {
    try {
      final config = await getActiveConfig();
      if (config == null) return false;

      final distanceResult = await _locationService.calculateDistance(
        origin: 'Bangalore, Karnataka, India', // Default seller location
        destination: address,
        useRouting: config.useRouting,
      );

      if (!distanceResult['success']) return false;

      double distanceKm = distanceResult['distance_km'];
      if (!config.useRouting) {
        distanceKm *= config.calibrationMultiplier;
      }

      return distanceKm <= config.maxServiceableDistanceKm;
    } catch (e) {
      print('❌ DELIVERY FEE - Error checking serviceability: $e');
      return false;
    }
  }

  /// Clear cached configuration (force refresh)
  void clearCache() {
    _cachedConfig = null;
    _cacheTimestamp = null;
    print('🗑️ DELIVERY FEE - Cache cleared');
  }

  /// Find applicable pricing tier for given distance
  DeliveryFeeTier? _findApplicableTier(
    List<DeliveryFeeTier> tiers,
    double distanceKm,
  ) {
    for (final tier in tiers) {
      if (tier.appliesTo(distanceKm)) {
        return tier;
      }
    }
    return null;
  }

  /// Apply dynamic multipliers (peak hours, weather, demand)
  double _applyDynamicMultipliers(
    double baseFee,
    DeliveryFeeMultipliers multipliers,
  ) {
    double finalFee = baseFee;

    // Apply peak hours multiplier
    if (multipliers.peakHours.enabled && _isPeakHours(multipliers.peakHours)) {
      finalFee *= multipliers.peakHours.multiplier;
    }

    // Apply weather multiplier (if enabled)
    if (multipliers.weather.enabled) {
      finalFee *= multipliers.weather.multiplier;
    }

    // Apply demand multiplier (if enabled)
    if (multipliers.demand.enabled) {
      finalFee *= multipliers.demand.multiplier;
    }

    return finalFee;
  }

  /// Check if current time is within peak hours
  bool _isPeakHours(PeakHoursMultiplier peakHours) {
    final now = DateTime.now();
    final currentDay = _getDayName(now.weekday);

    if (!peakHours.days.contains(currentDay.toLowerCase())) {
      return false;
    }

    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    return currentTime.compareTo(peakHours.startTime) >= 0 &&
        currentTime.compareTo(peakHours.endTime) <= 0;
  }

  /// Get day name from weekday number
  String _getDayName(int weekday) {
    const days = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ];
    return days[weekday - 1];
  }

  /// Get list of applied multipliers for transparency
  List<String> _getAppliedMultipliers(DeliveryFeeMultipliers multipliers) {
    final applied = <String>[];

    if (multipliers.peakHours.enabled && _isPeakHours(multipliers.peakHours)) {
      applied.add('Peak Hours (${multipliers.peakHours.multiplier}x)');
    }

    if (multipliers.weather.enabled) {
      applied.add('Weather (${multipliers.weather.multiplier}x)');
    }

    if (multipliers.demand.enabled) {
      applied.add('High Demand (${multipliers.demand.multiplier}x)');
    }

    return applied;
  }
}
