---
type: "agent_requested"
description: "Example description"
---
LOCKED FEATURE PROTECTION PROTOCOL is now absolute law - maintain locked features registry, require explicit permission before ANY edit to existing files, use composition-over-modification, fix regressions immediately, and verify all functionality before deployment.

🔒 LOCKED FEATURES REGISTRY - INITIALIZED
I am now maintaining this active registry and will reference it before ANY code changes:

PROTECTED FEATURES (UNTOUCHABLE):
✅ Notifications Panel - Admin panel notifications management
✅ FCM Service - Firebase Cloud Messaging integration
✅ SMS Integration - Fast2SMS OTP and notification system
✅ Customer Portal - Shopping cart, order tracking, authentication
✅ Seller Portal - Product management, dashboard, authentication
✅ Admin Panel Navigation - Core routing and screen switching
✅ Payment Integration - PhonePe gateway
✅ Database Schema - All existing tables and RLS policies
✅ Authentication System - OTP-based login for customers/sellers
✅ Product Management - Add/edit/activate/deactivate functionality
📋 MY NEW MANDATORY WORKFLOW:
BEFORE ANY EDIT:
❓ Check Registry - Is this file/feature in the locked list?
🛑 STOP if Locked - Do not proceed without permission
🙋 Ask Permission - "This file/feature is locked. May I have explicit permission to modify [specific change]?"
⏳ Wait for Approval - No action until explicit user permission
✏️ Minimal Changes Only - Only what was specifically approved
✅ Verify Functionality - Test existing features still work
📝 Update Registry - Add new completed features to locked list
🚨 ZERO-TOLERANCE COMMITMENT:
NO modifications to locked features without explicit permission
NO assumptions about what's "safe" to change
NO shortcuts - always follow the full workflow


To edit any locked files please ask me explicitly and make sure you only touch that specific file.
IMMEDIATE fixes for any regressions discovered
COMPOSITION-OVER-MODIFICATION - create new, don't modify existing