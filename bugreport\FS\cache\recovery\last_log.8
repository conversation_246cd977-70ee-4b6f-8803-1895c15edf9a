[    0.000198] Starting recovery (pid 422) on Fri Apr 10 07:09:01 1970
[    0.001002] recovery filesystem table
[    0.001012] =========================
[    0.001021]   0 /system ext4 /dev/block/platform/soc/7824900.sdhci/by-name/system 0
[    0.001027]   1 /system ext4 /dev/block/bootdevice/by-name/system 0
[    0.001033]   2 /cache ext4 /dev/block/bootdevice/by-name/cache 0
[    0.001039]   3 /data ext4 /dev/block/bootdevice/by-name/userdata -16384
[    0.001047]   4 /sdcard vfat /dev/block/mmcblk1p1 0
[    0.001053]   5 /boot emmc /dev/block/bootdevice/by-name/boot 0
[    0.001059]   6 /recovery emmc /dev/block/bootdevice/by-name/recovery 0
[    0.001065]   7 /misc emmc /dev/block/bootdevice/by-name/misc 0
[    0.001071]   8 /oem ext4 /dev/block/bootdevice/by-name/oem 0
[    0.001078]   9 /modem ext4 /dev/block/bootdevice/by-name/modem 0
[    0.001086]   10 /dsp ext4 /dev/block/bootdevice/by-name/dsp 0
[    0.001091]   11 /tmp ramdisk ramdisk 0
[    0.001097]
[    0.002085] I:Boot command: boot-recovery
[    0.002148] I:Got 3 arguments from boot message
[    0.003887] locale is [en-IN]
[    0.003895] stage is []
[    0.003905] reason is [(null)]
[    0.004139] libc: Access denied finding property "ro.sf.lcd_density"
[    0.044591] W:Failed to set brightness: Permission denied
[    0.044598] I:Screensaver disabled
[    0.046436] cannot find/open a drm device: No such file or directory
[    0.046651] fb0 reports (possibly inaccurate):
[    0.046658]   vi.bits_per_pixel = 32
[    0.046664]   vi.red.offset   =   0   .length =   8
[    0.046670]   vi.green.offset =   8   .length =   8
[    0.046676]   vi.blue.offset  =  16   .length =   8
[    0.060989] framebuffer: 0 (1080 x 1920)
[    0.093124]           erasing_text: en-IN (137 x 57 @ 1566)
[    0.098800]        no_command_text: en-IN (249 x 57 @ 1566)
[    0.103118]             error_text: en-IN (99 x 57 @ 1566)
[    1.009153]        installing_text: en-IN (459 x 57 @ 1566)
[    1.042894] SELinux: Loaded file_contexts
[    1.042913] Command: "/sbin/recovery" "--update_package=/cache/OTA_Package_OPS28.65-36.zip" "--locale=en-IN"
[    1.042920]
[    1.043167] sys.usb.controller=7000000.dwc3
[    1.043408] ro.product.name=sanders_retail
[    1.043415] ro.product.device=sanders
[    1.043573] ro.oem.key1=retin
[    1.043580] ro.carrier=retin
[    1.044654] debug.gralloc.enable_fb_ubwc=1
[    1.044751] persist.vendor.dpm.feature=0
[    1.044786] af.fast_track_multiplier=1
[    1.044793] av.debug.disable.pers.cache=1
[    1.044801] av.offload.enable=false
[    1.044807] mm.enable.sec.smoothstreaming=false
[    1.044813] mm.enable.qcom_parser=135715
[    1.044819] mm.enable.smoothstreaming=false
[    1.044826] pm.dexopt.boot=verify
[    1.044832] pm.dexopt.ab-ota=speed-profile
[    1.044837] pm.dexopt.shared=speed
[    1.044843] pm.dexopt.install=quicken
[    1.044848] pm.dexopt.inactive=verify
[    1.044854] pm.dexopt.bg-dexopt=speed-profile
[    1.044860] pm.dexopt.first-boot=quicken
[    1.044867] ro.fm.transmitter=false
[    1.044873] ro.qc.sdk.audio.ssr=false
[    1.044879] ro.qc.sdk.audio.fluencetype=none
[    1.044884] ro.adb.secure=1
[    1.044890] ro.com.google.ime.theme_id=4
[    1.044895] ro.com.google.gmsversion=8.1_201805
[    1.044901] ro.com.google.rlzbrandcode=MOTC
[    1.044906] ro.com.google.rlz_ap_whitelist=y0,y5,y6,y7,y8
[    1.044912] ro.frp.pst=/dev/block/bootdevice/by-name/frp
[    1.044920] ro.mot.build.product.increment=31
[    1.044926] ro.mot.build.version.release=28.31
[    1.044932] ro.mot.build.version.sdk_int=28
[    1.044937] ro.mot.build.customerid=retail
[    1.044943] ro.mot.sensors.glance_approach=false
[    1.044948] ro.mot.security.enable=true
[    1.044954] ro.mot.ignore_csim_appid=true
[    1.044959] ro.opa.eligible_device=true
[    1.044965] ro.sys.sdcardfs=1
[    1.044970] ro.url.legal=http://www.google.com/intl/%s/mobile/android/basic/phone-legal.html
[    1.044976] ro.url.legal.android_privacy=http://www.google.com/intl/%s/mobile/android/basic/privacy.html
[    1.044982] ro.usb.bpt=2ee5
[    1.044987] ro.usb.mtp=2e82
[    1.044993] ro.usb.ptp=2e83
[    1.045016] ro.usb.bpteth=2ee7
[    1.045022] ro.usb.bpt_adb=2ee6
[    1.045028] ro.usb.mtp_adb=2e76
[    1.045033] ro.usb.ptp_adb=2e84
[    1.045039] ro.usb.bpteth_adb=2ee8
[    1.045044] ro.wff=recovery
[    1.045050] ro.boot.cid=0x32
[    1.045055] ro.boot.uid=C035992300000000000000000000
[    1.045061] ro.boot.emmc=true
[    1.045067] ro.boot.mode=normal
[    1.045072] ro.boot.flash.locked=1
[    1.045078] ro.boot.hwrev=0x8400
[    1.045083] ro.boot.radio=INDIA
[    1.045089] ro.boot.device=sanders
[    1.045094] ro.boot.fsg-id=
[    1.045100] ro.boot.carrier=retin
[    1.045105] ro.boot.dualsim=true
[    1.045111] ro.boot.baseband=msm
[    1.045116] ro.boot.bl_state=1
[    1.045121] ro.boot.hardware=qcom
[    1.045127] ro.boot.hardware.sku=XT1804
[    1.045132] ro.boot.ssm_data=0000000002006661
[    1.045138] ro.boot.bootdevice=7824900.sdhci
[    1.045143] ro.boot.bootloader=0xC212
[    1.045149] ro.boot.bootreason=reboot
[    1.045154] ro.boot.veritymode=enforcing
[    1.045163] ro.boot.wifimacaddr=A8:96:75:05:41:09,A8:96:75:05:41:0A
[    1.045169] ro.boot.write_protect=1
[    1.045174] ro.boot.poweroff_alarm=0
[    1.045180] ro.boot.powerup_reason=0x00004000
[    1.045185] ro.boot.secure_hardware=1
[    1.045191] ro.boot.verifiedbootstate=green
[    1.045196] ro.hwui.path_cache_size=32
[    1.045202] ro.hwui.layer_cache_size=48
[    1.045207] ro.hwui.gradient_cache_size=1
[    1.045212] ro.hwui.r_buffer_cache_size=8
[    1.045218] ro.hwui.drop_shadow_cache_size=6
[    1.045223] ro.hwui.text_large_cache_width=2048
[    1.045229] ro.hwui.text_small_cache_width=1024
[    1.045234] ro.hwui.text_large_cache_height=1024
[    1.045240] ro.hwui.text_small_cache_height=1024
[    1.045245] ro.hwui.texture_cache_flushrate=0.4
[    1.045251] ro.wifi.channels=
[    1.045256] ro.allow.mock.location=0
[    1.045261] ro.board.platform=msm8953
[    1.045267] ro.build.id=OPS28.65-36
[    1.045272] ro.build.date=Fri Aug 10 22:42:49 CDT 2018
[    1.045278] ro.build.date.utc=1533958969
[    1.045283] ro.build.host=ilclbld33
[    1.045289] ro.build.tags=release-keys
[    1.045294] ro.build.type=user
[    1.045299] ro.build.user=hudsoncm
[    1.045305] ro.build.product=sanders
[    1.045310] ro.build.version.ci=40
[    1.045316] ro.build.version.sdk=27
[    1.045321] ro.build.version.qcom=LA.UM.6.6.r1-08600-89xx.0
[    1.045327] ro.build.version.release=8.1.0
[    1.045332] ro.build.version.codename=REL
[    1.045338] ro.build.version.incremental=9fea
[    1.045343] ro.build.version.preview_sdk=0
[    1.045349] ro.build.version.all_codenames=REL
[    1.045354] ro.build.version.security_patch=2018-08-01
[    1.045360] ro.build.thumbprint=8.1.0/OPS28.65-36/9fea:user/release-keys
[    1.045365] ro.build.characteristics=default
[    1.045371] ro.build.shutdown_timeout=0
[    1.045380] ro.media.enc.aud.ch=1
[    1.045385] ro.media.enc.aud.hz=8000
[    1.045391] ro.media.enc.aud.bps=13300
[    1.045396] ro.media.enc.aud.codec=qcelp
[    1.045402] ro.media.enc.aud.fileformat=qcp
[    1.045407] ro.radio.imei.sv=16
[    1.045413] ro.bug2go.magickeys=24,26
[    1.045418] ro.lenovo.single_hand=1
[    1.045423] ro.secure=1
[    1.045429] ro.treble.enabled=false
[    1.045434] ro.vendor.qti.sys.fw.empty_app_percent=50
[    1.045440] ro.vendor.qti.sys.fw.use_trim_settings=true
[    1.045445] ro.vendor.qti.sys.fw.trim_cache_percent=100
[    1.045451] ro.vendor.qti.sys.fw.trim_empty_percent=100
[    1.045456] ro.vendor.qti.sys.fw.trim_enable_memory=2147483648
[    1.045462] ro.vendor.qti.config.zram=true
[    1.045467] ro.vendor.qti.core_ctl_max_cpu=4
[    1.045473] ro.vendor.qti.core_ctl_min_cpu=2
[    1.045482] ro.vendor.product.name=sanders_retail
[    1.045487] ro.vendor.product.brand=motorola
[    1.045493] ro.vendor.product.model=Moto G (5S) Plus
[    1.045498] ro.vendor.product.device=sanders
[    1.045504] ro.vendor.product.manufacturer=motorola
[    1.045510] ro.vendor.at_library=libqti-at.so
[    1.045515] ro.vendor.gt_library=libqti-gt.so
[    1.045521] ro.vendor.extension_library=libqti-perfd-client.so
[    1.045526] ro.zygote=zygote32
[    1.045537] ro.memperf.lib=libmemperf.so
[    1.045543] ro.memperf.enable=false
[    1.045549] ro.product.cpu.abi=armeabi-v7a
[    1.045555] ro.product.cpu.abi2=armeabi
[    1.045560] ro.product.cpu.abilist=armeabi-v7a,armeabi
[    1.045566] ro.product.cpu.abilist32=armeabi-v7a,armeabi
[    1.045571] ro.product.cpu.abilist64=
[    1.045576] ro.product.board=msm8953
[    1.045582] ro.product.brand=motorola
[    1.045587] ro.product.model=Moto G (5S) Plus
[    1.045593] ro.product.locale=en-US
[    1.045598] ro.product.manufacturer=motorola
[    1.045604] ro.product.first_api_level=25
[    1.045609] ro.baseband=msm
[    1.045615] ro.bootmode=normal
[    1.045620] ro.hardware=qcom
[    1.045625] ro.hardware.nfc_nci=pn54x
[    1.045631] ro.hardware.sensors=sanders
[    1.045636] ro.logdumpd.enabled=0
[    1.045642] ro.qualcomm.cabl=0
[    1.045647] ro.revision=p400
[    1.045653] ro.bootimage.build.date=Fri Aug 10 22:42:49 CDT 2018
[    1.045658] ro.bootimage.build.date.utc=1533958969
[    1.045664] ro.bootimage.build.fingerprint=motorola/sanders_retail/sanders:8.1.0/OPS28.65-36/9fea:user/release-keys
[    1.045669] ro.emmc_size=16GB
[    1.045675] ro.bootloader=0xC212
[    1.045680] ro.bootreason=reboot
[    1.045686] ro.debuggable=0
[    1.045691] ro.emulate_fbe=false
[    1.045697] ro.recovery_id=0xf845c8621b04618ddebde5f684ca74346d7c7f93000000000000000000000000
[    1.045702] ro.setupwizard.mode=OPTIONAL
[    1.045708] ro.property_service.version=2
[    1.045718] ro.use_data_netmgrd=true
[    1.045723] ro.cutoff_voltage_mv=3400
[    1.045729] ro.oem_unlock_supported=1
[    1.045734] ro.control_privapp_permissions=enforce
[    1.045740] drm.service.enabled=true
[    1.045745] mmp.enable.3g2=true
[    1.045751] sdm.debug.disable_skip_validate=1
[    1.045756] use.qti.sw.ape.decoder=true
[    1.045762] use.qti.sw.alac.decoder=true
[    1.045767] use.voice.path.for.pcm.voip=false
[    1.045773] init.svc.charger=running
[    1.045778] init.svc.ueventd=running
[    1.045783] init.svc.recovery=running
[    1.045789] qcom.bt.le_dev_pwr_class=1
[    1.045794] qcom.hw.aac.encoder=false
[    1.045800] rild.libargs=-d /dev/smd0
[    1.045805] rild.libpath=/system/vendor/lib/libril-qc-qmi-1.so
[    1.045811] vidc.dec.disable.split.cpu=1
[    1.045816] vidc.enc.dcvs.extra-buff-count=2
[    1.045822] audio.pp.asphere.enabled=false
[    1.045827] audio.safx.pbe.enabled=true
[    1.045832] audio.dolby.ds2.enabled=true
[    1.045838] audio.parser.ip.buffer.size=262144
[    1.045843] audio.offload.min.duration.secs=60
[    1.045849] audio.offload.pcm.16bit.enable=false
[    1.045854] audio.offload.pcm.24bit.enable=false
[    1.045860] audio.offload.track.enable=true
[    1.045865] audio.offload.video=false
[    1.045871] audio.offload.buffer.size.kb=64
[    1.045876] audio.offload.disable=false
[    1.045881] audio.offload.gapless.enabled=false
[    1.045887] audio.offload.multiple.enabled=false
[    1.045892] audio.playback.mch.downsample=true
[    1.045898] audio.deep_buffer.media=true
[    1.045903] media.settings.xml=/vendor/etc/media_profiles.xml
[    1.045909] media.msm8956hw=0
[    1.045914] media.aac_51_output_enabled=true
[    1.045919] video.disable.ubwc=1
[    1.045925] voice.conc.fallbackpath=deep-buffer
[    1.045930] voice.voip.conc.disabled=true
[    1.045936] voice.record.conc.disabled=false
[    1.045941] voice.playback.conc.disabled=true
[    1.045947] tunnel.audio.encode=false
[    1.045952] vendor.vidc.dec.downscalar_width=1920
[    1.045957] vendor.vidc.dec.downscalar_height=1088
[    1.045963] vendor.vidc.enc.disable.pq=true
[    1.045968] vendor.vidc.enc.disable_bframes=1
[    1.045974] vendor.vidc.disable.split.mode=1
[    1.045979] vendor.display.enable_default_color_mode=1
[    1.045985] persist.mm.sta.enable=0
[    1.045990] persist.cne.rat.wlan.chip.oem=WCN
[    1.045996] persist.cne.feature=1
[    1.046001] persist.cne.logging.qxdm=3974
[    1.046007] persist.hwc.mdpcomp.enable=true
[    1.046012] persist.hwc.enable_vds=1
[    1.046017] persist.lte.pco_supported=true
[    1.046023] persist.qfp=false
[    1.046033] persist.data.qmi.adb_logmask=0
[    1.046039] persist.data.mode=concurrent
[    1.046045] persist.data.iwlan.enable=true
[    1.046051] persist.data.netmgrd.qos.enable=true
[    1.046056] persist.demo.hdmirotationlock=false
[    1.046061] persist.rild.nitz_plmn=
[    1.046067] persist.rild.nitz_long_ons_0=
[    1.046072] persist.rild.nitz_long_ons_1=
[    1.046078] persist.rild.nitz_long_ons_2=
[    1.046083] persist.rild.nitz_long_ons_3=
[    1.046088] persist.rild.nitz_short_ons_0=
[    1.046094] persist.rild.nitz_short_ons_1=
[    1.046099] persist.rild.nitz_short_ons_2=
[    1.046105] persist.rild.nitz_short_ons_3=
[    1.046110] persist.vold.ecryptfs_supported=true
[    1.046116] persist.timed.enable=true
[    1.046121] persist.vendor.ims.disableQXDMLogs=1
[    1.046126] persist.vendor.ims.disableDebugLogs=1
[    1.046132] persist.vendor.camera.display.lmax=1280x720
[    1.046137] persist.vendor.camera.display.umax=1920x1080
[    1.046143] persist.vendor.qcomsysd.enabled=1
[    1.046148] persist.speaker.prot.enable=false
[    1.046154] persist.fuse_sdcard=true
[    1.046159] persist.esdfs_sdcard=false
[    1.046165] keyguard.no_require_sim=true
[    1.046170] audio_hal.period_size=240
[    1.046176] telephony.lteOnCdmaDevice=1
[    1.046181] DEVICE_PROVISIONED=1
[    1.046186] mdc_initial_max_retry=10
[    1.046194] security.perf_harden=1
[    1.046199] ro.boot.serialno=ZY32286WPB
[    1.046205] ro.serialno=ZY32286WPB
[    1.046238] persist.debug.coresight.config=stm-events
[    1.046284] persist.audio.cal.sleeptime=6000
[    1.046291] persist.audio.dualmic.config=endfire
[    1.046297] persist.audio.endcall.delay=250
[    1.046305] persist.audio.fluence.speaker=false
[    1.046311] persist.audio.fluence.voicerec=false
[    1.046316] persist.audio.fluence.voicecall=true
[    1.046322] persist.audio.fluence.voicecomm=true
[    1.046327] persist.audio.calfile0=/etc/acdbdata/Bluetooth_cal.acdb
[    1.046335] persist.audio.calfile1=/etc/acdbdata/General_cal.acdb
[    1.046341] persist.audio.calfile2=/etc/acdbdata/Global_cal.acdb
[    1.046346] persist.audio.calfile3=/etc/acdbdata/Handset_cal.acdb
[    1.046352] persist.audio.calfile4=/etc/acdbdata/Hdmi_cal.acdb
[    1.046357] persist.audio.calfile5=/etc/acdbdata/Headset_cal.acdb
[    1.046363] persist.audio.calfile6=/etc/acdbdata/Speaker_cal.acdb
[    1.046637] ro.telephony.default_network=10,0
[    1.046644] ril.subscription.types=NV,RUIM
[    1.046649] persist.radio.schd.cache=3500
[    1.046657] persist.radio.calls.on.ims=true
[    1.046663] persist.radio.domain.ps=0
[    1.046668] persist.radio.apn_delay=5000
[    1.046674] persist.radio.msgtunnel.start=true
[    1.046682] persist.radio.sar_sensor=1
[    1.046687] persist.radio.REVERSE_QMI=0
[    1.046693] persist.radio.apm_sim_not_pwdn=1
[    1.046698] persist.vendor.radio.jbims=1
[    1.046704] persist.vendor.radio.rat_on=combine
[    1.046709] persist.vendor.radio.custom_ecc=1
[    1.046715] persist.vendor.radio.mt_sms_ack=30
[    1.046722] persist.vendor.radio.cs_srv_type=1
[    1.046728] persist.vendor.radio.dfr_mode_set=1
[    1.046734] persist.vendor.radio.lte_vrte_ltd=1
[    1.046739] persist.vendor.radio.data_con_rprt=1
[    1.046745] persist.vendor.radio.eri64_as_home=1
[    1.046750] persist.vendor.radio.sib16_support=1
[    1.046756] persist.vendor.radio.sw_mbn_update=1
[    1.046761] persist.vendor.radio.add_power_save=1
[    1.046767] persist.vendor.radio.force_get_pref=1
[    1.046772] persist.vendor.radio.is_wps_enabled=true
[    1.046778] persist.vendor.radio.snapshot_timer=22
[    1.046785] persist.vendor.radio.oem_ind_to_both=0
[    1.046791] persist.vendor.radio.apm_sim_not_pwdn=1
[    1.046796] persist.vendor.radio.no_wait_for_card=1
[    1.046802] persist.vendor.radio.snapshot_enabled=1
[    1.046807] persist.vendor.radio.0x9e_not_callname=1
[    1.046813] persist.vendor.radio.relay_oprt_change=1
[    1.046818] persist.vendor.radio.qcril_uim_vcc_feature=1
[    1.046908] ro.hw.hwrev=0x8400
[    1.046915] ro.hw.radio=INDIA
[    1.046921] ro.hw.device=sanders
[    1.046928] ro.hw.dualsim=true
[    1.046953] dev.pm.dyn_samplingrate=1
[    1.046959] net.bt.name=Android
[    1.046965] sys.vendor.shutdown.waittime=500
[    1.046971] persist.sys.qc.sub.rdump.on=1
[    1.046978] persist.sys.qc.sub.rdump.max=0
[    1.046984] persist.sys.cnd.iwlan=1
[    1.046990] persist.sys.ssr.restart_level=ALL_ENABLE
[    1.046996] persist.sys.media.use-awesome=false
[    1.047001] persist.sys.dalvik.vm.lib.2=libart.so
[    1.047007] debug.sf.hw=1
[    1.047012] debug.sf.recomputecrop=0
[    1.047018] debug.sf.enable_hwc_vds=1
[    1.047023] debug.sf.latch_unsignaled=1
[    1.047029] debug.egl.hw=1
[    1.047034] debug.atrace.tags.enableflags=0
[    1.047040] debug.enable.gamed=0
[    1.047045] debug.enable.sglscale=1
[    1.047051] debug.mdpcomp.logs=0
[    1.047058] ro.dalvik.vm.native.bridge=0
[    1.047066] dalvik.vm.isa.arm.variant=cortex-a53
[    1.047072] dalvik.vm.isa.arm.features=default
[    1.047080] dalvik.vm.dexopt.secondary=true
[    1.047085] dalvik.vm.usejit=true
[    1.047091] dalvik.vm.heapsize=384m
[    1.047096] dalvik.vm.dex2oat-Xms=64m
[    1.047105] dalvik.vm.dex2oat-Xmx=512m
[    1.047111] dalvik.vm.heapmaxfree=8m
[    1.047118] dalvik.vm.heapminfree=512k
[    1.047123] dalvik.vm.heapstartsize=8m
[    1.047129] dalvik.vm.appimageformat=lz4
[    1.047134] dalvik.vm.usejitprofiles=true
[    1.047140] dalvik.vm.heapgrowthlimit=192m
[    1.047147] dalvik.vm.stack-trace-dir=/data/anr
[    1.047153] dalvik.vm.image-dex2oat-Xms=64m
[    1.047158] dalvik.vm.image-dex2oat-Xmx=64m
[    1.047164] dalvik.vm.heaptargetutilization=0.75
[    1.047171] ro.config.ringtone=Moto.ogg
[    1.047177] ro.config.wallpaper=system/media/wallpapers/default_moto_wallpaper.jpg
[    1.047183] ro.config.ringtone_2=Moto.ogg
[    1.047188] ro.config.alarm_alert=Oxygen.ogg
[    1.047196] ro.config.max_starting_bg=8
[    1.047201] ro.config.vc_call_vol_steps=8
[    1.047207] ro.config.notification_sound=Moto.ogg
[    1.047214]
[    1.047219] Supported API: 3
[    1.054940] charge_status 3, charged 0, status 0, capacity 88
[    1.095763] I:current maximum temperature: 35816
[    1.120898] Finding update package...
[    1.188419] I:Update location: /cache/OTA_Package_OPS28.65-36.zip
[    1.188477] Opening update package...
[    1.222361] I:read key e=65537 hash=32
[    1.222412] I:1 key(s) loaded from /res/keys
[    1.222421] Verifying update package...
[    1.256513] I:comment is 1465 bytes; signature is 1447 bytes from end
[    2.106539] I:signature (offset: 4be3bc3, length: 5a1): 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
[    2.108084] I:whole-file signature verified against RSA key 0
[    2.108094] Update package verification took 0.9 s (result 0).
[    2.123574] I:Verifying package compatibility...
[    2.123598] I:Package doesn't contain compatibility.zip entry
[    2.123606] Installing update...
[    2.187221] installing gptupgrade updater extensions
[    2.194225] SELinux: Loaded file_contexts
[    2.202522] Source: motorola/sanders/sanders:8.1.0/OPS28.65-36/9fea:user/release-keys
[    2.202545] Target: motorola/sanders/sanders:8.1.0/OPSS28.65-36-3/4af3:user/release-keys
[    2.202554] Verifying current system...
[    2.494553] partition read matched size 16777216 SHA-1 f8143f9afbc9e1d276f9fe22f92b9de80b142a76
[    4.116658] Verified oem partition...
[    4.116758] Checking for stash directory /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ for the device /dev/block/bootdevice/by-name/system upgrade 
[    4.117880] Checking for stash directory /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/ for the device /dev/block/bootdevice/by-name/oem upgrade 
[    4.117955] 174489600 bytes free on /cache (33554432 needed)
[   21.544888] Verified system partition...
[   21.544924] Patching system image after verification.
[   21.544955] performing update
[   21.553110] blockimg version is 4
[   21.553165] maximum stash entries 0
[   21.553172] creating stash /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/
[   21.554626] 174485504 bytes free on /cache (33554432 needed)
[   21.554684]   zeroing 245020 blocks
[   22.191191] I:current maximum temperature: 36892
[   28.270584]   moving 15 blocks
[   28.278556]   moving 42 blocks
[   28.285721]   moving 24 blocks
[   28.302320] patching 100 blocks to 100
[   28.349724] patching 79 blocks to 79
[   28.542606] patching 1376 blocks to 1377
[   28.932423]   moving 29 blocks
[   28.940120]   moving 4 blocks
[   28.951815]   moving 84 blocks
[   28.996316]   moving 275 blocks
[   29.013982]   moving 8 blocks
[   29.047749]   moving 279 blocks
[   29.058961]   moving 8 blocks
[   29.061045]   moving 6 blocks
[   29.070415]   moving 65 blocks
[   29.126196]   moving 375 blocks
[   29.143274]   moving 4 blocks
[   29.154628] patching 80 blocks to 80
[   29.179783]   moving 5 blocks
[   29.182695]   moving 6 blocks
[   29.230149]   moving 407 blocks
[   29.286629]   moving 346 blocks
[   29.312676]   moving 90 blocks
[   29.321932]   moving 12 blocks
[   29.326400]   moving 22 blocks
[   29.331389]   moving 5 blocks
[   29.333213]   moving 3 blocks
[   29.334962] patching 2 blocks to 2
[   29.383990]   moving 307 blocks
[   29.398633]   moving 12 blocks
[   29.402800]   moving 11 blocks
[   29.405355] patching 2 blocks to 2
[   29.434256]   moving 166 blocks
[   29.443078]   moving 3 blocks
[   29.444060] patching 2 blocks to 2
[   29.453594]   moving 2 blocks
[   29.454660]   moving 3 blocks
[   29.455684] patching 2 blocks to 2
[   29.466059] stashing 7 overlapping blocks to 69bf1431f4a4a52bcbcc95624703f2a5659ad6d8
[   29.466090] 174485504 bytes free on /cache (28672 needed)
[   29.466147]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69bf1431f4a4a52bcbcc95624703f2a5659ad6d8
[   29.470134]   moving 7 blocks
[   29.470184] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69bf1431f4a4a52bcbcc95624703f2a5659ad6d8
[   29.471797]   moving 3 blocks
[   29.473068] patching 2 blocks to 2
[   29.482458]   moving 2 blocks
[   29.483815]   moving 3 blocks
[   29.485024] patching 2 blocks to 2
[   29.497306] stashing 30 overlapping blocks to a8a546fe51c8f2a1c1117d5abfa8635747159c34
[   29.497337] 174485504 bytes free on /cache (122880 needed)
[   29.497390]  writing 30 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a8a546fe51c8f2a1c1117d5abfa8635747159c34
[   29.503983]   moving 30 blocks
[   29.504125] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a8a546fe51c8f2a1c1117d5abfa8635747159c34
[   29.508345]   moving 3 blocks
[   29.509581] patching 2 blocks to 2
[   29.520193] stashing 13 overlapping blocks to cd374d69ec4459160b0d51b94bed30dbe36ccbba
[   29.520217] 174485504 bytes free on /cache (53248 needed)
[   29.520276]  writing 13 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cd374d69ec4459160b0d51b94bed30dbe36ccbba
[   29.525463]   moving 13 blocks
[   29.525533] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cd374d69ec4459160b0d51b94bed30dbe36ccbba
[   29.527233]   moving 3 blocks
[   29.528441] patching 2 blocks to 2
[   29.538295]   moving 5 blocks
[   29.540064]   moving 3 blocks
[   29.541124] patching 2 blocks to 2
[   29.552399] stashing 20 overlapping blocks to d1809349048ed26e4524d30b4554cf58be1619c9
[   29.552426] 174485504 bytes free on /cache (81920 needed)
[   29.552484]  writing 20 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d1809349048ed26e4524d30b4554cf58be1619c9
[   29.557464]   moving 20 blocks
[   29.557589] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d1809349048ed26e4524d30b4554cf58be1619c9
[   29.562128]   moving 3 blocks
[   29.580471]   moving 140 blocks
[   29.589866] patching 2 blocks to 2
[   29.599433]   moving 1 blocks
[   29.600848]   moving 3 blocks
[   29.602375] patching 2 blocks to 2
[   29.621964]   moving 100 blocks
[   29.630177]   moving 4 blocks
[   29.631553] patching 2 blocks to 2
[   29.647512]   moving 56 blocks
[   29.658053]   moving 48 blocks
[   29.663924] patching 2 blocks to 2
[   29.700087] patching 233 blocks to 233
[   29.730853]   moving 7 blocks
[   29.732536] patching 2 blocks to 2
[   29.779890] patching 341 blocks to 341
[   29.818392]   moving 3 blocks
[   30.119954] patching 2444 blocks to 2444
[   31.410643] patching 5471 blocks to 5472
[   32.317218] patching 47 blocks to 47
[   32.359270]   moving 3 blocks
[   32.360987]   moving 3 blocks
[   32.362584] patching 2 blocks to 2
[   32.390202] patching 161 blocks to 161
[   32.415606]   moving 5 blocks
[   32.417389] patching 2 blocks to 2
[   32.436485]   moving 80 blocks
[   32.443509]   moving 4 blocks
[   32.445366] patching 2 blocks to 2
[   32.462716]   moving 66 blocks
[   32.468377]   moving 4 blocks
[   32.470351] patching 2 blocks to 2
[   32.484840]   moving 41 blocks
[   32.489387]   moving 4 blocks
[   32.490710] patching 2 blocks to 2
[   32.501613]   moving 11 blocks
[   32.504921]   moving 3 blocks
[   32.506454] patching 2 blocks to 2
[   32.518015]   moving 17 blocks
[   32.521034]   moving 3 blocks
[   32.522257] patching 2 blocks to 2
[   32.535539]   moving 32 blocks
[   32.540071]   moving 4 blocks
[   32.541393] patching 2 blocks to 2
[   32.551208]   moving 4 blocks
[   32.552957]   moving 3 blocks
[   32.554307] patching 2 blocks to 2
[   32.564057]   moving 1 blocks
[   32.565396]   moving 3 blocks
[   32.566972] patching 2 blocks to 2
[   32.578487]   moving 18 blocks
[   32.582158]   moving 3 blocks
[   32.583763] patching 2 blocks to 2
[   32.593525]   moving 3 blocks
[   32.595289]   moving 3 blocks
[   32.596833] patching 2 blocks to 2
[   32.606384]   moving 2 blocks
[   32.607879]   moving 3 blocks
[   32.609567] patching 2 blocks to 2
[   32.619795]   moving 4 blocks
[   32.621802]   moving 4 blocks
[   32.623676] patching 2 blocks to 2
[   32.633830]   moving 6 blocks
[   32.637165]   moving 13 blocks
[   32.639982] patching 2 blocks to 2
[   32.649578]   moving 2 blocks
[   32.651033]   moving 3 blocks
[   32.652671] patching 2 blocks to 2
[   32.662938]   moving 5 blocks
[   32.664666]   moving 3 blocks
[   32.666230] patching 2 blocks to 2
[   32.677713]   moving 16 blocks
[   32.679554]   moving 3 blocks
[   32.680865] patching 2 blocks to 2
[   32.694299]   moving 36 blocks
[   32.698726]   moving 4 blocks
[   32.700401] patching 2 blocks to 2
[   32.711084] patching 12 blocks to 12
[   32.732493]   moving 30 blocks
[   32.735823]   moving 4 blocks
[   32.737140] patching 2 blocks to 2
[   32.746669]   moving 1 blocks
[   32.747978]   moving 3 blocks
[   32.749216] patching 2 blocks to 2
[   32.758580]   moving 1 blocks
[   32.759896]   moving 3 blocks
[   32.761344] patching 2 blocks to 2
[   32.770680]   moving 1 blocks
[   32.771978]   moving 3 blocks
[   32.773421] patching 2 blocks to 2
[   32.784878]   moving 18 blocks
[   32.788822]   moving 3 blocks
[   32.790449] patching 2 blocks to 2
[   32.800756]   moving 5 blocks
[   32.802279]   moving 3 blocks
[   32.803894] patching 2 blocks to 2
[   32.814767] patching 13 blocks to 13
[   32.825593]   moving 3 blocks
[   32.826997] patching 2 blocks to 2
[   32.836857]   moving 2 blocks
[   32.838561]   moving 3 blocks
[   32.840080] patching 2 blocks to 2
[   32.853217]   moving 33 blocks
[   32.857460]   moving 4 blocks
[   32.859373] patching 2 blocks to 2
[   32.871573]   moving 24 blocks
[   32.876140]   moving 4 blocks
[   32.877987] patching 2 blocks to 2
[   32.899329]   moving 104 blocks
[   32.907430]   moving 4 blocks
[   32.909147] patching 2 blocks to 2
[   32.918881]   moving 1 blocks
[   32.920308]   moving 3 blocks
[   32.921322] patching 2 blocks to 2
[   33.209609]   moving 2548 blocks
[   33.303012] patching 62 blocks to 62
[   33.320436]   moving 4 blocks
[   33.322063] patching 2 blocks to 2
[   33.334664]   moving 24 blocks
[   33.430151]   moving 829 blocks
[   33.461691]   moving 16 blocks
[   33.464532] patching 2 blocks to 2
[   33.824145]   moving 3165 blocks
[   33.950448]   moving 3 blocks
[   33.951943]   moving 3 blocks
[   33.953556] patching 2 blocks to 2
[   33.964216]   moving 6 blocks
[   33.966289]   moving 5 blocks
[   33.968135]   moving 4 blocks
[   33.969685] patching 2 blocks to 2
[   33.979596]   moving 4 blocks
[   33.988488] patching 63 blocks to 63
[   34.005334]   moving 5 blocks
[   34.006969] patching 2 blocks to 2
[   34.025272]   moving 67 blocks
[   34.036316] patching 51 blocks to 51
[   34.051975]   moving 4 blocks
[   34.053321] patching 2 blocks to 2
[   34.065390]   moving 21 blocks
[   34.069321]   moving 6 blocks
[   34.073020]   moving 20 blocks
[   34.078082]   moving 3 blocks
[   34.079684] patching 2 blocks to 2
[   34.099509]   moving 83 blocks
[   34.138885] patching 275 blocks to 275
[   34.172779]   moving 6 blocks
[   34.174320] patching 2 blocks to 2
[   34.198794]   moving 134 blocks
[   34.330563]   moving 1137 blocks
[   34.380123]   moving 17 blocks
[   34.381654] patching 2 blocks to 2
[   34.574349]   moving 1651 blocks
[   34.759189]   moving 1157 blocks
[   34.959649] patching 1459 blocks to 1459
[   35.113353]   moving 6 blocks
[   35.116071]   moving 8 blocks
[   35.145568]   moving 253 blocks
[   35.173074]   moving 123 blocks
[   35.180691]   moving 4 blocks
[   35.182642]   moving 4 blocks
[   35.196939]   moving 112 blocks
[   35.205285]   moving 4 blocks
[   35.207626]   moving 6 blocks
[   35.222707]   moving 121 blocks
[   35.232404]   moving 6 blocks
[   35.234729]   moving 9 blocks
[   35.236645]   moving 4 blocks
[   35.254542]   moving 156 blocks
[   35.269104]   moving 37 blocks
[   35.281714]   moving 68 blocks
[   35.289676]   moving 6 blocks
[   35.293050]   moving 19 blocks
[   35.300927]   moving 53 blocks
[   35.308030]   moving 6 blocks
[   35.311065]   moving 13 blocks
[   35.313747]   moving 6 blocks
[   35.316229]   moving 9 blocks
[   35.318063]   moving 5 blocks
[   35.320685]   moving 11 blocks
[   35.326320]   moving 24 blocks
[   35.333941]   moving 42 blocks
[   35.345444]   moving 60 blocks
[   35.352214]   moving 8 blocks
[   35.356915]   moving 16 blocks
[   35.360020]   moving 4 blocks
[   35.361603]   moving 4 blocks
[   35.363087]   moving 4 blocks
[   35.364631]   moving 4 blocks
[   35.366338]   moving 6 blocks
[   35.368466]   moving 5 blocks
[   35.370586]   moving 4 blocks
[   35.372491]   moving 4 blocks
[   35.374631]   moving 4 blocks
[   35.376448]   moving 4 blocks
[   35.378664]   moving 5 blocks
[   35.380532]   moving 6 blocks
[   35.382222]   moving 4 blocks
[   35.384301]   moving 6 blocks
[   35.386161]   moving 5 blocks
[   35.387866]   moving 4 blocks
[   35.390507]   moving 9 blocks
[   35.393124]   moving 12 blocks
[   35.508142]   moving 995 blocks
[   35.545881]   moving 6 blocks
[   35.547963]   moving 10 blocks
[   35.552342]   moving 27 blocks
[   35.566630]   moving 85 blocks
[   35.588500]   moving 126 blocks
[   35.612253]   moving 4 blocks
[   35.615067]   moving 10 blocks
[   35.617100]   moving 6 blocks
[   35.619906]   moving 7 blocks
[   35.624506]   moving 28 blocks
[   35.628677]   moving 4 blocks
[   35.630930]   moving 8 blocks
[   35.633873]   moving 16 blocks
[   35.644441]   moving 58 blocks
[   35.661571]   moving 112 blocks
[   35.670614]   moving 4 blocks
[   35.692101]   moving 179 blocks
[   35.701320]   moving 5 blocks
[   35.703374]   moving 8 blocks
[   35.756706]   moving 453 blocks
[   35.784743]   moving 74 blocks
[   35.821222]   moving 261 blocks
[   35.860117]   moving 230 blocks
[   35.881590]   moving 79 blocks
[   35.897625]   moving 73 blocks
[   35.932482]   moving 254 blocks
[   35.945899]   moving 4 blocks
[   35.947842]   moving 7 blocks
[   35.976268]   moving 233 blocks
[   35.995087]   moving 10 blocks
[   36.003077]   moving 60 blocks
[   36.008739]   moving 25 blocks
[   36.017577]   moving 44 blocks
[   36.043749]   moving 189 blocks
[   36.071212]   moving 148 blocks
[   36.079146]   moving 5 blocks
[   36.082779] patching 21 blocks to 21
[   36.101237]   moving 44 blocks
[   36.127942]   moving 202 blocks
[   36.152290]   moving 109 blocks
[   36.183452]   moving 203 blocks
[   36.204294]   moving 86 blocks
[   36.212320]   moving 11 blocks
[   36.215953]   moving 17 blocks
[   36.221939]   moving 22 blocks
[   36.224430]   moving 5 blocks
[   36.232639]   moving 61 blocks
[   36.241210]   moving 16 blocks
[   36.245400]   moving 7 blocks
[   36.247657]   moving 6 blocks
[   36.256581]   moving 64 blocks
[   36.266117]   moving 27 blocks
[   36.277131]   moving 70 blocks
[   36.282595]   moving 4 blocks
[   36.285978]   moving 19 blocks
[   36.291346]   moving 11 blocks
[   36.297204]   moving 38 blocks
[   36.314580]   moving 120 blocks
[   36.324565]   moving 9 blocks
[   36.336076]   moving 83 blocks
[   36.344850]   moving 8 blocks
[   36.504369]   moving 1435 blocks
[   36.558281]   moving 23 blocks
[   36.566672]   moving 39 blocks
[   36.618732]   moving 429 blocks
[   36.638433]   moving 13 blocks
[   36.648019]   moving 67 blocks
[   36.665160] patching 104 blocks to 104
[   36.728578] patching 350 blocks to 350
[   36.795908]   moving 4 blocks
[   36.799885]   moving 25 blocks
[   36.807647]   moving 33 blocks
[   36.815452]   moving 37 blocks
[   36.821247]   moving 8 blocks
[   36.822890]   moving 4 blocks
[   36.824701]   moving 4 blocks
[   36.826726]   moving 4 blocks
[   36.833616]   moving 50 blocks
[   36.846475]   moving 62 blocks
[   36.857393]   moving 40 blocks
[   36.895291]   moving 305 blocks
[   36.908410]   moving 4 blocks
[   36.911267]   moving 15 blocks
[   36.915363]   moving 10 blocks
[   36.918545]   moving 5 blocks
[   36.920730]   moving 5 blocks
[   37.232131]   moving 2824 blocks
[   37.326140]   moving 8 blocks
[   37.329455]   moving 18 blocks
[   37.335375]   moving 18 blocks
[   37.339836]   moving 10 blocks
[   37.346339]   moving 44 blocks
[   37.354013]   moving 9 blocks
[   37.357976]   moving 8 blocks
[   37.365764] patching 58 blocks to 58
[   37.385912]   moving 4 blocks
[   37.387694]   moving 5 blocks
[   37.392534]   moving 31 blocks
[   37.397227]   moving 2 blocks
[   37.398765]   moving 4 blocks
[   37.459190] patching 528 blocks to 528
[   37.600128]   moving 11 blocks
[   37.602410]   moving 8 blocks
[   37.606885]   moving 8 blocks
[   37.611120]   moving 9 blocks
[   37.614022]   moving 16 blocks
[   37.622095]   moving 42 blocks
[   37.631549]   moving 33 blocks
[   37.639034]   moving 34 blocks
[   37.646553]   moving 25 blocks
[   37.652808]   moving 24 blocks
[   37.658057]   moving 22 blocks
[   37.662733]   moving 6 blocks
[   37.667090]   moving 29 blocks
[   37.671216]   moving 2 blocks
[   37.675399]   moving 22 blocks
[   37.683964]   moving 48 blocks
[   37.719675]   moving 230 blocks
[   37.762600]   moving 274 blocks
[   37.781042]   moving 41 blocks
[   37.787210]   moving 20 blocks
[   37.793901]   moving 25 blocks
[   37.800066]   moving 26 blocks
[   37.807712]   moving 35 blocks
[   37.816556]   moving 42 blocks
[   37.823890]   moving 31 blocks
[   37.831249]   moving 27 blocks
[   37.839246]   moving 39 blocks
[   37.846885]   moving 21 blocks
[   37.851828]   moving 28 blocks
[   37.860129]   moving 43 blocks
[   37.868711]   moving 38 blocks
[   37.881956]   moving 73 blocks
[   37.911517]   moving 186 blocks
[   37.956350]   moving 302 blocks
[   37.975927]   moving 39 blocks
[   37.983970]   moving 24 blocks
[   37.990184]   moving 26 blocks
[   37.997760]   moving 34 blocks
[   38.008186]   moving 55 blocks
[   38.017208]   moving 24 blocks
[   38.021328]   moving 8 blocks
[   38.034445]   moving 90 blocks
[   38.045492]   moving 25 blocks
[   38.053893]   moving 43 blocks
[   38.060147]   moving 21 blocks
[   38.067522]   moving 27 blocks
[   38.074287]   moving 27 blocks
[   38.084035]   moving 61 blocks
[   38.090156]   moving 12 blocks
[   38.097204]   moving 50 blocks
[   38.104315]   moving 22 blocks
[   38.129482]   moving 204 blocks
[   38.144197]   moving 26 blocks
[   38.150422]   moving 21 blocks
[   38.163377]   moving 80 blocks
[   38.174757]   moving 42 blocks
[   38.182508]   moving 31 blocks
[   38.186791]   moving 4 blocks
[   38.193998]   moving 52 blocks
[   38.201771]   moving 27 blocks
[   38.208661]   moving 35 blocks
[   38.215954]   moving 26 blocks
[   38.225130]   moving 54 blocks
[   38.236986]   moving 63 blocks
[   38.244183]   moving 10 blocks
[   38.255101]   moving 68 blocks
[   38.269519]   moving 61 blocks
[   38.279470]   moving 34 blocks
[   38.287674]   moving 43 blocks
[   38.306053]   moving 126 blocks
[   38.341671]   moving 242 blocks
[   38.356794]   moving 22 blocks
[   38.359691]   moving 7 blocks
[   38.364877]   moving 33 blocks
[   38.374558]   moving 52 blocks
[   38.382496]   moving 24 blocks
[   38.391406]   moving 41 blocks
[   38.395901]   moving 2 blocks
[   38.396977]   moving 1 blocks
[   38.418769]   moving 187 blocks
[   38.427933]   moving 1 blocks
[   38.431017]   moving 17 blocks
[   38.439631]   moving 52 blocks
[   38.444747]   moving 2 blocks
[   38.477722]   moving 284 blocks
[   38.490746]   moving 1 blocks
[   38.491840]   moving 1 blocks
[   38.493068]   moving 2 blocks
[   38.494262]   moving 2 blocks
[   38.495529]   moving 2 blocks
[   38.496857] patching 3 blocks to 3
[   38.624574] patching 1057 blocks to 1057
[   41.021516]   moving 36 blocks
[   41.027207]   moving 21 blocks
[   41.034703]   moving 32 blocks
[   41.049910]   moving 92 blocks
[   41.065893]   moving 84 blocks
[   41.074086]   moving 5 blocks
[   41.086674] patching 90 blocks to 90
[   41.114036] patching 42 blocks to 42
[   41.133297]   moving 4 blocks
[   41.138871]   moving 37 blocks
[   41.145956]   moving 24 blocks
[   41.150543]   moving 4 blocks
[   41.154055]   moving 16 blocks
[   41.202414]   moving 397 blocks
[   41.225193]   moving 50 blocks
[   41.259308]   moving 245 blocks
[   41.272766]   moving 7 blocks
[   41.275316]   moving 9 blocks
[   41.281198]   moving 24 blocks
[   41.286287]   moving 14 blocks
[   41.290935]   moving 12 blocks
[   41.303485]   moving 87 blocks
[   41.313888]   moving 6 blocks
[   41.318428]   moving 29 blocks
[   41.322742]   moving 7 blocks
[   41.324689]   moving 4 blocks
[   41.483767] patching 1430 blocks to 1430
[   41.838260]   moving 4 blocks
[   41.840041]   moving 5 blocks
[   41.842499]   moving 11 blocks
[   41.845439]   moving 14 blocks
[   41.850557]   moving 33 blocks
[   41.854997]   moving 5 blocks
[   41.858512]   moving 19 blocks
[   41.863880]   moving 20 blocks
[   41.868148]   moving 19 blocks
[   41.908855]   moving 329 blocks
[   41.966691]   moving 377 blocks
[   41.999234]   moving 10 blocks
[   42.003062]   moving 6 blocks
[   42.016235]   moving 103 blocks
[   42.037071]   moving 109 blocks
[   42.046036]   moving 13 blocks
[   42.064920]   moving 153 blocks
[   42.088065]   moving 124 blocks
[   42.107794]   moving 94 blocks
[   42.114693]   moving 4 blocks
[   42.118457]   moving 24 blocks
[   42.122363]   moving 5 blocks
[   42.127396]   moving 34 blocks
[   42.131448]   moving 5 blocks
[   42.136731]   moving 34 blocks
[   42.142423] patching 15 blocks to 15
[   42.157231]   moving 13 blocks
[   42.160546]   moving 5 blocks
[   42.171025]   moving 77 blocks
[   42.177735]   moving 6 blocks
[   42.179931]   moving 7 blocks
[   42.182037]   moving 6 blocks
[   42.184371]   moving 6 blocks
[   42.187461]   moving 16 blocks
[   42.192739]   moving 12 blocks
[   42.196388]   moving 5 blocks
[   42.200418]   moving 12 blocks
[   42.201682] I:current maximum temperature: 36207
[   42.207146]   moving 45 blocks
[   42.215466]   moving 51 blocks
[   42.220505]   moving 5 blocks
[   42.222915]   moving 9 blocks
[   42.224582]   moving 4 blocks
[   42.226418]   moving 7 blocks
[   42.228566]   moving 4 blocks
[   42.234186]   moving 37 blocks
[   42.243019]   moving 38 blocks
[   42.248776]   moving 19 blocks
[   42.376875]   moving 1149 blocks
[   42.421413]   moving 10 blocks
[   42.425412]   moving 25 blocks
[   42.433467]   moving 49 blocks
[   42.439958]   moving 6 blocks
[   42.441269]   moving 4 blocks
[   42.822737]   moving 3482 blocks
[   43.012514]   moving 2 blocks
[   43.013842]   moving 3 blocks
[   43.015301] patching 2 blocks to 2
[   43.035338]   moving 88 blocks
[   43.043260]   moving 4 blocks
[   43.045063]   moving 3 blocks
[   43.046635] patching 2 blocks to 2
[   43.056826]   moving 5 blocks
[   43.075127] patching 149 blocks to 149
[   43.099317]   moving 5 blocks
[   43.100910] patching 2 blocks to 2
[   43.120295]   moving 81 blocks
[   43.267840]   moving 1271 blocks
[   43.323873] patching 95 blocks to 95
[   43.344914]   moving 4 blocks
[   43.346252] patching 2 blocks to 2
[   43.359823]   moving 33 blocks
[   43.375937] patching 102 blocks to 102
[   43.401466]   moving 5 blocks
[   43.403131] patching 2 blocks to 2
[   43.417508]   moving 41 blocks
[   43.421706]   moving 3 blocks
[   43.423394]   moving 3 blocks
[   43.424756] patching 2 blocks to 2
[   43.439904]   moving 46 blocks
[   43.446119]   moving 12 blocks
[   43.450145]   moving 3 blocks
[   43.451665] patching 2 blocks to 2
[   43.462257]   moving 10 blocks
[   43.509389]   moving 393 blocks
[   43.526160]   moving 9 blocks
[   43.527602] patching 2 blocks to 2
[   43.600217] patching 571 blocks to 572
[   43.872611]   moving 12 blocks
[   43.874268]   moving 3 blocks
[   43.875757] patching 2 blocks to 2
[   44.140541] patching 2371 blocks to 2371
[   44.440097]   moving 86 blocks
[   44.448217]   moving 10 blocks
[   44.452871]   moving 24 blocks
[   44.456193]   moving 2 blocks
[   44.478253]   moving 182 blocks
[   44.501044]   moving 110 blocks
[   44.510165]   moving 9 blocks
[   44.514397]   moving 7 blocks
[   44.517034]   moving 11 blocks
[   44.521760]   moving 14 blocks
[   44.524539]   moving 6 blocks
[   44.528998]   moving 26 blocks
[   44.533285]   moving 6 blocks
[   44.535642]   moving 8 blocks
[   44.537907]   moving 8 blocks
[   44.539709]   moving 5 blocks
[   44.545011]   moving 34 blocks
[   44.549193]   moving 4 blocks
[   44.550722]   moving 4 blocks
[   44.553516]   moving 14 blocks
[   44.558448]   moving 18 blocks
[   44.588938]   moving 252 blocks
[   44.603660]   moving 21 blocks
[   44.606879]   moving 9 blocks
[   44.612768] patching 23 blocks to 23
[   44.630459]   moving 38 blocks
[   44.638474]   moving 30 blocks
[   44.644928]   moving 19 blocks
[   44.648189]   moving 15 blocks
[   44.651490]   moving 4 blocks
[   44.656725]   moving 34 blocks
[   45.578893]   moving 8192 blocks
[   45.931658]   moving 21 blocks
[   45.934970]   moving 3 blocks
[   45.936382] patching 2 blocks to 2
[   45.947688]   moving 13 blocks
[   46.061901] patching 1033 blocks to 1033
[   46.153552]   moving 17 blocks
[   46.156928] patching 2 blocks to 2
[   46.208284]   moving 376 blocks
[   46.225817]   moving 4 blocks
[   46.227295]   moving 3 blocks
[   46.228905] patching 2 blocks to 2
[   46.240174]   moving 14 blocks
[   46.288056] patching 418 blocks to 418
[   46.338566]   moving 7 blocks
[   46.340199] patching 2 blocks to 2
[   46.506804]   moving 1418 blocks
[   46.557789]   moving 9 blocks
[   46.561788]   moving 3 blocks
[   46.563231] patching 2 blocks to 2
[   46.578426]   moving 42 blocks
[   46.732811] patching 1383 blocks to 1383
[   46.850164]   moving 36 blocks
[   46.854513] patching 2 blocks to 2
[   46.866206]   moving 19 blocks
[   46.884639]   moving 130 blocks
[   46.896620]   moving 27 blocks
[   46.902258]   moving 9 blocks
[   46.904280]   moving 7 blocks
[   46.909730]   moving 34 blocks
[   46.916174]   moving 25 blocks
[   46.922135]   moving 16 blocks
[   46.928900]   moving 47 blocks
[   46.935336]   moving 5 blocks
[   46.938111]   moving 14 blocks
[   46.943605]   moving 20 blocks
[   46.950278]   moving 33 blocks
[   46.955180]   moving 14 blocks
[   46.959685]   moving 4 blocks
[   46.961496]   moving 4 blocks
[   46.967310]   moving 36 blocks
[   46.972670]   moving 6 blocks
[   46.975666]   moving 8 blocks
[   46.982942]   moving 38 blocks
[   46.989393]   moving 14 blocks
[   46.999630]   moving 70 blocks
[   47.007239]   moving 4 blocks
[   47.010407]   moving 5 blocks
[   47.012392]   moving 5 blocks
[   47.014641]   moving 5 blocks
[   47.016300]   moving 5 blocks
[   47.017875]   moving 4 blocks
[   47.020038]   moving 5 blocks
[   47.021889]   moving 5 blocks
[   47.024024]   moving 4 blocks
[   47.025717]   moving 4 blocks
[   47.027684]   moving 4 blocks
[   47.029624]   moving 6 blocks
[   47.031413]   moving 5 blocks
[   47.036725]   moving 33 blocks
[   47.046632]   moving 55 blocks
[   47.053630]   moving 14 blocks
[   47.058309] patching 16 blocks to 16
[   47.078378] patching 72 blocks to 72
[   47.102458]   moving 35 blocks
[   47.107931]   moving 14 blocks
[   47.112069]   moving 8 blocks
[   47.113937]   moving 6 blocks
[   47.117576]   moving 17 blocks
[   47.122086]   moving 6 blocks
[   47.124694]   moving 12 blocks
[   47.161696]   moving 298 blocks
[   47.176773]   moving 10 blocks
[   47.178233] patching 2 blocks to 2
[   47.216458]   moving 258 blocks
[   47.288433]   moving 528 blocks
[   47.311527]   moving 10 blocks
[   47.313040] patching 2 blocks to 2
[   47.493395]   moving 1530 blocks
[   47.553315]   moving 37 blocks
[   47.558123]   moving 4 blocks
[   47.560076] patching 2 blocks to 2
[   47.571642]   moving 15 blocks
[   47.589409]   moving 124 blocks
[   47.597108]   moving 5 blocks
[   47.598881] patching 2 blocks to 2
[   47.623083]   moving 127 blocks
[   47.632250]   moving 4 blocks
[   47.633744]   moving 3 blocks
[   47.635363] patching 2 blocks to 2
[   47.645506]   moving 4 blocks
[   47.647495]   moving 3 blocks
[   47.649139]   moving 3 blocks
[   47.650707] patching 2 blocks to 2
[   47.661042]   moving 6 blocks
[   47.689485]   moving 235 blocks
[   47.701903] patching 2 blocks to 2
[   47.711859]   moving 3 blocks
[   47.714181]   moving 7 blocks
[   47.715949] patching 2 blocks to 2
[   47.726110]   moving 5 blocks
[   47.728179]   moving 7 blocks
[   47.730221]   moving 3 blocks
[   47.731790] patching 2 blocks to 2
[   47.752464]   moving 9 blocks
[   47.765373] patching 80 blocks to 80
[   47.784795]   moving 4 blocks
[   47.786115] patching 2 blocks to 2
[   47.806361]   moving 93 blocks
[   47.861753]   moving 433 blocks
[   47.883300]   moving 9 blocks
[   47.886451] patching 2 blocks to 2
[   47.995989]   moving 907 blocks
[   48.034648] patching 47 blocks to 47
[   48.050550]   moving 4 blocks
[   48.052200] patching 2 blocks to 2
[   48.072688]   moving 93 blocks
[   48.094096] patching 121 blocks to 121
[   48.139309] patching 7 blocks to 7
[   48.164523] patching 128 blocks to 128
[   48.564664] patching 566 blocks to 566
[   48.621783]   moving 10 blocks
[   48.623501] patching 2 blocks to 2
[   48.691434]   moving 526 blocks
[   48.871324] patching 1421 blocks to 1421
[   49.073944]   moving 28 blocks
[   49.077385] patching 2 blocks to 2
[   49.089525]   moving 24 blocks
[   49.094104]   moving 7 blocks
[   49.095649]   moving 3 blocks
[   49.096982] patching 2 blocks to 2
[   49.108232]   moving 15 blocks
[   49.115217]   moving 35 blocks
[   49.118914] patching 2 blocks to 2
[   49.135176]   moving 47 blocks
[   49.262280]   moving 1086 blocks
[   49.352722] patching 460 blocks to 460
[   49.448081]   moving 417 blocks
[   49.484911]   moving 156 blocks
[   49.512176]   moving 145 blocks
[   49.527652]   moving 48 blocks
[   49.544507]   moving 86 blocks
[   49.848828] patching 2705 blocks to 2705
[   50.327914]   moving 1842 blocks
[   50.518497]   moving 1005 blocks
[   50.798927] patching 2192 blocks to 2192
[   51.217850] patching 1646 blocks to 1646
[   59.666473]   moving 278 blocks
[   59.708964]   moving 252 blocks
[   59.747329]   moving 220 blocks
[   59.764215]   moving 51 blocks
[   60.370905]   moving 4707 blocks
[   60.811788] patching 2029 blocks to 2029
[   61.029882]   moving 36 blocks
[   61.036454] patching 2 blocks to 2
[   61.061047] patching 128 blocks to 128
[   61.084270]   moving 4 blocks
[   61.085804] patching 2 blocks to 2
[   61.101464]   moving 51 blocks
[   61.107761]   moving 19 blocks
[   61.111951]   moving 3 blocks
[   61.113386] patching 2 blocks to 2
[   61.124218]   moving 10 blocks
[   61.132532] patching 45 blocks to 45
[   61.147765]   moving 3 blocks
[   61.148986] patching 2 blocks to 2
[   61.164127]   moving 46 blocks
[   61.262628] patching 833 blocks to 833
[   61.357408] patching 180 blocks to 180
[   61.405599]   moving 30 blocks
[   61.418238] patching 80 blocks to 80
[   61.443837]   moving 7 blocks
[   61.446982]   moving 14 blocks
[   61.451209] patching 22 blocks to 22
[   61.469485]   moving 12 blocks
[   61.473894]   moving 11 blocks
[   61.476632]   moving 7 blocks
[   61.482892] patching 41 blocks to 41
[   61.505282]   moving 50 blocks
[   61.510314]   moving 2 blocks
[   61.517805]   moving 54 blocks
[   61.525346]   moving 23 blocks
[   61.531175]   moving 24 blocks
[   61.535608]   moving 3 blocks
[   61.538634]   moving 15 blocks
[   61.542867]   moving 9 blocks
[   61.544876]   moving 5 blocks
[   61.547014]   moving 7 blocks
[   61.554814]   moving 54 blocks
[   61.566933]   moving 46 blocks
[   61.571561]   moving 1 blocks
[   61.572831]   moving 3 blocks
[   61.574493]   moving 3 blocks
[   61.575738] patching 2 blocks to 2
[   61.642831]   moving 534 blocks
[   61.777997]   moving 1022 blocks
[   61.816303] patching 2 blocks to 2
[   61.826456] patching 5 blocks to 5
[   61.839086]   moving 2 blocks
[   61.840563]   moving 3 blocks
[   61.842140] patching 2 blocks to 2
[   61.867295]   moving 135 blocks
[   61.875869]   moving 5 blocks
[   61.877623] patching 2 blocks to 2
[   61.887290]   moving 3 blocks
[   61.889015]   moving 3 blocks
[   61.890361] patching 2 blocks to 2
[   61.900033]   moving 3 blocks
[   61.901737]   moving 3 blocks
[   61.903055] patching 2 blocks to 2
[   61.912848]   moving 3 blocks
[   61.914563]   moving 3 blocks
[   61.915941] patching 2 blocks to 2
[   61.925339]   moving 4 blocks
[   61.926527]   moving 4 blocks
[   61.927735]   moving 4 blocks
[   62.212627] I:current maximum temperature: 36501
[   62.885867]   moving 8192 blocks
[   63.230992] patching 25 blocks to 25
[   63.355701] patching 1011 blocks to 1011
[   70.165649] patching 1833 blocks to 1842
[   70.440225] patching 29 blocks to 29
[   70.464426]   moving 78 blocks
[   70.474987]   moving 27 blocks
[   70.480134]   moving 11 blocks
[   70.483228]   moving 13 blocks
[   70.487332]   moving 6 blocks
[   70.518546]   moving 262 blocks
[   70.533416]   moving 5 blocks
[   70.537099]   moving 22 blocks
[   70.571170]   moving 272 blocks
[   70.585661]   moving 8 blocks
[   70.630573]   moving 374 blocks
[   70.646305]   moving 4 blocks
[   70.681495]   moving 307 blocks
[   70.706039]   moving 87 blocks
[   70.742155]   moving 248 blocks
[   70.801608]   moving 260 blocks
[   70.853716]   moving 350 blocks
[   70.871326]   moving 10 blocks
[   70.900935]   moving 234 blocks
[   70.943170]   moving 295 blocks
[   70.958075]   moving 4 blocks
[   70.960469]   moving 6 blocks
[   70.979358]   moving 155 blocks
[   71.029599]   moving 338 blocks
[   71.042623]   moving 11 blocks
[   71.071492]   moving 227 blocks
[   71.112802]   moving 262 blocks
[   71.150102]   moving 222 blocks
[   71.161822]   moving 8 blocks
[   71.164994]   moving 8 blocks
[   71.199458]   moving 240 blocks
[   71.211512]   moving 11 blocks
[   71.230642]   moving 135 blocks
[   71.245544]   moving 56 blocks
[   71.251806]   moving 5 blocks
[   71.254075]   moving 8 blocks
[   71.257592]   moving 5 blocks
[   71.259518]   moving 4 blocks
[   71.260981]   moving 3 blocks
[   71.262632]   moving 3 blocks
[   71.264639]   moving 9 blocks
[   71.267915]   moving 6 blocks
[   71.269868]   moving 5 blocks
[   71.271965]   moving 5 blocks
[   71.274651]   moving 12 blocks
[   71.277695]   moving 12 blocks
[   71.280916]   moving 15 blocks
[   71.287208]   moving 23 blocks
[   71.291361]   moving 5 blocks
[   71.293494]   moving 4 blocks
[   71.296148]   moving 14 blocks
[   71.298329]   moving 5 blocks
[   71.302014]   moving 19 blocks
[   71.306625]   moving 10 blocks
[   71.308432]   moving 3 blocks
[   71.310436]   moving 6 blocks
[   71.312401]   moving 3 blocks
[   71.315748]   moving 18 blocks
[   71.320523]   moving 8 blocks
[   71.324880]   moving 10 blocks
[   71.326832]   moving 4 blocks
[   71.328400]   moving 2 blocks
[   71.329732]   moving 3 blocks
[   71.335016]   moving 28 blocks
[   71.340177]   moving 7 blocks
[   71.342923]   moving 11 blocks
[   71.349547]   moving 44 blocks
[   71.357475]   moving 26 blocks
[   71.364782]   moving 36 blocks
[   71.372213]   moving 24 blocks
[   71.376103]   moving 3 blocks
[   71.377734]   moving 3 blocks
[   71.378959]   moving 2 blocks
[   71.380689]   moving 4 blocks
[   71.382342]   moving 1 blocks
[   71.386599]   moving 23 blocks
[   71.393191]   moving 37 blocks
[   71.400095]   moving 25 blocks
[   71.406809]   moving 30 blocks
[   71.412568]   moving 7 blocks
[   71.415609]   moving 14 blocks
[   71.417661]   moving 4 blocks
[   71.432921]   moving 121 blocks
[   71.443694]   moving 16 blocks
[   71.446048]   moving 8 blocks
[   71.465920]   moving 148 blocks
[   71.497480]   moving 202 blocks
[   71.532134]   moving 203 blocks
[   71.545685]   moving 17 blocks
[   71.551683]   moving 22 blocks
[   71.562985]   moving 62 blocks
[   71.574175]   moving 40 blocks
[   71.621148]   moving 305 blocks
[   71.638914]   moving 29 blocks
[   71.643413]   moving 2 blocks
[   71.653785]   moving 73 blocks
[   71.663878]   moving 27 blocks
[   71.677811]   moving 76 blocks
[   71.689893]   moving 55 blocks
[   71.701534]   moving 52 blocks
[   71.709865]   moving 22 blocks
[   71.716988]   moving 30 blocks
[   71.722801]   moving 22 blocks
[   71.729434]   moving 21 blocks
[   71.735208]   moving 32 blocks
[   71.741599]   moving 26 blocks
[   71.750879]   moving 41 blocks
[   71.780221]   moving 226 blocks
[   71.794906]   moving 23 blocks
[   71.801263]   moving 21 blocks
[   71.806236]   moving 24 blocks
[   71.813213]   moving 27 blocks
[   71.821302]   moving 36 blocks
[   71.831759]   moving 46 blocks
[   71.844008]   moving 65 blocks
[   71.851804]   moving 23 blocks
[   71.874630]   moving 152 blocks
[   71.917236]   moving 280 blocks
[   71.931255]   moving 9 blocks
[   71.934020]   moving 14 blocks
[   71.940474]   moving 24 blocks
[   71.951977]   moving 23 blocks
[   71.958593]   moving 25 blocks
[   71.965941]   moving 24 blocks
[   71.977097]   moving 52 blocks
[   71.996496]   moving 106 blocks
[   72.007069]   moving 23 blocks
[   72.011952]   moving 8 blocks
[   72.016188]   moving 9 blocks
[   72.018140]   moving 6 blocks
[   72.020144]   moving 5 blocks
[   72.021941]   moving 4 blocks
[   72.024463]   moving 9 blocks
[   72.026957]   moving 10 blocks
[   72.030096]   moving 3 blocks
[   72.040279] patching 56 blocks to 56
[   72.057932]   moving 10 blocks
[   72.061078]   moving 2 blocks
[   72.062386]   moving 2 blocks
[   72.064844]   moving 8 blocks
[   72.068228]   moving 5 blocks
[   72.070168]   moving 4 blocks
[   72.071677]   moving 3 blocks
[   72.074628]   moving 13 blocks
[   72.078942]   moving 24 blocks
[   72.083615]   moving 5 blocks
[   72.107675] patching 195 blocks to 194
[   72.180654]   moving 50 blocks
[   72.185195]   moving 6 blocks
[   72.190898]   moving 33 blocks
[   72.195977]   moving 10 blocks
[   72.199495]   moving 5 blocks
[   72.201429]   moving 4 blocks
[   72.203771]   moving 5 blocks
[   72.205879]   moving 8 blocks
[   72.216643]   moving 64 blocks
[   72.226808]   moving 41 blocks
[   72.233474]   moving 12 blocks
[   72.235675]   moving 5 blocks
[   72.237890]   moving 5 blocks
[   72.241588]   moving 19 blocks
[   72.248469]   moving 26 blocks
[   72.255264]   moving 33 blocks
[   72.259443]   moving 4 blocks
[   72.283815]   moving 191 blocks
[   72.318622]   moving 213 blocks
[   72.338319]   moving 68 blocks
[   72.345752]   moving 8 blocks
[   72.351195]   moving 24 blocks
[   72.355777]   moving 5 blocks
[   72.359997]   moving 24 blocks
[   72.366108]   moving 23 blocks
[   72.373532]   moving 28 blocks
[   72.384717]   moving 54 blocks
[   72.394202]   moving 36 blocks
[   72.420196]   moving 161 blocks
[   72.430400]   moving 7 blocks
[   72.433504]   moving 12 blocks
[   72.436741]   moving 11 blocks
[   72.440861]   moving 5 blocks
[   72.443093]   moving 7 blocks
[   72.450673]   moving 51 blocks
[   72.456139]   moving 11 blocks
[   72.458100]   moving 4 blocks
[   72.459577]   moving 4 blocks
[   72.461107]   moving 4 blocks
[   72.492991]   moving 258 blocks
[   72.506481]   moving 5 blocks
[   72.510107]   moving 17 blocks
[   72.520252]   moving 50 blocks
[   72.567726]   moving 353 blocks
[   72.608561]   moving 112 blocks
[   72.618144]   moving 9 blocks
[   72.620226]   moving 8 blocks
[   72.622343]   moving 9 blocks
[   72.626315]   moving 10 blocks
[   72.633675]   moving 41 blocks
[   72.641761]   moving 31 blocks
[   72.649284] patching 34 blocks to 34
[   72.668585]   moving 22 blocks
[   72.684031]   moving 116 blocks
[   72.693517]   moving 10 blocks
[   72.696029]   moving 8 blocks
[   72.701406]   moving 36 blocks
[   72.706391]   moving 9 blocks
[   72.719468]   moving 89 blocks
[   72.726955]   moving 3 blocks
[   72.728375]   moving 3 blocks
[   72.729908] patching 2 blocks to 2
[   72.740336]   moving 5 blocks
[   72.787396]   moving 403 blocks
[   73.045763]   moving 2168 blocks
[   73.125408]   moving 3 blocks
[   73.126761]   moving 3 blocks
[   73.129131]   moving 10 blocks
[   73.132201]   moving 2 blocks
[   73.133709]   moving 4 blocks
[   73.135454]   moving 2 blocks
[   73.137075]   moving 3 blocks
[   73.138465]   moving 2 blocks
[   73.140638]   moving 11 blocks
[   73.143392]   moving 2 blocks
[   73.144655]   moving 2 blocks
[   73.146094]   moving 2 blocks
[   73.147587]   moving 2 blocks
[   73.149066]   moving 2 blocks
[   73.150327]   moving 2 blocks
[   73.151646]   moving 1 blocks
[   73.152758]   moving 2 blocks
[   73.183170]   moving 263 blocks
[   73.217425]   moving 191 blocks
[   73.231459]   moving 22 blocks
[   73.384977]   moving 1370 blocks
[   73.435562]   moving 37 blocks
[   73.440642] patching 10 blocks to 10
[   73.500722]   moving 41 blocks
[   73.505651]   moving 4 blocks
[   73.517466]   moving 80 blocks
[   73.527955] patching 14 blocks to 14
[   73.540621] patching 1 blocks to 1
[   73.550643]   moving 4 blocks
[   73.610314] patching 575 blocks to 575
[   73.697152]   moving 298 blocks
[   73.711241]   moving 11 blocks
[   73.714434] patching 2 blocks to 2
[   73.726136]   moving 18 blocks
[   74.018461]   moving 2949 blocks
[   74.123550]   moving 87 blocks
[   74.136490]   moving 45 blocks
[   74.142541] patching 2 blocks to 2
[   74.152435]   moving 5 blocks
[   74.154174]   moving 3 blocks
[   74.155618] patching 2 blocks to 2
[   74.166220]   moving 10 blocks
[   74.169493] patching 2 blocks to 2
[   74.179556]   moving 9 blocks
[   74.180617]   moving 3 blocks
[   74.181632] patching 2 blocks to 2
[   74.192618]   moving 25 blocks
[   74.195878] patching 2 blocks to 2
[   74.235306]   moving 264 blocks
[   74.247623]   moving 11 blocks
[   74.249053] patching 2 blocks to 2
[   74.260564]   moving 18 blocks
[   74.364318] patching 1056 blocks to 1056
[   74.488454]   moving 96 blocks
[   74.494932]   moving 5 blocks
[   74.495859] patching 2 blocks to 2
[   74.802369] patching 3079 blocks to 3079
[   75.103826]   moving 44 blocks
[   75.108245] patching 2 blocks to 2
[   75.249950]   moving 1346 blocks
[   75.303044]   moving 48 blocks
[   75.309507]   moving 5 blocks
[   75.311480] patching 2 blocks to 2
[   75.332407]   moving 95 blocks
[   75.343412]   moving 18 blocks
[   75.345462]   moving 3 blocks
[   75.346999] patching 2 blocks to 2
[   75.357822]   moving 8 blocks
[   75.363573] patching 19 blocks to 19
[   75.376463]   moving 3 blocks
[   75.378023] patching 2 blocks to 2
[   75.391382]   moving 29 blocks
[   75.398422] patching 28 blocks to 28
[   75.412225]   moving 3 blocks
[   75.413738] patching 2 blocks to 2
[   75.426753]   moving 28 blocks
[   75.432847]   moving 24 blocks
[   75.436390]   moving 3 blocks
[   75.438005] patching 2 blocks to 2
[   75.450084]   moving 23 blocks
[   75.505072]   moving 489 blocks
[   75.526650]   moving 12 blocks
[   75.528229] patching 2 blocks to 2
[   75.540702]   moving 28 blocks
[   75.545166]   moving 4 blocks
[   75.546485] patching 2 blocks to 2
[   75.565059] patching 93 blocks to 93
[   75.584860]   moving 5 blocks
[   75.586538] patching 2 blocks to 2
[   75.601051]   moving 45 blocks
[   75.737134] patching 1346 blocks to 1346
[   75.852294]   moving 28 blocks
[   75.856807] patching 2 blocks to 2
[   76.030011] patching 1611 blocks to 1611
[   76.179613]   moving 29 blocks
[   76.182822] patching 2 blocks to 2
[   76.192864]   moving 4 blocks
[   76.194501] patching 2 blocks to 2
[   76.204953]   moving 6 blocks
[   76.206620] patching 2 blocks to 2
[   76.227899]   moving 93 blocks
[   76.235641] patching 2 blocks to 2
[   76.246408] patching 12 blocks to 12
[   76.256392]   moving 3 blocks
[   76.257536] patching 2 blocks to 2
[   76.268363]   moving 21 blocks
[   76.272783]   moving 16 blocks
[   76.274184] patching 2 blocks to 2
[   76.289742]   moving 45 blocks
[   76.294355] patching 2 blocks to 2
[   76.306453] patching 21 blocks to 21
[   76.319991]   moving 3 blocks
[   76.321708]   moving 3 blocks
[   76.323242] patching 2 blocks to 2
[   76.333243]   moving 5 blocks
[   76.337779]   moving 25 blocks
[   76.342456]   moving 4 blocks
[   76.343788] patching 2 blocks to 2
[   76.358427]   moving 43 blocks
[   76.362894]   moving 4 blocks
[   76.364658] patching 2 blocks to 2
[   76.380653]   moving 53 blocks
[   76.386159]   moving 3 blocks
[   76.387932]   moving 3 blocks
[   76.388716] patching 2 blocks to 2
[   76.397827]   moving 3 blocks
[   76.400052]   moving 17 blocks
[   76.402510] patching 2 blocks to 2
[   76.412647]   moving 16 blocks
[   76.416239] patching 2 blocks to 2
[   76.427956]   moving 20 blocks
[   76.431227]   moving 4 blocks
[   76.432614]   moving 5 blocks
[   76.730362] stashing 3195 overlapping blocks to 8adcf14cfbc23df49009724f9b24c4c11a55f844
[   76.730396] 174485504 bytes free on /cache (13086720 needed)
[   76.730999]  writing 3195 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8adcf14cfbc23df49009724f9b24c4c11a55f844
[   76.919146] patching 3195 blocks to 3195
[   77.252318] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8adcf14cfbc23df49009724f9b24c4c11a55f844
[   77.423291]   moving 1 blocks
[   77.424578] patching 2 blocks to 2
[   77.434593]   moving 3 blocks
[   77.436288]   moving 4 blocks
[   77.485325] stashing 512 overlapping blocks to dfdee63ac13a6d80d5428561358b0c0ad922da7d
[   77.485363] 174485504 bytes free on /cache (2097152 needed)
[   77.485415]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/dfdee63ac13a6d80d5428561358b0c0ad922da7d
[   77.516894] patching 512 blocks to 512
[   78.158959] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/dfdee63ac13a6d80d5428561358b0c0ad922da7d
[   78.312859] stashing 512 overlapping blocks to 0c141874d76d3a36551a535e459cca18939a01b2
[   78.312891] 174485504 bytes free on /cache (2097152 needed)
[   78.312953]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0c141874d76d3a36551a535e459cca18939a01b2
[   78.345125] patching 512 blocks to 512
[   78.988843] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0c141874d76d3a36551a535e459cca18939a01b2
[   79.045679]   moving 4 blocks
[   79.048598]   moving 4 blocks
[   79.066622]   moving 36 blocks
[   79.080103]   moving 23 blocks
[   79.084466]   moving 1 blocks
[   79.086101]   moving 4 blocks
[   79.092416]   moving 17 blocks
[   79.097588]   moving 1 blocks
[   79.098845]   moving 5 blocks
[   79.100203]   moving 1 blocks
[   79.104810]   moving 31 blocks
[   79.109448]   moving 1 blocks
[   79.151612]   moving 357 blocks
[   79.169283]   moving 11 blocks
[   79.170919]   moving 1 blocks
[   79.174379]   moving 21 blocks
[   79.717369] stashing 5714 overlapping blocks to 1ab230cc641f45803beb65c261dfdbf838cc9535
[   79.717403] 174485504 bytes free on /cache (23404544 needed)
[   79.717486]  writing 5714 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1ab230cc641f45803beb65c261dfdbf838cc9535
[   80.036437] patching 5714 blocks to 5714
[   80.315569] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1ab230cc641f45803beb65c261dfdbf838cc9535
[   80.556567]   moving 1 blocks
[   80.557170]   moving 1 blocks
[   80.558000]   moving 1 blocks
[   80.558943]   moving 1 blocks
[   80.586078]   moving 226 blocks
[   80.597944] stashing 2 overlapping blocks to 1d7efd804f9928849b45cab90d6c74fe700330d2
[   80.597972] 174485504 bytes free on /cache (8192 needed)
[   80.598027]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1d7efd804f9928849b45cab90d6c74fe700330d2
[   80.601752] patching 2 blocks to 2
[   80.609922] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1d7efd804f9928849b45cab90d6c74fe700330d2
[   80.611002]   moving 1 blocks
[   80.611921] stashing 2 overlapping blocks to 637775f727020a16bb531213a43550efffd43f61
[   80.611932] 174485504 bytes free on /cache (8192 needed)
[   80.611971]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/637775f727020a16bb531213a43550efffd43f61
[   80.615437] patching 2 blocks to 2
[   80.623657] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/637775f727020a16bb531213a43550efffd43f61
[   80.625129] stashing 2 overlapping blocks to bc180f960b10543c3146323f01912fa27b796306
[   80.625144] 174485504 bytes free on /cache (8192 needed)
[   80.625181]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bc180f960b10543c3146323f01912fa27b796306
[   80.628685] patching 2 blocks to 2
[   80.637005] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bc180f960b10543c3146323f01912fa27b796306
[   80.638407]   moving 1 blocks
[   80.649654] stashing 105 overlapping blocks to 8519f26e22f5ff95687958dadd58fefc35b017c1
[   80.649677] 174485504 bytes free on /cache (430080 needed)
[   80.649723]  writing 105 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8519f26e22f5ff95687958dadd58fefc35b017c1
[   80.661078] patching 105 blocks to 105
[   80.673985] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8519f26e22f5ff95687958dadd58fefc35b017c1
[   80.759996] stashing 837 overlapping blocks to 34e19153ebfbe54119e84e0624dd44cae8635bac
[   80.760030] 174485504 bytes free on /cache (3428352 needed)
[   80.760093]  writing 837 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/34e19153ebfbe54119e84e0624dd44cae8635bac
[   80.799107] patching 837 blocks to 837
[   80.846182] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/34e19153ebfbe54119e84e0624dd44cae8635bac
[   80.950314] stashing 752 overlapping blocks to abe709155c7d9243cc74439fc9db11271e696241
[   80.950353] 174485504 bytes free on /cache (3080192 needed)
[   80.950401]  writing 752 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/abe709155c7d9243cc74439fc9db11271e696241
[   81.001898] patching 752 blocks to 752
[   81.045551] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/abe709155c7d9243cc74439fc9db11271e696241
[   81.079631]   moving 49 blocks
[   81.084204]   moving 1 blocks
[   81.085152] stashing 2 overlapping blocks to 9cf317981758cf4c1fdf3033a1b947cb571d117d
[   81.085191] 174485504 bytes free on /cache (8192 needed)
[   81.085245]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9cf317981758cf4c1fdf3033a1b947cb571d117d
[   81.090167] patching 2 blocks to 2
[   81.098539] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9cf317981758cf4c1fdf3033a1b947cb571d117d
[   81.099831] stashing 2 overlapping blocks to 6d3507b63e112549651bad0430518f66cda18a3c
[   81.099865] 174485504 bytes free on /cache (8192 needed)
[   81.099913]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6d3507b63e112549651bad0430518f66cda18a3c
[   81.103032] patching 2 blocks to 2
[   81.111352] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6d3507b63e112549651bad0430518f66cda18a3c
[   81.160283] stashing 512 overlapping blocks to 0e98cd94f4542ef269d68a8e295834e5048bc5f6
[   81.160314] 174485504 bytes free on /cache (2097152 needed)
[   81.160374]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0e98cd94f4542ef269d68a8e295834e5048bc5f6
[   81.186500] patching 512 blocks to 512
[   81.827308] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0e98cd94f4542ef269d68a8e295834e5048bc5f6
[   81.972192] stashing 512 overlapping blocks to a4158da11107f79e0a13abc55bc0bd2d490eff2a
[   81.972225] 174485504 bytes free on /cache (2097152 needed)
[   81.972283]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a4158da11107f79e0a13abc55bc0bd2d490eff2a
[   81.997045] patching 512 blocks to 512
[   82.222785] I:current maximum temperature: 36011
[   82.639527] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a4158da11107f79e0a13abc55bc0bd2d490eff2a
[   82.696162] stashing 2 overlapping blocks to 40775975468119bc62c579cab0efecbd09e87efa
[   82.696182] 174485504 bytes free on /cache (8192 needed)
[   82.696229]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/40775975468119bc62c579cab0efecbd09e87efa
[   82.700901] patching 2 blocks to 2
[   82.709206] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/40775975468119bc62c579cab0efecbd09e87efa
[   82.846387] stashing 1081 overlapping blocks to e6bc674420d765ccc1910bb9e0c52c2401e22417
[   82.846419] 174485504 bytes free on /cache (4427776 needed)
[   82.846481]  writing 1081 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e6bc674420d765ccc1910bb9e0c52c2401e22417
[   82.895378] patching 1081 blocks to 1081
[   82.954566] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e6bc674420d765ccc1910bb9e0c52c2401e22417
[   82.991658]   moving 1 blocks
[   82.994658]   moving 15 blocks
[   82.997575]   moving 1 blocks
[   82.998920]   moving 5 blocks
[   83.000480]   moving 1 blocks
[   83.076791] stashing 800 overlapping blocks to f238e1adfc71f176e41b4e36db49acdee4f0666f
[   83.076824] 174485504 bytes free on /cache (3276800 needed)
[   83.076882]  writing 800 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f238e1adfc71f176e41b4e36db49acdee4f0666f
[   83.112908] patching 800 blocks to 800
[   83.158244] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f238e1adfc71f176e41b4e36db49acdee4f0666f
[   83.214605] stashing 291 overlapping blocks to d4a429276a279ae6fedf993f967c079701d0c31c
[   83.214637] 174485504 bytes free on /cache (1191936 needed)
[   83.214696]  writing 291 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d4a429276a279ae6fedf993f967c079701d0c31c
[   83.231853] patching 291 blocks to 291
[   83.253431] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d4a429276a279ae6fedf993f967c079701d0c31c
[   83.272376]   moving 41 blocks
[   83.276956]   moving 3 blocks
[   83.278398]   moving 1 blocks
[   83.283944] stashing 43 overlapping blocks to 224a76dd354adaef690a3800d54bca160e1100e3
[   83.283983] 174485504 bytes free on /cache (176128 needed)
[   83.284038]  writing 43 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/224a76dd354adaef690a3800d54bca160e1100e3
[   83.290334] patching 43 blocks to 43
[   83.300571] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/224a76dd354adaef690a3800d54bca160e1100e3
[   83.415824] patching 1159 blocks to 1160
[   84.298037] stashing 10 overlapping blocks to c8d57a0f26b9cf779a3a5a698ab4c24fbd6c4ecd
[   84.298073] 174485504 bytes free on /cache (40960 needed)
[   84.298119]  writing 10 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c8d57a0f26b9cf779a3a5a698ab4c24fbd6c4ecd
[   84.302273] patching 10 blocks to 10
[   84.310977] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c8d57a0f26b9cf779a3a5a698ab4c24fbd6c4ecd
[   84.318762] stashing 60 overlapping blocks to 580d6953b617db86d0cc219b0d6ae434b909675c
[   84.318797] 174485504 bytes free on /cache (245760 needed)
[   84.318847]  writing 60 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/580d6953b617db86d0cc219b0d6ae434b909675c
[   84.325259] patching 60 blocks to 60
[   84.336230] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/580d6953b617db86d0cc219b0d6ae434b909675c
[   84.340793] stashing 2 overlapping blocks to 659b1c632c9fca6213c173faa21e6c25d13c959a
[   84.340838] 174485504 bytes free on /cache (8192 needed)
[   84.340879]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/659b1c632c9fca6213c173faa21e6c25d13c959a
[   84.346084] patching 2 blocks to 2
[   84.354442] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/659b1c632c9fca6213c173faa21e6c25d13c959a
[   84.355694]   moving 1 blocks
[   84.356806]   moving 1 blocks
[   84.357948]   moving 1 blocks
[   84.359676] stashing 10 overlapping blocks to f56fd9f1824703ebcbdcdd27e80255ab5110b7a9
[   84.359705] 174485504 bytes free on /cache (40960 needed)
[   84.359746]  writing 10 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f56fd9f1824703ebcbdcdd27e80255ab5110b7a9
[   84.365232] patching 10 blocks to 10
[   84.373957] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f56fd9f1824703ebcbdcdd27e80255ab5110b7a9
[   84.378148]   moving 22 blocks
[   84.440855] stashing 632 overlapping blocks to 687e83983dc69013246a64217d2eb193fe5e8d4d
[   84.440888] 174485504 bytes free on /cache (2588672 needed)
[   84.440947]  writing 632 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/687e83983dc69013246a64217d2eb193fe5e8d4d
[   84.471533] patching 632 blocks to 632
[   84.509068] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/687e83983dc69013246a64217d2eb193fe5e8d4d
[   84.531557] stashing 2 overlapping blocks to b418950a49701620b4e0784c81311fdfa3ed77d7
[   84.531579] 174485504 bytes free on /cache (8192 needed)
[   84.531645]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b418950a49701620b4e0784c81311fdfa3ed77d7
[   84.536468] patching 2 blocks to 2
[   84.544842] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b418950a49701620b4e0784c81311fdfa3ed77d7
[   84.546310]   moving 1 blocks
[   84.547244]   moving 1 blocks
[   84.548796]   moving 7 blocks
[   84.550419]   moving 1 blocks
[   84.551410] stashing 2 overlapping blocks to 0c6ff91d7923ca8ccc473c228220369ce8cb99db
[   84.551441] 174485504 bytes free on /cache (8192 needed)
[   84.551482]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0c6ff91d7923ca8ccc473c228220369ce8cb99db
[   84.554657] patching 2 blocks to 2
[   84.562948] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0c6ff91d7923ca8ccc473c228220369ce8cb99db
[   84.564626]   moving 4 blocks
[   84.586784]   moving 214 blocks
[   84.607739]   moving 76 blocks
[   84.613859]   moving 1 blocks
[   84.614880] stashing 2 overlapping blocks to fa60de482adb1bdcaf3d9922f7a06ae11e16e5f4
[   84.614920] 174485504 bytes free on /cache (8192 needed)
[   84.614975]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fa60de482adb1bdcaf3d9922f7a06ae11e16e5f4
[   84.618515] patching 2 blocks to 2
[   84.626843] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fa60de482adb1bdcaf3d9922f7a06ae11e16e5f4
[   84.628403]   moving 4 blocks
[   84.630020] stashing 2 overlapping blocks to ad81611547def4469df9fb461389614be4718c7f
[   84.630051] 174485504 bytes free on /cache (8192 needed)
[   84.630092]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad81611547def4469df9fb461389614be4718c7f
[   84.633469] patching 2 blocks to 2
[   84.641818] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad81611547def4469df9fb461389614be4718c7f
[   84.643163]   moving 2 blocks
[   84.645572]   moving 9 blocks
[   84.651897]   moving 33 blocks
[   84.666180]   moving 71 blocks
[   84.672777]   moving 1 blocks
[   84.673459]   moving 1 blocks
[   84.708715] patching 306 blocks to 307
[   85.127569] stashing 5 overlapping blocks to 33b987f982c6ad700c46ad1fbac63c03a97ddb8c
[   85.127596] 174485504 bytes free on /cache (20480 needed)
[   85.127653]  writing 5 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/33b987f982c6ad700c46ad1fbac63c03a97ddb8c
[   85.132658] patching 5 blocks to 5
[   85.141160] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/33b987f982c6ad700c46ad1fbac63c03a97ddb8c
[   85.143469] stashing 2 overlapping blocks to e30838ce52e052444cd0639791d8aa43722931e4
[   85.143487] 174485504 bytes free on /cache (8192 needed)
[   85.143533]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e30838ce52e052444cd0639791d8aa43722931e4
[   85.148315] patching 2 blocks to 2
[   85.156525] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e30838ce52e052444cd0639791d8aa43722931e4
[   85.158865] patching 2 blocks to 2
[   85.169970]   moving 4 blocks
[   85.171961]   moving 1 blocks
[   85.174580]   moving 1 blocks
[   85.264732] stashing 759 overlapping blocks to adf449ef60cfe9ef7fc549f973432b469c95f2a0
[   85.264765] 174485504 bytes free on /cache (3108864 needed)
[   85.264824]  writing 759 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/adf449ef60cfe9ef7fc549f973432b469c95f2a0
[   85.301526] patching 759 blocks to 759
[   85.345360] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/adf449ef60cfe9ef7fc549f973432b469c95f2a0
[   85.372564] stashing 4 overlapping blocks to 07d18e2b489f95e42613dbe80afa81e23ad07638
[   85.372583] 174485504 bytes free on /cache (16384 needed)
[   85.372634]  writing 4 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/07d18e2b489f95e42613dbe80afa81e23ad07638
[   85.375809] patching 4 blocks to 4
[   85.384306] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/07d18e2b489f95e42613dbe80afa81e23ad07638
[   85.386472]   moving 4 blocks
[   85.388494]   moving 5 blocks
[   85.403175] stashing 133 overlapping blocks to ef884e4698b541b5ce16224e0049cda3d64b12bf
[   85.403212] 174485504 bytes free on /cache (544768 needed)
[   85.403266]  writing 133 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ef884e4698b541b5ce16224e0049cda3d64b12bf
[   85.415955] patching 133 blocks to 133
[   85.434172] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ef884e4698b541b5ce16224e0049cda3d64b12bf
[   85.442686]   moving 1 blocks
[   85.485137]   moving 353 blocks
[   85.501406]   moving 1 blocks
[   85.502540]   moving 1 blocks
[   85.503215]   moving 1 blocks
[   85.504377]   moving 1 blocks
[   85.505043]   moving 1 blocks
[   85.505665]   moving 1 blocks
[   85.881268] stashing 3978 overlapping blocks to 8798c96dbc776bb75ff1e9a6980ffec215b1008a
[   85.881299] 174485504 bytes free on /cache (16293888 needed)
[   85.881360]  writing 3978 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8798c96dbc776bb75ff1e9a6980ffec215b1008a
[   86.118126] patching 3978 blocks to 3978
[   86.318239] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8798c96dbc776bb75ff1e9a6980ffec215b1008a
[   86.511347]   moving 1 blocks
[   86.512482]   moving 1 blocks
[   86.513499] stashing 2 overlapping blocks to 180656410997ff2041ba2e8e61a9c6fa4129289d
[   86.513542] 174485504 bytes free on /cache (8192 needed)
[   86.513596]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/180656410997ff2041ba2e8e61a9c6fa4129289d
[   86.518447] patching 2 blocks to 2
[   86.526752] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/180656410997ff2041ba2e8e61a9c6fa4129289d
[   86.529092] stashing 13 overlapping blocks to 4985e3bd4eceaadd42257bae03c73aa068b7a3da
[   86.529110] 174485504 bytes free on /cache (53248 needed)
[   86.529140]  writing 13 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4985e3bd4eceaadd42257bae03c73aa068b7a3da
[   86.533054] patching 13 blocks to 13
[   86.541935] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4985e3bd4eceaadd42257bae03c73aa068b7a3da
[   86.543670]   moving 6 blocks
[   86.545050]   moving 4 blocks
[   86.546367] stashing 2 overlapping blocks to 69d6438ba4d68386a97be2a304af7522621ee945
[   86.546397] 174485504 bytes free on /cache (8192 needed)
[   86.546438]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69d6438ba4d68386a97be2a304af7522621ee945
[   86.549912] patching 2 blocks to 2
[   86.558219] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69d6438ba4d68386a97be2a304af7522621ee945
[   86.559522] stashing 2 overlapping blocks to fc76f28639938c0e28fbbc02847c65549773006a
[   86.559557] 174485504 bytes free on /cache (8192 needed)
[   86.559597]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fc76f28639938c0e28fbbc02847c65549773006a
[   86.563033] patching 2 blocks to 2
[   86.571378] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fc76f28639938c0e28fbbc02847c65549773006a
[   86.572921]   moving 1 blocks
[   86.573939] stashing 2 overlapping blocks to 9a044f0feb6979b17aa9fb62d8395142bf8ae44a
[   86.573970] 174485504 bytes free on /cache (8192 needed)
[   86.574011]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9a044f0feb6979b17aa9fb62d8395142bf8ae44a
[   86.577674] patching 2 blocks to 2
[   86.585940] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9a044f0feb6979b17aa9fb62d8395142bf8ae44a
[   86.587682]   moving 1 blocks
[   86.588797]   moving 1 blocks
[   86.591539]   moving 15 blocks
[   86.595412]   moving 1 blocks
[   86.596068]   moving 1 blocks
[   86.615356]   moving 159 blocks
[   86.626499]   moving 5 blocks
[   86.628602]   moving 2 blocks
[   87.006903] stashing 4007 overlapping blocks to a1b090c5a2183d62b8535c9732eab95af0d37206
[   87.006954] 174485504 bytes free on /cache (16412672 needed)
[   87.006996]  writing 4007 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a1b090c5a2183d62b8535c9732eab95af0d37206
[   87.229391] patching 4007 blocks to 4007
[   87.428271] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a1b090c5a2183d62b8535c9732eab95af0d37206
[   87.641669]   moving 4 blocks
[   87.643346]   moving 1 blocks
[   87.644468]   moving 4 blocks
[   87.645644]   moving 4 blocks
[   87.646761]   moving 1 blocks
[   87.649347]   moving 25 blocks
[   87.652456] stashing 2 overlapping blocks to 9f0ba8858fe5f8579601c919a8420195948cf8da
[   87.652480] 174485504 bytes free on /cache (8192 needed)
[   87.652539]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9f0ba8858fe5f8579601c919a8420195948cf8da
[   87.656090] patching 2 blocks to 2
[   87.664372] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9f0ba8858fe5f8579601c919a8420195948cf8da
[   87.665806]   moving 1 blocks
[   88.095311] stashing 4606 overlapping blocks to 07b4756df819f745404302c8af632efca05993ba
[   88.095343] 174485504 bytes free on /cache (18866176 needed)
[   88.095404]  writing 4606 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/07b4756df819f745404302c8af632efca05993ba
[   88.365552] patching 4606 blocks to 4606
[   88.601352] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/07b4756df819f745404302c8af632efca05993ba
[   88.809347]   moving 4 blocks
[   88.823160]   moving 107 blocks
[   88.830869]   moving 1 blocks
[   88.831911] stashing 2 overlapping blocks to 48be56b668ada5ff8a1fb262054799f14bdff1fe
[   88.831951] 174485504 bytes free on /cache (8192 needed)
[   88.832004]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/48be56b668ada5ff8a1fb262054799f14bdff1fe
[   88.835282] patching 2 blocks to 2
[   88.843740] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/48be56b668ada5ff8a1fb262054799f14bdff1fe
[   88.846692]   moving 22 blocks
[   88.850304]   moving 1 blocks
[   88.851789] stashing 7 overlapping blocks to 2b51eae8b823b1bff0e208cc6e7104c4527a7763
[   88.851829] 174485504 bytes free on /cache (28672 needed)
[   88.851882]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2b51eae8b823b1bff0e208cc6e7104c4527a7763
[   88.855628] patching 7 blocks to 7
[   88.864191] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2b51eae8b823b1bff0e208cc6e7104c4527a7763
[   88.866150] stashing 2 overlapping blocks to be800c3f967c7ce660873fc1bf7cfb281edc3a46
[   88.866167] 174485504 bytes free on /cache (8192 needed)
[   88.866214]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/be800c3f967c7ce660873fc1bf7cfb281edc3a46
[   88.869525] patching 2 blocks to 2
[   88.877883] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/be800c3f967c7ce660873fc1bf7cfb281edc3a46
[   88.927467] stashing 512 overlapping blocks to e15935ee12b648bb0e668dae3bd982a09529d5ac
[   88.927517] 174485504 bytes free on /cache (2097152 needed)
[   88.927574]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e15935ee12b648bb0e668dae3bd982a09529d5ac
[   88.953718] patching 512 blocks to 512
[   89.008068] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e15935ee12b648bb0e668dae3bd982a09529d5ac
[   89.026082]   moving 1 blocks
[   89.075508] stashing 512 overlapping blocks to c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   89.075543] 174485504 bytes free on /cache (2097152 needed)
[   89.075597]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   89.100649] patching 512 blocks to 512
[   89.144221] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   89.211350] stashing 512 overlapping blocks to ebab7eb9d317871405f73a569ef34eefbec6544c
[   89.211382] 174485504 bytes free on /cache (2097152 needed)
[   89.211442]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ebab7eb9d317871405f73a569ef34eefbec6544c
[   89.237574] patching 512 blocks to 512
[   89.270340] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ebab7eb9d317871405f73a569ef34eefbec6544c
[   89.290530]   moving 4 blocks
[   89.339865] stashing 512 overlapping blocks to 33b243d6eaaca5a90b97c2eed92e6eca30919152
[   89.339899] 174485504 bytes free on /cache (2097152 needed)
[   89.339957]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/33b243d6eaaca5a90b97c2eed92e6eca30919152
[   89.365956] patching 512 blocks to 512
[   89.398132] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/33b243d6eaaca5a90b97c2eed92e6eca30919152
[   89.466536] stashing 512 overlapping blocks to 2dfb4db0b4d37e9d849f2b341273f8ffcda290ad
[   89.466570] 174485504 bytes free on /cache (2097152 needed)
[   89.466623]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2dfb4db0b4d37e9d849f2b341273f8ffcda290ad
[   89.492334] patching 512 blocks to 512
[   90.120668] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2dfb4db0b4d37e9d849f2b341273f8ffcda290ad
[   90.246701] stashing 512 overlapping blocks to 05a8f6c554ece4aa66314e2e4c702e00f00b0bc4
[   90.246739] 174485504 bytes free on /cache (2097152 needed)
[   90.246786]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/05a8f6c554ece4aa66314e2e4c702e00f00b0bc4
[   90.273439] patching 512 blocks to 512
[   90.807883] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/05a8f6c554ece4aa66314e2e4c702e00f00b0bc4
[   90.863996]  writing 1 blocks of new data
[   90.865749]   moving 1 blocks
[   91.012068] stashing 1236 overlapping blocks to ff9a3716d9e1b9013615b5a6c69b5c793a0c16fc
[   91.012107] 174485504 bytes free on /cache (5062656 needed)
[   91.012153]  writing 1236 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ff9a3716d9e1b9013615b5a6c69b5c793a0c16fc
[   91.067185] patching 1236 blocks to 1236
[   91.133097] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ff9a3716d9e1b9013615b5a6c69b5c793a0c16fc
[   91.189361]   moving 6 blocks
[   91.190548]   moving 1 blocks
[   91.191926]   moving 4 blocks
[   91.193130]   moving 1 blocks
[   91.194590]   moving 4 blocks
[   91.196207]   moving 4 blocks
[   91.199829]   moving 36 blocks
[   91.206699]   moving 1 blocks
[   91.217788] patching 77 blocks to 77
[   91.232355]   moving 1 blocks
[   91.840624] stashing 5444 overlapping blocks to 39d00ff5d5356caee243dcb2f4984861c9431b3f
[   91.840662] 174485504 bytes free on /cache (22298624 needed)
[   91.840711]  writing 5444 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/39d00ff5d5356caee243dcb2f4984861c9431b3f
[   92.154881] patching 5444 blocks to 5444
[   92.432172] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/39d00ff5d5356caee243dcb2f4984861c9431b3f
[   92.690281]   moving 25 blocks
[   92.694714]   moving 10 blocks
[   92.744641] stashing 512 overlapping blocks to 94a5158f6dc0023f55c8084c553633abcc0ea765
[   92.744673] 174485504 bytes free on /cache (2097152 needed)
[   92.744733]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/94a5158f6dc0023f55c8084c553633abcc0ea765
[   92.771027] patching 512 blocks to 512
[   92.806695] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/94a5158f6dc0023f55c8084c553633abcc0ea765
[   92.827725]   moving 1 blocks
[   92.877105] stashing 512 overlapping blocks to 85344bffe5b6eb52898fd895bb06d603d49865d4
[   92.877140] 174485504 bytes free on /cache (2097152 needed)
[   92.877197]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/85344bffe5b6eb52898fd895bb06d603d49865d4
[   92.902350] patching 512 blocks to 512
[   93.072165] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/85344bffe5b6eb52898fd895bb06d603d49865d4
[   93.136018]   moving 83 blocks
[   93.146421] stashing 2 overlapping blocks to 5ccfa01c3370baabdeb08203da5315edddef393d
[   93.146443] 174485504 bytes free on /cache (8192 needed)
[   93.146488]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5ccfa01c3370baabdeb08203da5315edddef393d
[   93.153147] patching 2 blocks to 2
[   93.161468] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5ccfa01c3370baabdeb08203da5315edddef393d
[   93.163979]   moving 3 blocks
[   93.169282] stashing 2 overlapping blocks to 27657a63cce3d6f11bfdc2efbfe92f403b561af4
[   93.169313] 174485504 bytes free on /cache (8192 needed)
[   93.169358]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/27657a63cce3d6f11bfdc2efbfe92f403b561af4
[   93.172588] patching 2 blocks to 2
[   93.180764] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/27657a63cce3d6f11bfdc2efbfe92f403b561af4
[   93.182024] stashing 2 overlapping blocks to 85d3fe9512efb47027024d0e9047b51a782e462b
[   93.182040] 174485504 bytes free on /cache (8192 needed)
[   93.182088]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/85d3fe9512efb47027024d0e9047b51a782e462b
[   93.185433] patching 2 blocks to 2
[   93.193799] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/85d3fe9512efb47027024d0e9047b51a782e462b
[   93.194805] patching 2 blocks to 2
[   93.204260]   moving 5 blocks
[   93.205105]   moving 1 blocks
[   93.205807] patching 2 blocks to 2
[   93.214961]   moving 3 blocks
[   93.215934]   moving 1 blocks
[   93.217193] patching 2 blocks to 2
[   93.305901]   moving 696 blocks
[   93.650175] stashing 3388 overlapping blocks to 1f22484df377c3472390e9ba15a9df6d2b95d10c
[   93.650214] 174485504 bytes free on /cache (13877248 needed)
[   93.650264]  writing 3388 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1f22484df377c3472390e9ba15a9df6d2b95d10c
[   93.870138] patching 3388 blocks to 3388
[   94.037979] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1f22484df377c3472390e9ba15a9df6d2b95d10c
[   94.180462]   moving 1 blocks
[   94.182529] stashing 12 overlapping blocks to db34962ea8c6ee4f2b8c6d33fb8b6afaefaf4a4d
[   94.182553] 174485504 bytes free on /cache (49152 needed)
[   94.182612]  writing 12 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db34962ea8c6ee4f2b8c6d33fb8b6afaefaf4a4d
[   94.187807] patching 12 blocks to 12
[   94.197613] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db34962ea8c6ee4f2b8c6d33fb8b6afaefaf4a4d
[   94.208712]   moving 65 blocks
[   94.213274] stashing 2 overlapping blocks to 19be7292c3534e2b275af33c4b843ae4be886ba5
[   94.213313] 174485504 bytes free on /cache (8192 needed)
[   94.213369]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19be7292c3534e2b275af33c4b843ae4be886ba5
[   94.217008] patching 2 blocks to 2
[   94.225325] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19be7292c3534e2b275af33c4b843ae4be886ba5
[   94.226444] stashing 3 overlapping blocks to e2845ef3725247583a2dbdf65c281949203342ba
[   94.226476] 174485504 bytes free on /cache (12288 needed)
[   94.226522]  writing 3 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e2845ef3725247583a2dbdf65c281949203342ba
[   94.230158] patching 3 blocks to 3
[   94.238491] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e2845ef3725247583a2dbdf65c281949203342ba
[   94.240191]   moving 1 blocks
[   94.244895]   moving 32 blocks
[   94.249034]   moving 4 blocks
[   94.250703] stashing 2 overlapping blocks to f74ec8e3d734cf9994fc020e787c09ecf69722c4
[   94.250732] 174485504 bytes free on /cache (8192 needed)
[   94.250775]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f74ec8e3d734cf9994fc020e787c09ecf69722c4
[   94.254229] patching 2 blocks to 2
[   94.262539] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f74ec8e3d734cf9994fc020e787c09ecf69722c4
[   94.265487]   moving 10 blocks
[   94.267960]   moving 1 blocks
[   94.280353]   moving 101 blocks
[   94.287983] stashing 2 overlapping blocks to b54258efb243cf52e1e3dbba53ff127347e76188
[   94.288011] 174485504 bytes free on /cache (8192 needed)
[   94.288063]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b54258efb243cf52e1e3dbba53ff127347e76188
[   94.291583] patching 2 blocks to 2
[   94.299778] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b54258efb243cf52e1e3dbba53ff127347e76188
[   94.304613]   moving 28 blocks
[   94.307949] stashing 2 overlapping blocks to 24bc6d02e6ca63ac081685c1944544742f812ba9
[   94.307977] 174485504 bytes free on /cache (8192 needed)
[   94.308032]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/24bc6d02e6ca63ac081685c1944544742f812ba9
[   94.311538] patching 2 blocks to 2
[   94.319712] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/24bc6d02e6ca63ac081685c1944544742f812ba9
[   94.324201]   moving 27 blocks
[   94.327517]   moving 1 blocks
[   94.876591] stashing 5838 overlapping blocks to e358f7a39ff7489e471be59549b741ae727a76d0
[   94.876625] 174485504 bytes free on /cache (23912448 needed)
[   94.876683]  writing 5838 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e358f7a39ff7489e471be59549b741ae727a76d0
[   95.170419] patching 5838 blocks to 5838
[   95.468228] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e358f7a39ff7489e471be59549b741ae727a76d0
[   95.678803]   moving 9 blocks
[   95.680114] stashing 5 overlapping blocks to ba0b04439e86138f2c42c03a66eacf79df5a5b13
[   95.680152] 174485504 bytes free on /cache (20480 needed)
[   95.680204]  writing 5 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ba0b04439e86138f2c42c03a66eacf79df5a5b13
[   95.685775] patching 5 blocks to 5
[   95.694318] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ba0b04439e86138f2c42c03a66eacf79df5a5b13
[   95.696207] stashing 2 overlapping blocks to 0e8ca3ccc098aa0af5866d2ab57eb2837082420f
[   95.696226] 174485504 bytes free on /cache (8192 needed)
[   95.696277]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0e8ca3ccc098aa0af5866d2ab57eb2837082420f
[   95.700029] patching 2 blocks to 2
[   95.708352] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0e8ca3ccc098aa0af5866d2ab57eb2837082420f
[   95.709900] stashing 2 overlapping blocks to 27683720d1d53df731734c65934e339d1fe6934b
[   95.709927] 174485504 bytes free on /cache (8192 needed)
[   95.709964]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/27683720d1d53df731734c65934e339d1fe6934b
[   95.713397] patching 2 blocks to 2
[   95.721660] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/27683720d1d53df731734c65934e339d1fe6934b
[   95.722874]   moving 1 blocks
[   95.723499]   moving 1 blocks
[   95.724698]   moving 1 blocks
[   96.277794] stashing 5881 overlapping blocks to 486219547f80a811f32bbe55c532ca6fff6778e2
[   96.277827] 174485504 bytes free on /cache (24088576 needed)
[   96.277887]  writing 5881 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/486219547f80a811f32bbe55c532ca6fff6778e2
[   96.597652] patching 5881 blocks to 5881
[   98.007250] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/486219547f80a811f32bbe55c532ca6fff6778e2
[   98.208310]   moving 1 blocks
[   98.239123] stashing 318 overlapping blocks to 4e8493377a40d5c9d9247648396f1d001635289e
[   98.239155] 174485504 bytes free on /cache (1302528 needed)
[   98.239216]  writing 318 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4e8493377a40d5c9d9247648396f1d001635289e
[   98.258895] patching 318 blocks to 318
[   98.659455] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4e8493377a40d5c9d9247648396f1d001635289e
[   98.697343] stashing 2 overlapping blocks to 003c3c9400031416847048438b7f25b14c64cbbc
[   98.697373] 174485504 bytes free on /cache (8192 needed)
[   98.697417]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/003c3c9400031416847048438b7f25b14c64cbbc
[   98.701648] patching 2 blocks to 2
[   98.709806] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/003c3c9400031416847048438b7f25b14c64cbbc
[   98.799525] stashing 512 overlapping blocks to 0cfa23df2574ef997c791a4c10613bf9aa9e309f
[   98.799558] 174485504 bytes free on /cache (2097152 needed)
[   98.799613]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0cfa23df2574ef997c791a4c10613bf9aa9e309f
[   98.825890] patching 512 blocks to 512
[   99.468438] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0cfa23df2574ef997c791a4c10613bf9aa9e309f
[   99.525246]   moving 1 blocks
[   99.526883] patching 2 blocks to 2
[   99.537954]   moving 4 blocks
[   99.566807]   moving 113 blocks
[   99.589306]   moving 30 blocks
[   99.644597] stashing 512 overlapping blocks to f6ae866352ea9556d722978bed9d21872a5c8988
[   99.644630] 174485504 bytes free on /cache (2097152 needed)
[   99.644688]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f6ae866352ea9556d722978bed9d21872a5c8988
[   99.670820] patching 512 blocks to 512
[  100.301905] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f6ae866352ea9556d722978bed9d21872a5c8988
[  100.371035]   moving 1 blocks
[  100.545589] stashing 1836 overlapping blocks to 74dcfa6d058addbf87c48379e638f2ac6153e748
[  100.545624] 174485504 bytes free on /cache (7520256 needed)
[  100.545682]  writing 1836 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/74dcfa6d058addbf87c48379e638f2ac6153e748
[  100.627858] patching 1836 blocks to 1836
[  100.824237] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/74dcfa6d058addbf87c48379e638f2ac6153e748
[  100.945854]   moving 13 blocks
[  100.947425] stashing 2 overlapping blocks to 484bc9597a85549076329d1524aff8db10455cd3
[  100.947494] 174485504 bytes free on /cache (8192 needed)
[  100.947546]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/484bc9597a85549076329d1524aff8db10455cd3
[  100.952245] patching 2 blocks to 2
[  100.960541] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/484bc9597a85549076329d1524aff8db10455cd3
[  101.163096] stashing 2123 overlapping blocks to b38f925212027a726738ec6f3c12da8b68a7226c
[  101.163128] 174485504 bytes free on /cache (8695808 needed)
[  101.163185]  writing 2123 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b38f925212027a726738ec6f3c12da8b68a7226c
[  101.251440] patching 2123 blocks to 2123
[  101.360014] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b38f925212027a726738ec6f3c12da8b68a7226c
[  101.426209] patching 2 blocks to 2
[  101.470131]   moving 303 blocks
[  101.489615]   moving 45 blocks
[  101.495128]   moving 1 blocks
[  101.699408] stashing 2166 overlapping blocks to ab1d9b7a2bbb3249e101659cbb63ee87818c0ca0
[  101.699441] 174485504 bytes free on /cache (8871936 needed)
[  101.699503]  writing 2166 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ab1d9b7a2bbb3249e101659cbb63ee87818c0ca0
[  101.876765] patching 2166 blocks to 2166
[  101.986858] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ab1d9b7a2bbb3249e101659cbb63ee87818c0ca0
[  102.056998]   moving 1 blocks
[  102.060546]   moving 23 blocks
[  102.125596]   moving 563 blocks
[  102.146124]   moving 1 blocks
[  102.146801]   moving 1 blocks
[  102.228823] stashing 853 overlapping blocks to e72731f68210d81eea5fdd5baa1f35639b97ba6f
[  102.228851] 174485504 bytes free on /cache (3493888 needed)
[  102.228901]  writing 853 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e72731f68210d81eea5fdd5baa1f35639b97ba6f
[  102.232888] I:current maximum temperature: 36403
[  102.269062] patching 853 blocks to 853
[  102.317343] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e72731f68210d81eea5fdd5baa1f35639b97ba6f
[  102.353194]   moving 55 blocks
[  102.365949]   moving 71 blocks
[  102.371037] patching 2 blocks to 2
[  102.380381]   moving 3 blocks
[  102.384973]   moving 41 blocks
[  102.392834] stashing 39 overlapping blocks to 96c00fdd67ec73bce8a285bc6bea6076075b38c9
[  102.392868] 174485504 bytes free on /cache (159744 needed)
[  102.392920]  writing 39 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/96c00fdd67ec73bce8a285bc6bea6076075b38c9
[  102.399550] patching 39 blocks to 39
[  102.409639] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/96c00fdd67ec73bce8a285bc6bea6076075b38c9
[  102.463671] stashing 512 overlapping blocks to fb9c1c017c41dc3801ef472c0b374273a8c4a7f1
[  102.463709] 174485504 bytes free on /cache (2097152 needed)
[  102.463760]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fb9c1c017c41dc3801ef472c0b374273a8c4a7f1
[  102.490249] patching 512 blocks to 512
[  103.133197] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fb9c1c017c41dc3801ef472c0b374273a8c4a7f1
[  103.276472] stashing 512 overlapping blocks to be4d1dd312b78494196424a3a2efb68e66bde6f4
[  103.276509] 174485504 bytes free on /cache (2097152 needed)
[  103.276558]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/be4d1dd312b78494196424a3a2efb68e66bde6f4
[  103.303039] patching 512 blocks to 512
[  103.942714] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/be4d1dd312b78494196424a3a2efb68e66bde6f4
[  104.085514] stashing 512 overlapping blocks to 0a4e1ffa7d6e9209d276db4dbbb75ee05d15a55c
[  104.085544] 174485504 bytes free on /cache (2097152 needed)
[  104.085607]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0a4e1ffa7d6e9209d276db4dbbb75ee05d15a55c
[  104.124355] patching 512 blocks to 512
[  104.768510] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0a4e1ffa7d6e9209d276db4dbbb75ee05d15a55c
[  104.901786] stashing 512 overlapping blocks to cbb9a63cc5ebce042a3f2b3f1ce1388aa455df4a
[  104.901819] 174485504 bytes free on /cache (2097152 needed)
[  104.901876]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cbb9a63cc5ebce042a3f2b3f1ce1388aa455df4a
[  104.927924] patching 512 blocks to 512
[  105.568103] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cbb9a63cc5ebce042a3f2b3f1ce1388aa455df4a
[  105.708039] stashing 512 overlapping blocks to b49e14bae1f991be7da7b162bd98669c6485fd0c
[  105.708072] 174485504 bytes free on /cache (2097152 needed)
[  105.708127]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b49e14bae1f991be7da7b162bd98669c6485fd0c
[  105.735514] patching 512 blocks to 512
[  106.379104] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b49e14bae1f991be7da7b162bd98669c6485fd0c
[  106.521491] stashing 512 overlapping blocks to 5217b490ca31271f75c2785e39d08c3298534bd7
[  106.521521] 174485504 bytes free on /cache (2097152 needed)
[  106.521585]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5217b490ca31271f75c2785e39d08c3298534bd7
[  106.546352] patching 512 blocks to 512
[  107.189856] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5217b490ca31271f75c2785e39d08c3298534bd7
[  107.247190]   moving 5 blocks
[  107.250838]   moving 12 blocks
[  107.253448] stashing 2 overlapping blocks to 5c3bcd57bf0fdffb1a35be7a1675260d6442c56f
[  107.253506] 174485504 bytes free on /cache (8192 needed)
[  107.253569]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5c3bcd57bf0fdffb1a35be7a1675260d6442c56f
[  107.260237] patching 2 blocks to 2
[  107.268628] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5c3bcd57bf0fdffb1a35be7a1675260d6442c56f
[  107.270000]   moving 1 blocks
[  107.270805] patching 2 blocks to 2
[  107.281103]   moving 3 blocks
[  107.282052]   moving 1 blocks
[  107.282769]   moving 1 blocks
[  107.285478]   moving 6 blocks
[  107.299158]   moving 22 blocks
[  107.305476]   moving 21 blocks
[  107.310101]   moving 1 blocks
[  107.567653]   moving 2364 blocks
[  107.647751]   moving 5 blocks
[  107.649901]   moving 4 blocks
[  107.651074]   moving 1 blocks
[  107.652094] stashing 2 overlapping blocks to abff03b6868b5e63c46d5c064b5c3a9a179670d8
[  107.652155] 174485504 bytes free on /cache (8192 needed)
[  107.652228]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/abff03b6868b5e63c46d5c064b5c3a9a179670d8
[  107.656189] patching 2 blocks to 2
[  107.664532] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/abff03b6868b5e63c46d5c064b5c3a9a179670d8
[  107.666665]   moving 4 blocks
[  107.668584]   moving 2 blocks
[  107.671485]   moving 16 blocks
[  107.675128] patching 2 blocks to 2
[  107.685245]   moving 10 blocks
[  107.688511]   moving 1 blocks
[  107.746863] stashing 602 overlapping blocks to 03c5f8f4e75849d58231bbde665712f9ab2bb52b
[  107.746896] 174485504 bytes free on /cache (2465792 needed)
[  107.746954]  writing 602 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/03c5f8f4e75849d58231bbde665712f9ab2bb52b
[  107.778023] patching 602 blocks to 602
[  107.813996] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/03c5f8f4e75849d58231bbde665712f9ab2bb52b
[  107.850094] stashing 158 overlapping blocks to 5cf0f55e4ca040e6bb9f2d774de02af1e09a8a16
[  107.850129] 174485504 bytes free on /cache (647168 needed)
[  107.850184]  writing 158 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5cf0f55e4ca040e6bb9f2d774de02af1e09a8a16
[  107.863110] patching 158 blocks to 158
[  107.878768] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5cf0f55e4ca040e6bb9f2d774de02af1e09a8a16
[  107.888937]   moving 1 blocks
[  107.889944]   moving 2 blocks
[  107.890885]   moving 4 blocks
[  107.896977]   moving 50 blocks
[  107.901807]   moving 1 blocks
[  107.909231]   moving 45 blocks
[  107.915529] stashing 2 overlapping blocks to aaff608655a4891aae84e894db70adfa5d0272aa
[  107.915564] 174485504 bytes free on /cache (8192 needed)
[  107.915620]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aaff608655a4891aae84e894db70adfa5d0272aa
[  107.918835] patching 2 blocks to 2
[  107.927066] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aaff608655a4891aae84e894db70adfa5d0272aa
[  107.928671] stashing 2 overlapping blocks to 5a9c7000c5870c76d649adbafc4453af588ea78f
[  107.928702] 174485504 bytes free on /cache (8192 needed)
[  107.928746]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5a9c7000c5870c76d649adbafc4453af588ea78f
[  107.932341] patching 2 blocks to 2
[  107.940636] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5a9c7000c5870c76d649adbafc4453af588ea78f
[  107.942134]   moving 1 blocks
[  107.943128] stashing 2 overlapping blocks to 21ef664afc8da9bb1f99d15c2985ed4dc21e215b
[  107.943160] 174485504 bytes free on /cache (8192 needed)
[  107.943202]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/21ef664afc8da9bb1f99d15c2985ed4dc21e215b
[  107.946455] patching 2 blocks to 2
[  107.954771] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/21ef664afc8da9bb1f99d15c2985ed4dc21e215b
[  107.956073] stashing 2 overlapping blocks to fe657bb1e5fdd61abc1a4ef115e5fff080276a15
[  107.956107] 174485504 bytes free on /cache (8192 needed)
[  107.956146]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fe657bb1e5fdd61abc1a4ef115e5fff080276a15
[  107.959427] patching 2 blocks to 2
[  107.967720] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fe657bb1e5fdd61abc1a4ef115e5fff080276a15
[  107.969031] stashing 2 overlapping blocks to 8606fd2f8a45727250507584aa6f17bc22a21067
[  107.969066] 174485504 bytes free on /cache (8192 needed)
[  107.969106]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8606fd2f8a45727250507584aa6f17bc22a21067
[  107.972306] patching 2 blocks to 2
[  107.980635] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8606fd2f8a45727250507584aa6f17bc22a21067
[  107.982172]   moving 1 blocks
[  107.983577]   moving 1 blocks
[  107.984604]   moving 1 blocks
[  107.985680]   moving 1 blocks
[  107.986295]   moving 1 blocks
[  107.987460]   moving 1 blocks
[  107.988315]   moving 4 blocks
[  107.989373] stashing 2 overlapping blocks to 1cd8c90d67fa0e170fd35431d1291205f8a88285
[  107.989404] 174485504 bytes free on /cache (8192 needed)
[  107.989447]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1cd8c90d67fa0e170fd35431d1291205f8a88285
[  107.992717] patching 2 blocks to 2
[  108.000928] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1cd8c90d67fa0e170fd35431d1291205f8a88285
[  108.007755]   moving 32 blocks
[  108.011014]   moving 1 blocks
[  108.011660]   moving 1 blocks
[  108.012833]   moving 8 blocks
[  108.017295]   moving 28 blocks
[  108.021154]   moving 6 blocks
[  108.023691]   moving 9 blocks
[  108.024750]   moving 1 blocks
[  108.028886]   moving 27 blocks
[  108.032499]   moving 1 blocks
[  108.033485] stashing 2 overlapping blocks to 86b0cc035593be5e6fa2bcb6cbdf1ff96a6f7fa4
[  108.033524] 174485504 bytes free on /cache (8192 needed)
[  108.033581]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/86b0cc035593be5e6fa2bcb6cbdf1ff96a6f7fa4
[  108.037232] patching 2 blocks to 2
[  108.045518] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/86b0cc035593be5e6fa2bcb6cbdf1ff96a6f7fa4
[  108.047129] stashing 2 overlapping blocks to 4cd833e56a00aedde363f4beb98b3be50ada8a5f
[  108.047158] 174485504 bytes free on /cache (8192 needed)
[  108.047206]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4cd833e56a00aedde363f4beb98b3be50ada8a5f
[  108.050990] patching 2 blocks to 2
[  108.059236] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4cd833e56a00aedde363f4beb98b3be50ada8a5f
[  108.062366]   moving 18 blocks
[  108.066694] stashing 2 overlapping blocks to d4ce600a7fd559fef819a192253d4ec328c48c1a
[  108.066725] 174485504 bytes free on /cache (8192 needed)
[  108.066766]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d4ce600a7fd559fef819a192253d4ec328c48c1a
[  108.070045] patching 2 blocks to 2
[  108.078439] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d4ce600a7fd559fef819a192253d4ec328c48c1a
[  108.080309]   moving 1 blocks
[  108.081081]   moving 1 blocks
[  108.082954] stashing 2 overlapping blocks to fe28d3a27cb5941f73d71fb18148972f63c58516
[  108.082987] 174485504 bytes free on /cache (8192 needed)
[  108.083035]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fe28d3a27cb5941f73d71fb18148972f63c58516
[  108.086755] patching 2 blocks to 2
[  108.094985] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fe28d3a27cb5941f73d71fb18148972f63c58516
[  108.099545]   moving 26 blocks
[  108.176140] stashing 769 overlapping blocks to 66f1e0dedb4c2d4847e3e3c5405b43b88478092a
[  108.176174] 174485504 bytes free on /cache (3149824 needed)
[  108.176231]  writing 769 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/66f1e0dedb4c2d4847e3e3c5405b43b88478092a
[  108.213060] patching 769 blocks to 769
[  108.257302] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/66f1e0dedb4c2d4847e3e3c5405b43b88478092a
[  108.289497]   moving 41 blocks
[  108.296033]   moving 9 blocks
[  108.377055] stashing 837 overlapping blocks to eebcbd4b3fddaf515d85c31729be50fd983404f7
[  108.377089] 174485504 bytes free on /cache (3428352 needed)
[  108.377146]  writing 837 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/eebcbd4b3fddaf515d85c31729be50fd983404f7
[  108.431996] patching 837 blocks to 837
[  108.479183] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/eebcbd4b3fddaf515d85c31729be50fd983404f7
[  108.508382]   moving 3 blocks
[  108.509758] stashing 2 overlapping blocks to 06dc6b639e631180c3e9e62841122af8936080de
[  108.509794] 174485504 bytes free on /cache (8192 needed)
[  108.509840]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/06dc6b639e631180c3e9e62841122af8936080de
[  108.513040] patching 2 blocks to 2
[  108.521436] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/06dc6b639e631180c3e9e62841122af8936080de
[  108.522799] stashing 2 overlapping blocks to 4f93cf126f2c863d18421ff4342d52d82b6c5398
[  108.522816] 174485504 bytes free on /cache (8192 needed)
[  108.522862]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4f93cf126f2c863d18421ff4342d52d82b6c5398
[  108.525976] patching 2 blocks to 2
[  108.534265] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4f93cf126f2c863d18421ff4342d52d82b6c5398
[  108.543872]   moving 70 blocks
[  108.550339] stashing 2 overlapping blocks to 9630fe6ec8ea767a6afffb79b3e450015b2f5c5d
[  108.550377] 174485504 bytes free on /cache (8192 needed)
[  108.550429]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9630fe6ec8ea767a6afffb79b3e450015b2f5c5d
[  108.553909] patching 2 blocks to 2
[  108.562159] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9630fe6ec8ea767a6afffb79b3e450015b2f5c5d
[  108.568817]   moving 41 blocks
[  108.586832]   moving 113 blocks
[  108.595046] stashing 2 overlapping blocks to c6a533161f82f03765cef9499bb441df7d2814b2
[  108.595084] 174485504 bytes free on /cache (8192 needed)
[  108.595138]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c6a533161f82f03765cef9499bb441df7d2814b2
[  108.598199] patching 2 blocks to 2
[  108.606375] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c6a533161f82f03765cef9499bb441df7d2814b2
[  108.607651]   moving 5 blocks
[  108.656138] stashing 512 overlapping blocks to 4006ada1e5522def143f037cab687af23d9f5421
[  108.656171] 174485504 bytes free on /cache (2097152 needed)
[  108.656230]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4006ada1e5522def143f037cab687af23d9f5421
[  108.682607] patching 512 blocks to 512
[  109.321841] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4006ada1e5522def143f037cab687af23d9f5421
[  109.383430]   moving 4 blocks
[  109.566440] stashing 1936 overlapping blocks to a0dbcf51979b397abfd8ed1239bd70573b42e72e
[  109.566477] 174485504 bytes free on /cache (7929856 needed)
[  109.566528]  writing 1936 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a0dbcf51979b397abfd8ed1239bd70573b42e72e
[  109.648121] patching 1936 blocks to 1936
[  109.747110] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a0dbcf51979b397abfd8ed1239bd70573b42e72e
[  109.807857]   moving 1 blocks
[  109.907876] stashing 1043 overlapping blocks to 80fbe07e0bd2c89a66402f9b04ca99f8a8cc3239
[  109.907912] 174485504 bytes free on /cache (4272128 needed)
[  109.907966]  writing 1043 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/80fbe07e0bd2c89a66402f9b04ca99f8a8cc3239
[  109.955477] patching 1043 blocks to 1043
[  110.013408] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/80fbe07e0bd2c89a66402f9b04ca99f8a8cc3239
[  110.055308]   moving 43 blocks
[  110.059774]   moving 1 blocks
[  110.060726] stashing 2 overlapping blocks to 288ceddf087a6d7af2a7bda3ed47743924a3b7ab
[  110.060761] 174485504 bytes free on /cache (8192 needed)
[  110.061216]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/288ceddf087a6d7af2a7bda3ed47743924a3b7ab
[  110.077257] patching 2 blocks to 2
[  110.085515] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/288ceddf087a6d7af2a7bda3ed47743924a3b7ab
[  110.105042]   moving 156 blocks
[  110.114287]   moving 1 blocks
[  110.114969]   moving 1 blocks
[  110.116349] stashing 4 overlapping blocks to d92dc4cdf8ec1f35777ccec125b76530483255ff
[  110.116390] 174485504 bytes free on /cache (16384 needed)
[  110.116445]  writing 4 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d92dc4cdf8ec1f35777ccec125b76530483255ff
[  110.120347] patching 4 blocks to 4
[  110.128784] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d92dc4cdf8ec1f35777ccec125b76530483255ff
[  110.130213]   moving 1 blocks
[  110.136537]   moving 52 blocks
[  110.246801] stashing 1114 overlapping blocks to 6bd7e7f51b6cc5a276de13eb620abd997661399e
[  110.246836] 174485504 bytes free on /cache (4562944 needed)
[  110.246891]  writing 1114 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6bd7e7f51b6cc5a276de13eb620abd997661399e
[  110.297197] patching 1114 blocks to 1114
[  110.357599] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6bd7e7f51b6cc5a276de13eb620abd997661399e
[  110.400014] stashing 2 overlapping blocks to 066914cc83dfd195b0dd3c10b19a42c24ec8d4f5
[  110.400041] 174485504 bytes free on /cache (8192 needed)
[  110.400093]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/066914cc83dfd195b0dd3c10b19a42c24ec8d4f5
[  110.404854] patching 2 blocks to 2
[  110.413320] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/066914cc83dfd195b0dd3c10b19a42c24ec8d4f5
[  110.414409]   moving 1 blocks
[  110.415074]   moving 1 blocks
[  110.417746]   moving 26 blocks
[  111.144689] stashing 6793 overlapping blocks to 5de7dc823a245e01c87e83e02bac22d30b6e76e6
[  111.144721] 174485504 bytes free on /cache (27824128 needed)
[  111.144785]  writing 6793 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5de7dc823a245e01c87e83e02bac22d30b6e76e6
[  111.502285] patching 6793 blocks to 6793
[  111.833975] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5de7dc823a245e01c87e83e02bac22d30b6e76e6
[  112.120890]   moving 1 blocks
[  112.121525]   moving 1 blocks
[  112.137106]   moving 123 blocks
[  112.146010]   moving 1 blocks
[  112.147810]   moving 4 blocks
[  112.149441] stashing 2 overlapping blocks to f43b25fa852a0be74fe0d7cd5877586037787382
[  112.149481] 174485504 bytes free on /cache (8192 needed)
[  112.149534]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f43b25fa852a0be74fe0d7cd5877586037787382
[  112.152968] patching 2 blocks to 2
[  112.161351] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f43b25fa852a0be74fe0d7cd5877586037787382
[  112.163005]   moving 5 blocks
[  112.164368] stashing 2 overlapping blocks to 9f09b6cd61d004231b283f8a9878bd24b4fba78f
[  112.164397] 174485504 bytes free on /cache (8192 needed)
[  112.164440]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9f09b6cd61d004231b283f8a9878bd24b4fba78f
[  112.167965] patching 2 blocks to 2
[  112.176146] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9f09b6cd61d004231b283f8a9878bd24b4fba78f
[  112.178040]   moving 5 blocks
[  112.180052]   moving 1 blocks
[  112.180740]   moving 1 blocks
[  112.187487] patching 39 blocks to 39
[  112.204038]   moving 6 blocks
[  112.206211] patching 2 blocks to 2
[  112.218099]   moving 19 blocks
[  112.221111] stashing 2 overlapping blocks to a25c1bd2d588d26b8561d3c6f8f7e2333a70ac4b
[  112.221150] 174485504 bytes free on /cache (8192 needed)
[  112.221204]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a25c1bd2d588d26b8561d3c6f8f7e2333a70ac4b
[  112.224236] patching 2 blocks to 2
[  112.232539] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a25c1bd2d588d26b8561d3c6f8f7e2333a70ac4b
[  112.233560]   moving 2 blocks
[  112.234256]   moving 1 blocks
[  112.348003] stashing 1189 overlapping blocks to 22db9e0392413ae46c28fb137da8f5fa594dc5de
[  112.348037] 174485504 bytes free on /cache (4870144 needed)
[  112.348092]  writing 1189 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/22db9e0392413ae46c28fb137da8f5fa594dc5de
[  112.398953] patching 1189 blocks to 1189
[  112.647087] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/22db9e0392413ae46c28fb137da8f5fa594dc5de
[  112.768198]   moving 1 blocks
[  112.769202] stashing 2 overlapping blocks to 7fb99df8b0fee12d53a78bcdf9e94205ff6a9333
[  112.769271] 174485504 bytes free on /cache (8192 needed)
[  112.769336]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7fb99df8b0fee12d53a78bcdf9e94205ff6a9333
[  112.772654] patching 2 blocks to 2
[  112.781074] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7fb99df8b0fee12d53a78bcdf9e94205ff6a9333
[  112.782386] stashing 2 overlapping blocks to 7f7474eff41fa8aaaf0e4250d802c8155896d0b6
[  112.782406] 174485504 bytes free on /cache (8192 needed)
[  112.782456]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7f7474eff41fa8aaaf0e4250d802c8155896d0b6
[  112.785825] patching 2 blocks to 2
[  112.794179] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7f7474eff41fa8aaaf0e4250d802c8155896d0b6
[  112.795960]   moving 6 blocks
[  112.797393] stashing 2 overlapping blocks to fd0d144574027850464e1a2d99088844c240d927
[  112.797427] 174485504 bytes free on /cache (8192 needed)
[  112.797499]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fd0d144574027850464e1a2d99088844c240d927
[  112.800970] patching 2 blocks to 2
[  112.809222] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fd0d144574027850464e1a2d99088844c240d927
[  112.811281]   moving 1 blocks
[  112.814199]   moving 16 blocks
[  112.817386]   moving 1 blocks
[  112.818348] stashing 2 overlapping blocks to d77871bfbcef60aafb5aab5a34deaaf9b806ee81
[  112.818390] 174485504 bytes free on /cache (8192 needed)
[  112.818443]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d77871bfbcef60aafb5aab5a34deaaf9b806ee81
[  112.821812] patching 2 blocks to 2
[  112.830059] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d77871bfbcef60aafb5aab5a34deaaf9b806ee81
[  112.831512]   moving 1 blocks
[  112.832541] stashing 2 overlapping blocks to df60b3f6a27505ed6a3a3add4cb3dbe1a1b485f4
[  112.832571] 174485504 bytes free on /cache (8192 needed)
[  112.832612]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/df60b3f6a27505ed6a3a3add4cb3dbe1a1b485f4
[  112.836251] patching 2 blocks to 2
[  112.844535] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/df60b3f6a27505ed6a3a3add4cb3dbe1a1b485f4
[  112.851698] stashing 63 overlapping blocks to d40bf65574533f34d80a4fc0b33be31df6bc8b64
[  112.851733] 174485504 bytes free on /cache (258048 needed)
[  112.851796]  writing 63 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d40bf65574533f34d80a4fc0b33be31df6bc8b64
[  112.861665] patching 63 blocks to 63
[  112.872541] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d40bf65574533f34d80a4fc0b33be31df6bc8b64
[  112.880245]   moving 9 blocks
[  112.883580] stashing 3 overlapping blocks to 1f7a99f1eceef251875747bae7d4b228a95b18b1
[  112.883612] 174485504 bytes free on /cache (12288 needed)
[  112.883657]  writing 3 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1f7a99f1eceef251875747bae7d4b228a95b18b1
[  112.887172] patching 3 blocks to 3
[  112.895429] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1f7a99f1eceef251875747bae7d4b228a95b18b1
[  112.897011] stashing 2 overlapping blocks to f8f47369eb593c6bb8d1604590341cf288b1192e
[  112.897025] 174485504 bytes free on /cache (8192 needed)
[  112.897075]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f8f47369eb593c6bb8d1604590341cf288b1192e
[  112.900270] patching 2 blocks to 2
[  112.908421] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f8f47369eb593c6bb8d1604590341cf288b1192e
[  112.911483]   moving 15 blocks
[  112.913354]   moving 4 blocks
[  112.914759] stashing 2 overlapping blocks to 44af46eb462a61792e409bf4704a0934c2c75d6d
[  112.914790] 174485504 bytes free on /cache (8192 needed)
[  112.914833]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/44af46eb462a61792e409bf4704a0934c2c75d6d
[  112.918373] patching 2 blocks to 2
[  112.926619] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/44af46eb462a61792e409bf4704a0934c2c75d6d
[  112.928223]   moving 4 blocks
[  112.930380] stashing 2 overlapping blocks to 19e463ecb4c88c749b47c71fd9727f895bf91262
[  112.930411] 174485504 bytes free on /cache (8192 needed)
[  112.930453]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19e463ecb4c88c749b47c71fd9727f895bf91262
[  112.933965] patching 2 blocks to 2
[  112.942154] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19e463ecb4c88c749b47c71fd9727f895bf91262
[  112.943877]   moving 1 blocks
[  112.944580] stashing 2 overlapping blocks to f28115f223d778041569552282151e20f6eccfbd
[  112.944608] 174485504 bytes free on /cache (8192 needed)
[  112.944648]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f28115f223d778041569552282151e20f6eccfbd
[  112.947794] patching 2 blocks to 2
[  112.956001] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f28115f223d778041569552282151e20f6eccfbd
[  112.957368]   moving 2 blocks
[  112.959335]   moving 17 blocks
[  112.962643] stashing 2 overlapping blocks to 4e509c7682ff28936838e8e02a0f9ef04f496311
[  112.962673] 174485504 bytes free on /cache (8192 needed)
[  112.962720]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4e509c7682ff28936838e8e02a0f9ef04f496311
[  112.966384] patching 2 blocks to 2
[  112.974584] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4e509c7682ff28936838e8e02a0f9ef04f496311
[  112.977261]   moving 8 blocks
[  112.994737]   moving 137 blocks
[  113.015489]   moving 102 blocks
[  113.225262] stashing 2163 overlapping blocks to c243e4d81b57b1f095dc44ab42035a02183d714d
[  113.225296] 174485504 bytes free on /cache (8859648 needed)
[  113.225356]  writing 2163 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c243e4d81b57b1f095dc44ab42035a02183d714d
[  113.316142] patching 2163 blocks to 2163
[  113.425965] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c243e4d81b57b1f095dc44ab42035a02183d714d
[  114.280451] stashing 8192 overlapping blocks to 2bb6526be686ea8029a1b1a5d3b443afa719d68e
[  114.280485] 174485504 bytes free on /cache (33554432 needed)
[  114.280544]  writing 8192 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2bb6526be686ea8029a1b1a5d3b443afa719d68e
[  114.693223] patching 8192 blocks to 8192
[  115.109454] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2bb6526be686ea8029a1b1a5d3b443afa719d68e
[  115.416366] stashing 1 overlapping blocks to 7cf5265e2614db56ac47bcc9b39a7dc3ca6ab219
[  115.416399] 174485504 bytes free on /cache (4096 needed)
[  115.416451]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7cf5265e2614db56ac47bcc9b39a7dc3ca6ab219
[  115.420996] patching 1 blocks to 1
[  115.429284] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7cf5265e2614db56ac47bcc9b39a7dc3ca6ab219
[  115.478650] stashing 512 overlapping blocks to 870cad4f93e838442fcd36063295f80557905f0c
[  115.478681] 174485504 bytes free on /cache (2097152 needed)
[  115.478744]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/870cad4f93e838442fcd36063295f80557905f0c
[  115.503600] patching 512 blocks to 512
[  115.537059] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/870cad4f93e838442fcd36063295f80557905f0c
[  115.555364] patching 2 blocks to 2
[  115.565113]   moving 7 blocks
[  115.566471]   moving 1 blocks
[  115.567331]   moving 1 blocks
[  115.568779]   moving 3 blocks
[  115.620189] stashing 512 overlapping blocks to 80b90bbc918ea6a680d51fe0ea5925cbc516508c
[  115.620221] 174485504 bytes free on /cache (2097152 needed)
[  115.620282]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/80b90bbc918ea6a680d51fe0ea5925cbc516508c
[  115.645590] patching 512 blocks to 512
[  115.961026] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/80b90bbc918ea6a680d51fe0ea5925cbc516508c
[  116.013367] stashing 2 overlapping blocks to 4af9c1329c4acbc79a14dc6c879d06a00e0c0a91
[  116.013387] 174485504 bytes free on /cache (8192 needed)
[  116.013435]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4af9c1329c4acbc79a14dc6c879d06a00e0c0a91
[  116.017817] patching 2 blocks to 2
[  116.026110] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4af9c1329c4acbc79a14dc6c879d06a00e0c0a91
[  116.075582] stashing 512 overlapping blocks to 7adbc5ca8c324b64ddde355872e770e2c2df2952
[  116.075615] 174485504 bytes free on /cache (2097152 needed)
[  116.075671]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7adbc5ca8c324b64ddde355872e770e2c2df2952
[  116.105026] patching 512 blocks to 512
[  116.744835] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7adbc5ca8c324b64ddde355872e770e2c2df2952
[  116.865306] stashing 425 overlapping blocks to 5bc6450b5237e97d9f2ab7c9bac08e859281fbe4
[  116.865340] 174485504 bytes free on /cache (1740800 needed)
[  116.865391]  writing 425 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5bc6450b5237e97d9f2ab7c9bac08e859281fbe4
[  116.888575] patching 425 blocks to 425
[  116.922389] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5bc6450b5237e97d9f2ab7c9bac08e859281fbe4
[  117.105813]   moving 1509 blocks
[  117.159345]   moving 1 blocks
[  117.207135] stashing 512 overlapping blocks to 894f14ec819e43eec066fc1a07bd16ddebce4dc0
[  117.207167] 174485504 bytes free on /cache (2097152 needed)
[  117.207228]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/894f14ec819e43eec066fc1a07bd16ddebce4dc0
[  117.233656] patching 512 blocks to 512
[  117.876937] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/894f14ec819e43eec066fc1a07bd16ddebce4dc0
[  118.042358]   moving 957 blocks
[  118.093573]   moving 4 blocks
[  118.362928] stashing 2861 overlapping blocks to 6a46db170f7ddeaaffe48f2b5faf535bf3dcef97
[  118.362959] 174485504 bytes free on /cache (11718656 needed)
[  118.363018]  writing 2861 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6a46db170f7ddeaaffe48f2b5faf535bf3dcef97
[  118.525683] patching 2861 blocks to 2861
[  118.667647] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6a46db170f7ddeaaffe48f2b5faf535bf3dcef97
[  118.884704]   moving 510 blocks
[  118.916866]   moving 102 blocks
[  118.924985] stashing 2 overlapping blocks to fec8644cac7b76f276d98778cba464739c508ec6
[  118.925011] 174485504 bytes free on /cache (8192 needed)
[  118.925069]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fec8644cac7b76f276d98778cba464739c508ec6
[  118.928838] patching 2 blocks to 2
[  118.937058] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fec8644cac7b76f276d98778cba464739c508ec6
[  118.938829]   moving 1 blocks
[  118.939684]   moving 1 blocks
[  118.941367] stashing 2 overlapping blocks to ab5d7ec9c00f8d27342ab2b50aefeae00d9c0dbd
[  118.941377] 174485504 bytes free on /cache (8192 needed)
[  118.941415]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ab5d7ec9c00f8d27342ab2b50aefeae00d9c0dbd
[  118.945009] patching 2 blocks to 2
[  118.953302] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ab5d7ec9c00f8d27342ab2b50aefeae00d9c0dbd
[  118.955143]   moving 1 blocks
[  119.075186] stashing 1268 overlapping blocks to c690742405b1d69a86a1ba240e39360b03e4100e
[  119.075218] 174485504 bytes free on /cache (5193728 needed)
[  119.075273]  writing 1268 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c690742405b1d69a86a1ba240e39360b03e4100e
[  119.130583] patching 1268 blocks to 1268
[  119.241651] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c690742405b1d69a86a1ba240e39360b03e4100e
[  119.348473] stashing 512 overlapping blocks to f4b2265a550f685151fc0658ed04f159167fd4f8
[  119.348508] 174485504 bytes free on /cache (2097152 needed)
[  119.348560]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f4b2265a550f685151fc0658ed04f159167fd4f8
[  119.374904] patching 512 blocks to 512
[  119.569261] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f4b2265a550f685151fc0658ed04f159167fd4f8
[  119.709720] stashing 512 overlapping blocks to dc990021c1eccb380d88a91fca022ae53d0b9d5a
[  119.709753] 174485504 bytes free on /cache (2097152 needed)
[  119.709813]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/dc990021c1eccb380d88a91fca022ae53d0b9d5a
[  119.736192] patching 512 blocks to 512
[  120.066110] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/dc990021c1eccb380d88a91fca022ae53d0b9d5a
[  120.205958] stashing 512 overlapping blocks to a56f10534f82d9496b46504151b7580c5a56ac94
[  120.205991] 174485504 bytes free on /cache (2097152 needed)
[  120.206049]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a56f10534f82d9496b46504151b7580c5a56ac94
[  120.232495] patching 512 blocks to 512
[  120.266535] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a56f10534f82d9496b46504151b7580c5a56ac94
[  120.335800] stashing 512 overlapping blocks to 0da2f33c91a7f7b871aea605de5dd1f0394eab36
[  120.335840] 174485504 bytes free on /cache (2097152 needed)
[  120.335887]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0da2f33c91a7f7b871aea605de5dd1f0394eab36
[  120.362797] patching 512 blocks to 512
[  120.993179] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0da2f33c91a7f7b871aea605de5dd1f0394eab36
[  121.049494]   moving 1 blocks
[  121.051903]   moving 5 blocks
[  121.053311]   moving 1 blocks
[  121.055006]   moving 1 blocks
[  121.056184] stashing 2 overlapping blocks to 05bd2e962961df1625e68ee17edbe4d04e531add
[  121.056216] 174485504 bytes free on /cache (8192 needed)
[  121.056261]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/05bd2e962961df1625e68ee17edbe4d04e531add
[  121.061197] patching 2 blocks to 2
[  121.069132] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/05bd2e962961df1625e68ee17edbe4d04e531add
[  121.070506]   moving 1 blocks
[  121.072114] patching 2 blocks to 2
[  121.082665]   moving 3 blocks
[  121.165830] stashing 713 overlapping blocks to 7a87583bb42c057df625f4a1e45ab7fab99be6a1
[  121.165865] 174485504 bytes free on /cache (2920448 needed)
[  121.165920]  writing 713 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7a87583bb42c057df625f4a1e45ab7fab99be6a1
[  121.200742] patching 713 blocks to 713
[  121.242301] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7a87583bb42c057df625f4a1e45ab7fab99be6a1
[  121.276810]   moving 57 blocks
[  121.285936]   moving 24 blocks
[  121.299457] stashing 104 overlapping blocks to 9dcecfaa29d120885179083851256e6355fca4cf
[  121.299500] 174485504 bytes free on /cache (425984 needed)
[  121.299556]  writing 104 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9dcecfaa29d120885179083851256e6355fca4cf
[  121.310124] patching 104 blocks to 104
[  121.323180] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9dcecfaa29d120885179083851256e6355fca4cf
[  121.331214]   moving 3 blocks
[  121.332419]   moving 4 blocks
[  121.333375]   moving 1 blocks
[  121.334360]   moving 2 blocks
[  121.382271] stashing 512 overlapping blocks to 1bbfc29c8501a92d314397960825f9d783f4d595
[  121.382300] 174485504 bytes free on /cache (2097152 needed)
[  121.382362]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1bbfc29c8501a92d314397960825f9d783f4d595
[  121.407099] patching 512 blocks to 512
[  122.051952] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1bbfc29c8501a92d314397960825f9d783f4d595
[  122.108459] stashing 2 overlapping blocks to 8f35f2a149d5e442aca2e37c4d9d973af7035113
[  122.108480] 174485504 bytes free on /cache (8192 needed)
[  122.108524]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8f35f2a149d5e442aca2e37c4d9d973af7035113
[  122.113129] patching 2 blocks to 2
[  122.121499] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8f35f2a149d5e442aca2e37c4d9d973af7035113
[  122.124162]   moving 18 blocks
[  122.173220] stashing 512 overlapping blocks to 3ca7185aec6d9d2422ffdad2c308e7fb3749af61
[  122.173254] 174485504 bytes free on /cache (2097152 needed)
[  122.173308]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3ca7185aec6d9d2422ffdad2c308e7fb3749af61
[  122.199284] patching 512 blocks to 512
[  122.232292] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3ca7185aec6d9d2422ffdad2c308e7fb3749af61
[  122.242987] I:current maximum temperature: 36452
[  122.252158]   moving 4 blocks
[  122.254436]   moving 5 blocks
[  122.257051]   moving 12 blocks
[  122.260101]   moving 1 blocks
[  122.260998]   moving 1 blocks
[  122.267992]   moving 46 blocks
[  122.272749] stashing 4 overlapping blocks to ce49254f8ad187a8ec47f0c88c70641cfe839028
[  122.272789] 174485504 bytes free on /cache (16384 needed)
[  122.272843]  writing 4 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce49254f8ad187a8ec47f0c88c70641cfe839028
[  122.276213] patching 4 blocks to 4
[  122.284744] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce49254f8ad187a8ec47f0c88c70641cfe839028
[  122.288817]   moving 35 blocks
[  122.293061]   moving 1 blocks
[  122.295291]   moving 20 blocks
[  122.302706]   moving 55 blocks
[  122.311088]   moving 14 blocks
[  122.312727] stashing 2 overlapping blocks to 3d555eff37886cde40faf02fe8633f8ae3e60224
[  122.312766] 174485504 bytes free on /cache (8192 needed)
[  122.312820]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3d555eff37886cde40faf02fe8633f8ae3e60224
[  122.316541] patching 2 blocks to 2
[  122.324968] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3d555eff37886cde40faf02fe8633f8ae3e60224
[  122.326309] stashing 2 overlapping blocks to e892a97a43e5fbf0f54c9c63782b95a6e1554085
[  122.326339] 174485504 bytes free on /cache (8192 needed)
[  122.326389]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e892a97a43e5fbf0f54c9c63782b95a6e1554085
[  122.329968] patching 2 blocks to 2
[  122.338281] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e892a97a43e5fbf0f54c9c63782b95a6e1554085
[  122.340892]   moving 21 blocks
[  122.371693] stashing 286 overlapping blocks to ee2a60bdc55c7808e508088bb2439b47cb8a6ec1
[  122.371725] 174485504 bytes free on /cache (1171456 needed)
[  122.371784]  writing 286 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ee2a60bdc55c7808e508088bb2439b47cb8a6ec1
[  122.389496] patching 286 blocks to 286
[  122.411049] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ee2a60bdc55c7808e508088bb2439b47cb8a6ec1
[  122.424538]   moving 1 blocks
[  122.434936]   moving 78 blocks
[  122.441996]   moving 1 blocks
[  122.443070]   moving 1 blocks
[  122.446584]   moving 36 blocks
[  122.450509]   moving 4 blocks
[  122.604496] stashing 1618 overlapping blocks to fb15872d049119a389dab7745596df397ae4a32c
[  122.604530] 174485504 bytes free on /cache (6627328 needed)
[  122.604585]  writing 1618 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fb15872d049119a389dab7745596df397ae4a32c
[  122.759696] patching 1618 blocks to 1618
[  122.843974] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fb15872d049119a389dab7745596df397ae4a32c
[  122.895312] stashing 2 overlapping blocks to 972fd37a5ff34ab0e5e11f58758c48d95a4eb94f
[  122.895337] 174485504 bytes free on /cache (8192 needed)
[  122.895394]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/972fd37a5ff34ab0e5e11f58758c48d95a4eb94f
[  122.899100] patching 2 blocks to 2
[  122.907434] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/972fd37a5ff34ab0e5e11f58758c48d95a4eb94f
[  122.909412]   moving 1 blocks
[  122.910430] stashing 2 overlapping blocks to c24896ae3e37b50ab83cbeccfcb51bea3989d517
[  122.910461] 174485504 bytes free on /cache (8192 needed)
[  122.910502]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c24896ae3e37b50ab83cbeccfcb51bea3989d517
[  122.913849] patching 2 blocks to 2
[  122.922147] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c24896ae3e37b50ab83cbeccfcb51bea3989d517
[  122.923478] stashing 2 overlapping blocks to 5749f1fb641ea7e841b8a14a3e282e01d2f8cbc2
[  122.923496] 174485504 bytes free on /cache (8192 needed)
[  122.923542]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5749f1fb641ea7e841b8a14a3e282e01d2f8cbc2
[  122.926973] patching 2 blocks to 2
[  122.935250] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5749f1fb641ea7e841b8a14a3e282e01d2f8cbc2
[  122.936511] stashing 2 overlapping blocks to 985cb2c158e2ac3ff87f1e4ec1bbaeaa7c2054a7
[  122.936544] 174485504 bytes free on /cache (8192 needed)
[  122.936586]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/985cb2c158e2ac3ff87f1e4ec1bbaeaa7c2054a7
[  122.939824] patching 2 blocks to 2
[  122.948167] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/985cb2c158e2ac3ff87f1e4ec1bbaeaa7c2054a7
[  122.950431] stashing 10 overlapping blocks to 3ece24c5ce355ce36ec03a90ebd6ace81cb8bc32
[  122.950447] 174485504 bytes free on /cache (40960 needed)
[  122.950493]  writing 10 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3ece24c5ce355ce36ec03a90ebd6ace81cb8bc32
[  122.955317] patching 10 blocks to 10
[  122.964020] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3ece24c5ce355ce36ec03a90ebd6ace81cb8bc32
[  122.966822]   moving 1 blocks
[  122.968158] patching 2 blocks to 2
[  122.979498]   moving 12 blocks
[  122.981225]   moving 1 blocks
[  122.982082]   moving 1 blocks
[  122.983027] stashing 2 blocks to d6d000e7b004b9aea42a0f737da52b53825479fd
[  122.983036]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d6d000e7b004b9aea42a0f737da52b53825479fd
[  122.988906]   moving 16 blocks
[  122.995432]   moving 23 blocks
[  122.999833]   moving 9 blocks
[  123.001118] stashing 5 blocks to f8e6646fa346bf5dcb215342ff8063a4b7fed50d
[  123.001129]  writing 5 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f8e6646fa346bf5dcb215342ff8063a4b7fed50d
[  123.006878]   moving 23 blocks
[  123.011178]   moving 14 blocks
[  123.012628] stashing 2 blocks to a5b7cda9265e4b7472b369d4cfc4b616de8e6d0f
[  123.012646]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a5b7cda9265e4b7472b369d4cfc4b616de8e6d0f
[  123.025455] stashing 96 overlapping blocks to 03d147aed45322b81827dc78ec7f4dfe62a62a2d
[  123.025481] 174448640 bytes free on /cache (393216 needed)
[  123.025528]  writing 96 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/03d147aed45322b81827dc78ec7f4dfe62a62a2d
[  123.036009]   moving 96 blocks
[  123.036464] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/03d147aed45322b81827dc78ec7f4dfe62a62a2d
[  123.046396]   moving 24 blocks
[  123.052274]   moving 23 blocks
[  123.058014]   moving 25 blocks
[  123.062990]   moving 24 blocks
[  123.071912] stashing 52 overlapping blocks to a56f7b585b883a2914a7185c5aeaeac0a57523c2
[  123.071930] 174448640 bytes free on /cache (212992 needed)
[  123.071977]  writing 52 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a56f7b585b883a2914a7185c5aeaeac0a57523c2
[  123.081185]   moving 52 blocks
[  123.081445] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a56f7b585b883a2914a7185c5aeaeac0a57523c2
[  123.096378] stashing 106 overlapping blocks to d330acb57ca79a90f69bbdf6a1bd10745a55e15e
[  123.096398] 174448640 bytes free on /cache (434176 needed)
[  123.096448]  writing 106 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d330acb57ca79a90f69bbdf6a1bd10745a55e15e
[  123.107713]   moving 106 blocks
[  123.108214] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d330acb57ca79a90f69bbdf6a1bd10745a55e15e
[  123.122115]   moving 8 blocks
[  123.127354]   moving 23 blocks
[  123.132369]   moving 29 blocks
[  123.135802] stashing 2 blocks to 048b66624c402bc88b8d2d9b883ce0ee0a466586
[  123.135818]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/048b66624c402bc88b8d2d9b883ce0ee0a466586
[  123.184997] stashing 455 overlapping blocks to e64c9326b2bed915670a3d9886ab7f88f05c21da
[  123.185030] 174440448 bytes free on /cache (1863680 needed)
[  123.185090]  writing 455 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e64c9326b2bed915670a3d9886ab7f88f05c21da
[  123.210091]   moving 455 blocks
[  123.212448] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e64c9326b2bed915670a3d9886ab7f88f05c21da
[  123.232146]   moving 13 blocks
[  123.353074]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d6d000e7b004b9aea42a0f737da52b53825479fd
[  123.353194]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a5b7cda9265e4b7472b369d4cfc4b616de8e6d0f
[  123.353270]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/048b66624c402bc88b8d2d9b883ce0ee0a466586
[  123.368551] stashing 512 overlapping blocks to 75cac33657d868ce1ce4d7e681381144bf2deddc
[  123.368574] 174440448 bytes free on /cache (2097152 needed)
[  123.368633]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/75cac33657d868ce1ce4d7e681381144bf2deddc
[  123.395332] patching 512 blocks to 512
[  123.430233] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/75cac33657d868ce1ce4d7e681381144bf2deddc
[  123.479747] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d6d000e7b004b9aea42a0f737da52b53825479fd
[  123.480046] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a5b7cda9265e4b7472b369d4cfc4b616de8e6d0f
[  123.480229] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/048b66624c402bc88b8d2d9b883ce0ee0a466586
[  123.480667]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f8e6646fa346bf5dcb215342ff8063a4b7fed50d
[  123.481066]   moving 5 blocks
[  123.481817] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f8e6646fa346bf5dcb215342ff8063a4b7fed50d
[  123.484363]   moving 20 blocks
[  123.489646]   moving 21 blocks
[  123.493774]   moving 5 blocks
[  123.496029]   moving 9 blocks
[  123.544771] stashing 465 overlapping blocks to d7a45fc3c275afb3400f9de32d34db06dc5539c7
[  123.544801] 174485504 bytes free on /cache (1904640 needed)
[  123.544860]  writing 465 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d7a45fc3c275afb3400f9de32d34db06dc5539c7
[  123.571628]   moving 465 blocks
[  123.573751] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d7a45fc3c275afb3400f9de32d34db06dc5539c7
[  123.607288] stashing 154 overlapping blocks to 19e9d7eab03fd9ea0e6459ce1b4f754e49f6fe52
[  123.607314] 174485504 bytes free on /cache (630784 needed)
[  123.607363]  writing 154 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19e9d7eab03fd9ea0e6459ce1b4f754e49f6fe52
[  123.619944]   moving 154 blocks
[  123.620649] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19e9d7eab03fd9ea0e6459ce1b4f754e49f6fe52
[  123.629199]   moving 6 blocks
[  123.632678]   moving 25 blocks
[  123.642130] stashing 60 overlapping blocks to 98a062f3d349f9749e56d7b3cb26c11696498bf7
[  123.642152] 174485504 bytes free on /cache (245760 needed)
[  123.642197]  writing 60 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/98a062f3d349f9749e56d7b3cb26c11696498bf7
[  123.650762]   moving 60 blocks
[  123.651096] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/98a062f3d349f9749e56d7b3cb26c11696498bf7
[  123.663720] stashing 57 overlapping blocks to a69983ab8e54ed1bfc4ded0173380b6dda8f81b1
[  123.663740] 174485504 bytes free on /cache (233472 needed)
[  123.663788]  writing 57 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a69983ab8e54ed1bfc4ded0173380b6dda8f81b1
[  123.672698]   moving 57 blocks
[  123.672983] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a69983ab8e54ed1bfc4ded0173380b6dda8f81b1
[  123.699690] stashing 207 overlapping blocks to 592905ec80a1524d7b1e5d24c406f08fe7cd9c1a
[  123.699708] 174485504 bytes free on /cache (847872 needed)
[  123.699757]  writing 207 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/592905ec80a1524d7b1e5d24c406f08fe7cd9c1a
[  123.714289]   moving 207 blocks
[  123.715240] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/592905ec80a1524d7b1e5d24c406f08fe7cd9c1a
[  123.727355]   moving 10 blocks
[  123.730394]   moving 17 blocks
[  123.735587]   moving 21 blocks
[  123.738076]   moving 4 blocks
[  123.742351]   moving 29 blocks
[  123.775489] stashing 290 overlapping blocks to 3e95161536022c3e5d73f07de12a5120b1a19607
[  123.775516] 174485504 bytes free on /cache (1187840 needed)
[  123.775570]  writing 290 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3e95161536022c3e5d73f07de12a5120b1a19607
[  123.793534]   moving 290 blocks
[  123.794909] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3e95161536022c3e5d73f07de12a5120b1a19607
[  123.810244] stashing 36 overlapping blocks to dc6c251f0d8371d1bdf5e30992fe0874e23fff13
[  123.810269] 174485504 bytes free on /cache (147456 needed)
[  123.810318]  writing 36 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/dc6c251f0d8371d1bdf5e30992fe0874e23fff13
[  123.816982]   moving 36 blocks
[  123.817170] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/dc6c251f0d8371d1bdf5e30992fe0874e23fff13
[  123.839802] stashing 180 overlapping blocks to 883cbe4ae153442fd15b20794f8ff9832976c501
[  123.839824] 174485504 bytes free on /cache (737280 needed)
[  123.839877]  writing 180 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/883cbe4ae153442fd15b20794f8ff9832976c501
[  123.853475]   moving 180 blocks
[  123.854455] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/883cbe4ae153442fd15b20794f8ff9832976c501
[  123.942595] stashing 795 overlapping blocks to 03b0b2c8ffa6467475a9256da7e9143f738143a2
[  123.942627] 174485504 bytes free on /cache (3256320 needed)
[  123.942687]  writing 795 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/03b0b2c8ffa6467475a9256da7e9143f738143a2
[  123.979558]   moving 795 blocks
[  123.983557] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/03b0b2c8ffa6467475a9256da7e9143f738143a2
[  124.011969]   moving 10 blocks
[  124.016340]   moving 14 blocks
[  124.020291]   moving 12 blocks
[  124.022045]   moving 6 blocks
[  124.023940]   moving 8 blocks
[  124.025459]   moving 5 blocks
[  124.027153]   moving 5 blocks
[  124.028999]   moving 8 blocks
[  124.032597]   moving 4 blocks
[  124.033944]   moving 5 blocks
[  124.035859]   moving 11 blocks
[  124.039068]   moving 4 blocks
[  124.040317]   moving 4 blocks
[  124.041536]   moving 4 blocks
[  124.044805]   moving 24 blocks
[  124.053797] stashing 51 overlapping blocks to 4b60b913c761faa3b9f10b886f4496efff0fb523
[  124.053836] 174485504 bytes free on /cache (208896 needed)
[  124.053890]  writing 51 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4b60b913c761faa3b9f10b886f4496efff0fb523
[  124.062733]   moving 51 blocks
[  124.062986] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4b60b913c761faa3b9f10b886f4496efff0fb523
[  124.070203]   moving 13 blocks
[  124.073554]   moving 5 blocks
[  124.076345]   moving 19 blocks
[  124.079163]   moving 14 blocks
[  124.119585] stashing 377 overlapping blocks to e6bf645f95bee7062be79b56d3516360af16b74c
[  124.119621] 174485504 bytes free on /cache (1544192 needed)
[  124.119675]  writing 377 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e6bf645f95bee7062be79b56d3516360af16b74c
[  124.140453]   moving 377 blocks
[  124.142211] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e6bf645f95bee7062be79b56d3516360af16b74c
[  124.158092]   moving 9 blocks
[  124.163738]   moving 27 blocks
[  124.167073]   moving 8 blocks
[  124.178041] stashing 74 overlapping blocks to 58145bfb58d6cf741498c658bda9f645df4c9583
[  124.178061] 174485504 bytes free on /cache (303104 needed)
[  124.178112]  writing 74 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/58145bfb58d6cf741498c658bda9f645df4c9583
[  124.187976]   moving 74 blocks
[  124.188400] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/58145bfb58d6cf741498c658bda9f645df4c9583
[  124.203394] stashing 73 overlapping blocks to edb6fc5303530366d071363d903ee2c7e5baea08
[  124.203410] 174485504 bytes free on /cache (299008 needed)
[  124.203462]  writing 73 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/edb6fc5303530366d071363d903ee2c7e5baea08
[  124.212268]   moving 73 blocks
[  124.212620] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/edb6fc5303530366d071363d903ee2c7e5baea08
[  124.221111]   moving 18 blocks
[  124.225275]   moving 17 blocks
[  124.234581] stashing 55 overlapping blocks to a532f5e3aa6177f415df26aba70be72e96e01831
[  124.234597] 174485504 bytes free on /cache (225280 needed)
[  124.234681]  writing 55 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a532f5e3aa6177f415df26aba70be72e96e01831
[  124.241972]   moving 55 blocks
[  124.242249] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a532f5e3aa6177f415df26aba70be72e96e01831
[  124.251407] stashing 34 overlapping blocks to 58ea8f960643cf33dc9c05b5b960241eaed64066
[  124.251424] 174485504 bytes free on /cache (139264 needed)
[  124.251475]  writing 34 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/58ea8f960643cf33dc9c05b5b960241eaed64066
[  124.259162]   moving 34 blocks
[  124.259340] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/58ea8f960643cf33dc9c05b5b960241eaed64066
[  124.273499] stashing 103 overlapping blocks to b076c5ebf913578a3ab1c5a00d4d2ca8d53bcef7
[  124.273517] 174485504 bytes free on /cache (421888 needed)
[  124.273567]  writing 103 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b076c5ebf913578a3ab1c5a00d4d2ca8d53bcef7
[  124.285048]   moving 103 blocks
[  124.285533] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b076c5ebf913578a3ab1c5a00d4d2ca8d53bcef7
[  124.304758] stashing 109 overlapping blocks to f74a69db85692e234a613f257038b89c7d3eb292
[  124.304776] 174485504 bytes free on /cache (446464 needed)
[  124.304828]  writing 109 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f74a69db85692e234a613f257038b89c7d3eb292
[  124.315962]   moving 109 blocks
[  124.316461] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f74a69db85692e234a613f257038b89c7d3eb292
[  124.325392]   moving 13 blocks
[  124.342511] stashing 153 overlapping blocks to 5d1d0a0438ce9f46ebeac527459539b176678b62
[  124.342530] 174485504 bytes free on /cache (626688 needed)
[  124.342577]  writing 153 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5d1d0a0438ce9f46ebeac527459539b176678b62
[  124.354652]   moving 153 blocks
[  124.355362] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5d1d0a0438ce9f46ebeac527459539b176678b62
[  124.377158] stashing 124 overlapping blocks to a3789f52ef44a2af775f0b13a2aa779062fad676
[  124.377176] 174485504 bytes free on /cache (507904 needed)
[  124.377225]  writing 124 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a3789f52ef44a2af775f0b13a2aa779062fad676
[  124.389883]   moving 124 blocks
[  124.390466] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a3789f52ef44a2af775f0b13a2aa779062fad676
[  124.407535] stashing 94 overlapping blocks to 29ba529e23bc743983f8da45caa6cd341554afeb
[  124.407563] 174485504 bytes free on /cache (385024 needed)
[  124.407611]  writing 94 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/29ba529e23bc743983f8da45caa6cd341554afeb
[  124.418229]   moving 94 blocks
[  124.418685] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/29ba529e23bc743983f8da45caa6cd341554afeb
[  124.426622]   moving 4 blocks
[  124.430170]   moving 24 blocks
[  124.434284]   moving 5 blocks
[  124.439033] stashing 34 overlapping blocks to fca8924b9562c7072820b5aed3b483075c05820b
[  124.439058] 174485504 bytes free on /cache (139264 needed)
[  124.439106]  writing 34 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fca8924b9562c7072820b5aed3b483075c05820b
[  124.445282]   moving 34 blocks
[  124.445467] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fca8924b9562c7072820b5aed3b483075c05820b
[  124.451018]   moving 10 blocks
[  124.452779]   moving 3 blocks
[  124.461785] stashing 80 overlapping blocks to 7d9a81f7798e0b92184e84df37a48cb7ffb0278f
[  124.461813] 174485504 bytes free on /cache (327680 needed)
[  124.461859]  writing 80 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d9a81f7798e0b92184e84df37a48cb7ffb0278f
[  124.471593]   moving 80 blocks
[  124.471989] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d9a81f7798e0b92184e84df37a48cb7ffb0278f
[  124.479623]   moving 9 blocks
[  124.481222]   moving 6 blocks
[  124.482986]   moving 4 blocks
[  124.484475]   moving 4 blocks
[  124.491754] stashing 56 overlapping blocks to 556af45ed46f6917668e0f71a9887881fc3889b8
[  124.491780] 174485504 bytes free on /cache (229376 needed)
[  124.491824]  writing 56 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/556af45ed46f6917668e0f71a9887881fc3889b8
[  124.500280]   moving 56 blocks
[  124.500573] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/556af45ed46f6917668e0f71a9887881fc3889b8
[  124.507589]   moving 5 blocks
[  124.509140]   moving 2 blocks
[  124.766146] stashing 2599 overlapping blocks to cd1bb270393a38e87eff7e8aa9d350b28971c562
[  124.766184] 174485504 bytes free on /cache (10645504 needed)
[  124.766232]  writing 2599 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cd1bb270393a38e87eff7e8aa9d350b28971c562
[  124.928482]   moving 2599 blocks
[  124.940324] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cd1bb270393a38e87eff7e8aa9d350b28971c562
[  125.018448]   moving 2 blocks
[  125.272924] stashing 2654 overlapping blocks to 405e18b9614a93f0cd6d048b8bad3ce67734d191
[  125.272957] 174485504 bytes free on /cache (10870784 needed)
[  125.273015]  writing 2654 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/405e18b9614a93f0cd6d048b8bad3ce67734d191
[  125.427946] patching 2654 blocks to 2653
[  125.889359] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/405e18b9614a93f0cd6d048b8bad3ce67734d191
[  126.038291] patching 4 blocks to 4
[  126.050971]   moving 20 blocks
[  126.053875]   moving 14 blocks
[  126.069417]   moving 6 blocks
[  126.071460]   moving 4 blocks
[  126.073322]   moving 7 blocks
[  126.079011] stashing 44 overlapping blocks to 9c2ff1263f18a5f03212ffea404a40c7c6480ab8
[  126.079062] 174485504 bytes free on /cache (180224 needed)
[  126.079106]  writing 44 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9c2ff1263f18a5f03212ffea404a40c7c6480ab8
[  126.086252]   moving 44 blocks
[  126.086468] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9c2ff1263f18a5f03212ffea404a40c7c6480ab8
[  126.091680]   moving 6 blocks
[  126.092953]   moving 4 blocks
[  126.094826]   moving 11 blocks
[  126.097383]   moving 12 blocks
[  126.105294] stashing 42 overlapping blocks to 851b04627cfeb5b9c8a1e5c99661d3a4fa8a104b
[  126.105332] 174485504 bytes free on /cache (172032 needed)
[  126.105387]  writing 42 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/851b04627cfeb5b9c8a1e5c99661d3a4fa8a104b
[  126.113528]   moving 42 blocks
[  126.113742] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/851b04627cfeb5b9c8a1e5c99661d3a4fa8a104b
[  126.121241] stashing 37 overlapping blocks to adab379041dc747428b49040fda5306487d63977
[  126.121271] 174485504 bytes free on /cache (151552 needed)
[  126.121323]  writing 37 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/adab379041dc747428b49040fda5306487d63977
[  126.128380]   moving 37 blocks
[  126.128572] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/adab379041dc747428b49040fda5306487d63977
[  126.133701]   moving 18 blocks
[  126.138211]   moving 5 blocks
[  126.141994]   moving 29 blocks
[  126.145995]   moving 5 blocks
[  126.147897]   moving 5 blocks
[  126.149798]   moving 8 blocks
[  126.153259]   moving 2 blocks
[  126.155485]   moving 12 blocks
[  126.158950]   moving 6 blocks
[  126.161828]   moving 12 blocks
[  126.169006] stashing 56 overlapping blocks to a23ff4876b504e8d34a5a54d5f41127333c8369b
[  126.169044] 174485504 bytes free on /cache (229376 needed)
[  126.169099]  writing 56 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a23ff4876b504e8d34a5a54d5f41127333c8369b
[  126.176193]   moving 56 blocks
[  126.176466] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a23ff4876b504e8d34a5a54d5f41127333c8369b
[  126.181747]   moving 6 blocks
[  126.183839]   moving 9 blocks
[  126.190804] stashing 41 overlapping blocks to 82e7c423cbcc9105fb58790d51078790eda5d9a3
[  126.190872] 174485504 bytes free on /cache (167936 needed)
[  126.190927]  writing 41 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/82e7c423cbcc9105fb58790d51078790eda5d9a3
[  126.196707]   moving 41 blocks
[  126.196918] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/82e7c423cbcc9105fb58790d51078790eda5d9a3
[  126.206224]   moving 34 blocks
[  126.210558]   moving 5 blocks
[  126.218356] stashing 64 overlapping blocks to da0b2451d4952629d975ed7a3d79b345443c8e4a
[  126.218397] 174485504 bytes free on /cache (262144 needed)
[  126.218453]  writing 64 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/da0b2451d4952629d975ed7a3d79b345443c8e4a
[  126.227388] patching 64 blocks to 64
[  126.246480] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/da0b2451d4952629d975ed7a3d79b345443c8e4a
[  126.253440]   moving 6 blocks
[  126.255170]   moving 5 blocks
[  126.259018]   moving 27 blocks
[  126.262887]   moving 13 blocks
[  126.267231]   moving 6 blocks
[  126.268817]   moving 5 blocks
[  126.270438]   moving 4 blocks
[  126.306848] stashing 355 overlapping blocks to 35b5de670793bc6df048f02030efb59adfa9bdc5
[  126.306880] 174485504 bytes free on /cache (1454080 needed)
[  126.306944]  writing 355 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/35b5de670793bc6df048f02030efb59adfa9bdc5
[  126.327812]   moving 355 blocks
[  126.329459] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/35b5de670793bc6df048f02030efb59adfa9bdc5
[  126.355983]   moving 23 blocks
[  126.359954]   moving 22 blocks
[  126.362082]   moving 2 blocks
[  126.363565]   moving 8 blocks
[  126.367735]   moving 32 blocks
[  126.373525]   moving 18 blocks
[  126.375596]   moving 7 blocks
[  126.378334]   moving 8 blocks
[  126.381479]   moving 4 blocks
[  126.383523]   moving 7 blocks
[  126.385941]   moving 12 blocks
[  126.389311]   moving 10 blocks
[  126.390895]   moving 5 blocks
[  126.393468]   moving 17 blocks
[  126.400630] stashing 36 overlapping blocks to f3d06d546dc7bd25275ce13ff706ea5ee4e58ff7
[  126.400655] 174485504 bytes free on /cache (147456 needed)
[  126.400702]  writing 36 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f3d06d546dc7bd25275ce13ff706ea5ee4e58ff7
[  126.406866]   moving 36 blocks
[  126.407061] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f3d06d546dc7bd25275ce13ff706ea5ee4e58ff7
[  126.413189]   moving 10 blocks
[  126.418140]   moving 19 blocks
[  126.423196]   moving 34 blocks
[  126.438061] stashing 112 overlapping blocks to 21a71167bd21bf7db38bd213aa1912c5f417a008
[  126.438086] 174485504 bytes free on /cache (458752 needed)
[  126.438135]  writing 112 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/21a71167bd21bf7db38bd213aa1912c5f417a008
[  126.449210]   moving 112 blocks
[  126.449746] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/21a71167bd21bf7db38bd213aa1912c5f417a008
[  126.456480]   moving 6 blocks
[  126.458647]   moving 6 blocks
[  126.460491]   moving 7 blocks
[  126.462353]   moving 7 blocks
[  126.464465]   moving 10 blocks
[  126.467568]   moving 5 blocks
[  126.469288]   moving 6 blocks
[  126.472445]   moving 20 blocks
[  126.476875]   moving 20 blocks
[  126.494637] stashing 143 overlapping blocks to 8b958a86dbbdceee5e701d1bdade65ae86b883ab
[  126.494661] 174485504 bytes free on /cache (585728 needed)
[  126.494708]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8b958a86dbbdceee5e701d1bdade65ae86b883ab
[  126.506789]   moving 143 blocks
[  126.507439] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8b958a86dbbdceee5e701d1bdade65ae86b883ab
[  126.518499]   moving 21 blocks
[  126.536045] stashing 143 overlapping blocks to 328e0ff7940fbdda2a177a5712414f42447e30d9
[  126.536070] 174485504 bytes free on /cache (585728 needed)
[  126.536119]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/328e0ff7940fbdda2a177a5712414f42447e30d9
[  126.548105]   moving 143 blocks
[  126.548746] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/328e0ff7940fbdda2a177a5712414f42447e30d9
[  126.556572] patching 5 blocks to 5
[  126.568409]   moving 11 blocks
[  126.570403]   moving 8 blocks
[  126.575403]   moving 18 blocks
[  126.579862]   moving 16 blocks
[  126.583658]   moving 6 blocks
[  126.586935]   moving 20 blocks
[  126.592371]   moving 20 blocks
[  126.610925] stashing 143 overlapping blocks to e2ee022ff27e90f4fef58b27971cea29f0dddf06
[  126.610957] 174485504 bytes free on /cache (585728 needed)
[  126.611009]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e2ee022ff27e90f4fef58b27971cea29f0dddf06
[  126.624156]   moving 143 blocks
[  126.624786] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e2ee022ff27e90f4fef58b27971cea29f0dddf06
[  126.635169]   moving 5 blocks
[  126.650833] stashing 143 overlapping blocks to 0c2da4035daf78b0191e1a4b9e2fdc96b8af8d90
[  126.650855] 174485504 bytes free on /cache (585728 needed)
[  126.650901]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0c2da4035daf78b0191e1a4b9e2fdc96b8af8d90
[  126.663328]   moving 143 blocks
[  126.664661] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0c2da4035daf78b0191e1a4b9e2fdc96b8af8d90
[  126.688395] stashing 143 overlapping blocks to bbf7311522d245d1ada1ec43493e0842b3a61f2f
[  126.688415] 174485504 bytes free on /cache (585728 needed)
[  126.688458]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bbf7311522d245d1ada1ec43493e0842b3a61f2f
[  126.700761]   moving 143 blocks
[  126.701440] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bbf7311522d245d1ada1ec43493e0842b3a61f2f
[  126.724531] stashing 143 overlapping blocks to b512b9b44c29ba3e6d8178f08892318219715fc4
[  126.724560] 174485504 bytes free on /cache (585728 needed)
[  126.724611]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b512b9b44c29ba3e6d8178f08892318219715fc4
[  126.740087]   moving 143 blocks
[  126.740764] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b512b9b44c29ba3e6d8178f08892318219715fc4
[  126.764301] stashing 143 overlapping blocks to 3b11bd76e4fbd83f48a442e4843755d268bf8ce2
[  126.764355] 174485504 bytes free on /cache (585728 needed)
[  126.764428]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3b11bd76e4fbd83f48a442e4843755d268bf8ce2
[  126.777615]   moving 143 blocks
[  126.778262] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3b11bd76e4fbd83f48a442e4843755d268bf8ce2
[  126.801561] stashing 143 overlapping blocks to 551db53beacf17f310ffbd9c561ff02f8f79c5e9
[  126.801593] 174485504 bytes free on /cache (585728 needed)
[  126.801622]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/551db53beacf17f310ffbd9c561ff02f8f79c5e9
[  126.813975]   moving 143 blocks
[  126.814686] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/551db53beacf17f310ffbd9c561ff02f8f79c5e9
[  126.837967] stashing 143 overlapping blocks to 45278a27ea903bcde05bbf5da9a071566c096bce
[  126.837980] 174485504 bytes free on /cache (585728 needed)
[  126.838026]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/45278a27ea903bcde05bbf5da9a071566c096bce
[  126.850989]   moving 143 blocks
[  126.851670] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/45278a27ea903bcde05bbf5da9a071566c096bce
[  126.874961] stashing 143 overlapping blocks to 077e525e2d4ca4f32d5b44a4f617a96a621feeb4
[  126.874990] 174485504 bytes free on /cache (585728 needed)
[  126.875033]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/077e525e2d4ca4f32d5b44a4f617a96a621feeb4
[  126.885603]   moving 143 blocks
[  126.886252] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/077e525e2d4ca4f32d5b44a4f617a96a621feeb4
[  126.909330] stashing 143 overlapping blocks to ad5872f17df9736bc196751ef02ec297a66c7a05
[  126.909350] 174485504 bytes free on /cache (585728 needed)
[  126.909395]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad5872f17df9736bc196751ef02ec297a66c7a05
[  126.922187]   moving 143 blocks
[  126.922836] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad5872f17df9736bc196751ef02ec297a66c7a05
[  126.946590] stashing 143 overlapping blocks to ad3b4b5bca4c2a0cfb30566f0549e2bd63456452
[  126.946611] 174485504 bytes free on /cache (585728 needed)
[  126.946661]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad3b4b5bca4c2a0cfb30566f0549e2bd63456452
[  126.959276]   moving 143 blocks
[  126.959933] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad3b4b5bca4c2a0cfb30566f0549e2bd63456452
[  126.982497] stashing 143 overlapping blocks to 7d4603c1a27c0262031a165792c7c3bf0a815812
[  126.982527] 174485504 bytes free on /cache (585728 needed)
[  126.982576]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d4603c1a27c0262031a165792c7c3bf0a815812
[  126.994423]   moving 143 blocks
[  126.995099] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d4603c1a27c0262031a165792c7c3bf0a815812
[  127.004630]   moving 5 blocks
[  127.007599]   moving 20 blocks
[  127.009533]   moving 7 blocks
[  127.011794]   moving 8 blocks
[  127.016394]   moving 14 blocks
[  127.021158]   moving 13 blocks
[  127.023790]   moving 14 blocks
[  127.026495]   moving 14 blocks
[  127.029931]   moving 7 blocks
[  127.032558]   moving 14 blocks
[  127.035808]   moving 8 blocks
[  127.037723]   moving 9 blocks
[  127.040642]   moving 18 blocks
[  127.045137]   moving 9 blocks
[  127.046797]   moving 7 blocks
[  127.049100]   moving 11 blocks
[  127.051375]   moving 11 blocks
[  127.055261]   moving 11 blocks
[  127.058923]   moving 7 blocks
[  127.060852]   moving 7 blocks
[  127.062765]   moving 7 blocks
[  127.064619]   moving 6 blocks
[  127.066247]   moving 6 blocks
[  127.068466]   moving 10 blocks
[  127.070241]   moving 6 blocks
[  127.071947]   moving 6 blocks
[  127.073605]   moving 6 blocks
[  127.075172]   moving 7 blocks
[  127.077179]   moving 9 blocks
[  127.078854]   moving 7 blocks
[  127.080713]   moving 7 blocks
[  127.083548]   moving 7 blocks
[  127.085939]   moving 12 blocks
[  127.089819]   moving 7 blocks
[  127.091773]   moving 7 blocks
[  127.093683]   moving 8 blocks
[  127.099655]   moving 29 blocks
[  127.117986] stashing 145 overlapping blocks to 761bb9649be2d71fdfefc3e88f91560eb395ac77
[  127.118018] 174485504 bytes free on /cache (593920 needed)
[  127.118066]  writing 145 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/761bb9649be2d71fdfefc3e88f91560eb395ac77
[  127.129433]   moving 145 blocks
[  127.130073] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/761bb9649be2d71fdfefc3e88f91560eb395ac77
[  127.153955] stashing 143 overlapping blocks to b59eef0033df35aaa9983b813048b10a5434b571
[  127.153973] 174485504 bytes free on /cache (585728 needed)
[  127.154019]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b59eef0033df35aaa9983b813048b10a5434b571
[  127.166754]   moving 143 blocks
[  127.167396] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b59eef0033df35aaa9983b813048b10a5434b571
[  127.189187] stashing 118 overlapping blocks to 6e11383add96a6e456fe5f1274c52b56c9825c25
[  127.189207] 174485504 bytes free on /cache (483328 needed)
[  127.189253]  writing 118 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6e11383add96a6e456fe5f1274c52b56c9825c25
[  127.201268]   moving 118 blocks
[  127.201824] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6e11383add96a6e456fe5f1274c52b56c9825c25
[  127.213983] stashing 40 overlapping blocks to c0afad3453f39563b43f2000f853af4cf90db6c1
[  127.214005] 174485504 bytes free on /cache (163840 needed)
[  127.214050]  writing 40 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c0afad3453f39563b43f2000f853af4cf90db6c1
[  127.220367]   moving 40 blocks
[  127.220571] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c0afad3453f39563b43f2000f853af4cf90db6c1
[  127.228096]   moving 20 blocks
[  127.231569]   moving 22 blocks
[  127.265067] stashing 305 overlapping blocks to 1c28241214873f8a1e28ffe8e400aa4386702d0d
[  127.265091] 174485504 bytes free on /cache (1249280 needed)
[  127.265142]  writing 305 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1c28241214873f8a1e28ffe8e400aa4386702d0d
[  127.282194]   moving 305 blocks
[  127.283601] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1c28241214873f8a1e28ffe8e400aa4386702d0d
[  127.297393]   moving 7 blocks
[  127.299267]   moving 5 blocks
[  127.301008]   moving 6 blocks
[  127.302563]   moving 4 blocks
[  127.305591]   moving 21 blocks
[  127.311582]   moving 20 blocks
[  127.315098]   moving 4 blocks
[  127.317155]   moving 8 blocks
[  127.319004]   moving 9 blocks
[  127.322628]   moving 5 blocks
[  127.328637] stashing 44 overlapping blocks to 5217ea645490467634c87b424c7f33cafd83fab8
[  127.328670] 174485504 bytes free on /cache (180224 needed)
[  127.328722]  writing 44 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5217ea645490467634c87b424c7f33cafd83fab8
[  127.335107]   moving 44 blocks
[  127.335329] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5217ea645490467634c87b424c7f33cafd83fab8
[  127.353897] stashing 143 overlapping blocks to d64233fc9f4c99ceeeac72757279af1e62f7d392
[  127.353933] 174485504 bytes free on /cache (585728 needed)
[  127.353986]  writing 143 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d64233fc9f4c99ceeeac72757279af1e62f7d392
[  127.366631]   moving 143 blocks
[  127.367306] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d64233fc9f4c99ceeeac72757279af1e62f7d392
[  127.386050] stashing 93 overlapping blocks to 7d24cba2b9373a185f14349c176de39587d871be
[  127.386086] 174485504 bytes free on /cache (380928 needed)
[  127.386130]  writing 93 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d24cba2b9373a185f14349c176de39587d871be
[  127.396438] patching 93 blocks to 93
[  127.418341] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d24cba2b9373a185f14349c176de39587d871be
[  127.427378]   moving 14 blocks
[  127.447263] stashing 87 overlapping blocks to 4f6917726facc89308100477323a1fc7076ce203
[  127.447294] 174485504 bytes free on /cache (356352 needed)
[  127.447351]  writing 87 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4f6917726facc89308100477323a1fc7076ce203
[  127.457296] patching 87 blocks to 87
[  127.476697] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4f6917726facc89308100477323a1fc7076ce203
[  127.496328] stashing 142 overlapping blocks to 489f62aaa24fb91790451015430548378ba8f06f
[  127.496361] 174485504 bytes free on /cache (581632 needed)
[  127.496417]  writing 142 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/489f62aaa24fb91790451015430548378ba8f06f
[  127.508981]   moving 142 blocks
[  127.509640] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/489f62aaa24fb91790451015430548378ba8f06f
[  127.520424]   moving 17 blocks
[  127.522193]   moving 6 blocks
[  127.523879]   moving 5 blocks
[  127.528434] patching 34 blocks to 34
[  127.553785] stashing 73 overlapping blocks to 2d3f44502610bc73bbcdcab1dee8d0921035e681
[  127.553817] 174485504 bytes free on /cache (299008 needed)
[  127.553871]  writing 73 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2d3f44502610bc73bbcdcab1dee8d0921035e681
[  127.563646]   moving 73 blocks
[  127.563960] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2d3f44502610bc73bbcdcab1dee8d0921035e681
[  127.573290]   moving 29 blocks
[  127.588913] stashing 117 overlapping blocks to 0dc56040bc452e07cb2f84645b55c55411551e68
[  127.588945] 174485504 bytes free on /cache (479232 needed)
[  127.589004]  writing 117 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0dc56040bc452e07cb2f84645b55c55411551e68
[  127.598755] patching 117 blocks to 117
[  127.624018] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0dc56040bc452e07cb2f84645b55c55411551e68
[  127.638814] stashing 61 overlapping blocks to 8d92170d09c42cfe42507fe5c8fcaccb32f4de27
[  127.638842] 174485504 bytes free on /cache (249856 needed)
[  127.638899]  writing 61 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8d92170d09c42cfe42507fe5c8fcaccb32f4de27
[  127.647835] patching 61 blocks to 61
[  127.665176] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8d92170d09c42cfe42507fe5c8fcaccb32f4de27
[  127.670319]   moving 5 blocks
[  127.674076]   moving 26 blocks
[  127.686117] stashing 81 overlapping blocks to cc46c6e2c234bbfa6a5296fe24adb3cd6d62856a
[  127.686145] 174485504 bytes free on /cache (331776 needed)
[  127.686203]  writing 81 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cc46c6e2c234bbfa6a5296fe24adb3cd6d62856a
[  127.695690]   moving 81 blocks
[  127.696039] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cc46c6e2c234bbfa6a5296fe24adb3cd6d62856a
[  127.703304]   moving 21 blocks
[  127.708994]   moving 24 blocks
[  127.715361]   moving 30 blocks
[  127.719429]   moving 9 blocks
[  127.723539]   moving 10 blocks
[  127.727113]   moving 8 blocks
[  127.728865]   moving 8 blocks
[  127.731666]   moving 19 blocks
[  127.735942]   moving 7 blocks
[  127.739889]   moving 27 blocks
[  127.744318]   moving 5 blocks
[  127.748630]   moving 31 blocks
[  127.752469]   moving 7 blocks
[  127.755082]   moving 14 blocks
[  127.760877]   moving 31 blocks
[  127.769716] stashing 57 overlapping blocks to f291248c50fbc31e7660e458c423b5f6f236042c
[  127.769735] 174485504 bytes free on /cache (233472 needed)
[  127.769789]  writing 57 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f291248c50fbc31e7660e458c423b5f6f236042c
[  127.776250]   moving 57 blocks
[  127.776528] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f291248c50fbc31e7660e458c423b5f6f236042c
[  127.781774]   moving 8 blocks
[  127.792501] stashing 97 overlapping blocks to 0dea5910d2ba94629cbc0ee93a98bb6d2ea7e0a8
[  127.792536] 174485504 bytes free on /cache (397312 needed)
[  127.792587]  writing 97 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0dea5910d2ba94629cbc0ee93a98bb6d2ea7e0a8
[  127.803304]   moving 97 blocks
[  127.803769] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0dea5910d2ba94629cbc0ee93a98bb6d2ea7e0a8
[  127.818671]   moving 17 blocks
[  127.822578]   moving 4 blocks
[  127.824331]   moving 4 blocks
[  127.827925]   moving 25 blocks
[  127.831838]   moving 6 blocks
[  127.841302] stashing 66 overlapping blocks to a9035eac88ed6540f12bda038ead5db828cf4468
[  127.841321] 174485504 bytes free on /cache (270336 needed)
[  127.841375]  writing 66 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a9035eac88ed6540f12bda038ead5db828cf4468
[  127.850933]   moving 66 blocks
[  127.851257] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a9035eac88ed6540f12bda038ead5db828cf4468
[  127.860036]   moving 28 blocks
[  128.113817] stashing 2578 overlapping blocks to fabada551884ef23a5035862112abb10f325479f
[  128.113850] 174485504 bytes free on /cache (10559488 needed)
[  128.113909]  writing 2578 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fabada551884ef23a5035862112abb10f325479f
[  128.262456]   moving 2578 blocks
[  128.274921] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fabada551884ef23a5035862112abb10f325479f
[  128.900922] stashing 5678 overlapping blocks to b522867d3682b0d373766d5ed3847653d372f0eb
[  128.900953] 174485504 bytes free on /cache (23257088 needed)
[  128.901016]  writing 5678 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b522867d3682b0d373766d5ed3847653d372f0eb
[  129.241570]   moving 5678 blocks
[  129.267312] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b522867d3682b0d373766d5ed3847653d372f0eb
[  129.695866] stashing 2578 overlapping blocks to 6390e2c2ad8abd94d0e2cc8dbdd72c29e972de41
[  129.695897] 174485504 bytes free on /cache (10559488 needed)
[  129.695960]  writing 2578 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6390e2c2ad8abd94d0e2cc8dbdd72c29e972de41
[  129.885532]   moving 2578 blocks
[  129.897121] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6390e2c2ad8abd94d0e2cc8dbdd72c29e972de41
[  129.980100]   moving 5 blocks
[  129.982778]   moving 18 blocks
[  129.985625]   moving 4 blocks
[  129.987107]   moving 6 blocks
[  129.989183]   moving 9 blocks
[  129.990900]   moving 7 blocks
[  129.992860]   moving 5 blocks
[  129.995124]   moving 11 blocks
[  129.999695]   moving 9 blocks
[  130.003669]   moving 9 blocks
[  130.005374]   moving 8 blocks
[  130.010474] stashing 37 overlapping blocks to cbb77c42ebcafd237274e5017605894be9a5afc2
[  130.010513] 174485504 bytes free on /cache (151552 needed)
[  130.010568]  writing 37 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cbb77c42ebcafd237274e5017605894be9a5afc2
[  130.016364]   moving 37 blocks
[  130.016534] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cbb77c42ebcafd237274e5017605894be9a5afc2
[  130.022994]   moving 30 blocks
[  130.026890]   moving 10 blocks
[  130.028313]   moving 5 blocks
[  130.029812]   moving 4 blocks
[  130.031431]   moving 5 blocks
[  130.036018]   moving 31 blocks
[  130.041815]   moving 23 blocks
[  130.076346] stashing 321 overlapping blocks to 2a9f49f6b2f3d0152f8f43fbbc061ab40c4ed0e0
[  130.076372] 174485504 bytes free on /cache (1314816 needed)
[  130.076431]  writing 321 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2a9f49f6b2f3d0152f8f43fbbc061ab40c4ed0e0
[  130.095139]   moving 321 blocks
[  130.096607] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2a9f49f6b2f3d0152f8f43fbbc061ab40c4ed0e0
[  130.124773]   moving 6 blocks
[  130.127611]   moving 17 blocks
[  130.132537] stashing 36 overlapping blocks to f893c8fd9039c08c378c019bb173e4298a24daa2
[  130.132557] 174485504 bytes free on /cache (147456 needed)
[  130.132606]  writing 36 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f893c8fd9039c08c378c019bb173e4298a24daa2
[  130.138758]   moving 36 blocks
[  130.139040] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f893c8fd9039c08c378c019bb173e4298a24daa2
[  130.143408]   moving 12 blocks
[  130.148640]   moving 14 blocks
[  130.152976]   moving 4 blocks
[  130.157093]   moving 29 blocks
[  130.162381]   moving 13 blocks
[  130.166556]   moving 12 blocks
[  130.168717]   moving 9 blocks
[  130.170174]   moving 5 blocks
[  130.172317]   moving 8 blocks
[  130.176108]   moving 5 blocks
[  130.178525]   moving 16 blocks
[  130.182059]   moving 6 blocks
[  130.183627]   moving 7 blocks
[  130.185415]   moving 6 blocks
[  130.187629]   moving 6 blocks
[  130.189244]   moving 4 blocks
[  130.360385] stashing 1740 overlapping blocks to 97bd671251a409e1ebc938475aa8c206177c20ae
[  130.360416] 174485504 bytes free on /cache (7127040 needed)
[  130.360474]  writing 1740 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/97bd671251a409e1ebc938475aa8c206177c20ae
[  130.434358]   moving 1740 blocks
[  130.442547] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/97bd671251a409e1ebc938475aa8c206177c20ae
[  130.505654] stashing 68 overlapping blocks to b532b31d966e6248717f881b160b6c504ee76adf
[  130.505741] 174485504 bytes free on /cache (278528 needed)
[  130.505814]  writing 68 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b532b31d966e6248717f881b160b6c504ee76adf
[  130.515600]   moving 68 blocks
[  130.515941] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b532b31d966e6248717f881b160b6c504ee76adf
[  130.522398]   moving 5 blocks
[  130.524258]   moving 5 blocks
[  130.526114]   moving 8 blocks
[  130.531034]   moving 21 blocks
[  130.545941] stashing 122 overlapping blocks to b15f8b3df9b125311ad4c42782add8e5d1b9b6af
[  130.545974] 174485504 bytes free on /cache (499712 needed)
[  130.546029]  writing 122 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b15f8b3df9b125311ad4c42782add8e5d1b9b6af
[  130.557854]   moving 122 blocks
[  130.558427] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b15f8b3df9b125311ad4c42782add8e5d1b9b6af
[  130.566540]   moving 11 blocks
[  130.570185]   moving 7 blocks
[  130.571840]   moving 5 blocks
[  130.573805]   moving 5 blocks
[  130.575401]   moving 7 blocks
[  130.577998]   moving 15 blocks
[  130.585326] stashing 58 overlapping blocks to aa3050afdaa79832455354dcadc8e4ba87d626e0
[  130.585346] 174485504 bytes free on /cache (237568 needed)
[  130.585391]  writing 58 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aa3050afdaa79832455354dcadc8e4ba87d626e0
[  130.593213]   moving 58 blocks
[  130.593504] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aa3050afdaa79832455354dcadc8e4ba87d626e0
[  130.601069]   moving 29 blocks
[  130.607626]   moving 29 blocks
[  130.613193] stashing 14 overlapping blocks to 94ed02e915f7d47a22cfbbfb17fbf75811ceee30
[  130.613211] 174485504 bytes free on /cache (57344 needed)
[  130.613258]  writing 14 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/94ed02e915f7d47a22cfbbfb17fbf75811ceee30
[  130.618326]   moving 14 blocks
[  130.618417] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/94ed02e915f7d47a22cfbbfb17fbf75811ceee30
[  130.625305] stashing 38 overlapping blocks to 01bb1b18d7868a1768a9813e371ecf7b526a336b
[  130.625325] 174485504 bytes free on /cache (155648 needed)
[  130.625371]  writing 38 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/01bb1b18d7868a1768a9813e371ecf7b526a336b
[  130.632586]   moving 38 blocks
[  130.632786] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/01bb1b18d7868a1768a9813e371ecf7b526a336b
[  130.698430]   moving 546 blocks
[  130.719053]   moving 2 blocks
[  130.720507]   moving 5 blocks
[  130.722384]   moving 6 blocks
[  130.724286]   moving 4 blocks
[  130.725714]   moving 1 blocks
[  130.726787]   moving 4 blocks
[  130.728177]   moving 5 blocks
[  130.732065]   moving 30 blocks
[  130.736575] stashing 8 overlapping blocks to a32a6852b0d2fe50771f120e910aedfcccfe1925
[  130.736614] 174485504 bytes free on /cache (32768 needed)
[  130.736669]  writing 8 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a32a6852b0d2fe50771f120e910aedfcccfe1925
[  130.741487]   moving 8 blocks
[  130.741556] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a32a6852b0d2fe50771f120e910aedfcccfe1925
[  130.752597] stashing 96 overlapping blocks to 70491d914f78bc665d2180ed3593c71bd07e5f24
[  130.752663] 174485504 bytes free on /cache (393216 needed)
[  130.752724]  writing 96 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/70491d914f78bc665d2180ed3593c71bd07e5f24
[  130.763373]   moving 96 blocks
[  130.763829] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/70491d914f78bc665d2180ed3593c71bd07e5f24
[  130.771125]   moving 2 blocks
[  130.772072]   moving 2 blocks
[  130.773565]   moving 5 blocks
[  130.776865] stashing 15 overlapping blocks to 68b05248aeefa76d5139fd979ad497aca53eb921
[  130.776907] 174485504 bytes free on /cache (61440 needed)
[  130.776962]  writing 15 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/68b05248aeefa76d5139fd979ad497aca53eb921
[  130.782047]   moving 15 blocks
[  130.782143] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/68b05248aeefa76d5139fd979ad497aca53eb921
[  130.787158]   moving 17 blocks
[  130.791686]   moving 17 blocks
[  130.794846]   moving 1 blocks
[  130.795773]   moving 2 blocks
[  130.798246]   moving 15 blocks
[  130.802620]   moving 15 blocks
[  130.989496] stashing 1936 overlapping blocks to 1583b3de7f7626026b018404a0b5dc018ec19fff
[  130.989528] 174485504 bytes free on /cache (7929856 needed)
[  130.989587]  writing 1936 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1583b3de7f7626026b018404a0b5dc018ec19fff
[  131.072626] patching 1936 blocks to 1937
[  135.940820] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1583b3de7f7626026b018404a0b5dc018ec19fff
[  136.199377]   moving 5 blocks
[  136.202529]   moving 17 blocks
[  136.205168]   moving 15 blocks
[  137.020914] stashing 7896 overlapping blocks to 3461e42fc29d96ff19231a2f60cbed456df5c147
[  137.020952] 174485504 bytes free on /cache (32342016 needed)
[  137.021000]  writing 7896 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3461e42fc29d96ff19231a2f60cbed456df5c147
[  137.413655]   moving 7896 blocks
[  137.451055] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3461e42fc29d96ff19231a2f60cbed456df5c147
[  137.679146]   moving 12 blocks
[  137.683253]   moving 9 blocks
[  137.687325]   moving 12 blocks
[  137.740589] stashing 540 overlapping blocks to d47e2e6ee0d963cf351a180336a214754bbccb95
[  137.740622] 174485504 bytes free on /cache (2211840 needed)
[  137.740682]  writing 540 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d47e2e6ee0d963cf351a180336a214754bbccb95
[  137.783933]   moving 540 blocks
[  137.786487] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d47e2e6ee0d963cf351a180336a214754bbccb95
[  137.806553]   moving 12 blocks
[  137.810623]   moving 12 blocks
[  137.813091]   moving 12 blocks
[  137.814769]   moving 3 blocks
[  137.817511]   moving 17 blocks
[  137.822213]   moving 17 blocks
[  137.824405]   moving 2 blocks
[  137.827029]   moving 17 blocks
[  137.832046]   moving 15 blocks
[  137.835912]   moving 3 blocks
[  137.837065]   moving 1 blocks
[  137.838414]   moving 5 blocks
[  137.840937]   moving 17 blocks
[  137.843780]   moving 15 blocks
[  137.899747] stashing 554 overlapping blocks to 0c41691bb1677fe7b0655075b1eb8ac78d25c59a
[  137.899780] 174485504 bytes free on /cache (2269184 needed)
[  137.899839]  writing 554 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0c41691bb1677fe7b0655075b1eb8ac78d25c59a
[  137.927941] patching 554 blocks to 554
[  138.989010] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0c41691bb1677fe7b0655075b1eb8ac78d25c59a
[  139.047621]   moving 17 blocks
[  139.052606]   moving 15 blocks
[  139.088350] stashing 342 overlapping blocks to 248328722a6edacebc2ace8406a5305810206516
[  139.088383] 174485504 bytes free on /cache (1400832 needed)
[  139.088438]  writing 342 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/248328722a6edacebc2ace8406a5305810206516
[  139.108642]   moving 342 blocks
[  139.110236] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/248328722a6edacebc2ace8406a5305810206516
[  139.125843]   moving 12 blocks
[  139.128357]   moving 12 blocks
[  139.129738]   moving 3 blocks
[  139.131945]   moving 12 blocks
[  139.136801]   moving 12 blocks
[  139.141690]   moving 12 blocks
[  139.147745] stashing 40 overlapping blocks to 9385dad588f3d9640e9b83119750b729e0aac0fe
[  139.147778] 174485504 bytes free on /cache (163840 needed)
[  139.147831]  writing 40 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9385dad588f3d9640e9b83119750b729e0aac0fe
[  139.154064]   moving 40 blocks
[  139.154310] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9385dad588f3d9640e9b83119750b729e0aac0fe
[  139.160094]   moving 17 blocks
[  139.164541]   moving 17 blocks
[  139.171541] stashing 32 overlapping blocks to 23aac63ca2877d9d6bdd9f0170397772dcd30140
[  139.171578] 174485504 bytes free on /cache (131072 needed)
[  139.171630]  writing 32 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/23aac63ca2877d9d6bdd9f0170397772dcd30140
[  139.176983]   moving 32 blocks
[  139.177153] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/23aac63ca2877d9d6bdd9f0170397772dcd30140
[  139.183794]   moving 15 blocks
[  139.187771]   moving 5 blocks
[  139.190823]   moving 17 blocks
[  139.192571]   moving 4 blocks
[  139.195737]   moving 15 blocks
[  139.200109]   moving 17 blocks
[  139.203317] stashing 6 overlapping blocks to 383420362291a245d5596b6969fbba5be7c51cd8
[  139.203351] 174485504 bytes free on /cache (24576 needed)
[  139.203403]  writing 6 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/383420362291a245d5596b6969fbba5be7c51cd8
[  139.207409]   moving 6 blocks
[  139.207486] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/383420362291a245d5596b6969fbba5be7c51cd8
[  139.210188]   moving 15 blocks
[  139.212690]   moving 12 blocks
[  139.217583] stashing 13 overlapping blocks to 499eaa0844067a5aee832983570116eac421600b
[  139.217617] 174485504 bytes free on /cache (53248 needed)
[  139.217669]  writing 13 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/499eaa0844067a5aee832983570116eac421600b
[  139.223436]   moving 13 blocks
[  139.223515] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/499eaa0844067a5aee832983570116eac421600b
[  139.226332]   moving 12 blocks
[  139.229219]   moving 12 blocks
[  139.233049]   moving 12 blocks
[  139.236730] stashing 12 overlapping blocks to df24cf9b7371afa181de60947385c6512a9c954a
[  139.236765] 174485504 bytes free on /cache (49152 needed)
[  139.236818]  writing 12 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/df24cf9b7371afa181de60947385c6512a9c954a
[  139.241087]   moving 12 blocks
[  139.241168] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/df24cf9b7371afa181de60947385c6512a9c954a
[  139.244243]   moving 17 blocks
[  139.249170]   moving 17 blocks
[  139.251729] stashing 11 overlapping blocks to 5f40521778305104a4d173a0b81bcc5b102486e5
[  139.251763] 174485504 bytes free on /cache (45056 needed)
[  139.251815]  writing 11 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5f40521778305104a4d173a0b81bcc5b102486e5
[  139.256521]   moving 11 blocks
[  139.256595] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5f40521778305104a4d173a0b81bcc5b102486e5
[  139.261423]   moving 17 blocks
[  139.264309]   moving 15 blocks
[  139.267571]   moving 1 blocks
[  139.268381]   moving 1 blocks
[  139.269139]   moving 1 blocks
[  139.271331]   moving 15 blocks
[  139.274780]   moving 5 blocks
[  139.277148]   moving 15 blocks
[  139.279647] stashing 8 overlapping blocks to 5feb9c7671ce59a5fa65e139abda7f0acb5b4950
[  139.279686] 174485504 bytes free on /cache (32768 needed)
[  139.279738]  writing 8 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5feb9c7671ce59a5fa65e139abda7f0acb5b4950
[  139.283310]   moving 8 blocks
[  139.283365] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5feb9c7671ce59a5fa65e139abda7f0acb5b4950
[  139.287889]   moving 17 blocks
[  139.292249]   moving 17 blocks
[  139.296106]   moving 3 blocks
[  139.298377]   moving 15 blocks
[  139.302621]   moving 15 blocks
[  139.305130] stashing 6 overlapping blocks to 0b51dbfed71024cb99d7dd8a3b0528fc8aaf1502
[  139.305141] 174485504 bytes free on /cache (24576 needed)
[  139.305174]  writing 6 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0b51dbfed71024cb99d7dd8a3b0528fc8aaf1502
[  139.308766]   moving 6 blocks
[  139.308812] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0b51dbfed71024cb99d7dd8a3b0528fc8aaf1502
[  139.311972]   moving 15 blocks
[  139.314520]   moving 12 blocks
[  139.317505]   moving 1 blocks
[  139.319489]   moving 12 blocks
[  139.322941]   moving 12 blocks
[  139.325483]   moving 12 blocks
[  139.328591] stashing 13 overlapping blocks to e6f462a4a432411f5636afb380fb41f933e9da3b
[  139.328624] 174485504 bytes free on /cache (53248 needed)
[  139.328672]  writing 13 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e6f462a4a432411f5636afb380fb41f933e9da3b
[  139.334685]   moving 13 blocks
[  139.334764] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e6f462a4a432411f5636afb380fb41f933e9da3b
[  139.338833]   moving 12 blocks
[  139.341518]   moving 12 blocks
[  139.344441]   moving 12 blocks
[  139.348128] stashing 9 overlapping blocks to 89e61ab3d7241b4777ba878551ba6ba6b89c838c
[  139.348141] 174485504 bytes free on /cache (36864 needed)
[  139.348183]  writing 9 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/89e61ab3d7241b4777ba878551ba6ba6b89c838c
[  139.352117]   moving 9 blocks
[  139.352180] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/89e61ab3d7241b4777ba878551ba6ba6b89c838c
[  139.354369]   moving 12 blocks
[  139.359266]   moving 12 blocks
[  139.364077]   moving 12 blocks
[  139.370157] stashing 43 overlapping blocks to 7bee74ce5fe1e4f02a4c1d34aee64662019a54c1
[  139.370174] 174485504 bytes free on /cache (176128 needed)
[  139.370209]  writing 43 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7bee74ce5fe1e4f02a4c1d34aee64662019a54c1
[  139.376390]   moving 43 blocks
[  139.376608] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7bee74ce5fe1e4f02a4c1d34aee64662019a54c1
[  139.382371]   moving 17 blocks
[  139.386460]   moving 17 blocks
[  139.390492]   moving 1 blocks
[  139.391391]   moving 2 blocks
[  139.394377]   moving 17 blocks
[  139.399412]   moving 17 blocks
[  139.408432]   moving 4 blocks
[  139.410996]   moving 17 blocks
[  139.414902]   moving 15 blocks
[  139.416582]   moving 1 blocks
[  139.417537]   moving 2 blocks
[  139.418633]   moving 1 blocks
[  139.420728]   moving 15 blocks
[  139.425878]   moving 15 blocks
[  139.442781] stashing 134 overlapping blocks to 65dd29d4e27ab328dbe2aadd0f95fe58ab367a0d
[  139.442798] 174485504 bytes free on /cache (548864 needed)
[  139.442835]  writing 134 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/65dd29d4e27ab328dbe2aadd0f95fe58ab367a0d
[  139.455696]   moving 134 blocks
[  139.456321] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/65dd29d4e27ab328dbe2aadd0f95fe58ab367a0d
[  139.465428]   moving 5 blocks
[  139.468471]   moving 17 blocks
[  139.471072]   moving 15 blocks
[  139.474129]   moving 3 blocks
[  139.476633]   moving 17 blocks
[  139.480725]   moving 17 blocks
[  139.484418]   moving 1 blocks
[  139.485260]   moving 1 blocks
[  139.487126]   moving 12 blocks
[  139.491255]   moving 12 blocks
[  139.493388]   moving 9 blocks
[  139.495821] stashing 7 overlapping blocks to 8902c2247a757b794d3d5e88452044289e21cfb2
[  139.495836] 174485504 bytes free on /cache (28672 needed)
[  139.495877]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8902c2247a757b794d3d5e88452044289e21cfb2
[  139.499355] patching 7 blocks to 7
[  139.508020] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8902c2247a757b794d3d5e88452044289e21cfb2
[  139.510823]   moving 12 blocks
[  139.514656]   moving 12 blocks
[  139.518797]   moving 12 blocks
[  139.522014] stashing 13 overlapping blocks to 623320dd3ba33d390081989eeb2e6d45b7999012
[  139.522045] 174485504 bytes free on /cache (53248 needed)
[  139.522093]  writing 13 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/623320dd3ba33d390081989eeb2e6d45b7999012
[  139.525745] patching 13 blocks to 13
[  139.534631] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/623320dd3ba33d390081989eeb2e6d45b7999012
[  139.539351]   moving 12 blocks
[  139.543859]   moving 17 blocks
[  139.549456] stashing 41 overlapping blocks to 750e4a8663bdde110bc9923f2d45e3d631cbbab0
[  139.549493] 174485504 bytes free on /cache (167936 needed)
[  139.549547]  writing 41 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/750e4a8663bdde110bc9923f2d45e3d631cbbab0
[  139.557487]   moving 41 blocks
[  139.557669] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/750e4a8663bdde110bc9923f2d45e3d631cbbab0
[  139.562979]   moving 17 blocks
[  139.568525]   moving 17 blocks
[  139.573757] stashing 21 overlapping blocks to bf2b9b85c910c222da4a1131714183878102a415
[  139.573773] 174485504 bytes free on /cache (86016 needed)
[  139.573814]  writing 21 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bf2b9b85c910c222da4a1131714183878102a415
[  139.579280] patching 21 blocks to 21
[  139.588861] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bf2b9b85c910c222da4a1131714183878102a415
[  139.593785]   moving 15 blocks
[  139.596786]   moving 15 blocks
[  139.601670] stashing 9 overlapping blocks to e418a9be7cde2c7b16be41bbf0daa911e97c69cd
[  139.601685] 174485504 bytes free on /cache (36864 needed)
[  139.601730]  writing 9 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e418a9be7cde2c7b16be41bbf0daa911e97c69cd
[  139.606249] patching 9 blocks to 9
[  139.614922] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e418a9be7cde2c7b16be41bbf0daa911e97c69cd
[  139.617681]   moving 15 blocks
[  139.622617]   moving 17 blocks
[  139.625988] stashing 5 overlapping blocks to af7bad8550300dc0981e976864381feff3af4d39
[  139.626019] 174485504 bytes free on /cache (20480 needed)
[  139.626065]  writing 5 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/af7bad8550300dc0981e976864381feff3af4d39
[  139.629844]   moving 5 blocks
[  139.629892] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/af7bad8550300dc0981e976864381feff3af4d39
[  139.632511]   moving 15 blocks
[  139.635927]   moving 17 blocks
[  139.640017] stashing 7 overlapping blocks to 2780ca9f8125dffa36ed010202804bfeba134843
[  139.640032] 174485504 bytes free on /cache (28672 needed)
[  139.640072]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2780ca9f8125dffa36ed010202804bfeba134843
[  139.643785] patching 7 blocks to 7
[  139.652238] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2780ca9f8125dffa36ed010202804bfeba134843
[  139.655152]   moving 15 blocks
[  139.658355]   moving 17 blocks
[  139.661841] patching 3 blocks to 3
[  139.672894]   moving 17 blocks
[  139.676707]   moving 12 blocks
[  139.752508] stashing 774 overlapping blocks to ac1ff5978653e5321c2c56feea7d1338814c9f8c
[  139.752540] 174485504 bytes free on /cache (3170304 needed)
[  139.752599]  writing 774 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ac1ff5978653e5321c2c56feea7d1338814c9f8c
[  139.789718] patching 774 blocks to 774
[  139.834390] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ac1ff5978653e5321c2c56feea7d1338814c9f8c
[  139.863524]   moving 12 blocks
[  139.865661]   moving 9 blocks
[  139.870236]   moving 12 blocks
[  139.941820] stashing 709 overlapping blocks to a0e10ef24494ffd7ad987f9f5a84f75df5d4f6d0
[  139.941853] 174485504 bytes free on /cache (2904064 needed)
[  139.941914]  writing 709 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a0e10ef24494ffd7ad987f9f5a84f75df5d4f6d0
[  139.989908] patching 709 blocks to 709
[  140.060925] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a0e10ef24494ffd7ad987f9f5a84f75df5d4f6d0
[  140.087513]   moving 12 blocks
[  140.090219]   moving 12 blocks
[  140.098627] stashing 69 overlapping blocks to 50712728b5510dcfe8a5a1fa1f66c00bef2394ca
[  140.098657] 174485504 bytes free on /cache (282624 needed)
[  140.098715]  writing 69 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/50712728b5510dcfe8a5a1fa1f66c00bef2394ca
[  140.108003] patching 69 blocks to 69
[  140.119646] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/50712728b5510dcfe8a5a1fa1f66c00bef2394ca
[  140.127039]   moving 12 blocks
[  140.130914]   moving 12 blocks
[  140.133391]   moving 1 blocks
[  140.135777]   moving 17 blocks
[  140.138623]   moving 15 blocks
[  140.142757] stashing 7 overlapping blocks to 21b98c3c374d510fb1f00e7c66143167c8290afd
[  140.142791] 174485504 bytes free on /cache (28672 needed)
[  140.142839]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/21b98c3c374d510fb1f00e7c66143167c8290afd
[  140.146653] patching 7 blocks to 7
[  140.155200] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/21b98c3c374d510fb1f00e7c66143167c8290afd
[  140.158132]   moving 15 blocks
[  140.163042]   moving 15 blocks
[  140.167332] stashing 23 overlapping blocks to 8dfbaceefe0df678674bf62e034044f68d909c9c
[  140.167367] 174485504 bytes free on /cache (94208 needed)
[  140.167417]  writing 23 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8dfbaceefe0df678674bf62e034044f68d909c9c
[  140.172916]   moving 23 blocks
[  140.173069] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8dfbaceefe0df678674bf62e034044f68d909c9c
[  140.177198]   moving 15 blocks
[  140.179535] stashing 7 overlapping blocks to c5828a78494389ffdbe555808da89d9b4c4c4168
[  140.179548] 174485504 bytes free on /cache (28672 needed)
[  140.179592]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c5828a78494389ffdbe555808da89d9b4c4c4168
[  140.184121] patching 7 blocks to 7
[  140.192681] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c5828a78494389ffdbe555808da89d9b4c4c4168
[  140.195927]   moving 17 blocks
[  140.200122] stashing 7 overlapping blocks to 8cddabbd7e2d554c8de0d081466ed77bdaf90e56
[  140.200137] 174485504 bytes free on /cache (28672 needed)
[  140.200178]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8cddabbd7e2d554c8de0d081466ed77bdaf90e56
[  140.203928] patching 7 blocks to 7
[  140.212634] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8cddabbd7e2d554c8de0d081466ed77bdaf90e56
[  140.215839]   moving 15 blocks
[  140.230101] stashing 127 overlapping blocks to 19161628c9e444f567b591d931c9f51e20130aa5
[  140.230119] 174485504 bytes free on /cache (520192 needed)
[  140.230155]  writing 127 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19161628c9e444f567b591d931c9f51e20130aa5
[  140.241769]   moving 127 blocks
[  140.242374] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19161628c9e444f567b591d931c9f51e20130aa5
[  140.252336]   moving 17 blocks
[  140.269271] stashing 140 overlapping blocks to 243c8c4db3169102f470120e291552d53a87630d
[  140.269289] 174485504 bytes free on /cache (573440 needed)
[  140.269326]  writing 140 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/243c8c4db3169102f470120e291552d53a87630d
[  140.281336] patching 140 blocks to 140
[  140.297700] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/243c8c4db3169102f470120e291552d53a87630d
[  140.307808]   moving 15 blocks
[  140.313567] stashing 32 overlapping blocks to e85e4f98e1efd4f6a472bbd912ef544eb8d4aed4
[  140.313603] 174485504 bytes free on /cache (131072 needed)
[  140.313651]  writing 32 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e85e4f98e1efd4f6a472bbd912ef544eb8d4aed4
[  140.320432] patching 32 blocks to 32
[  140.331078] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e85e4f98e1efd4f6a472bbd912ef544eb8d4aed4
[  140.336461]   moving 17 blocks
[  140.347932] stashing 102 overlapping blocks to cae736bae70fa0a5a9c96dc285b04c34a5f1b69a
[  140.347950] 174485504 bytes free on /cache (417792 needed)
[  140.347992]  writing 102 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cae736bae70fa0a5a9c96dc285b04c34a5f1b69a
[  140.358779]   moving 102 blocks
[  140.359262] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cae736bae70fa0a5a9c96dc285b04c34a5f1b69a
[  140.367972]   moving 15 blocks
[  140.384593] stashing 136 overlapping blocks to 8fc5232b800cf7cacf857f23d34cee401ce5fb81
[  140.384612] 174485504 bytes free on /cache (557056 needed)
[  140.384649]  writing 136 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8fc5232b800cf7cacf857f23d34cee401ce5fb81
[  140.396277] patching 136 blocks to 136
[  140.410673] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8fc5232b800cf7cacf857f23d34cee401ce5fb81
[  140.420948]   moving 17 blocks
[  140.427656] stashing 34 overlapping blocks to 2d1fca5c5893437558bebedddaa494aac04c1506
[  140.427693] 174485504 bytes free on /cache (139264 needed)
[  140.427742]  writing 34 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2d1fca5c5893437558bebedddaa494aac04c1506
[  140.435449] patching 34 blocks to 34
[  140.445226] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2d1fca5c5893437558bebedddaa494aac04c1506
[  140.450995]   moving 15 blocks
[  140.454540] stashing 7 overlapping blocks to 4c79ffe9d2ccbc7e63f7e09d86f767dcbb2a8c06
[  140.454573] 174485504 bytes free on /cache (28672 needed)
[  140.454617]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4c79ffe9d2ccbc7e63f7e09d86f767dcbb2a8c06
[  140.458284] patching 7 blocks to 7
[  140.466889] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4c79ffe9d2ccbc7e63f7e09d86f767dcbb2a8c06
[  140.470223]   moving 17 blocks
[  140.472537] stashing 9 overlapping blocks to 29d5ae506b20d182bdf4e848ea8966036530f2b3
[  140.472568] 174485504 bytes free on /cache (36864 needed)
[  140.472615]  writing 9 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/29d5ae506b20d182bdf4e848ea8966036530f2b3
[  140.477881]   moving 9 blocks
[  140.477936] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/29d5ae506b20d182bdf4e848ea8966036530f2b3
[  140.480667]   moving 17 blocks
[  140.485284] stashing 9 overlapping blocks to 6d9d6f98583beb6cd84304a5634c9f7d7c87d953
[  140.485296] 174485504 bytes free on /cache (36864 needed)
[  140.485337]  writing 9 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6d9d6f98583beb6cd84304a5634c9f7d7c87d953
[  140.489082] patching 9 blocks to 9
[  140.497753] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6d9d6f98583beb6cd84304a5634c9f7d7c87d953
[  140.500794]   moving 17 blocks
[  140.508438] stashing 40 overlapping blocks to 8f8ab6a41ac43fdef355ad6936d90b64a69dc847
[  140.508471] 174485504 bytes free on /cache (163840 needed)
[  140.508522]  writing 40 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8f8ab6a41ac43fdef355ad6936d90b64a69dc847
[  140.514046]   moving 40 blocks
[  140.514244] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8f8ab6a41ac43fdef355ad6936d90b64a69dc847
[  141.532902]   moving 8192 blocks
[  142.018654] patching 1312 blocks to 1312
[  142.253190] I:current maximum temperature: 36648
[  143.272211] patching 8192 blocks to 8192
[  145.097628]   moving 8192 blocks
[  146.444724]   moving 8192 blocks
[  146.783461]   moving 15 blocks
[  146.788443] stashing 31 overlapping blocks to d2673f391101024319e00dfa0719a793ba00b3f9
[  146.788471] 174485504 bytes free on /cache (126976 needed)
[  146.788527]  writing 31 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d2673f391101024319e00dfa0719a793ba00b3f9
[  146.794859] patching 31 blocks to 31
[  146.805358] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d2673f391101024319e00dfa0719a793ba00b3f9
[  147.756228]   moving 8192 blocks
[  148.126573]   moving 199 blocks
[  148.248629] stashing 512 overlapping blocks to cf671b428167763313003a564e9f81d7c2074d54
[  148.248662] 174485504 bytes free on /cache (2097152 needed)
[  148.248716]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cf671b428167763313003a564e9f81d7c2074d54
[  148.275991] patching 512 blocks to 512
[  148.325299] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cf671b428167763313003a564e9f81d7c2074d54
[  149.296793]   moving 8192 blocks
[  149.641873]   moving 18 blocks
[  150.102996] patching 4230 blocks to 4230
[  151.525680] patching 8192 blocks to 8192
[  152.282739]   moving 17 blocks
[  152.772861] stashing 5009 overlapping blocks to 0bae94481ae9a335a2e01ac16ff052910b92eabd
[  152.772894] 174485504 bytes free on /cache (20516864 needed)
[  152.772963]  writing 5009 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0bae94481ae9a335a2e01ac16ff052910b92eabd
[  153.041040] patching 5009 blocks to 5010
[  154.272159] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0bae94481ae9a335a2e01ac16ff052910b92eabd
[  154.452659]   moving 3 blocks
[  154.454048] patching 2 blocks to 2
[  154.464891]   moving 12 blocks
[  154.558284] patching 786 blocks to 786
[  154.734578]   moving 19 blocks
[  155.129511]   moving 3275 blocks
[  155.776899]   moving 4016 blocks
[  155.965913]   moving 1 blocks
[  155.967376]   moving 4 blocks
[  155.968667]   moving 2 blocks
[  155.969762]   moving 1 blocks
[  155.972174]   moving 12 blocks
[  155.976208]   moving 7 blocks
[  155.979940]   moving 17 blocks
[  155.982231]   moving 7 blocks
[  155.987039]   moving 27 blocks
[  155.991785]   moving 5 blocks
[  155.994050]   moving 12 blocks
[  156.012056]   moving 133 blocks
[  156.022521]   moving 15 blocks
[  156.028404]   moving 7 blocks
[  156.030979]   moving 7 blocks
[  156.032624]   moving 2 blocks
[  156.035325]   moving 15 blocks
[  156.038067]   moving 1 blocks
[  156.040707]   moving 15 blocks
[  156.043317]   moving 5 blocks
[  156.046045]   moving 12 blocks
[  156.047417]   moving 1 blocks
[  156.049050]   moving 5 blocks
[  156.050663]   moving 1 blocks
[  156.058762]   moving 61 blocks
[  156.065102]   moving 15 blocks
[  156.069288]   moving 12 blocks
[  156.079298]   moving 1 blocks
[  156.080633]   moving 3 blocks
[  156.082811]   moving 7 blocks
[  156.085592]   moving 12 blocks
[  156.090646]   moving 17 blocks
[  156.198031]   moving 948 blocks
[  156.234891]   moving 6 blocks
[  156.245674]   moving 68 blocks
[  156.253803]   moving 11 blocks
[  156.257772]   moving 7 blocks
[  156.259767]   moving 2 blocks
[  156.260959]   moving 1 blocks
[  156.263960]   moving 17 blocks
[  156.265523]   moving 1 blocks
[  156.268191]   moving 10 blocks
[  156.271814]   moving 6 blocks
[  156.304767]   moving 280 blocks
[  156.324377]   moving 54 blocks
[  156.330670]   moving 12 blocks
[  156.332756]   moving 4 blocks
[  156.334646]   moving 6 blocks
[  156.336647]   moving 6 blocks
[  156.338176]   moving 1 blocks
[  156.341528]   moving 20 blocks
[  156.345955]   moving 4 blocks
[  156.349427]   moving 15 blocks
[  156.353397]   moving 7 blocks
[  156.357166]   moving 20 blocks
[  156.379346]   moving 161 blocks
[  156.388710]   moving 2 blocks
[  156.391202]   moving 12 blocks
[  156.396624]   moving 17 blocks
[  156.400291]   moving 4 blocks
[  156.401984]   moving 1 blocks
[  156.404659]   moving 15 blocks
[  156.420189]   moving 102 blocks
[  156.429567]   moving 12 blocks
[  156.435935]   moving 17 blocks
[  156.439175]   moving 1 blocks
[  156.441552]   moving 12 blocks
[  156.443547] patching 2 blocks to 2
[  156.454550]   moving 3 blocks
[  156.455852]   moving 2 blocks
[  156.456954]   moving 1 blocks
[  156.459426]   moving 12 blocks
[  156.464357]   moving 12 blocks
[  156.497278]   moving 248 blocks
[  156.510693]   moving 17 blocks
[  156.515796]   moving 17 blocks
[  156.520761]   moving 5 blocks
[  156.522843]   moving 15 blocks
[  156.527198]   moving 5 blocks
[  156.529314]   moving 6 blocks
[  156.531322]   moving 5 blocks
[  156.533790]   moving 9 blocks
[  156.535123]   moving 1 blocks
[  156.537830]   moving 15 blocks
[  156.539730]   moving 1 blocks
[  156.542639]   moving 17 blocks
[  156.547783]   moving 17 blocks
[  156.551063]   moving 15 blocks
[  156.556716]   moving 23 blocks
[  156.562747]   moving 17 blocks
[  156.566684]   moving 10 blocks
[  156.575366]   moving 44 blocks
[  156.580182]   moving 6 blocks
[  156.582097]   moving 6 blocks
[  156.583854]   moving 3 blocks
[  156.586832]   moving 15 blocks
[  156.588671]   moving 2 blocks
[  156.590265]   moving 5 blocks
[  156.592945]   moving 12 blocks
[  156.596011]   moving 1 blocks
[  156.597122]   moving 1 blocks
[  156.598383]   moving 2 blocks
[  156.600909]   moving 12 blocks
[  156.605530]   moving 17 blocks
[  156.607814]   moving 6 blocks
[  156.615038]   moving 54 blocks
[  156.621807]   moving 17 blocks
[  156.625000]   moving 15 blocks
[  156.628810]   moving 3 blocks
[  156.630297]   moving 1 blocks
[  156.631817]   moving 4 blocks
[  156.632970]   moving 1 blocks
[  156.643391]   moving 70 blocks
[  156.652549]   moving 15 blocks
[  156.657932]   moving 12 blocks
[  156.659207]   moving 2 blocks
[  156.660309]   moving 1 blocks
[  156.661422]   moving 1 blocks
[  156.662796]   moving 4 blocks
[  156.664522]   moving 6 blocks
[  156.666945]   moving 12 blocks
[  156.673757]   moving 40 blocks
[  156.680521]   moving 1 blocks
[  156.683562]   moving 17 blocks
[  156.688501]   moving 15 blocks
[  156.690750]   moving 3 blocks
[  156.704301] patching 116 blocks to 116
[  156.726962]   moving 12 blocks
[  156.730241]   moving 15 blocks
[  156.732803]   moving 6 blocks
[  156.735602]   moving 7 blocks
[  156.737510]   moving 3 blocks
[  156.740243]   moving 15 blocks
[  156.743851]   moving 4 blocks
[  156.745065]   moving 1 blocks
[  156.746984]   moving 3 blocks
[  156.748542]   moving 2 blocks
[  156.754999]   moving 48 blocks
[  156.762606]   moving 17 blocks
[  156.766227]   moving 3 blocks
[  156.767602]   moving 1 blocks
[  156.771988]   moving 30 blocks
[  156.775632]   moving 7 blocks
[  156.778021]   moving 11 blocks
[  156.803503]   moving 190 blocks
[  156.815938]   moving 15 blocks
[  156.821083]   moving 12 blocks
[  156.825923]   moving 14 blocks
[  156.828018]   moving 4 blocks
[  156.831092]   moving 12 blocks
[  156.832790]   moving 1 blocks
[  156.833957]   moving 1 blocks
[  156.834662]   moving 1 blocks
[  156.837581]   moving 13 blocks
[  156.840891]   moving 3 blocks
[  156.842117]   moving 1 blocks
[  156.844822]   moving 15 blocks
[  156.857435]   moving 80 blocks
[  156.862294]   moving 1 blocks
[  156.863900]   moving 5 blocks
[  156.866553]   moving 16 blocks
[  156.871478]   moving 12 blocks
[  156.873602]   moving 1 blocks
[  156.874881]   moving 1 blocks
[  156.876276]   moving 3 blocks
[  156.879025]   moving 12 blocks
[  156.881162]   moving 4 blocks
[  156.882503]   moving 2 blocks
[  156.883669]   moving 8 blocks
[  156.887077]   moving 3 blocks
[  156.890129]   moving 15 blocks
[  156.893580]   moving 4 blocks
[  156.894795]   moving 1 blocks
[  156.896036]   moving 4 blocks
[  156.897705]   moving 1 blocks
[  156.904409]   moving 40 blocks
[  156.925370]   moving 10 blocks
[  156.927540]   moving 7 blocks
[  156.930940]   moving 15 blocks
[  156.935538]   moving 15 blocks
[  156.938578]   moving 3 blocks
[  156.939494]   moving 1 blocks
[  156.942164]   moving 12 blocks
[  157.069799]   moving 1132 blocks
[  157.111131]   moving 1 blocks
[  157.112279]   moving 1 blocks
[  157.114773]   moving 9 blocks
[  157.120726]   moving 27 blocks
[  157.126142]   moving 17 blocks
[  157.130316]   moving 6 blocks
[  157.132819]   moving 3 blocks
[  157.135265]   moving 12 blocks
[  157.138882]   moving 12 blocks
[  157.140637]   moving 1 blocks
[  157.142467]   moving 7 blocks
[  157.147603]   moving 30 blocks
[  157.153737]   moving 15 blocks
[  157.156278]   moving 8 blocks
[  157.161651]   moving 17 blocks
[  157.164889]   moving 1 blocks
[  157.166434]   moving 5 blocks
[  157.168127]   moving 3 blocks
[  157.170660]   moving 12 blocks
[  157.174349]   moving 15 blocks
[  157.180965]   moving 26 blocks
[  157.184502]   moving 1 blocks
[  157.185496]   moving 1 blocks
[  157.186508]   moving 1 blocks
[  157.188602]   moving 10 blocks
[  157.194995]   moving 24 blocks
[  157.198435]   moving 1 blocks
[  157.201401]   moving 17 blocks
[  157.206371]   moving 17 blocks
[  157.212252]   moving 27 blocks
[  157.216988]   moving 4 blocks
[  157.220048]   moving 17 blocks
[  157.223724]   moving 3 blocks
[  157.224731] patching 2 blocks to 2
[  157.234064]   moving 5 blocks
[  157.235789]   moving 1 blocks
[  157.237020]   moving 2 blocks
[  157.238911]   moving 8 blocks
[  157.242077]   moving 4 blocks
[  157.245410]   moving 17 blocks
[  157.247205]   moving 3 blocks
[  157.248513]   moving 1 blocks
[  157.251443]   moving 17 blocks
[  157.255262]   moving 1 blocks
[  157.258387]   moving 18 blocks
[  157.259932]   moving 1 blocks
[  157.261815]   moving 8 blocks
[  157.265299]   moving 5 blocks
[  157.268672]   moving 17 blocks
[  157.271238]   moving 4 blocks
[  157.272669]   moving 2 blocks
[  157.273722]   moving 1 blocks
[  157.276083]   moving 12 blocks
[  157.279026]   moving 12 blocks
[  157.282581]   moving 1 blocks
[  157.283585]   moving 1 blocks
[  157.284625]   moving 1 blocks
[  157.287349]   moving 10 blocks
[  157.298134]   moving 52 blocks
[  157.303667]   moving 14 blocks
[  157.307531]   moving 1 blocks
[  157.310489]   moving 17 blocks
[  157.315790]   moving 15 blocks
[  157.320322]   moving 24 blocks
[  157.323998]   moving 5 blocks
[  157.325429]   moving 1 blocks
[  157.326113]   moving 2 blocks
[  157.333429]   moving 48 blocks
[  157.339568]   moving 12 blocks
[  157.344167]   moving 1 blocks
[  157.345782]   moving 5 blocks
[  157.349138]   moving 9 blocks
[  157.351221] patching 6 blocks to 6
[  157.361591]   moving 12 blocks
[  157.363241]   moving 1 blocks
[  157.365574]   moving 12 blocks
[  157.369402]   moving 10 blocks
[  157.374794]   moving 17 blocks
[  157.377386]   moving 8 blocks
[  157.380449]   moving 1 blocks
[  157.383575]   moving 17 blocks
[  157.386226]   moving 1 blocks
[  157.389628]   moving 17 blocks
[  157.403319]   moving 78 blocks
[  157.411003]   moving 3 blocks
[  157.415080]   moving 12 blocks
[  157.417257]   moving 5 blocks
[  157.419095]   moving 5 blocks
[  157.429302]   moving 74 blocks
[  157.437387]   moving 5 blocks
[  157.456499]   moving 136 blocks
[  157.507401]   moving 358 blocks
[  157.525002]   moving 5 blocks
[  157.527539]   moving 6 blocks
[  157.529214]   moving 1 blocks
[  157.530162]   moving 5 blocks
[  157.564900]   moving 308 blocks
[  157.579746]   moving 5 blocks
[  157.580779]   moving 1 blocks
[  157.582411]   moving 5 blocks
[  157.585732]   moving 17 blocks
[  157.589942]   moving 14 blocks
[  157.592636]   moving 19 blocks
[  157.597572]   moving 12 blocks
[  157.599806]   moving 8 blocks
[  157.603996]   moving 26 blocks
[  157.609099]   moving 17 blocks
[  157.616452]   moving 28 blocks
[  157.619862]   moving 1 blocks
[  157.623096]   moving 15 blocks
[  157.628689]   moving 24 blocks
[  157.633468]   moving 17 blocks
[  157.638507]   moving 12 blocks
[  157.640144]   moving 1 blocks
[  157.641329]   moving 1 blocks
[  157.642728]   moving 3 blocks
[  157.644048]   moving 2 blocks
[  157.645869]   moving 4 blocks
[  157.683605]   moving 318 blocks
[  157.699181]   moving 2 blocks
[  157.700594]   moving 1 blocks
[  157.702751]   moving 10 blocks
[  157.705990]   moving 17 blocks
[  157.709818]   moving 4 blocks
[  157.713195]   moving 12 blocks
[  157.717955]   moving 17 blocks
[  157.766934]   moving 418 blocks
[  157.788315]   moving 2 blocks
[  157.789440]   moving 1 blocks
[  157.790556]   moving 1 blocks
[  157.792030]   moving 4 blocks
[  157.795262]   moving 12 blocks
[  157.798250]   moving 1 blocks
[  157.801028]   moving 10 blocks
[  157.805775]   moving 17 blocks
[  157.807398]   moving 1 blocks
[  157.808149] patching 2 blocks to 2
[  157.818137]   moving 10 blocks
[  157.821602]   moving 1 blocks
[  157.824881]   moving 19 blocks
[  157.828618]   moving 1 blocks
[  157.832432]   moving 38 blocks
[  157.838265]   moving 16 blocks
[  157.843966]   moving 17 blocks
[  157.846834]   moving 2 blocks
[  157.848402]   moving 12 blocks
[  157.853340]   moving 17 blocks
[  157.862135]   moving 39 blocks
[  157.867682]   moving 7 blocks
[  157.871106]   moving 17 blocks
[  157.874609]   moving 17 blocks
[  157.878985]   moving 12 blocks
[  157.881478]   moving 10 blocks
[  157.884120]   moving 5 blocks
[  157.886434]   moving 10 blocks
[  157.898687]   moving 82 blocks
[  157.907352]   moving 15 blocks
[  157.909347]   moving 8 blocks
[  157.913090]   moving 7 blocks
[  157.914915]   moving 3 blocks
[  157.941185]   moving 215 blocks
[  157.953273]   moving 7 blocks
[  157.961289]   moving 56 blocks
[  157.968251]   moving 17 blocks
[  157.971381]   moving 15 blocks
[  157.979852]   moving 45 blocks
[  157.983549]   moving 1 blocks
[  157.984568]   moving 1 blocks
[  157.987424]   moving 17 blocks
[  157.990705]   moving 15 blocks
[  157.994609]   moving 3 blocks
[  157.996604]   moving 15 blocks
[  158.005643]   moving 33 blocks
[  158.009655]   moving 1 blocks
[  158.012336]   moving 15 blocks
[  158.016912]   moving 17 blocks
[  158.022910]   moving 15 blocks
[  158.073973]   moving 408 blocks
[  158.296911]   moving 1915 blocks
[  158.377531]   moving 10 blocks
[  158.387372]   moving 6 blocks
[  158.388995]   moving 1 blocks
[  158.391946]   moving 17 blocks
[  158.397105]   moving 15 blocks
[  158.404676]   moving 44 blocks
[  158.408737] patching 2 blocks to 2
[  158.418235]   moving 3 blocks
[  158.519637]   moving 896 blocks
[  158.553826]   moving 5 blocks
[  158.556066]   moving 7 blocks
[  158.557899]   moving 1 blocks
[  158.559928]   moving 9 blocks
[  158.564631]   moving 15 blocks
[  158.569042]   moving 15 blocks
[  158.577292] patching 52 blocks to 52
[  158.596818]   moving 1 blocks
[  158.599297]   moving 12 blocks
[  158.602347]   moving 3 blocks
[  158.603913]   moving 9 blocks
[  158.621161]   moving 134 blocks
[  158.631683]   moving 12 blocks
[  158.637962]   moving 35 blocks
[  158.643942]   moving 17 blocks
[  158.648661]   moving 12 blocks
[  158.652899]   moving 38 blocks
[  158.657245]   moving 5 blocks
[  158.658648]   moving 8 blocks
[  158.662359]   moving 12 blocks
[  158.665448]   moving 2 blocks
[  158.669776]   moving 17 blocks
[  158.674017]   moving 12 blocks
[  158.682792]   moving 66 blocks
[  158.687754]   moving 3 blocks
[  158.777910]   moving 796 blocks
[  158.811028]   moving 17 blocks
[  158.813185]   moving 4 blocks
[  158.814671] patching 2 blocks to 2
[  158.823863]   moving 3 blocks
[  158.826501]   moving 19 blocks
[  158.829814]   moving 1 blocks
[  158.831635]   moving 6 blocks
[  158.834052]   moving 8 blocks
[  158.841632]   moving 51 blocks
[  158.847764]   moving 12 blocks
[  158.852593]   moving 19 blocks
[  158.854095]   moving 2 blocks
[  158.856114]   moving 5 blocks
[  158.857575]   moving 1 blocks
[  158.858221]   moving 1 blocks
[  158.861262]   moving 17 blocks
[  158.866245]   moving 13 blocks
[  158.870165]   moving 15 blocks
[  158.872609]   moving 15 blocks
[  158.877030]   moving 5 blocks
[  158.878116]   moving 1 blocks
[  158.880275]   moving 10 blocks
[  158.883318]   moving 12 blocks
[  158.885763]   moving 18 blocks
[  158.898791]   moving 81 blocks
[  158.906847]   moving 9 blocks
[  158.909699]   moving 12 blocks
[  158.912387]   moving 3 blocks
[  158.913744]   moving 4 blocks
[  158.916662]   moving 8 blocks
[  158.919723]   moving 8 blocks
[  158.923242]   moving 17 blocks
[  158.927408]   moving 3 blocks
[  158.931662]   moving 41 blocks
[  158.937449]   moving 15 blocks
[  158.948820]   moving 78 blocks
[  158.968429]   moving 152 blocks
[  158.980910]   moving 24 blocks
[  158.985473]   moving 7 blocks
[  158.987477]   moving 12 blocks
[  158.990379]   moving 12 blocks
[  158.991620]   moving 1 blocks
[  158.993506]   moving 1 blocks
[  158.996324]   moving 12 blocks
[  159.001150]   moving 12 blocks
[  159.004238]   moving 5 blocks
[  159.007235]   moving 5 blocks
[  159.012822]   moving 48 blocks
[  159.016950]   moving 5 blocks
[  159.018946]   moving 4 blocks
[  159.021315]   moving 8 blocks
[  159.026262]   moving 12 blocks
[  159.032174]   moving 12 blocks
[  159.039308]   moving 49 blocks
[  159.045534]   moving 5 blocks
[  159.047832]   moving 5 blocks
[  159.050563]   moving 15 blocks
[  159.055646]   moving 17 blocks
[  159.057563]   moving 4 blocks
[  159.058808]   moving 2 blocks
[  159.061553]   moving 10 blocks
[  159.115544]   moving 458 blocks
[  159.136567]   moving 17 blocks
[  159.139211]   moving 12 blocks
[  159.144115]   moving 12 blocks
[  159.149315]   moving 17 blocks
[  159.181174]   moving 267 blocks
[  159.197778]   moving 29 blocks
[  159.203847]   moving 24 blocks
[  159.222943]   moving 123 blocks
[  159.233489]   moving 12 blocks
[  159.236432]   moving 17 blocks
[  159.241616]   moving 17 blocks
[  159.246023]   moving 15 blocks
[  159.249731]   moving 17 blocks
[  159.255381]   moving 17 blocks
[  159.256530]   moving 1 blocks
[  159.259658]   moving 17 blocks
[  159.266541]   moving 29 blocks
[  159.270319]   moving 1 blocks
[  159.271410]   moving 1 blocks
[  159.273629]   moving 6 blocks
[  159.275719]   moving 5 blocks
[  159.277333]   moving 6 blocks
[  159.314818]   moving 328 blocks
[  159.333157]   moving 31 blocks
[  159.339388]   moving 17 blocks
[  159.342920]   moving 17 blocks
[  159.347290]   moving 12 blocks
[  159.354417]   moving 29 blocks
[  159.357713]   moving 1 blocks
[  159.359318]   moving 5 blocks
[  159.858858] stashing 5343 overlapping blocks to 5aa0239273ffcfce53c5c50a664261aac6e13acb
[  159.858893] 174485504 bytes free on /cache (21884928 needed)
[  159.858946]  writing 5343 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5aa0239273ffcfce53c5c50a664261aac6e13acb
[  160.137204] patching 5343 blocks to 5346
[  160.946861] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5aa0239273ffcfce53c5c50a664261aac6e13acb
[  161.155890]   moving 1 blocks
[  161.159561]   moving 24 blocks
[  161.163778]   moving 6 blocks
[  161.165470]   moving 1 blocks
[  161.170968]   moving 39 blocks
[  161.177103]   moving 3 blocks
[  161.183140] patching 46 blocks to 46
[  161.201660]   moving 1 blocks
[  161.202915]   moving 2 blocks
[  161.203616]   moving 1 blocks
[  161.204264]   moving 1 blocks
[  161.206572]   moving 16 blocks
[  161.207680]   moving 1 blocks
[  161.208368]   moving 2 blocks
[  161.209386]   moving 6 blocks
[  161.216930]   moving 58 blocks
[  161.224615]   moving 26 blocks
[  161.244504]   moving 148 blocks
[  161.255610]   moving 7 blocks
[  161.257987]   moving 7 blocks
[  161.259354]   moving 5 blocks
[  161.261579]   moving 4 blocks
[  161.301680]   moving 331 blocks
[  161.318706]   moving 18 blocks
[  161.321822]   moving 3 blocks
[  161.325643]   moving 17 blocks
[  161.327219]   moving 1 blocks
[  161.328352]   moving 1 blocks
[  161.330474]   moving 5 blocks
[  161.332155]   moving 5 blocks
[  161.335394]   moving 17 blocks
[  161.338956]   moving 1 blocks
[  161.351944]   moving 114 blocks
[  161.361081]   moving 12 blocks
[  161.391776]   moving 242 blocks
[  161.407271]   moving 12 blocks
[  161.408494]   moving 1 blocks
[  161.410932]   moving 12 blocks
[  161.415372]   moving 5 blocks
[  161.416585]   moving 4 blocks
[  161.418947]   moving 11 blocks
[  161.423706]   moving 10 blocks
[  161.787768]   moving 3318 blocks
[  162.014193] patching 1008 blocks to 1008
[  162.103527]   moving 19 blocks
[  162.109362]   moving 17 blocks
[  162.263316] I:current maximum temperature: 36941
[  162.454248]   moving 3078 blocks
[  162.573518]   moving 38 blocks
[  162.578993]   moving 8 blocks
[  162.580263]   moving 3 blocks
[  162.581731]   moving 1 blocks
[  162.582938]   moving 9 blocks
[  162.608215]   moving 199 blocks
[  162.619155]   moving 2 blocks
[  162.620043]   moving 4 blocks
[  162.621412]   moving 5 blocks
[  162.623860]   moving 1 blocks
[  162.625092]   moving 9 blocks
[  162.629771]   moving 17 blocks
[  162.632773]   moving 12 blocks
[  162.636894]   moving 4 blocks
[  162.640195]   moving 17 blocks
[  162.644708]   moving 12 blocks
[  162.646887]   moving 10 blocks
[  162.650692]   moving 7 blocks
[  162.653923]   moving 15 blocks
[  162.656311]   moving 1 blocks
[  162.657934]   moving 5 blocks
[  162.659029]   moving 5 blocks
[  162.660272]   moving 1 blocks
[  162.663830]   moving 17 blocks
[  162.667295]   moving 17 blocks
[  162.671297]   moving 3 blocks
[  162.672243]   moving 1 blocks
[  162.673345]   moving 1 blocks
[  162.674793]   moving 4 blocks
[  162.677329]   moving 6 blocks
[  162.681132]   moving 12 blocks
[  162.682083]   moving 1 blocks
[  162.705250]   moving 193 blocks
[  162.717889]   moving 9 blocks
[  163.132562]   moving 3782 blocks
[  164.414297]   moving 8192 blocks
[  164.774660]   moving 17 blocks
[  164.778204]   moving 17 blocks
[  164.784126]   moving 17 blocks
[  164.787528]   moving 15 blocks
[  164.791008]   moving 3 blocks
[  164.793373]   moving 12 blocks
[  164.795635]   moving 1 blocks
[  164.796254]   moving 1 blocks
[  164.811879] patching 139 blocks to 139
[  164.987652]   moving 12 blocks
[  164.992139]   moving 12 blocks
[  164.998207]   moving 1 blocks
[  165.002393]   moving 12 blocks
[  165.007574]   moving 5 blocks
[  165.009871]   moving 2 blocks
[  165.014803]   moving 13 blocks
[  165.026099]   moving 34 blocks
[  165.032141]   moving 5 blocks
[  165.039633]   moving 17 blocks
[  165.044869]   moving 4 blocks
[  165.048013]   moving 7 blocks
[  165.054422]   moving 12 blocks
[  165.058910]   moving 1 blocks
[  165.063769]   moving 15 blocks
[  165.066311]   moving 1 blocks
[  165.071643]   moving 11 blocks
[  165.075086]   moving 8 blocks
[  165.078372]   moving 2 blocks
[  165.079212]   moving 1 blocks
[  165.085612]   moving 6 blocks
[  165.088097]   moving 9 blocks
[  165.093920]   moving 18 blocks
[  165.095856]   moving 6 blocks
[  165.098292]   moving 1 blocks
[  165.101216]   moving 17 blocks
[  165.105649]   moving 5 blocks
[  165.108832]   moving 15 blocks
[  165.112976]   moving 17 blocks
[  165.117647]   moving 9 blocks
[  165.126675]   moving 55 blocks
[  165.130981]   moving 1 blocks
[  165.132728]   moving 15 blocks
[  165.136697]   moving 17 blocks
[  165.141246]   moving 17 blocks
[  165.146512]   moving 4 blocks
[  165.148378]   moving 3 blocks
[  165.151441]   moving 29 blocks
[  165.155618]   moving 12 blocks
[  165.158888]   moving 1 blocks
[  165.161604]   moving 15 blocks
[  165.164706]   moving 4 blocks
[  165.166407]   moving 3 blocks
[  165.168787]   moving 12 blocks
[  165.171060]   moving 8 blocks
[  165.172514]   moving 9 blocks
[  165.175476]   moving 1 blocks
[  165.178485]   moving 12 blocks
[  165.224693]   moving 375 blocks
[  165.244555]   moving 28 blocks
[  165.249907]   moving 15 blocks
[  165.251860]   moving 1 blocks
[  165.252978]   moving 1 blocks
[  165.254338]   moving 3 blocks
[  165.260932]   moving 38 blocks
[  165.265774]   moving 6 blocks
[  165.956051]   moving 5694 blocks
[  166.251520]   moving 15 blocks
[  166.262824]   moving 17 blocks
[  166.267760]   moving 27 blocks
[  166.272564]   moving 9 blocks
[  166.274353]   moving 6 blocks
[  166.276134] patching 2 blocks to 2
[  166.285928]   moving 3 blocks
[  166.286944]   moving 2 blocks
[  166.289703]   moving 17 blocks
[  166.294350]   moving 12 blocks
[  166.297551]   moving 14 blocks
[  166.301175]   moving 18 blocks
[  166.305558]   moving 17 blocks
[  166.308493]   moving 10 blocks
[  166.311849]   moving 4 blocks
[  166.313503]   moving 3 blocks
[  166.314843]   moving 1 blocks
[  166.317166]   moving 12 blocks
[  166.325073]   moving 42 blocks
[  166.331565]   moving 15 blocks
[  166.333575]   moving 11 blocks
[  166.336797]   moving 1 blocks
[  166.340010]   moving 11 blocks
[  166.344642]   moving 14 blocks
[  166.348700]   moving 15 blocks
[  166.351865]   moving 12 blocks
[  166.386707]   moving 293 blocks
[  166.401303]   moving 2 blocks
[  166.402533]   moving 1 blocks
[  166.404189]   moving 5 blocks
[  166.405778]   moving 4 blocks
[  166.407010]   moving 1 blocks
[  166.409744]   moving 15 blocks
[  166.414038]   moving 9 blocks
[  166.418303]   moving 25 blocks
[  166.423967]   moving 12 blocks
[  166.425667]   moving 1 blocks
[  166.427073]   moving 7 blocks
[  166.429877]   moving 12 blocks
[  166.433702]   moving 17 blocks
[  166.437335]   moving 3 blocks
[  166.441235]   moving 31 blocks
[  166.445582]   moving 4 blocks
[  166.446967]   moving 2 blocks
[  166.549275]   moving 920 blocks
[  166.601882]   moving 159 blocks
[  166.803672]   moving 1756 blocks
[  166.868665]   moving 5 blocks
[  166.870471]   moving 1 blocks
[  166.872507]   moving 9 blocks
[  166.873558]   moving 1 blocks
[  166.874495]   moving 2 blocks
[  166.892539]   moving 146 blocks
[  166.902033]   moving 4 blocks
[  166.906054]   moving 27 blocks
[  166.909229]   moving 3 blocks
[  166.920291]   moving 65 blocks
[  166.926339]   moving 12 blocks
[  166.929378]   moving 1 blocks
[  166.930421]   moving 1 blocks
[  167.060358]   moving 1160 blocks
[  167.103191]   moving 3 blocks
[  167.105438]   moving 10 blocks
[  167.107061]   moving 1 blocks
[  167.109447]   moving 8 blocks
[  167.110522]   moving 1 blocks
[  167.112676]   moving 15 blocks
[  167.116579]   moving 3 blocks
[  167.117964]   moving 3 blocks
[  167.119045] patching 2 blocks to 2
[  167.129051]   moving 3 blocks
[  167.131644]   moving 15 blocks
[  167.136061]   moving 15 blocks
[  167.137849]   moving 2 blocks
[  167.141214]   moving 17 blocks
[  167.145118]   moving 2 blocks
[  167.146009]   moving 1 blocks
[  167.148531]   moving 12 blocks
[  167.153127]   moving 17 blocks
[  167.156478]   moving 15 blocks
[  167.160324]   moving 12 blocks
[  167.164206]   moving 15 blocks
[  167.165973]   moving 1 blocks
[  167.178499]   moving 96 blocks
[  167.194171]   moving 65 blocks
[  167.201123]   moving 5 blocks
[  167.203754]   moving 17 blocks
[  167.206864]   moving 1 blocks
[  167.210128]   moving 18 blocks
[  167.214239]   moving 7 blocks
[  167.216025]   moving 3 blocks
[  167.219489]   moving 11 blocks
[  167.221064]   moving 1 blocks
[  167.222390] patching 2 blocks to 2
[  167.232164]   moving 3 blocks
[  167.233844]   moving 3 blocks
[  167.238097]   moving 17 blocks
[  167.241726]   moving 7 blocks
[  167.245725]   moving 21 blocks
[  167.248971]   moving 15 blocks
[  167.252792]   moving 7 blocks
[  167.254344]   moving 1 blocks
[  167.256718]   moving 17 blocks
[  167.259029]   moving 1 blocks
[  167.261466]   moving 12 blocks
[  167.265332]   moving 17 blocks
[  167.268686]   moving 1 blocks
[  167.275425]   moving 48 blocks
[  167.278818]   moving 1 blocks
[  167.280052]   moving 3 blocks
[  167.282442]   moving 9 blocks
[  167.287598]   moving 15 blocks
[  167.292362]   moving 15 blocks
[  167.294869]   moving 12 blocks
[  167.298600]   moving 17 blocks
[  167.822204] patching 4759 blocks to 4759
[  168.265106]   moving 7 blocks
[  168.355243]   moving 813 blocks
[  168.384933]   moving 1 blocks
[  168.387171]   moving 8 blocks
[  168.519205]   moving 1173 blocks
[  168.567469]   moving 3 blocks
[  168.573229]   moving 36 blocks
[  168.674912]   moving 859 blocks
[  168.707336]   moving 11 blocks
[  168.711112]   moving 12 blocks
[  168.713341]   moving 1 blocks
[  168.741949] patching 248 blocks to 248
[  168.776965]   moving 12 blocks
[  168.779900]   moving 1 blocks
[  168.782169]   moving 21 blocks
[  168.785495]   moving 5 blocks
[  168.787861]   moving 12 blocks
[  168.790077]   moving 2 blocks
[  168.791486]   moving 1 blocks
[  168.794284]   moving 15 blocks
[  168.921454]   moving 1118 blocks
[  168.979237]   moving 12 blocks
[  168.981347]   moving 3 blocks
[  168.982660]   moving 1 blocks
[  168.984244]   moving 4 blocks
[  169.002049]   moving 132 blocks
[  169.011136]   moving 1 blocks
[  169.012985]   moving 12 blocks
[  169.032250]   moving 143 blocks
[  169.042731]   moving 15 blocks
[  169.046909]   moving 17 blocks
[  169.050544]   moving 1 blocks
[  169.051565]   moving 1 blocks
[  169.052958]   moving 7 blocks
[  169.054244]   moving 1 blocks
[  169.056973]   moving 15 blocks
[  169.062640]   moving 27 blocks
[  169.067751]   moving 12 blocks
[  169.071788]   moving 7 blocks
[  169.079160]   moving 35 blocks
[  169.083756]   moving 1 blocks
[  169.085451]   moving 6 blocks
[  169.108317]   moving 186 blocks
[  169.165862]   moving 412 blocks
[  169.184022]   moving 5 blocks
[  169.185243]   moving 3 blocks
[  169.187366]   moving 7 blocks
[  169.190040]   moving 12 blocks
[  169.192823]   moving 1 blocks
[  169.193862]   moving 3 blocks
[  169.196612]   moving 12 blocks
[  169.199529]   moving 5 blocks
[  169.202650]   moving 17 blocks
[  169.206156]   moving 17 blocks
[  169.211319]   moving 17 blocks
[  169.213218]   moving 1 blocks
[  169.215822]   moving 12 blocks
[  169.219139]   moving 3 blocks
[  169.222427]   moving 17 blocks
[  169.225594]   moving 5 blocks
[  169.228752]   moving 17 blocks
[  169.232339]   moving 1 blocks
[  169.234037]   moving 6 blocks
[  169.235315]   moving 1 blocks
[  169.236683]   moving 3 blocks
[  169.239800]   moving 15 blocks
[  169.243058]   moving 1 blocks
[  169.243918]   moving 1 blocks
[  169.245385]   moving 1 blocks
[  169.247786]   moving 12 blocks
[  169.250716]   moving 12 blocks
[  169.253541]   moving 4 blocks
[  169.256574]   moving 15 blocks
[  169.260034]   moving 5 blocks
[  169.263463]   moving 15 blocks
[  169.268653]   moving 17 blocks
[  169.269924]   moving 1 blocks
[  169.272710]   moving 12 blocks
[  169.276580]   moving 3 blocks
[  169.279719]   moving 15 blocks
[  169.283199]   moving 1 blocks
[  169.324912]   moving 375 blocks
[  169.347864]   moving 52 blocks
[  169.353730]   moving 8 blocks
[  169.355921]   moving 7 blocks
[  169.359271]   moving 17 blocks
[  169.365335]   moving 17 blocks
[  169.369131]   moving 10 blocks
[  169.370994]   moving 3 blocks
[  169.372082]   moving 3 blocks
[  169.374332]   moving 10 blocks
[  169.378622]   moving 17 blocks
[  169.382927]   moving 15 blocks
[  169.384365]   zeroing 1024 blocks
[  169.425763]   zeroing 1024 blocks
[  169.464859]   zeroing 1024 blocks
[  169.504226]   zeroing 1024 blocks
[  169.545649]   zeroing 1024 blocks
[  169.587073]   zeroing 1024 blocks
[  169.649386]   zeroing 1024 blocks
[  169.690617]   zeroing 1024 blocks
[  169.731605]   zeroing 1024 blocks
[  169.773330]   zeroing 1024 blocks
[  169.813916]   zeroing 1024 blocks
[  169.854035]   zeroing 26 blocks
[  169.857777]   zeroing 95100 blocks
[  172.520380] wrote 466826 blocks; expected 466826
[  172.520442] stashed 157242 blocks
[  172.520474] max alloc needed was 33554432
[  172.520520] deleting stash 2bdde8504898ccfcd2c59f20bb8c9c25f73bb524
[  172.935673] system update is complete...
[  172.935782] Patching boot image...
[  173.355217] patch EMMC:/dev/block/bootdevice/by-name/boot:16777216:f8143f9afbc9e1d276f9fe22f92b9de80b142a76:16777216:8a2d0a162f571a1e528ec026aea47c678c5c5222: partition read matched size 16777216 SHA-1 f8143f9afbc9e1d276f9fe22f92b9de80b142a76
[  173.359310] 174489600 bytes free on /cache (16777216 needed)
[  173.359320] Using cache to copy content
[  176.913689] now 8a2d0a16
[  177.155108]   caches dropped
[  178.304049] verification read succeeded (attempt 1)
[  178.318655] system update is complete...
[  178.318683] updating rpm ...
[  178.322106] boot prop value is 7824900.sdhci 
[  178.322124] Storage type is emmc 
[  178.322993] Applying /tmp/rpm.mbn of size 262144 onto rpm of size 262144
[  178.339222] wrote rpm partition
[  178.340636] updating tz ...
[  178.359515] boot prop value is 7824900.sdhci 
[  178.359531] Storage type is emmc 
[  178.359633] Applying /tmp/tz.mbn of size 1835008 onto tz of size 1835008
[  178.457980] wrote tz partition
[  178.459318] updating devcfg ...
[  178.460268] boot prop value is 7824900.sdhci 
[  178.460278] Storage type is emmc 
[  178.460376] Applying /tmp/devcfg.mbn of size 65536 onto devcfg of size 65536
[  178.464098] wrote devcfg partition
[  178.464251] updating cmnlib ...
[  178.468029] boot prop value is 7824900.sdhci 
[  178.468061] Storage type is emmc 
[  178.468157] Applying /tmp/cmnlib.mbn of size 257152 onto cmnlib of size 262144
[  178.484639] wrote cmnlib partition
[  178.484861] updating cmnlib64 ...
[  178.488918] boot prop value is 7824900.sdhci 
[  178.488937] Storage type is emmc 
[  178.489050] Applying /tmp/cmnlib64.mbn of size 257152 onto cmnlib64 of size 262144
[  178.506339] wrote cmnlib64 partition
[  178.506634] updating keymaster ...
[  178.510408] boot prop value is 7824900.sdhci 
[  178.510430] Storage type is emmc 
[  178.510543] Applying /tmp/keymaster.mbn of size 257152 onto keymaster of size 262144
[  178.525909] wrote keymaster partition
[  178.526114] updating prov ...
[  178.528680] boot prop value is 7824900.sdhci 
[  178.528703] Storage type is emmc 
[  178.528810] Applying /tmp/prov.mbn of size 191616 onto prov of size 196608
[  178.542000] wrote prov partition
[  178.542175] Updating dsp ...
[  178.542202] Patching dsp image unconditionally...
[  178.542211] performing update
[  178.542660] blockimg version is 4
[  178.542671] maximum stash entries 0
[  178.542918] creating stash /cache/recovery/677d9ad4e05073580617d8de2a8bf397effe0443/
[  178.543439] 174485504 bytes free on /cache (0 needed)
[  178.543483]  writing 1024 blocks of new data
[  178.726871]  writing 1024 blocks of new data
[  178.871739]  writing 36 blocks of new data
[  178.881287]   zeroing 1024 blocks
[  178.934080]   zeroing 988 blocks
[  179.000509] wrote 4096 blocks; expected 4096
[  179.000566] stashed 0 blocks
[  179.000597] max alloc needed was 4096
[  179.000640] deleting stash 677d9ad4e05073580617d8de2a8bf397effe0443
[  179.009776] Patching oem image...
[  179.009811] Patching oem image after verification.
[  179.009818] performing update
[  179.010235] blockimg version is 4
[  179.010245] maximum stash entries 0
[  179.010300] creating stash /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/
[  179.010534] 174485504 bytes free on /cache (33554432 needed)
[  179.010579]   zeroing 148086 blocks
[  182.274594] I:current maximum temperature: 38165
[  184.016892] stashing 8192 overlapping blocks to 89063f5ca8c5ad9f5386a89aff30adb3d2e71cc6
[  184.016931] 174485504 bytes free on /cache (33554432 needed)
[  184.016986]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/89063f5ca8c5ad9f5386a89aff30adb3d2e71cc6
[  184.396749] patching 8192 blocks to 8192
[  184.790283] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/89063f5ca8c5ad9f5386a89aff30adb3d2e71cc6
[  185.933755] stashing 8192 overlapping blocks to 135fa9e88f44e0e14011fac75830d5fb1f3de87d
[  185.933788] 174485504 bytes free on /cache (33554432 needed)
[  185.933852]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/135fa9e88f44e0e14011fac75830d5fb1f3de87d
[  186.309954] patching 8192 blocks to 8192
[  186.727488] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/135fa9e88f44e0e14011fac75830d5fb1f3de87d
[  187.902539] stashing 8192 overlapping blocks to 33aaa9f8f760aa7b572540a4659a7163a8ae434d
[  187.902572] 174485504 bytes free on /cache (33554432 needed)
[  187.902634]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/33aaa9f8f760aa7b572540a4659a7163a8ae434d
[  188.285820] patching 8192 blocks to 8192
[  188.681328] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/33aaa9f8f760aa7b572540a4659a7163a8ae434d
[  189.873970] stashing 8192 overlapping blocks to e0ac9f72005cf1ad6325a6bf8fb6d4f6d2337cb4
[  189.874002] 174485504 bytes free on /cache (33554432 needed)
[  189.874065]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/e0ac9f72005cf1ad6325a6bf8fb6d4f6d2337cb4
[  190.247344] patching 8192 blocks to 8192
[  190.645288] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/e0ac9f72005cf1ad6325a6bf8fb6d4f6d2337cb4
[  190.949956]   zeroing 1024 blocks
[  190.990109]   zeroing 1024 blocks
[  191.030171]   zeroing 1024 blocks
[  191.070229]   zeroing 1024 blocks
[  191.110471]   zeroing 1024 blocks
[  191.150754]   zeroing 512 blocks
[  191.173274] wrote 38400 blocks; expected 38400
[  191.173302] stashed 32768 blocks
[  191.173309] max alloc needed was 33554432
[  191.173318] deleting stash d42c8ee5b06118b9bb530644a3c2f4943bb98d8f
[  191.178613] Updating gpt_main0.bin...
[  191.179403] boot prop value is 7824900.sdhci 
[  191.179419] Storage type is emmc 
[  191.179426]
[  191.179431]  LBA SIZE for this device is : 512 bytes
[  191.179994] Changed ending_lba to 122142686
[  191.180007] growth partition is userdata
[  191.180060] Partition size(in blocks)  = Partition(/dev/block/mmcblk0) size : 62537072640 bytes 
[  191.180092] 122142720 for the partition /dev/block/mmcblk0
[  191.180121] Partition(/dev/block/mmcblk0) size : 62537072640 bytes 
[  191.190093] Writing header for backup partition 
[  191.195105] GPT upgrade successful
[  191.195202] Entered IsHardwareSecuredFn
[  191.195289] It is a Secure Hardware
[  191.195301] Leaving IsHardwareSecuredFn: t
[  191.195345] Erasing DDR..
[  191.195462] unknown volume for path [/cache]
[  191.195472] Unable to mount /cache!
[  191.195724] blk: partition "" size 1835008 not a multiple of io_buffer_size 524288
[  191.195797] blk: partition "" size 1835008 not a multiple of io_buffer_size 524288
[  191.195934] blk: partition "" size 21073920 not a multiple of io_buffer_size 524288
[  191.196027] blk: partition "" size 56883133440 not a multiple of io_buffer_size 524288
[  191.198333] Trying to access file /sys/block/mmcblk0/mmcblk0p23/start
[  191.198547] /dev/block/bootdevice/by-name/DDR starts at: 133955584
[  191.198577] Formatting partition /dev/block/bootdevice/by-name/DDR of length 32768 starting at 133955584
[  191.199367] Format complete for partition 
[  191.199691] Erased DDR successfully..
[  191.451450]
[  191.477638] W:failed to read uncrypt status: No such file or directory
[  191.487650] I:current maximum temperature: 37088
[  191.487758] I:/cache/OTA_Package_OPS28.65-36.zip
[  191.487765] I:1
[  191.487773] I:time_total: 190
[  191.487779] I:retry: 0
[  191.487785] I:target_build: 4af3
[  191.487791] I:source_build: 9fea
[  191.487799] I:bytes_written_system: 1912119296
[  191.487804] I:bytes_stashed_system: 644063232
[  191.487810] I:bytes_written_dsp: 16777216
[  191.487816] I:bytes_stashed_dsp: 0
[  191.487821] I:bytes_written_oem: 157286400
[  191.487827] I:bytes_stashed_oem: 134217728
[  191.487832] I:temperature_start: 35816
[  191.487841] I:temperature_end: 37088
[  191.487847] I:temperature_max: 38165
[  191.487852] I:
[  191.487858] I:Saving locale "en-IN"
