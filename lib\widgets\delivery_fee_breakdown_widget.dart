import 'package:flutter/material.dart';

/// DeliveryFeeBreakdownWidget - Enhanced delivery fee display with multi-seller support
/// 
/// Phase 3C: UI/UX Enhancements
/// This widget provides a comprehensive breakdown of delivery fees, supporting both
/// single-seller and multi-seller scenarios with improved visual design.
/// 
/// Features:
/// - Single-seller delivery fee display
/// - Multi-seller breakdown with per-seller fees
/// - Real-time updates and animations
/// - Enhanced visual design with emerald theme
/// - Expandable details for complex scenarios
class DeliveryFeeBreakdownWidget extends StatefulWidget {
  final double deliveryFee;
  final Map<String, dynamic>? deliveryFeeDetails;
  final bool showDetails;
  final VoidCallback? onTap;

  const DeliveryFeeBreakdownWidget({
    super.key,
    required this.deliveryFee,
    this.deliveryFeeDetails,
    this.showDetails = false,
    this.onTap,
  });

  @override
  State<DeliveryFeeBreakdownWidget> createState() => _DeliveryFeeBreakdownWidgetState();
}

class _DeliveryFeeBreakdownWidgetState extends State<DeliveryFeeBreakdownWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    if (widget.showDetails) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isMultiSeller = widget.deliveryFeeDetails?['is_multi_seller'] ?? false;
    final isFreeDelivery = widget.deliveryFee == 0.0;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isFreeDelivery ? Colors.green.shade200 : const Color(0xFF059669).withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Main delivery fee display
          InkWell(
            onTap: isMultiSeller ? _toggleExpanded : widget.onTap,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Delivery icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isFreeDelivery 
                          ? Colors.green.shade50 
                          : const Color(0xFF059669).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      isFreeDelivery ? Icons.local_shipping : Icons.delivery_dining,
                      color: isFreeDelivery ? Colors.green.shade600 : const Color(0xFF059669),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Delivery fee text
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Delivery Fee',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        if (isMultiSeller) ...[
                          const SizedBox(height: 2),
                          Text(
                            '${widget.deliveryFeeDetails?['seller_count'] ?? 1} sellers',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // Fee amount
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        isFreeDelivery ? 'FREE' : '₹${widget.deliveryFee.toStringAsFixed(0)}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isFreeDelivery ? Colors.green.shade600 : Colors.black87,
                        ),
                      ),
                      if (isFreeDelivery) ...[
                        Text(
                          'Above threshold',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.green.shade500,
                          ),
                        ),
                      ],
                    ],
                  ),
                  
                  // Expand icon for multi-seller
                  if (isMultiSeller) ...[
                    const SizedBox(width: 8),
                    AnimatedRotation(
                      turns: _isExpanded ? 0.5 : 0.0,
                      duration: const Duration(milliseconds: 300),
                      child: Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600,
                        size: 20,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          // Expandable multi-seller breakdown
          if (isMultiSeller)
            AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return ClipRect(
                  child: Align(
                    alignment: Alignment.topCenter,
                    heightFactor: _fadeAnimation.value,
                    child: Opacity(
                      opacity: _fadeAnimation.value,
                      child: _buildMultiSellerBreakdown(),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildMultiSellerBreakdown() {
    final breakdown = widget.deliveryFeeDetails?['breakdown'] as List<dynamic>? ?? [];
    
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        children: [
          // Divider
          Container(
            height: 1,
            color: Colors.grey.shade200,
            margin: const EdgeInsets.only(bottom: 12),
          ),
          
          // Breakdown header
          Row(
            children: [
              Text(
                'Delivery Fee Breakdown',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // Seller breakdown items
          ...breakdown.map<Widget>((seller) {
            final sellerName = seller['seller_name'] as String? ?? 'Unknown Seller';
            final sellerFee = (seller['delivery_fee'] as num?)?.toDouble() ?? 0.0;
            final itemsCount = seller['items_count'] as int? ?? 0;
            
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  // Seller info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          sellerName,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          '$itemsCount item${itemsCount != 1 ? 's' : ''}',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Seller fee
                  Text(
                    sellerFee == 0.0 ? 'FREE' : '₹${sellerFee.toStringAsFixed(0)}',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: sellerFee == 0.0 ? Colors.green.shade600 : Colors.black87,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }
}

/// DeliveryFeeLoadingWidget - Loading state for delivery fee calculation
class DeliveryFeeLoadingWidget extends StatelessWidget {
  const DeliveryFeeLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF059669).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Loading icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF059669).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF059669)),
              ),
            ),
          ),
          const SizedBox(width: 12),
          
          // Loading text
          Expanded(
            child: Text(
              'Calculating delivery fee...',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
