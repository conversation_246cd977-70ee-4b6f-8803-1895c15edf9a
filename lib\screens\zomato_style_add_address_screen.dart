import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../services/location_service.dart';
import '../services/auto_location_service.dart';
import '../services/delivery_address_state.dart';
import '../supabase_service.dart';
import '../config/maps_config.dart';

/// ZomatoStyleAddAddressScreen - Map-first address input experience
///
/// This screen provides a Zomato-inspired address input flow with:
/// - Interactive Google Map with draggable pin
/// - Real-time address preview that updates on camera move
/// - Bottom sheet with form fields for address details
/// - Address label selection (Home/Work/Other)
/// - Receiver details with validation
/// - Smooth animations and proper error handling
class ZomatoStyleAddAddressScreen extends StatefulWidget {
  final String customerId;
  final LatLng? initialLocation;
  final String? initialAddress;

  const ZomatoStyleAddAddressScreen({
    super.key,
    required this.customerId,
    this.initialLocation,
    this.initialAddress,
  });

  @override
  State<ZomatoStyleAddAddressScreen> createState() =>
      _ZomatoStyleAddAddressScreenState();
}

class _ZomatoStyleAddAddressScreenState
    extends State<ZomatoStyleAddAddressScreen>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  final LocationService _locationService = LocationService();

  // Form controllers
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _additionalDetailsController =
      TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  // State variables
  LatLng _currentLocation = const LatLng(kDefaultLatitude, kDefaultLongitude);
  String? _currentAddress;
  bool _isLoadingAddress = false;
  bool _isSaving = false;
  String _selectedLabel = 'Home';

  // Animation controllers
  late AnimationController _bottomSheetController;
  late AnimationController _pinController;
  late Animation<double> _bottomSheetAnimation;
  late Animation<double> _pinBounceAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeLocation();
    _loadUserProfile();
  }

  void _initializeAnimations() {
    _bottomSheetController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _pinController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _bottomSheetAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _bottomSheetController,
        curve: Curves.easeOutCubic,
      ),
    );

    _pinBounceAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pinController, curve: Curves.elasticOut),
    );

    _bottomSheetController.forward();
  }

  void _initializeLocation() {
    if (widget.initialLocation != null) {
      _currentLocation = widget.initialLocation!;
      _reverseGeocodeLocation(_currentLocation);
    } else {
      _getCurrentLocation();
    }

    if (widget.initialAddress != null) {
      _addressController.text = widget.initialAddress!;
      _currentAddress = widget.initialAddress!;
    }
  }

  Future<void> _loadUserProfile() async {
    try {
      final response = await SupabaseService().client
          .from('customers')
          .select('name, phone')
          .eq('id', widget.customerId)
          .single();

      if (response != null && mounted) {
        setState(() {
          _nameController.text = response['name'] ?? '';
          _phoneController.text = response['phone'] ?? '';
        });
      }
    } catch (e) {
      print('❌ ADD_ADDRESS - Error loading user profile: $e');
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final locationData = await AutoLocationService.getCurrentLocationOnce();
      if (locationData != null && mounted) {
        final location = LatLng(
          locationData['latitude'],
          locationData['longitude'],
        );

        setState(() {
          _currentLocation = location;
        });

        _mapController?.animateCamera(CameraUpdate.newLatLng(location));

        _reverseGeocodeLocation(location);
      }
    } catch (e) {
      print('❌ ADD_ADDRESS - Error getting current location: $e');
    }
  }

  Future<void> _reverseGeocodeLocation(LatLng location) async {
    setState(() {
      _isLoadingAddress = true;
    });

    try {
      final address = await _locationService.reverseGeocode(
        location.latitude,
        location.longitude,
      );

      if (address != null && mounted) {
        setState(() {
          _currentAddress = address;
          _addressController.text = address;
        });
      }
    } catch (e) {
      print('❌ ADD_ADDRESS - Error reverse geocoding: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingAddress = false;
        });
      }
    }
  }

  void _onCameraMove(CameraPosition position) {
    setState(() {
      _currentLocation = position.target;
    });

    // Animate pin bounce
    _pinController.forward().then((_) {
      _pinController.reverse();
    });
  }

  void _onCameraIdle() {
    _reverseGeocodeLocation(_currentLocation);
  }

  Future<void> _saveAddress() async {
    if (!_validateForm()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      // Save address to Supabase
      await SupabaseService().client.from('customer_addresses').insert({
        'customer_id': widget.customerId,
        'address': _addressController.text.trim(),
        'additional_details': _additionalDetailsController.text.trim(),
        'label': _selectedLabel,
        'latitude': _currentLocation.latitude,
        'longitude': _currentLocation.longitude,
        'receiver_name': _nameController.text.trim(),
        'receiver_phone': _phoneController.text.trim(),
        'is_default': false, // TODO: Add option to set as default
      });

      // Update delivery address state
      DeliveryAddressState.setAddress(
        _addressController.text.trim(),
        locationData: {
          'latitude': _currentLocation.latitude,
          'longitude': _currentLocation.longitude,
        },
        customerId: widget.customerId,
      );

      // Provide haptic feedback
      HapticFeedback.lightImpact();

      // Show success message and close
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Address saved successfully!'),
            backgroundColor: Colors.green[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
        Navigator.pop(context, {
          'address': _addressController.text.trim(),
          'location_data': {
            'latitude': _currentLocation.latitude,
            'longitude': _currentLocation.longitude,
          },
        });
      }
    } catch (e) {
      print('❌ ADD_ADDRESS - Error saving address: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to save address. Please try again.'),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  bool _validateForm() {
    if (_addressController.text.trim().isEmpty) {
      _showValidationError('Please enter a valid address');
      return false;
    }

    if (_nameController.text.trim().isEmpty) {
      _showValidationError('Please enter receiver name');
      return false;
    }

    if (_phoneController.text.trim().isEmpty) {
      _showValidationError('Please enter receiver phone number');
      return false;
    }

    if (_phoneController.text.trim().length < 10) {
      _showValidationError('Please enter a valid phone number');
      return false;
    }

    return true;
  }

  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red[600],
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  void dispose() {
    _bottomSheetController.dispose();
    _pinController.dispose();
    _searchController.dispose();
    _addressController.dispose();
    _additionalDetailsController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Stack(
        children: [
          _buildMap(),
          _buildHeader(),
          _buildAddressPreview(),
          _buildBottomSheet(),
        ],
      ),
    );
  }

  Widget _buildMap() {
    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      initialCameraPosition: CameraPosition(
        target: _currentLocation,
        zoom: 16.0,
      ),
      onCameraMove: _onCameraMove,
      onCameraIdle: _onCameraIdle,
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
      buildingsEnabled: true,
      trafficEnabled: false,
    );
  }

  Widget _buildHeader() {
    return Positioned(
      top: MediaQuery.of(context).padding.top,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back),
                iconSize: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search for area, street name...',
                    prefixIcon: Icon(Icons.search, color: Colors.grey),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                  onSubmitted: (value) {
                    // TODO: Implement search functionality
                  },
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                color: Colors.green[600],
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: _getCurrentLocation,
                icon: const Icon(Icons.my_location, color: Colors.white),
                iconSize: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressPreview() {
    return Positioned(
      top: MediaQuery.of(context).size.height * 0.35,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(Icons.location_on, color: Colors.green[600], size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: _isLoadingAddress
                  ? Row(
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation(
                              Colors.green[600],
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text('Getting address...'),
                      ],
                    )
                  : Text(
                      _currentAddress ??
                          'Move pin to your exact delivery location',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheet() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 1),
          end: Offset.zero,
        ).animate(_bottomSheetAnimation),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.55,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDeliveryDetailsSection(),
                      const SizedBox(height: 24),
                      _buildReceiverDetailsSection(),
                      const SizedBox(height: 24),
                      _buildAddressLabelSection(),
                      const SizedBox(height: 32),
                      _buildSaveButton(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeliveryDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Delivery details',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _addressController,
          decoration: InputDecoration(
            prefixIcon: Icon(Icons.location_on, color: Colors.green[600]),
            hintText: 'Complete address',
            filled: true,
            fillColor: Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.green[600]!),
            ),
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _additionalDetailsController,
          decoration: InputDecoration(
            hintText: 'Additional address details*',
            helperText: 'E.g. Floor, House no.',
            filled: true,
            fillColor: Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.green[600]!),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReceiverDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Receiver details for this address',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _nameController,
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.person_outline),
            hintText: 'Receiver name',
            filled: true,
            fillColor: Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.green[600]!),
            ),
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.phone_outlined),
            hintText: 'Phone number',
            filled: true,
            fillColor: Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.green[600]!),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAddressLabelSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Save address as',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildLabelChip('Home', Icons.home),
            const SizedBox(width: 12),
            _buildLabelChip('Work', Icons.work),
            const SizedBox(width: 12),
            _buildLabelChip('Other', Icons.location_on),
          ],
        ),
      ],
    );
  }

  Widget _buildLabelChip(String label, IconData icon) {
    final isSelected = _selectedLabel == label;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedLabel = label;
        });
        HapticFeedback.selectionClick();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green[600] : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.green[600]! : Colors.grey[300]!,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected ? Colors.white : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _saveAddress,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green[600],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: _isSaving
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation(Colors.white),
                ),
              )
            : const Text(
                'Save address',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
      ),
    );
  }
}

// Center pin widget for the map
class _CenterPin extends StatelessWidget {
  final Animation<double> bounceAnimation;

  const _CenterPin({required this.bounceAnimation});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQuery.of(context).size.height * 0.5 - 40,
      left: MediaQuery.of(context).size.width * 0.5 - 20,
      child: AnimatedBuilder(
        animation: bounceAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: bounceAnimation.value,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.green[600],
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.location_on,
                color: Colors.white,
                size: 24,
              ),
            ),
          );
        },
      ),
    );
  }
}
