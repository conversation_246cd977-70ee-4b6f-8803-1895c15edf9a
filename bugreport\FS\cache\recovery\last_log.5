[    0.000185] Starting recovery (pid 427) on Mon Nov  9 12:01:14 1970
[    0.001002] recovery filesystem table
[    0.001013] =========================
[    0.001022]   0 /system ext4 /dev/block/platform/soc/7824900.sdhci/by-name/system 0
[    0.001028]   1 /system ext4 /dev/block/bootdevice/by-name/system 0
[    0.001034]   2 /cache ext4 /dev/block/bootdevice/by-name/cache 0
[    0.001040]   3 /data ext4 /dev/block/bootdevice/by-name/userdata -16384
[    0.001048]   4 /sdcard vfat /dev/block/mmcblk1p1 0
[    0.001054]   5 /boot emmc /dev/block/bootdevice/by-name/boot 0
[    0.001060]   6 /recovery emmc /dev/block/bootdevice/by-name/recovery 0
[    0.001066]   7 /misc emmc /dev/block/bootdevice/by-name/misc 0
[    0.001071]   8 /oem ext4 /dev/block/bootdevice/by-name/oem 0
[    0.001078]   9 /modem ext4 /dev/block/bootdevice/by-name/modem 0
[    0.001086]   10 /dsp ext4 /dev/block/bootdevice/by-name/dsp 0
[    0.001092]   11 /tmp ramdisk ramdisk 0
[    0.001097]
[    0.002156] I:Boot command: boot-recovery
[    0.002196] I:Got 3 arguments from boot message
[    0.004000] locale is [en-IN]
[    0.004007] stage is []
[    0.004021] reason is [(null)]
[    0.004249] libc: Access denied finding property "ro.sf.lcd_density"
[    0.057878] W:Failed to set brightness: Permission denied
[    0.057888] I:Screensaver disabled
[    0.059735] cannot find/open a drm device: No such file or directory
[    0.059941] fb0 reports (possibly inaccurate):
[    0.059947]   vi.bits_per_pixel = 32
[    0.059953]   vi.red.offset   =   0   .length =   8
[    0.059959]   vi.green.offset =   8   .length =   8
[    0.059964]   vi.blue.offset  =  16   .length =   8
[    0.074131] framebuffer: 0 (1080 x 1920)
[    0.108018]           erasing_text: en-IN (137 x 57 @ 1566)
[    0.113592]        no_command_text: en-IN (249 x 57 @ 1566)
[    0.117951]             error_text: en-IN (99 x 57 @ 1566)
[    1.031747]        installing_text: en-IN (459 x 57 @ 1566)
[    1.057755] SELinux: Loaded file_contexts
[    1.057773] Command: "/sbin/recovery" "--update_package=/cache/OTA_Package_OPSS28.65-36-9.zip" "--locale=en-IN"
[    1.057781]
[    1.058047] sys.usb.controller=7000000.dwc3
[    1.058290] ro.product.name=sanders_retail
[    1.058297] ro.product.device=sanders
[    1.058456] ro.oem.key1=retin
[    1.058462] ro.carrier=retin
[    1.059500] debug.gralloc.enable_fb_ubwc=1
[    1.059606] persist.vendor.dpm.feature=0
[    1.059643] af.fast_track_multiplier=1
[    1.059650] av.debug.disable.pers.cache=1
[    1.059656] av.offload.enable=false
[    1.059664] mm.enable.sec.smoothstreaming=false
[    1.059669] mm.enable.qcom_parser=135715
[    1.059675] mm.enable.smoothstreaming=false
[    1.059681] pm.dexopt.boot=verify
[    1.059686] pm.dexopt.ab-ota=speed-profile
[    1.059694] pm.dexopt.shared=speed
[    1.059699] pm.dexopt.install=quicken
[    1.059705] pm.dexopt.inactive=verify
[    1.059711] pm.dexopt.bg-dexopt=speed-profile
[    1.059716] pm.dexopt.first-boot=quicken
[    1.059722] ro.fm.transmitter=false
[    1.059727] ro.qc.sdk.audio.ssr=false
[    1.059735] ro.qc.sdk.audio.fluencetype=none
[    1.059740] ro.adb.secure=1
[    1.059746] ro.com.google.ime.theme_id=4
[    1.059752] ro.com.google.gmsversion=8.1_201805
[    1.059758] ro.com.google.rlzbrandcode=MOTC
[    1.059763] ro.com.google.rlz_ap_whitelist=y0,y5,y6,y7,y8
[    1.059769] ro.frp.pst=/dev/block/bootdevice/by-name/frp
[    1.059774] ro.mot.build.product.increment=221
[    1.059780] ro.mot.build.version.release=28.221
[    1.059788] ro.mot.build.version.sdk_int=28
[    1.059794] ro.mot.build.customerid=retail
[    1.059800] ro.mot.sensors.glance_approach=false
[    1.059805] ro.mot.security.enable=true
[    1.059811] ro.mot.base_buildid=OPS28.65-36/9fea
[    1.059816] ro.mot.ignore_csim_appid=true
[    1.059822] ro.opa.eligible_device=true
[    1.059827] ro.sys.sdcardfs=1
[    1.059833] ro.url.legal=http://www.google.com/intl/%s/mobile/android/basic/phone-legal.html
[    1.059838] ro.url.legal.android_privacy=http://www.google.com/intl/%s/mobile/android/basic/privacy.html
[    1.059844] ro.usb.bpt=2ee5
[    1.059868] ro.usb.mtp=2e82
[    1.059874] ro.usb.ptp=2e83
[    1.059879] ro.usb.bpteth=2ee7
[    1.059887] ro.usb.bpt_adb=2ee6
[    1.059893] ro.usb.mtp_adb=2e76
[    1.059898] ro.usb.ptp_adb=2e84
[    1.059904] ro.usb.bpteth_adb=2ee8
[    1.059909] ro.wff=recovery
[    1.059915] ro.boot.cid=0x32
[    1.059920] ro.boot.uid=C035992300000000000000000000
[    1.059926] ro.boot.emmc=true
[    1.059931] ro.boot.mode=normal
[    1.059937] ro.boot.flash.locked=1
[    1.059942] ro.boot.hwrev=0x8400
[    1.059947] ro.boot.radio=INDIA
[    1.059953] ro.boot.device=sanders
[    1.059958] ro.boot.fsg-id=
[    1.059963] ro.boot.carrier=retin
[    1.059969] ro.boot.dualsim=true
[    1.059974] ro.boot.baseband=msm
[    1.059980] ro.boot.bl_state=1
[    1.059985] ro.boot.hardware=qcom
[    1.059990] ro.boot.hardware.sku=XT1804
[    1.059996] ro.boot.ssm_data=0000000002009991
[    1.060001] ro.boot.bootdevice=7824900.sdhci
[    1.060007] ro.boot.bootloader=0xC212
[    1.060012] ro.boot.bootreason=reboot
[    1.060017] ro.boot.veritymode=enforcing
[    1.060023] ro.boot.wifimacaddr=A8:96:75:05:41:09,A8:96:75:05:41:0A
[    1.060028] ro.boot.write_protect=1
[    1.060036] ro.boot.poweroff_alarm=0
[    1.060042] ro.boot.powerup_reason=0x00004000
[    1.060047] ro.boot.secure_hardware=1
[    1.060053] ro.boot.verifiedbootstate=green
[    1.060058] ro.hwui.path_cache_size=32
[    1.060063] ro.hwui.layer_cache_size=48
[    1.060069] ro.hwui.gradient_cache_size=1
[    1.060074] ro.hwui.r_buffer_cache_size=8
[    1.060080] ro.hwui.drop_shadow_cache_size=6
[    1.060085] ro.hwui.text_large_cache_width=2048
[    1.060091] ro.hwui.text_small_cache_width=1024
[    1.060096] ro.hwui.text_large_cache_height=1024
[    1.060102] ro.hwui.text_small_cache_height=1024
[    1.060107] ro.hwui.texture_cache_flushrate=0.4
[    1.060113] ro.wifi.channels=
[    1.060118] ro.allow.mock.location=0
[    1.060123] ro.board.platform=msm8953
[    1.060129] ro.build.id=OPSS28.65-36-9
[    1.060134] ro.build.date=Wed Feb  6 03:25:49 CST 2019
[    1.060140] ro.build.date.utc=1549445149
[    1.060145] ro.build.host=ilclbld31
[    1.060151] ro.build.tags=release-keys
[    1.060156] ro.build.type=user
[    1.060161] ro.build.user=hudsoncm
[    1.060167] ro.build.product=sanders
[    1.060172] ro.build.version.ci=10
[    1.060177] ro.build.version.sdk=27
[    1.060183] ro.build.version.qcom=LA.UM.6.6.r1-08600-89xx.0
[    1.060188] ro.build.version.release=8.1.0
[    1.060194] ro.build.version.codename=REL
[    1.060199] ro.build.version.incremental=03c05
[    1.060204] ro.build.version.preview_sdk=0
[    1.060210] ro.build.version.all_codenames=REL
[    1.060215] ro.build.version.security_patch=2019-02-01
[    1.060221] ro.build.thumbprint=8.1.0/OPSS28.65-36-9/03c05:user/release-keys
[    1.060226] ro.build.characteristics=default
[    1.060232] ro.build.shutdown_timeout=0
[    1.060237] ro.media.enc.aud.ch=1
[    1.060242] ro.media.enc.aud.hz=8000
[    1.060251] ro.media.enc.aud.bps=13300
[    1.060256] ro.media.enc.aud.codec=qcelp
[    1.060262] ro.media.enc.aud.fileformat=qcp
[    1.060268] ro.radio.imei.sv=17
[    1.060273] ro.bug2go.magickeys=24,26
[    1.060278] ro.lenovo.single_hand=1
[    1.060284] ro.secure=1
[    1.060289] ro.treble.enabled=false
[    1.060295] ro.vendor.qti.sys.fw.empty_app_percent=50
[    1.060300] ro.vendor.qti.sys.fw.use_trim_settings=true
[    1.060306] ro.vendor.qti.sys.fw.trim_cache_percent=100
[    1.060311] ro.vendor.qti.sys.fw.trim_empty_percent=100
[    1.060316] ro.vendor.qti.sys.fw.trim_enable_memory=2147483648
[    1.060322] ro.vendor.qti.config.zram=true
[    1.060327] ro.vendor.qti.core_ctl_max_cpu=4
[    1.060333] ro.vendor.qti.core_ctl_min_cpu=2
[    1.060338] ro.vendor.product.name=sanders_retail
[    1.060343] ro.vendor.product.brand=motorola
[    1.060349] ro.vendor.product.model=Moto G (5S) Plus
[    1.060354] ro.vendor.product.device=sanders
[    1.060360] ro.vendor.product.manufacturer=motorola
[    1.060365] ro.vendor.at_library=libqti-at.so
[    1.060370] ro.vendor.gt_library=libqti-gt.so
[    1.060376] ro.vendor.extension_library=libqti-perfd-client.so
[    1.060386] ro.zygote=zygote32
[    1.060392] ro.memperf.lib=libmemperf.so
[    1.060398] ro.memperf.enable=false
[    1.060403] ro.product.cpu.abi=armeabi-v7a
[    1.060409] ro.product.cpu.abi2=armeabi
[    1.060414] ro.product.cpu.abilist=armeabi-v7a,armeabi
[    1.060420] ro.product.cpu.abilist32=armeabi-v7a,armeabi
[    1.060425] ro.product.cpu.abilist64=
[    1.060431] ro.product.board=msm8953
[    1.060436] ro.product.brand=motorola
[    1.060442] ro.product.model=Moto G (5S) Plus
[    1.060447] ro.product.locale=en-US
[    1.060452] ro.product.manufacturer=motorola
[    1.060458] ro.product.first_api_level=25
[    1.060463] ro.baseband=msm
[    1.060468] ro.bootmode=normal
[    1.060474] ro.hardware=qcom
[    1.060479] ro.hardware.nfc_nci=pn54x
[    1.060484] ro.hardware.sensors=sanders
[    1.060490] ro.logdumpd.enabled=0
[    1.060495] ro.qualcomm.cabl=0
[    1.060500] ro.revision=p400
[    1.060506] ro.bootimage.build.date=Wed Feb 6 03:25:49 CST 2019
[    1.060511] ro.bootimage.build.date.utc=1549445149
[    1.060517] ro.bootimage.build.fingerprint=motorola/sanders_retail/sanders:8.1.0/OPSS28.65-36-9/03c05:user/release-keys
[    1.060523] ro.emmc_size=16GB
[    1.060528] ro.bootloader=0xC212
[    1.060533] ro.bootreason=reboot
[    1.060539] ro.debuggable=0
[    1.060544] ro.emulate_fbe=false
[    1.060549] ro.recovery_id=0x655cb2482c109bb4a810c1b74520d4eb5d136dfa000000000000000000000000
[    1.060555] ro.setupwizard.mode=OPTIONAL
[    1.060561] ro.property_service.version=2
[    1.060569] ro.use_data_netmgrd=true
[    1.060575] ro.cutoff_voltage_mv=3400
[    1.060580] ro.oem_unlock_supported=1
[    1.060586] ro.control_privapp_permissions=enforce
[    1.060591] drm.service.enabled=true
[    1.060596] mmp.enable.3g2=true
[    1.060602] sdm.debug.disable_skip_validate=1
[    1.060607] use.qti.sw.ape.decoder=true
[    1.060613] use.qti.sw.alac.decoder=true
[    1.060618] use.voice.path.for.pcm.voip=false
[    1.060623] init.svc.charger=running
[    1.060629] init.svc.ueventd=running
[    1.060634] init.svc.recovery=running
[    1.060639] qcom.bt.le_dev_pwr_class=1
[    1.060644] qcom.hw.aac.encoder=false
[    1.060650] rild.libargs=-d /dev/smd0
[    1.060655] rild.libpath=/system/vendor/lib/libril-qc-qmi-1.so
[    1.060661] vidc.dec.disable.split.cpu=1
[    1.060666] vidc.enc.dcvs.extra-buff-count=2
[    1.060672] audio.pp.asphere.enabled=false
[    1.060677] audio.safx.pbe.enabled=true
[    1.060682] audio.dolby.ds2.enabled=true
[    1.060688] audio.parser.ip.buffer.size=262144
[    1.060693] audio.offload.min.duration.secs=60
[    1.060699] audio.offload.pcm.16bit.enable=false
[    1.060704] audio.offload.pcm.24bit.enable=false
[    1.060709] audio.offload.track.enable=true
[    1.060715] audio.offload.video=false
[    1.060720] audio.offload.buffer.size.kb=64
[    1.060726] audio.offload.disable=false
[    1.060731] audio.offload.gapless.enabled=false
[    1.060737] audio.offload.multiple.enabled=false
[    1.060742] audio.playback.mch.downsample=true
[    1.060747] audio.deep_buffer.media=true
[    1.060753] media.settings.xml=/vendor/etc/media_profiles.xml
[    1.060781] media.msm8956hw=0
[    1.060789] media.aac_51_output_enabled=true
[    1.060794] video.disable.ubwc=1
[    1.060802] voice.conc.fallbackpath=deep-buffer
[    1.060807] voice.voip.conc.disabled=true
[    1.060814] voice.record.conc.disabled=false
[    1.060819] voice.playback.conc.disabled=true
[    1.060825] tunnel.audio.encode=false
[    1.060831] vendor.vidc.dec.downscalar_width=1920
[    1.060837] vendor.vidc.dec.downscalar_height=1088
[    1.060842] vendor.vidc.enc.disable.pq=true
[    1.060849] vendor.vidc.enc.disable_bframes=1
[    1.060854] vendor.vidc.disable.split.mode=1
[    1.060860] vendor.display.enable_default_color_mode=1
[    1.060866] persist.mm.sta.enable=0
[    1.060872] persist.cne.rat.wlan.chip.oem=WCN
[    1.060877] persist.cne.feature=1
[    1.060884] persist.cne.logging.qxdm=3974
[    1.060889] persist.hwc.mdpcomp.enable=true
[    1.060895] persist.hwc.enable_vds=1
[    1.060901] persist.lte.pco_supported=true
[    1.060912] persist.qfp=false
[    1.060919] persist.data.qmi.adb_logmask=0
[    1.060924] persist.data.mode=concurrent
[    1.060931] persist.data.iwlan.enable=true
[    1.060936] persist.data.netmgrd.qos.enable=true
[    1.060942] persist.demo.hdmirotationlock=false
[    1.060948] persist.rild.nitz_plmn=
[    1.060954] persist.rild.nitz_long_ons_0=
[    1.060959] persist.rild.nitz_long_ons_1=
[    1.060966] persist.rild.nitz_long_ons_2=
[    1.060971] persist.rild.nitz_long_ons_3=
[    1.060978] persist.rild.nitz_short_ons_0=
[    1.060983] persist.rild.nitz_short_ons_1=
[    1.060989] persist.rild.nitz_short_ons_2=
[    1.060995] persist.rild.nitz_short_ons_3=
[    1.061001] persist.vold.ecryptfs_supported=true
[    1.061006] persist.timed.enable=true
[    1.061013] persist.vendor.ims.disableQXDMLogs=1
[    1.061018] persist.vendor.ims.disableDebugLogs=1
[    1.061025] persist.vendor.camera.display.lmax=1280x720
[    1.061030] persist.vendor.camera.display.umax=1920x1080
[    1.061036] persist.vendor.qcomsysd.enabled=1
[    1.061042] persist.speaker.prot.enable=false
[    1.061048] persist.fuse_sdcard=true
[    1.061053] persist.esdfs_sdcard=false
[    1.061059] keyguard.no_require_sim=true
[    1.061065] audio_hal.period_size=240
[    1.061071] telephony.lteOnCdmaDevice=1
[    1.061077] DEVICE_PROVISIONED=1
[    1.061086] mdc_initial_max_retry=10
[    1.061092] security.perf_harden=1
[    1.061097] ro.boot.serialno=ZY32286WPB
[    1.061103] ro.serialno=ZY32286WPB
[    1.061142] persist.debug.coresight.config=stm-events
[    1.061189] persist.audio.cal.sleeptime=6000
[    1.061196] persist.audio.dualmic.config=endfire
[    1.061201] persist.audio.endcall.delay=250
[    1.061210] persist.audio.fluence.speaker=false
[    1.061215] persist.audio.fluence.voicerec=false
[    1.061221] persist.audio.fluence.voicecall=true
[    1.061226] persist.audio.fluence.voicecomm=true
[    1.061232] persist.audio.calfile0=/etc/acdbdata/Bluetooth_cal.acdb
[    1.061239] persist.audio.calfile1=/etc/acdbdata/General_cal.acdb
[    1.061245] persist.audio.calfile2=/etc/acdbdata/Global_cal.acdb
[    1.061250] persist.audio.calfile3=/etc/acdbdata/Handset_cal.acdb
[    1.061256] persist.audio.calfile4=/etc/acdbdata/Hdmi_cal.acdb
[    1.061261] persist.audio.calfile5=/etc/acdbdata/Headset_cal.acdb
[    1.061267] persist.audio.calfile6=/etc/acdbdata/Speaker_cal.acdb
[    1.061537] ro.telephony.default_network=10,0
[    1.061544] ril.subscription.types=NV,RUIM
[    1.061550] persist.radio.schd.cache=3500
[    1.061558] persist.radio.calls.on.ims=true
[    1.061563] persist.radio.domain.ps=0
[    1.061569] persist.radio.apn_delay=5000
[    1.061574] persist.radio.msgtunnel.start=true
[    1.061583] persist.radio.sar_sensor=1
[    1.061588] persist.radio.REVERSE_QMI=0
[    1.061594] persist.radio.apm_sim_not_pwdn=1
[    1.061599] persist.vendor.radio.jbims=1
[    1.061604] persist.vendor.radio.rat_on=combine
[    1.061610] persist.vendor.radio.custom_ecc=1
[    1.061615] persist.vendor.radio.mt_sms_ack=30
[    1.061623] persist.vendor.radio.cs_srv_type=1
[    1.061629] persist.vendor.radio.dfr_mode_set=1
[    1.061634] persist.vendor.radio.lte_vrte_ltd=1
[    1.061640] persist.vendor.radio.data_con_rprt=1
[    1.061645] persist.vendor.radio.eri64_as_home=1
[    1.061651] persist.vendor.radio.sib16_support=1
[    1.061656] persist.vendor.radio.sw_mbn_update=1
[    1.061662] persist.vendor.radio.add_power_save=1
[    1.061667] persist.vendor.radio.force_get_pref=1
[    1.061672] persist.vendor.radio.is_wps_enabled=true
[    1.061678] persist.vendor.radio.snapshot_timer=22
[    1.061685] persist.vendor.radio.oem_ind_to_both=0
[    1.061691] persist.vendor.radio.apm_sim_not_pwdn=1
[    1.061696] persist.vendor.radio.no_wait_for_card=1
[    1.061702] persist.vendor.radio.snapshot_enabled=1
[    1.061707] persist.vendor.radio.0x9e_not_callname=1
[    1.061713] persist.vendor.radio.relay_oprt_change=1
[    1.061718] persist.vendor.radio.qcril_uim_vcc_feature=1
[    1.061806] ro.hw.hwrev=0x8400
[    1.061813] ro.hw.radio=INDIA
[    1.061824] ro.hw.device=sanders
[    1.061832] ro.hw.dualsim=true
[    1.061838] dev.pm.dyn_samplingrate=1
[    1.061843] net.bt.name=Android
[    1.061849] sys.vendor.shutdown.waittime=500
[    1.061854] persist.sys.qc.sub.rdump.on=1
[    1.061859] persist.sys.qc.sub.rdump.max=0
[    1.061865] persist.sys.cnd.iwlan=1
[    1.061872] persist.sys.ssr.restart_level=ALL_ENABLE
[    1.061878] persist.sys.media.use-awesome=false
[    1.061883] persist.sys.dalvik.vm.lib.2=libart.so
[    1.061891] debug.sf.hw=1
[    1.061896] debug.sf.recomputecrop=0
[    1.061902] debug.sf.enable_hwc_vds=1
[    1.061909] debug.sf.latch_unsignaled=1
[    1.061915] debug.egl.hw=1
[    1.061920] debug.atrace.tags.enableflags=0
[    1.061925] debug.enable.gamed=0
[    1.061931] debug.enable.sglscale=1
[    1.061938] debug.mdpcomp.logs=0
[    1.062005] ro.dalvik.vm.native.bridge=0
[    1.062012] dalvik.vm.isa.arm.variant=cortex-a53
[    1.062017] dalvik.vm.isa.arm.features=default
[    1.062025] dalvik.vm.dexopt.secondary=true
[    1.062031] dalvik.vm.usejit=true
[    1.062036] dalvik.vm.heapsize=384m
[    1.062042] dalvik.vm.dex2oat-Xms=64m
[    1.062048] dalvik.vm.dex2oat-Xmx=512m
[    1.062055] dalvik.vm.heapmaxfree=8m
[    1.062061] dalvik.vm.heapminfree=512k
[    1.062066] dalvik.vm.heapstartsize=8m
[    1.062071] dalvik.vm.appimageformat=lz4
[    1.062077] dalvik.vm.usejitprofiles=true
[    1.062082] dalvik.vm.heapgrowthlimit=192m
[    1.062088] dalvik.vm.stack-trace-dir=/data/anr
[    1.062093] dalvik.vm.image-dex2oat-Xms=64m
[    1.062101] dalvik.vm.image-dex2oat-Xmx=64m
[    1.062106] dalvik.vm.heaptargetutilization=0.75
[    1.062114] ro.config.ringtone=Moto.ogg
[    1.062119] ro.config.wallpaper=system/media/wallpapers/default_moto_wallpaper.jpg
[    1.062125] ro.config.ringtone_2=Moto.ogg
[    1.062132] ro.config.alarm_alert=Oxygen.ogg
[    1.062138] ro.config.max_starting_bg=8
[    1.062143] ro.config.vc_call_vol_steps=8
[    1.062149] ro.config.notification_sound=Moto.ogg
[    1.062156]
[    1.062163] Supported API: 3
[    1.069776] charge_status 3, charged 0, status 0, capacity 100
[    1.110649] I:current maximum temperature: 35080
[    1.137512] Finding update package...
[    1.202596] I:Update location: /cache/OTA_Package_OPSS28.65-36-9.zip
[    1.202618] Opening update package...
[    1.237146] I:read key e=65537 hash=32
[    1.237164] I:1 key(s) loaded from /res/keys
[    1.237172] Verifying update package...
[    1.271743] I:comment is 1465 bytes; signature is 1447 bytes from end
[    2.689321] I:signature (offset: 7e85f41, length: 5a1): 3082059d06092a864886f70d010702a082058e3082058a020101310f300d06096086480165030402010500300b06092a864886f70d010701a08203bf308203bb308202a3a00302010202021ca7300d06092a864886f70d01010b05003071310b30090603550406130255533111300f06035504081308496c6c696e6f6973311530130603550407130c4c69626572747976696c6c653111300f060355040a13084d6f746f726f6c61310c300a060355040b13034d4462311730150603550403130e4d415034204f5441203436362d36301e170d3136313132333230333932315a170d3431313132333230333932315a3073310b30090603550406130255533111300f06035504081308496c6c696e6f69733110300e060355040713074368696361676f310f300d060355040a13064c656e6f766f3111300f060355040b13084d6f746f726f6c61311b3019060355040313124d415034204f5441203436362d362d31383930820122300d06092a864886f70d01010105000382010f003082010a0282010100c798de53bb884cb850e5117eabb051e8909a8060eaa13cd7ab863702cb1a281a1eb67514a1ad03627d4f5d9e76f47558c766c8141fc8151dd7fd52cfbf6a3a342a58bd7495cfcd4e5c513d1a9d2738c71ca6b598aceb0ff8b70a2eab6a52d8c0c4e7e8dfa6c8cd93e475c4507926c893a4d60f504827d280d02ee2db74fc5fdf75787a12e1545eeed1029699185da87cdb41519c2725cfce54546f91fd07fa398cf4b85590f10f1cdf73bbb1268be5514b36435a25f37067fa6cb0a76896e000a209889549c17bce1be708a55cb0c0ef7add2a430877fcfa9173c053c4f1bf526407aef3bdf25da19ffa44b420c6eb9b60ecef34c69d5a274f2f1ee621e2c0350203010001a35b3059301f0603551d23041830168014b640f6ee9102b88daa3450b13ef25fc9eb143d63301106096086480186f8420101040403020410300e0603551d0f0101ff0404030205e030130603551d25040c300a06082b06010505070303300d06092a864886f70d01010b050003820101006e0ddef7ffa7f25e441da5484187980ff51611733f8b67ed0b6b44e222734486616dfbb65842360fe228c8149cb78e2872c08b7b05f8a233939507dbf976911143e18cbd58ae194847b99e0ec24a715b8ac3e414279e821bd458584177670bfad97d550d543cf395174e3494d4e9359daadfa26e3db3459d1e09c66446801679383148b6ba6e4d0fcc93bfbbc0876b328e0e7b8251c17893386affa9fc768a7cbfdff7d7e10571c090a8d8300a84541a43e65f9c8aa8aefa7cc1813fe5e7090297327ada6a7b75757942c4f47cc17f80b9ecf490815fd3e8a889fc12737818d80c8d993965bd1b4dc881a29c299c5241b27ec5a40e881109d1b04978c7e8b9b7318201a23082019e02010130773071310b30090603550406130255533111300f06035504081308496c6c696e6f6973311530130603550407130c4c69626572747976696c6c653111300f060355040a13084d6f746f726f6c61310c300a060355040b13034d4462311730150603550403130e4d415034204f5441203436362d3602021ca7300d06096086480165030402010500300d06092a864886f70d010101050004820100b4e56a822c0b708526ae8fda83db24ffef0d454598da0939ae4dd2715f9086f498f607693b3a53e134d2356d4fdced0a06c210bb8840ef589d7b1447579787c1174fd66b9b23ada72c7509dd6ba4cd8ea4db76ed3fec6b64898f4a92e31e806fa7848046bfb420afc83ffc7851f622d20b515147de5de1ebb9f9952d34e3bdb28791eb09d9610198f515447610f90fd364119fcd88ab0e91270acea4a054b651ab45b700ee803ae9d64eddf3daae1fd4e0a7eb924ef18ca68a688bb424f38520e1b8d82a5cc78c9d3679269fe4b47208765eab5bcd8364aed6b141516d855f705ec498106353abb2d816367675bb543f09487749dd273e887be0427318035fa8
[    2.690861] I:whole-file signature verified against RSA key 0
[    2.690872] Update package verification took 1.4 s (result 0).
[    2.706416] I:Verifying package compatibility...
[    2.706444] I:Package doesn't contain compatibility.zip entry
[    2.706452] Installing update...
[    2.770464] installing gptupgrade updater extensions
[    2.780160] SELinux: Loaded file_contexts
[    2.788249] Source: motorola/sanders/sanders:8.1.0/OPSS28.65-36-9/03c05:user/release-keys
[    2.788272] Target: motorola/sanders/sanders:8.1.0/OPS28.65-36-11/431cb:user/release-keys
[    2.788280] Verifying current system...
[    3.086419] partition read matched size 16777216 SHA-1 7fe68c444188a7d1cd8f20244f1da7a6dc4aa81d
[    5.382785] Verified oem partition...
[    5.382817] Checking for stash directory /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ for the device /dev/block/bootdevice/by-name/system upgrade 
[    5.383539] Checking for stash directory /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/ for the device /dev/block/bootdevice/by-name/oem upgrade 
[    5.383579] 119439360 bytes free on /cache (30679040 needed)
[   22.774298] I:current maximum temperature: 36108
[   24.838033] Verified system partition...
[   24.838141] Patching system image after verification.
[   24.838152] performing update
[   24.854600] blockimg version is 4
[   24.854702] maximum stash entries 0
[   24.854713] creating stash /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/
[   24.856025] 119435264 bytes free on /cache (30679040 needed)
[   24.856070]   zeroing 155894 blocks
[   30.311555]   moving 795 blocks
[   30.346591]   moving 34 blocks
[   30.357449]   moving 17 blocks
[   30.687061]   moving 2578 blocks
[   30.773564]   moving 34 blocks
[   30.779041]   moving 14 blocks
[   30.784037]   moving 14 blocks
[   30.797174]   moving 74 blocks
[   30.809417]   moving 55 blocks
[   30.834696]   moving 153 blocks
[   30.847223] patching 23 blocks to 23
[   30.984474]   moving 1118 blocks
[   31.027215] patching 25 blocks to 25
[   31.170792]   moving 1173 blocks
[   32.324632]   moving 8192 blocks
[   33.286849]   moving 4759 blocks
[   33.520724]   moving 27 blocks
[   33.527511]   moving 24 blocks
[   33.533909]   moving 23 blocks
[   33.548273]   moving 109 blocks
[   33.558272]   moving 8 blocks
[   33.561857]   moving 5 blocks
[   33.568883]   moving 37 blocks
[   33.573720]   moving 4 blocks
[   33.575062]   moving 2 blocks
[   33.576972]   moving 6 blocks
[   33.578739]   moving 5 blocks
[   33.614858]   moving 290 blocks
[   33.631898]   moving 24 blocks
[   33.636497]   moving 4 blocks
[   33.641174] patching 24 blocks to 24
[   33.658160]   moving 14 blocks
[   34.367681]   moving 5694 blocks
[   34.653731]   moving 29 blocks
[   34.658107]   moving 2 blocks
[   34.661389]   moving 19 blocks
[   34.671526] patching 52 blocks to 52
[   34.692101]   moving 5 blocks
[   35.622843]   moving 8192 blocks
[   37.581498]   moving 8192 blocks
[   38.338787]   moving 3782 blocks
[   38.535290]   moving 12 blocks
[   38.542736] patching 45 blocks to 45
[   38.563701]   moving 23 blocks
[   38.583377]   moving 145 blocks
[   38.591652]   moving 5 blocks
[   38.597372]   moving 34 blocks
[   38.603281]   moving 20 blocks
[   38.617335] patching 96 blocks to 96
[   39.016423]   moving 3318 blocks
[   39.148214] patching 23 blocks to 23
[   39.164808]   moving 4 blocks
[   39.166851]   moving 4 blocks
[   39.170269]   moving 16 blocks
[   39.831773]   moving 5678 blocks
[   40.101515] patching 6 blocks to 6
[   40.118025]   moving 51 blocks
[   40.123217]   moving 12 blocks
[   40.126348]   moving 7 blocks
[   40.140484]   moving 94 blocks
[   40.151170]   moving 29 blocks
[   40.156312]   moving 8 blocks
[   40.158089]   moving 7 blocks
[   40.168175]   moving 73 blocks
[   40.175347]   moving 4 blocks
[   40.183588]   moving 56 blocks
[   40.191205]   moving 4 blocks
[   40.196477]   moving 36 blocks
[   41.193753] patching 8192 blocks to 8192
[   42.785152] I:current maximum temperature: 35521
[   44.475975] patching 546 blocks to 392
[   44.971656] patching 13 blocks to 13
[   45.278868] patching 2599 blocks to 2599
[   45.604965]   moving 143 blocks
[   45.614920]   moving 4 blocks
[   45.619718]   moving 42 blocks
[   45.626890]   moving 25 blocks
[   45.632641]   moving 18 blocks
[   45.647811]   moving 5 blocks
[   45.650680]   moving 10 blocks
[   45.653568]   moving 8 blocks
[   45.665487]   moving 103 blocks
[   45.965322] patching 2654 blocks to 2654
[   46.621408]   moving 80 blocks
[   46.640424] patching 106 blocks to 107
[   46.674609]   moving 10 blocks
[   46.678640]   moving 9 blocks
[   46.681458]   moving 9 blocks
[   46.703195]   moving 180 blocks
[   46.713612]   moving 4 blocks
[   46.716255]   moving 8 blocks
[   46.720233]   moving 5 blocks
[   46.721948]   moving 1 blocks
[   46.722847]   moving 4 blocks
[   46.726028]   moving 13 blocks
[   46.727852]   moving 11 blocks
[   46.729317]   moving 4 blocks
[   46.731418]   moving 6 blocks
[   46.749404]   moving 143 blocks
[   46.758608]   moving 5 blocks
[   46.762382] patching 24 blocks to 24
[   46.781055] patching 29 blocks to 29
[   46.845882]   moving 455 blocks
[   46.865553]   moving 3 blocks
[   46.866805]   moving 5 blocks
[   46.880059]   moving 124 blocks
[   46.889567]   moving 6 blocks
[   46.933259]   moving 377 blocks
[   46.949006]   moving 9 blocks
[   46.951568]   moving 5 blocks
[   46.952790]   moving 4 blocks
[   46.955039]   moving 12 blocks
[   46.957895]   moving 12 blocks
[   46.960682]   moving 15 blocks
[   46.965266]   moving 7 blocks
[   46.966965]   moving 5 blocks
[   46.969972]   moving 20 blocks
[   46.973737]   moving 9 blocks
[   46.975701]   moving 9 blocks
[   46.979343]   moving 11 blocks
[   46.982721]   moving 8 blocks
[   46.984956]   moving 12 blocks
[   46.986583]   moving 4 blocks
[   46.987920]   moving 5 blocks
[   46.991008]   moving 18 blocks
[   46.996240]   moving 15 blocks
[   47.010068]   moving 122 blocks
[   47.020047]   moving 21 blocks
[   47.022674]   moving 6 blocks
[   47.025539] patching 17 blocks to 17
[   47.038216]   moving 5 blocks
[   47.039611]   moving 5 blocks
[   47.044801]   moving 36 blocks
[   47.049750] patching 12 blocks to 12
[   47.061015]   moving 6 blocks
[   47.063663]   moving 7 blocks
[   47.066018]   moving 12 blocks
[   47.068630]   moving 14 blocks
[   47.076890]   moving 58 blocks
[   47.083017]   moving 4 blocks
[   47.084963]   moving 5 blocks
[   47.088878]   moving 29 blocks
[   47.092764]   moving 5 blocks
[   47.094065]   moving 8 blocks
[   47.095874]   moving 7 blocks
[   47.097617]   moving 5 blocks
[   47.099398]   moving 8 blocks
[   47.103315]   moving 7 blocks
[   47.105204]   moving 4 blocks
[   47.106481]   moving 5 blocks
[   47.270623]   moving 1740 blocks
[   47.332930]   moving 29 blocks
[   47.337413]   moving 16 blocks
[   47.340644] patching 6 blocks to 6
[   47.357271]   moving 68 blocks
[   47.364001]   moving 13 blocks
[   47.366282]   moving 9 blocks
[   47.367560]   moving 6 blocks
[   47.369887]   moving 11 blocks
[   47.374858]   moving 15 blocks
[   47.380101]   moving 17 blocks
[   47.382540]   moving 12 blocks
[   47.387247]   moving 15 blocks
[   47.392002]   moving 17 blocks
[   47.394912]   moving 17 blocks
[   47.399418]   moving 12 blocks
[   47.402408]   moving 17 blocks
[   47.406296]   moving 9 blocks
[   47.409726]   moving 12 blocks
[   47.412944]   moving 17 blocks
[   47.417912]   moving 15 blocks
[   47.422488]   moving 12 blocks
[   47.425445]   moving 12 blocks
[   47.428205]   moving 15 blocks
[   47.432191]   moving 12 blocks
[   47.435657]   moving 12 blocks
[   47.438373]   moving 15 blocks
[   47.441322]   moving 12 blocks
[   47.445888]   moving 15 blocks
[   47.449822]   moving 12 blocks
[   47.452725]   moving 17 blocks
[   47.457336]   moving 17 blocks
[   47.467249]   moving 17 blocks
[   47.472833]   moving 17 blocks
[   47.477164]   moving 15 blocks
[   47.480206]   moving 17 blocks
[   47.484682]   moving 17 blocks
[   47.487477]   moving 15 blocks
[   47.492269]   moving 17 blocks
[   47.496238]   moving 9 blocks
[   47.498646]   moving 15 blocks
[   47.503313]   moving 15 blocks
[   47.508232]   moving 17 blocks
[   47.510553]   moving 12 blocks
[   47.516184]   moving 17 blocks
[   47.520154]   moving 12 blocks
[   47.523623]   moving 17 blocks
[   47.528168]   moving 17 blocks
[   47.531269]   moving 17 blocks
[   47.535975]   moving 12 blocks
[   47.538106]   moving 5 blocks
[   47.540731]   moving 15 blocks
[   47.544340]   moving 17 blocks
[   47.548522]   moving 12 blocks
[   47.552730]   moving 30 blocks
[   47.558037]   moving 12 blocks
[   47.563556]   moving 38 blocks
[   47.568818]   moving 17 blocks
[   47.573804]   moving 15 blocks
[   47.578360]   moving 17 blocks
[   47.581401]   moving 17 blocks
[   47.586232]   moving 17 blocks
[   47.588861]   moving 15 blocks
[   47.593104]   moving 5 blocks
[   47.595480]   moving 12 blocks
[   47.600917]   moving 17 blocks
[   47.603475]   moving 15 blocks
[   47.607600]   moving 5 blocks
[   47.610187]   moving 17 blocks
[   47.614067]   moving 12 blocks
[   47.616742]   moving 15 blocks
[   47.622142]   moving 15 blocks
[   47.627563]   moving 17 blocks
[   47.630010]   moving 12 blocks
[   47.634159]   moving 12 blocks
[   47.638030]   moving 15 blocks
[   47.641200]   moving 15 blocks
[   47.643439]   moving 9 blocks
[   47.648419]   moving 15 blocks
[   47.653527]   moving 17 blocks
[   47.656460]   moving 17 blocks
[   47.659834]   moving 5 blocks
[   47.662243]   moving 12 blocks
[   47.665355]   moving 17 blocks
[   47.670090]   moving 12 blocks
[   47.674425]   moving 15 blocks
[   47.677531]   moving 17 blocks
[   47.681350]   moving 12 blocks
[   47.684841]   moving 12 blocks
[   47.688250]   moving 29 blocks
[   47.694386]   moving 17 blocks
[   47.697252]   moving 15 blocks
[   47.702263]   moving 15 blocks
[   47.706699]   moving 12 blocks
[   47.709362]   moving 12 blocks
[   47.712049]   moving 12 blocks
[   47.717178]   moving 17 blocks
[   47.722090]   moving 17 blocks
[   47.727125]   moving 17 blocks
[   47.731317]   moving 17 blocks
[   47.736077]   moving 12 blocks
[   47.740099]   moving 12 blocks
[   47.742447]   moving 12 blocks
[   47.745998]   moving 17 blocks
[   47.750633]   moving 15 blocks
[   47.753705]   moving 17 blocks
[   47.759273]   moving 17 blocks
[   47.763594]   moving 12 blocks
[   47.775834]   moving 5 blocks
[   47.778481]   moving 17 blocks
[   47.783121]   moving 17 blocks
[   47.786036]   moving 17 blocks
[   47.790340]   moving 12 blocks
[   47.800829]   moving 96 blocks
[   47.809092]   moving 15 blocks
[   47.811980]   moving 12 blocks
[   47.817228]   moving 12 blocks
[   47.821271]   moving 12 blocks
[   47.824009]   moving 15 blocks
[   47.826122]   moving 12 blocks
[   47.830972]   moving 15 blocks
[   47.834450]   moving 5 blocks
[   47.836724]   moving 17 blocks
[   47.839769]   moving 17 blocks
[   47.844598]   moving 12 blocks
[   47.846212]   moving 6 blocks
[   47.848992]   moving 17 blocks
[   47.853006]   moving 15 blocks
[   47.856893]   moving 15 blocks
[   47.860384]   moving 17 blocks
[   47.865820]   moving 15 blocks
[   47.870401]   moving 17 blocks
[   47.871787]   moving 5 blocks
[   47.874625]   moving 17 blocks
[   47.878584]   moving 12 blocks
[   47.879805]   moving 5 blocks
[   47.882501]   moving 15 blocks
[   47.884916]   moving 15 blocks
[   47.889816]   moving 12 blocks
[   47.893795]   moving 12 blocks
[   47.896805]   moving 12 blocks
[   47.899872]   moving 15 blocks
[   47.904734]   moving 15 blocks
[   47.909145]   moving 17 blocks
[   47.911510]   moving 17 blocks
[   47.915536]   moving 12 blocks
[   47.917883]   moving 15 blocks
[   47.920515]   moving 12 blocks
[   47.924820]   moving 17 blocks
[   47.927822]   moving 5 blocks
[   47.928848]   moving 4 blocks
[   47.931640]   moving 12 blocks
[   47.933378]   moving 12 blocks
[   47.937626]   moving 15 blocks
[   47.941918]   moving 17 blocks
[   47.943778]   moving 12 blocks
[   47.947719]   moving 17 blocks
[   47.950697]   moving 5 blocks
[   47.952954]   moving 12 blocks
[   47.955948]   moving 17 blocks
[   47.959743]   moving 5 blocks
[   47.961192]   moving 4 blocks
[   47.962252]   moving 5 blocks
[   47.968521]   moving 30 blocks
[   47.972439]   moving 10 blocks
[   47.977045]   moving 22 blocks
[   47.979983]   moving 9 blocks
[   47.983388]   moving 6 blocks
[   47.986725]   moving 23 blocks
[   47.991353]   moving 17 blocks
[   48.006905]   moving 118 blocks
[   48.018879]   moving 40 blocks
[   48.025105]   moving 37 blocks
[   48.032416] patching 31 blocks to 31
[   48.076477]   moving 321 blocks
[   48.350638]   moving 2578 blocks
[   48.473803]   moving 8 blocks
[   48.499276]   moving 6 blocks
[   48.523828]   moving 19 blocks
[   48.549268]   moving 5 blocks
[   48.590560]   moving 190 blocks
[   48.647522]   moving 13 blocks
[   48.658189]   moving 21 blocks
[   48.664176]   moving 21 blocks
[   48.668192]   moving 5 blocks
[   48.670956]   moving 9 blocks
[   48.726477] patching 465 blocks to 465
[   48.799301]   moving 154 blocks
[   48.809662]   moving 6 blocks
[   48.814009]   moving 25 blocks
[   48.824260]   moving 60 blocks
[   48.836356]   moving 57 blocks
[   48.864770]   moving 207 blocks
[   48.875856]   moving 10 blocks
[   48.879543]   moving 17 blocks
[   48.885908]   moving 21 blocks
[   48.888308]   moving 4 blocks
[   48.892844]   moving 29 blocks
[   48.913165]   moving 143 blocks
[   48.939166]   moving 143 blocks
[   48.965424]   moving 143 blocks
[   48.991408]   moving 143 blocks
[   49.025723]   moving 143 blocks
[   49.051357]   moving 143 blocks
[   49.077741]   moving 143 blocks
[   49.087796]   moving 5 blocks
[   49.091598]   moving 20 blocks
[   49.094344]   moving 7 blocks
[   49.096812]   moving 8 blocks
[   49.101843]   moving 14 blocks
[   49.106343]   moving 13 blocks
[   49.109771]   moving 14 blocks
[   49.113589]   moving 14 blocks
[   49.117354]   moving 7 blocks
[   49.120268]   moving 14 blocks
[   49.123697]   moving 8 blocks
[   49.125870]   moving 9 blocks
[   49.129895]   moving 18 blocks
[   49.134474]   moving 9 blocks
[   49.136719]   moving 7 blocks
[   49.139512]   moving 11 blocks
[   49.142179]   moving 11 blocks
[   49.146308]   moving 11 blocks
[   49.149634]   moving 7 blocks
[   49.151980]   moving 7 blocks
[   49.154273]   moving 7 blocks
[   49.156317]   moving 6 blocks
[   49.158648]   moving 6 blocks
[   49.161005]   moving 10 blocks
[   49.163181]   moving 6 blocks
[   49.165161]   moving 6 blocks
[   49.167700]   moving 6 blocks
[   49.169562]   moving 7 blocks
[   49.171592]   moving 9 blocks
[   49.173150]   moving 7 blocks
[   49.174687]   moving 7 blocks
[   49.176248]   moving 7 blocks
[   49.180604]   moving 21 blocks
[   49.200751]   moving 143 blocks
[   49.210778] patching 5 blocks to 5
[   49.222762]   moving 11 blocks
[   49.225120]   moving 8 blocks
[   49.230098]   moving 18 blocks
[   49.234762]   moving 16 blocks
[   49.239255]   moving 6 blocks
[   49.241155] patching 4 blocks to 4
[   49.253814]   moving 20 blocks
[   49.258683]   moving 14 blocks
[   49.260920]   moving 6 blocks
[   49.262507]   moving 4 blocks
[   49.264657]   moving 7 blocks
[   49.270589]   moving 44 blocks
[   49.276503]   moving 6 blocks
[   49.278346]   moving 4 blocks
[   49.280857]   moving 11 blocks
[   49.285381]   moving 12 blocks
[   49.289434]   moving 20 blocks
[   49.309361]   moving 143 blocks
[   49.319075]   moving 5 blocks
[   49.337738]   moving 143 blocks
[   49.360694]   moving 143 blocks
[   49.370569]   moving 7 blocks
[   49.372506]   moving 5 blocks
[   49.373999]   moving 6 blocks
[   49.375336]   moving 4 blocks
[   49.379387]   moving 21 blocks
[   49.385287]   moving 20 blocks
[   49.387520]   moving 4 blocks
[   49.389333]   moving 8 blocks
[   49.392926]   moving 9 blocks
[   49.395754]   moving 5 blocks
[   49.400997]   moving 44 blocks
[   49.419133]   moving 143 blocks
[   49.428846]   moving 14 blocks
[   49.440438] patching 87 blocks to 87
[   49.480002]   moving 142 blocks
[   49.496881]   moving 17 blocks
[   49.500991]   moving 6 blocks
[   49.502673]   moving 5 blocks
[   49.507173] patching 34 blocks to 34
[   49.532614]   moving 73 blocks
[   49.540560]   moving 29 blocks
[   49.556564] patching 117 blocks to 117
[   49.595405] patching 61 blocks to 61
[   49.617181]   moving 5 blocks
[   49.620912]   moving 26 blocks
[   49.632658]   moving 81 blocks
[   49.641294]   moving 21 blocks
[   49.646983]   moving 24 blocks
[   49.653788]   moving 30 blocks
[   49.657760]   moving 9 blocks
[   49.661412]   moving 10 blocks
[   49.664606]   moving 8 blocks
[   49.666345]   moving 8 blocks
[   49.669313]   moving 19 blocks
[   49.673836] patching 7 blocks to 7
[   49.687607]   moving 27 blocks
[   49.691511]   moving 5 blocks
[   49.695741]   moving 31 blocks
[   49.699329]   moving 7 blocks
[   49.706650]   moving 14 blocks
[   49.739700]   moving 31 blocks
[   49.770153]   moving 57 blocks
[   49.798209]   moving 8 blocks
[   49.831672]   moving 97 blocks
[   49.862590]   moving 17 blocks
[   49.889741]   moving 4 blocks
[   49.914022]   moving 4 blocks
[   49.937928]   moving 25 blocks
[   49.942222]   moving 6 blocks
[   49.951684]   moving 66 blocks
[   49.961196]   moving 28 blocks
[   49.978768] patching 112 blocks to 112
[   50.002211]   moving 6 blocks
[   50.004410]   moving 6 blocks
[   50.006829]   moving 7 blocks
[   50.009116]   moving 7 blocks
[   50.011669]   moving 10 blocks
[   50.014742]   moving 5 blocks
[   50.016104]   moving 6 blocks
[   50.018657]   moving 20 blocks
[   50.021370]   moving 20 blocks
[   50.054244] patching 248 blocks to 248
[   50.099693]   moving 7 blocks
[   50.102244]   moving 10 blocks
[   50.112029]   moving 55 blocks
[   50.117244]   moving 6 blocks
[   50.118964]   moving 5 blocks
[   50.120963]   moving 8 blocks
[   50.125560]   moving 10 blocks
[   50.130608] patching 18 blocks to 18
[   50.143791]   moving 14 blocks
[   50.147657]   moving 7 blocks
[   50.149879]   moving 7 blocks
[   50.151928]   moving 5 blocks
[   50.154357]   moving 7 blocks
[   50.156860]   moving 10 blocks
[   50.160301] patching 7 blocks to 7
[   50.172122] patching 11 blocks to 11
[   50.186194] patching 29 blocks to 29
[   50.201935] patching 10 blocks to 10
[   50.216235] patching 30 blocks to 30
[   50.231748] patching 6 blocks to 6
[   50.244152] patching 18 blocks to 18
[   50.259832] patching 38 blocks to 38
[   50.279190]   moving 38 blocks
[   50.288194] patching 48 blocks to 48
[   50.310487] patching 54 blocks to 54
[   50.328378]   moving 11 blocks
[   50.340673]   moving 78 blocks
[   50.364570]   moving 146 blocks
[   50.374699]   moving 21 blocks
[   50.493971]   moving 1132 blocks
[   50.548260]   moving 48 blocks
[   50.555087]   moving 18 blocks
[   50.559951]   moving 24 blocks
[   50.571390]   moving 74 blocks
[   50.584470]   moving 65 blocks
[   50.591686]   moving 10 blocks
[   50.593886]   moving 10 blocks
[   50.602742]   moving 58 blocks
[   50.612306] patching 42 blocks to 42
[   50.635626] patching 33 blocks to 33
[   50.655476] patching 34 blocks to 34
[   50.687379] patching 143 blocks to 143
[   50.729617] patching 12 blocks to 12
[   50.744507] patching 40 blocks to 40
[   50.761928] patching 28 blocks to 28
[   50.776343]   moving 2 blocks
[   50.777575]   moving 2 blocks
[   50.778838]   moving 2 blocks
[   50.779869]   moving 2 blocks
[   50.781351]   moving 3 blocks
[   50.782750]   moving 3 blocks
[   50.783800] patching 2 blocks to 2
[   50.793653]   moving 4 blocks
[   50.795306]   moving 3 blocks
[   50.796284] patching 2 blocks to 2
[   50.805674]   moving 3 blocks
[   50.807030]   moving 3 blocks
[   50.808009] patching 2 blocks to 2
[   50.818630]   moving 14 blocks
[   50.820912]   moving 12 blocks
[   50.825324]   moving 12 blocks
[   50.830324]   moving 24 blocks
[   50.836510]   moving 24 blocks
[   50.841884]   moving 24 blocks
[   50.845871]   moving 12 blocks
[   50.848339]   moving 12 blocks
[   50.852672]   moving 9 blocks
[   50.855888]   moving 3 blocks
[   50.856972]   moving 1 blocks
[   50.858894]   moving 13 blocks
[   50.861199]   moving 13 blocks
[   50.863495]   moving 13 blocks
[   50.867342]   moving 3 blocks
[   50.868196]   moving 1 blocks
[   50.869018]   moving 2 blocks
[   50.870228]   moving 1 blocks
[   50.871422]   moving 4 blocks
[   50.872777]   moving 3 blocks
[   50.874254]   moving 5 blocks
[   50.875488]   moving 4 blocks
[   50.876975]   moving 2 blocks
[   50.878233]   moving 3 blocks
[   50.879163]   moving 2 blocks
[   50.880179]   moving 3 blocks
[   50.881281]   moving 1 blocks
[   50.882321]   moving 3 blocks
[   50.883439]   moving 1 blocks
[   50.884462]   moving 3 blocks
[   50.885744]   moving 3 blocks
[   50.886540]   moving 1 blocks
[   50.887289]   moving 1 blocks
[   50.888021]   moving 1 blocks
[   50.888771]   moving 1 blocks
[   50.889486]   moving 1 blocks
[   50.890250]   moving 1 blocks
[   50.890999]   moving 1 blocks
[   50.891753]   moving 1 blocks
[   50.892678]   moving 3 blocks
[   50.894460]   moving 8 blocks
[   50.897657]   moving 8 blocks
[   50.899322]   moving 8 blocks
[   50.901005]   moving 8 blocks
[   50.904557]   moving 3 blocks
[   50.906605]   moving 11 blocks
[   50.909549]   moving 1 blocks
[   50.915054] patching 48 blocks to 48
[   50.928805]   moving 1 blocks
[   50.929677]   moving 1 blocks
[   50.930519]   moving 1 blocks
[   50.931397]   moving 1 blocks
[   50.932429]   moving 3 blocks
[   50.933572]   moving 1 blocks
[   50.934512]   moving 2 blocks
[   50.935337]   moving 1 blocks
[   50.936181]   moving 1 blocks
[   50.937005]   moving 1 blocks
[   50.937854]   moving 1 blocks
[   50.938667]   moving 1 blocks
[   50.975167]   moving 375 blocks
[   51.026877]   moving 375 blocks
[   51.041888]   moving 1 blocks
[   51.042833]   moving 2 blocks
[   51.043711]   moving 1 blocks
[   51.044565]   moving 1 blocks
[   51.045390]   moving 1 blocks
[   51.046250]   moving 1 blocks
[   51.047091]   moving 1 blocks
[   51.047926]   moving 1 blocks
[   51.048745]   moving 1 blocks
[   51.049576]   moving 1 blocks
[   51.050862]   moving 1 blocks
[   51.051705]   moving 1 blocks
[   51.052512]   moving 1 blocks
[   51.053350]   moving 1 blocks
[   51.054172]   moving 1 blocks
[   51.054997]   moving 1 blocks
[   51.055788]   moving 1 blocks
[   51.056608]   moving 1 blocks
[   51.057410]   moving 1 blocks
[   51.058250]   moving 1 blocks
[   51.059050]   moving 1 blocks
[   51.059884]   moving 1 blocks
[   51.060685]   moving 1 blocks
[   51.061530]   moving 1 blocks
[   51.062324]   moving 1 blocks
[   51.063165]   moving 1 blocks
[   51.063976]   moving 1 blocks
[   51.064832]   moving 1 blocks
[   51.065624]   moving 1 blocks
[   51.066450]   moving 1 blocks
[   51.067258]   moving 1 blocks
[   51.068143]   moving 1 blocks
[   51.068985]   moving 1 blocks
[   51.069932]   moving 2 blocks
[   51.071469]   moving 5 blocks
[   51.073089]   moving 5 blocks
[   51.074995]   moving 5 blocks
[   51.075940]   moving 1 blocks
[   51.076783]   moving 1 blocks
[   51.077578]   moving 1 blocks
[   51.079138]   moving 9 blocks
[   51.082593]   moving 7 blocks
[   51.084447]   moving 4 blocks
[   51.086178]   moving 6 blocks
[   51.087905]   moving 6 blocks
[   51.089629]   moving 1 blocks
[   51.090537]   moving 2 blocks
[   51.091620]   moving 3 blocks
[   51.092964]   moving 3 blocks
[   51.094204]   moving 2 blocks
[   51.095182]   moving 2 blocks
[   51.096051]   moving 1 blocks
[   51.096985]   moving 2 blocks
[   51.098517]   moving 5 blocks
[   51.100020]   moving 5 blocks
[   51.101245]   moving 3 blocks
[   51.102413]   moving 1 blocks
[   51.103936]   moving 8 blocks
[   51.106878]   moving 5 blocks
[   51.108216]   moving 2 blocks
[   51.109063]   moving 1 blocks
[   51.109936]   moving 1 blocks
[   51.110796]   moving 1 blocks
[   51.111627]   moving 1 blocks
[   51.112434]   moving 1 blocks
[   51.113263]   moving 1 blocks
[   51.114073]   moving 1 blocks
[   51.114926]   moving 1 blocks
[   51.115745]   moving 1 blocks
[   51.116582]   moving 1 blocks
[   51.117387]   moving 1 blocks
[   51.118246] patching 1 blocks to 1
[   51.127492]   moving 1 blocks
[   51.128402]   moving 1 blocks
[   51.129230]   moving 1 blocks
[   51.130302]   moving 3 blocks
[   51.132279]   moving 9 blocks
[   51.133487]   moving 2 blocks
[   51.134402]   moving 1 blocks
[   51.135214]   moving 1 blocks
[   51.136521]   moving 6 blocks
[   51.137779]   moving 1 blocks
[   51.138994]   moving 5 blocks
[   51.140450]   moving 1 blocks
[   51.141280]   moving 1 blocks
[   51.142112]   moving 1 blocks
[   51.142921]   moving 1 blocks
[   51.143751]   moving 1 blocks
[   51.144568]   moving 1 blocks
[   51.145409]   moving 1 blocks
[   51.146198]   moving 1 blocks
[   51.147037]   moving 1 blocks
[   51.148118]   moving 1 blocks
[   51.149600]   moving 1 blocks
[   51.150417]   moving 1 blocks
[   51.151278]   moving 1 blocks
[   51.152096]   moving 1 blocks
[   51.152965]   moving 1 blocks
[   51.153808]   moving 1 blocks
[   51.154680]   moving 1 blocks
[   51.155500]   moving 1 blocks
[   51.156373]   moving 1 blocks
[   51.157197]   moving 1 blocks
[   51.158070]   moving 1 blocks
[   51.158887]   moving 1 blocks
[   51.159724]   moving 1 blocks
[   51.160513]   moving 1 blocks
[   51.161422]   moving 1 blocks
[   51.162269]   moving 1 blocks
[   51.163097]   moving 1 blocks
[   51.163925]   moving 1 blocks
[   51.164870]   moving 2 blocks
[   51.165974]   moving 1 blocks
[   51.166803]   moving 1 blocks
[   51.167647]   moving 1 blocks
[   51.168590]   moving 2 blocks
[   51.169587]   moving 2 blocks
[   51.170557]   moving 2 blocks
[   51.171408]   moving 1 blocks
[   51.172237]   moving 1 blocks
[   51.173030]   moving 1 blocks
[   51.173896]   moving 1 blocks
[   51.174727]   moving 1 blocks
[   51.175663]   moving 2 blocks
[   51.176802]   moving 1 blocks
[   51.177655]   moving 1 blocks
[   51.178636]   moving 1 blocks
[   51.181785]   moving 24 blocks
[   51.186843]   moving 9 blocks
[   51.188130]   moving 3 blocks
[   51.189398]   moving 3 blocks
[   51.190677]   moving 3 blocks
[   51.191723]   moving 3 blocks
[   51.193006]   moving 3 blocks
[   51.194270]   moving 3 blocks
[   51.195588]   moving 3 blocks
[   51.196847]   moving 3 blocks
[   51.197938]   moving 3 blocks
[   51.199249]   moving 3 blocks
[   51.200592]   moving 3 blocks
[   51.201651]   moving 3 blocks
[   51.202970]   moving 3 blocks
[   51.204325]   moving 3 blocks
[   51.205642]   moving 3 blocks
[   51.206911]   moving 3 blocks
[   51.207968]   moving 3 blocks
[   51.209226]   moving 3 blocks
[   51.210525]   moving 3 blocks
[   51.211610]   moving 3 blocks
[   51.212951]   moving 3 blocks
[   51.214343]   moving 3 blocks
[   51.215741]   moving 3 blocks
[   51.217046]   moving 3 blocks
[   51.218143]   moving 3 blocks
[   51.219435]   moving 3 blocks
[   51.220724]   moving 3 blocks
[   51.222273]   moving 8 blocks
[   51.223472]   moving 2 blocks
[   51.224299]   moving 1 blocks
[   51.225102]   moving 1 blocks
[   51.225870]   moving 1 blocks
[   51.226791]   moving 2 blocks
[   51.227710]   moving 1 blocks
[   51.229284]   moving 7 blocks
[   51.231815]   moving 14 blocks
[   51.234729]   moving 2 blocks
[   51.243421]   moving 81 blocks
[   51.253212]   moving 35 blocks
[   51.257677]   moving 1 blocks
[   51.266419]   moving 82 blocks
[   51.273506]   moving 2 blocks
[   51.274905]   moving 6 blocks
[   51.277503]   moving 16 blocks
[   51.286045]   moving 61 blocks
[   51.291324]   moving 1 blocks
[   51.292100]   moving 1 blocks
[   51.294875]   moving 20 blocks
[   51.322098]   moving 267 blocks
[   51.339028]   moving 31 blocks
[   51.343173]   moving 1 blocks
[   51.344643]   moving 7 blocks
[   51.346645]   moving 8 blocks
[   51.350050]   moving 23 blocks
[   51.354225]   moving 7 blocks
[   51.356360]   moving 10 blocks
[   51.358536]   moving 8 blocks
[   51.369592]   moving 102 blocks
[   51.378223]   moving 5 blocks
[   51.379758]   moving 5 blocks
[   51.381945]   moving 10 blocks
[   51.494674]   moving 1160 blocks
[   51.644417]   moving 21 blocks
[   51.667712]   moving 6 blocks
[   51.693016]   moving 24 blocks
[   51.736806]   moving 159 blocks
[   51.779133]   moving 10 blocks
[   51.780992]   moving 5 blocks
[   51.782602]   moving 5 blocks
[   51.788449] patching 44 blocks to 44
[   51.809742]   moving 49 blocks
[   51.820044] patching 46 blocks to 46
[   51.841012]   moving 10 blocks
[   51.844630]   moving 26 blocks
[   51.849243]   moving 6 blocks
[   51.851618]   moving 11 blocks
[   51.856325]   moving 28 blocks
[   51.860873]   moving 10 blocks
[   51.869362]   moving 6 blocks
[   51.871448]   moving 6 blocks
[   51.873363]   moving 1 blocks
[   51.875204]   moving 9 blocks
[   51.880204]   moving 12 blocks
[   51.884594]   moving 12 blocks
[   51.923311]   moving 328 blocks
[   51.939320]   moving 10 blocks
[   51.944309]   moving 19 blocks
[   51.947870]   moving 9 blocks
[   51.958734] patching 80 blocks to 80
[   51.981090]   moving 7 blocks
[   51.983348]   moving 7 blocks
[   51.985802]   moving 9 blocks
[   51.989190]   moving 20 blocks
[   51.997721]   moving 48 blocks
[   52.004348]   moving 9 blocks
[   52.014656]   moving 78 blocks
[   52.034475]   moving 114 blocks
[   52.051318] patching 70 blocks to 70
[   52.117119] patching 412 blocks to 412
[   53.176758] patching 8192 blocks to 8192
[   56.688446]   moving 18 blocks
[   56.715303]   moving 5 blocks
[   56.743435]   moving 29 blocks
[   56.772040]   moving 5 blocks
[   56.776673]   moving 5 blocks
[   56.779078]   moving 8 blocks
[   56.780552]   moving 2 blocks
[   56.783261]   moving 12 blocks
[   56.787511]   moving 6 blocks
[   56.790875]   moving 12 blocks
[   56.800735]   moving 56 blocks
[   56.813564]   moving 6 blocks
[   56.816139] patching 9 blocks to 9
[   56.832561]   moving 41 blocks
[   56.840451]   moving 34 blocks
[   56.845823] patching 5 blocks to 5
[   56.863487] patching 64 blocks to 64
[   56.890631]   moving 6 blocks
[   56.892680]   moving 5 blocks
[   56.897275]   moving 27 blocks
[   56.902408]   moving 13 blocks
[   56.905795]   moving 6 blocks
[   56.908093]   moving 5 blocks
[   56.909902]   moving 4 blocks
[   56.950860]   moving 355 blocks
[   56.968054] patching 23 blocks to 23
[   56.982828]   moving 22 blocks
[   56.987065]   moving 8 blocks
[   56.992028]   moving 32 blocks
[   56.998120]   moving 18 blocks
[   57.002228]   moving 7 blocks
[   57.004517]   moving 8 blocks
[   57.007590]   moving 4 blocks
[   57.009788]   moving 7 blocks
[   57.012970]   moving 12 blocks
[   57.015413] patching 10 blocks to 10
[   57.026697]   moving 5 blocks
[   57.030333]   moving 17 blocks
[   57.038310]   moving 36 blocks
[   57.043225]   moving 10 blocks
[   57.046186]   moving 19 blocks
[   57.053469]   moving 29 blocks
[   57.072948]   moving 134 blocks
[   58.187157] patching 8192 blocks to 8192
[   62.795890] I:current maximum temperature: 35472
[   64.364343] patching 8192 blocks to 8192
[   66.733353]   moving 12 blocks
[   66.736911]   moving 5 blocks
[   66.740448]   moving 17 blocks
[   66.744790]   moving 15 blocks
[   66.748263]   moving 12 blocks
[   66.753242]   moving 12 blocks
[   66.757805]   moving 12 blocks
[   66.760830]   moving 12 blocks
[   66.764102]   moving 12 blocks
[   66.769135]   moving 17 blocks
[   66.773742]   moving 17 blocks
[   66.779813]   moving 17 blocks
[   66.784633]   moving 15 blocks
[   66.786944]   moving 5 blocks
[   66.790186]   moving 17 blocks
[   66.795246]   moving 15 blocks
[   66.798578]   moving 15 blocks
[   66.801575]   moving 12 blocks
[   66.807373]   moving 12 blocks
[   66.812242]   moving 12 blocks
[   66.815367]   moving 12 blocks
[   66.818367]   moving 12 blocks
[   66.824298]   moving 17 blocks
[   66.873839]   moving 408 blocks
[   66.895119]   moving 27 blocks
[   66.899785] patching 6 blocks to 6
[   66.938289]   moving 242 blocks
[   66.972809]   moving 193 blocks
[   67.005951]   moving 186 blocks
[   67.021869]   moving 17 blocks
[   67.025400]   moving 17 blocks
[   67.029966]   moving 15 blocks
[   67.033719]   moving 17 blocks
[   67.039077]   moving 15 blocks
[   67.044622]   moving 15 blocks
[   67.048212]   moving 15 blocks
[   67.051670]   moving 15 blocks
[   67.056189]   moving 15 blocks
[   67.060001]   moving 12 blocks
[   67.063251]   moving 12 blocks
[   67.066140]   moving 12 blocks
[   67.070912]   moving 12 blocks
[   67.075222]   moving 12 blocks
[   67.078392]   moving 12 blocks
[   67.081247]   moving 12 blocks
[   67.085425]   moving 12 blocks
[   67.089435]   moving 12 blocks
[   67.092637]   moving 12 blocks
[   67.095537]   moving 12 blocks
[   67.100186]   moving 12 blocks
[   67.104520]   moving 12 blocks
[   67.108229]   moving 17 blocks
[   67.113466]   moving 17 blocks
[   67.116011]   moving 1 blocks
[   67.117262]   moving 2 blocks
[   67.118111]   moving 1 blocks
[   67.118927]   moving 1 blocks
[   67.121877]   moving 10 blocks
[   67.126436]   moving 15 blocks
[   67.130510]   moving 4 blocks
[   68.231311]   moving 8192 blocks
[   68.585310]   moving 4 blocks
[   68.587356]   moving 4 blocks
[   68.589155]   moving 4 blocks
[   68.591204]   moving 4 blocks
[   68.592991]   moving 4 blocks
[   68.595127] patching 5 blocks to 5
[   68.606292]   moving 4 blocks
[   68.608059]   moving 4 blocks
[   68.609600]   moving 4 blocks
[   68.611406] patching 4 blocks to 4
[   68.621984]   moving 4 blocks
[   68.623698]   moving 4 blocks
[   68.625310]   moving 5 blocks
[   68.627361]   moving 4 blocks
[   68.629455] patching 6 blocks to 6
[   68.641736] patching 12 blocks to 12
[   68.655552]   moving 4 blocks
[   68.659050] patching 18 blocks to 18
[   68.672429]   moving 10 blocks
[   68.676744]   moving 4 blocks
[   68.679778]   moving 14 blocks
[   68.699241]   moving 132 blocks
[   68.712829]   moving 35 blocks
[   68.717395]   moving 2 blocks
[   68.722914] patching 39 blocks to 39
[   68.740876]   moving 8 blocks
[   68.743416]   moving 11 blocks
[   68.745325]   moving 4 blocks
[   68.747563]   moving 7 blocks
[   68.752660]   moving 30 blocks
[   68.758142]   moving 15 blocks
[   68.760333]   moving 5 blocks
[   68.763753]   moving 17 blocks
[   68.768877]   moving 15 blocks
[   68.771336]   moving 5 blocks
[   68.774299]   moving 17 blocks
[   68.779219]   moving 15 blocks
[   68.783956]   moving 17 blocks
[   68.787123]   moving 15 blocks
[   68.791904]   moving 12 blocks
[   68.796456]   moving 12 blocks
[   68.799149]   moving 12 blocks
[   68.801768]   moving 12 blocks
[   68.806425]   moving 17 blocks
[   68.810956]   moving 17 blocks
[   68.816586]   moving 15 blocks
[   68.822470]   moving 17 blocks
[   68.825759]   moving 15 blocks
[   68.829842]   moving 5 blocks
[   68.831118]   moving 1 blocks
[   68.832057]   moving 1 blocks
[   68.835015]   moving 18 blocks
[   68.837713]   moving 1 blocks
[   68.838941]   moving 3 blocks
[   68.857949]   moving 156 blocks
[   68.880195]   moving 102 blocks
[   68.888784]   moving 6 blocks
[   68.898871]   moving 76 blocks
[   68.906497]   moving 16 blocks
[   68.922142] patching 102 blocks to 102
[   68.957171] patching 55 blocks to 55
[   68.981544] patching 21 blocks to 21
[   69.002614] patching 55 blocks to 55
[   69.029774] patching 50 blocks to 50
[   69.054035] patching 41 blocks to 41
[   69.080035] patching 41 blocks to 41
[   69.101813] patching 43 blocks to 43
[   69.124027] patching 49 blocks to 49
[   69.159711] patching 137 blocks to 137
[   69.196036] patching 25 blocks to 26
[   69.216224] patching 33 blocks to 33
[   69.238719] patching 52 blocks to 52
[   69.262414] patching 22 blocks to 22
[   69.281078] patching 30 blocks to 31
[   69.300989] patching 22 blocks to 22
[   69.318537] patching 21 blocks to 21
[   69.337287] patching 32 blocks to 33
[   69.359251] patching 26 blocks to 26
[   69.379443] patching 41 blocks to 41
[   69.402558] patching 22 blocks to 22
[   69.443080] patching 226 blocks to 227
[   69.499807] patching 23 blocks to 23
[   69.518160] patching 21 blocks to 21
[   69.535892] patching 24 blocks to 24
[   69.554389] patching 27 blocks to 27
[   69.575241] patching 36 blocks to 36
[   69.598022] patching 46 blocks to 46
[   69.619097]   moving 5 blocks
[   69.622203]   moving 15 blocks
[   69.624997]   moving 10 blocks
[   69.627077]   moving 4 blocks
[   69.629480]   moving 6 blocks
[   69.633504]   moving 23 blocks
[   69.638000]   moving 6 blocks
[   69.639555]   moving 4 blocks
[   69.641512]   moving 5 blocks
[   69.644842]   moving 17 blocks
[   69.647098]   moving 8 blocks
[   69.651220]   moving 5 blocks
[   69.660715]   moving 71 blocks
[   69.669469]   moving 16 blocks
[   69.680715]   moving 65 blocks
[   69.704870]   moving 152 blocks
[   69.746724]   moving 280 blocks
[   69.877510] patching 1056 blocks to 1056
[   69.982191]   moving 17 blocks
[   69.986702]   moving 12 blocks
[   69.990120]   moving 17 blocks
[   69.995831]   moving 17 blocks
[   70.001264]   moving 17 blocks
[   70.006913]   moving 17 blocks
[   70.011617]   moving 17 blocks
[   70.017094]   moving 15 blocks
[   70.021992]   moving 15 blocks
[   70.025318]   moving 15 blocks
[   70.028990]   moving 17 blocks
[   70.032535]   moving 5 blocks
[   70.035947]   moving 15 blocks
[   70.039715]   moving 17 blocks
[   70.045433]   moving 17 blocks
[   70.050088]   moving 15 blocks
[   70.053689]   moving 17 blocks
[   70.058564]   moving 15 blocks
[   70.062067]   moving 15 blocks
[   70.065535]   moving 15 blocks
[   70.070252]   moving 12 blocks
[   70.074802]   moving 12 blocks
[   70.077396]   moving 12 blocks
[   70.080248]   moving 12 blocks
[   70.084406]   moving 12 blocks
[   70.088230]   moving 12 blocks
[   70.090676]   moving 12 blocks
[   70.093369]   moving 12 blocks
[   70.103477]   moving 12 blocks
[   70.107860]   moving 12 blocks
[   70.110310]   moving 12 blocks
[   70.113003]   moving 12 blocks
[   70.117718]   moving 17 blocks
[   70.122273]   moving 17 blocks
[   70.128079]   moving 17 blocks
[   70.133083]   moving 17 blocks
[   70.137773]   moving 17 blocks
[   70.141118]   moving 4 blocks
[   70.143520]   moving 8 blocks
[   70.146746]   moving 17 blocks
[   70.152344]   moving 17 blocks
[   70.155645]   moving 15 blocks
[   70.164193]   moving 40 blocks
[   70.171444]   moving 16 blocks
[   70.173792]   moving 8 blocks
[   70.178660]   moving 17 blocks
[   70.182941]   moving 15 blocks
[   70.186228]   moving 15 blocks
[   70.191944]   moving 15 blocks
[   70.197097]   moving 17 blocks
[   70.200088]   moving 15 blocks
[   70.205244]   moving 18 blocks
[   70.208344]   moving 4 blocks
[   70.209844]   moving 5 blocks
[   70.211848]   moving 4 blocks
[   70.213976]   moving 8 blocks
[   70.216149]   moving 10 blocks
[   70.221128]   moving 10 blocks
[   70.224605]   moving 4 blocks
[   70.226392]   moving 5 blocks
[   70.233049]   moving 38 blocks
[   70.238517]   moving 12 blocks
[   70.241891]   moving 18 blocks
[   70.248933]   moving 27 blocks
[   70.255714]   moving 31 blocks
[   70.260066]   moving 5 blocks
[   70.276650]   moving 133 blocks
[   70.286581]   moving 10 blocks
[   70.305014]   moving 148 blocks
[   70.349601]   moving 308 blocks
[   70.394337]   moving 161 blocks
[   70.410889]   moving 52 blocks
[   70.421130]   moving 26 blocks
[   70.525610]   moving 859 blocks
[   70.558069]   moving 4 blocks
[   70.561141]   moving 17 blocks
[   70.565704] patching 6 blocks to 6
[   70.577948] patching 7 blocks to 7
[   70.589489]   moving 8 blocks
[   70.598059] patching 45 blocks to 45
[   70.615183] patching 9 blocks to 9
[   70.626232] patching 5 blocks to 5
[   70.636657]   moving 2 blocks
[   70.638774]   moving 8 blocks
[   70.642776]   moving 4 blocks
[   70.650265] patching 52 blocks to 52
[   70.670043] patching 8 blocks to 8
[   70.681270] patching 5 blocks to 5
[   70.696070] patching 38 blocks to 38
[   70.711948] patching 10 blocks to 10
[   70.727213] patching 26 blocks to 26
[   70.740587]   moving 4 blocks
[   70.742474]   moving 4 blocks
[   70.744354]   moving 7 blocks
[   70.747696]   moving 17 blocks
[   70.757468] patching 56 blocks to 56
[   70.778052] patching 17 blocks to 17
[   70.791417] patching 7 blocks to 7
[   70.802257]   moving 5 blocks
[   70.804316]   moving 6 blocks
[   70.806233] patching 7 blocks to 7
[   70.817626]   moving 5 blocks
[   70.819374]   moving 5 blocks
[   70.821890]   moving 4 blocks
[   70.824133]   moving 9 blocks
[   70.833122]   moving 65 blocks
[   70.840054]   moving 6 blocks
[   70.841620]   moving 4 blocks
[   70.843304]   moving 4 blocks
[   70.845006]   moving 6 blocks
[   70.847191]   moving 8 blocks
[   70.850763]   moving 5 blocks
[   70.852896]   moving 6 blocks
[   70.855570]   moving 9 blocks
[   70.858885]   moving 4 blocks
[   70.860833]   moving 8 blocks
[   70.862580]   moving 5 blocks
[   70.864659]   moving 5 blocks
[   70.871832]   moving 51 blocks
[   70.877763]   moving 9 blocks
[   70.881165]   moving 7 blocks
[   70.905243]   moving 199 blocks
[   70.915442]   moving 3 blocks
[   70.917186]   moving 5 blocks
[   70.918874]   moving 6 blocks
[   70.920420]   moving 4 blocks
[   70.922647]   moving 9 blocks
[   70.924797]   moving 9 blocks
[   70.926886]   moving 6 blocks
[   70.929269]   moving 9 blocks
[   70.934360]   moving 14 blocks
[   70.938689]   moving 11 blocks
[   70.940138]   moving 1 blocks
[   70.941082]   moving 1 blocks
[   70.942107]   moving 1 blocks
[   70.943566]   moving 4 blocks
[   70.945943]   moving 7 blocks
[   70.947810]   moving 4 blocks
[   70.949298]   moving 4 blocks
[   70.951949]   moving 11 blocks
[   70.953905]   moving 4 blocks
[   70.955771]   moving 3 blocks
[   70.957134] patching 2 blocks to 2
[   70.966884]   moving 3 blocks
[   70.968373]   moving 2 blocks
[   70.969967]   moving 3 blocks
[   70.971168] patching 2 blocks to 2
[   70.980852]   moving 3 blocks
[   70.982512]   moving 3 blocks
[   70.983759]   moving 2 blocks
[   70.985101]   moving 3 blocks
[   70.986580] patching 2 blocks to 2
[   70.996384]   moving 2 blocks
[   71.011605]   moving 116 blocks
[   71.020673]   moving 5 blocks
[   71.021992] patching 2 blocks to 2
[   71.037693]   moving 54 blocks
[   71.041891]   moving 2 blocks
[   71.067586]   moving 215 blocks
[   71.080022]   moving 1 blocks
[   71.081531]   moving 5 blocks
[   71.086464]   moving 29 blocks
[   71.101496]   moving 96 blocks
[   71.110988]   moving 17 blocks
[   71.116042]   moving 17 blocks
[   71.121425]   moving 17 blocks
[   71.125694]   moving 15 blocks
[   71.128892]   moving 15 blocks
[   71.132809]   moving 5 blocks
[   71.136377]   moving 17 blocks
[   71.141226]   moving 15 blocks
[   71.145088]   moving 17 blocks
[   71.150326]   moving 15 blocks
[   71.153748]   moving 15 blocks
[   71.157191]   moving 12 blocks
[   71.162039]   moving 12 blocks
[   71.166469]   moving 12 blocks
[   71.169617]   moving 12 blocks
[   71.173117]   moving 12 blocks
[   71.177375]   moving 12 blocks
[   71.181954]   moving 17 blocks
[   71.185256]   moving 17 blocks
[   71.190888]   moving 17 blocks
[   71.194189]   moving 17 blocks
[   71.199053]   moving 17 blocks
[   71.202261]   moving 15 blocks
[   71.206230]   moving 4 blocks
[   71.208347]   moving 4 blocks
[   71.209998]   moving 3 blocks
[   71.211232]   moving 1 blocks
[   71.212997]   moving 7 blocks
[   71.214558]   moving 2 blocks
[   71.215505]   moving 1 blocks
[   71.216410]   moving 1 blocks
[   71.217315]   moving 1 blocks
[   71.218243]   moving 1 blocks
[   71.219133]   moving 1 blocks
[   71.220053]   moving 1 blocks
[   71.223899]   moving 27 blocks
[   71.232192]   moving 39 blocks
[   71.239047]   moving 27 blocks
[   71.243974]   moving 11 blocks
[   71.246489]   moving 7 blocks
[   71.248731]   moving 7 blocks
[   71.251025]   moving 7 blocks
[   71.253288]   moving 7 blocks
[   71.255868]   moving 7 blocks
[   71.258494]   moving 7 blocks
[   71.260767]   moving 7 blocks
[   71.263074]   moving 7 blocks
[   71.265322]   moving 7 blocks
[   71.267627]   moving 7 blocks
[   71.269632]   moving 5 blocks
[   71.274069]   moving 25 blocks
[   71.277852]   moving 4 blocks
[   71.279394]   moving 4 blocks
[   71.373151]   moving 833 blocks
[   71.405634]   moving 4 blocks
[   71.407799] patching 4 blocks to 4
[   71.419053]   moving 4 blocks
[   71.422620]   moving 17 blocks
[   71.428193]   moving 15 blocks
[   71.433275]   moving 17 blocks
[   71.436150]   moving 12 blocks
[   71.440442]   moving 12 blocks
[   71.444531]   moving 12 blocks
[   71.447040]   moving 11 blocks
[   71.486640]   moving 331 blocks
[   71.594696]   moving 813 blocks
[   71.724712]   moving 896 blocks
[   71.757613]   moving 6 blocks
[   71.759905]   moving 5 blocks
[   71.761798]   moving 4 blocks
[   71.763302]   moving 4 blocks
[   71.764437]   moving 1 blocks
[   71.765498]   moving 1 blocks
[   71.767187]   moving 5 blocks
[   71.768987]   moving 1 blocks
[   71.770574]   moving 5 blocks
[   71.772024]   moving 1 blocks
[   71.773017]   moving 1 blocks
[   71.779099]   moving 44 blocks
[   71.783854]   moving 1 blocks
[   71.787756]   moving 24 blocks
[   71.803024] patching 100 blocks to 100
[   71.861965] patching 183 blocks to 183
[   71.963233] patching 110 blocks to 110
[   72.009888]   moving 9 blocks
[   72.034462]   moving 7 blocks
[   72.057046]   moving 11 blocks
[   72.060031]   moving 14 blocks
[   72.062348]   moving 6 blocks
[   72.066366]   moving 26 blocks
[   72.071150] patching 6 blocks to 6
[   72.082512]   moving 8 blocks
[   72.084645]   moving 8 blocks
[   72.088121]   moving 5 blocks
[   72.093115]   moving 34 blocks
[   72.099051]   moving 4 blocks
[   72.102141]   moving 14 blocks
[   72.107202]   moving 18 blocks
[   72.137352]   moving 252 blocks
[   72.153735]   moving 21 blocks
[   72.156414]   moving 9 blocks
[   72.164280]   moving 38 blocks
[   72.190180] patching 166 blocks to 166
[   72.219696]   moving 12 blocks
[   72.223522]   moving 12 blocks
[   72.226692]   moving 12 blocks
[   72.229724]   moving 12 blocks
[   72.235031]   moving 17 blocks
[   72.239727]   moving 15 blocks
[   72.243329]   moving 15 blocks
[   72.247951]   moving 15 blocks
[   72.251134]   moving 5 blocks
[   72.254142]   moving 15 blocks
[   72.258051]   moving 17 blocks
[   72.263516]   moving 15 blocks
[   72.268539]   moving 17 blocks
[   72.271726]   moving 15 blocks
[   72.276491]   moving 17 blocks
[   72.280737]   moving 15 blocks
[   72.284611]   moving 17 blocks
[   72.289877]   moving 15 blocks
[   72.293862]   moving 17 blocks
[   72.299261]   moving 17 blocks
[   72.304154]   moving 17 blocks
[   72.309581]   moving 15 blocks
[   72.314963]   moving 17 blocks
[   72.317737]   moving 12 blocks
[   72.321968]   moving 12 blocks
[   72.325999]   moving 12 blocks
[   72.328968]   moving 12 blocks
[   72.331864]   moving 12 blocks
[   72.336659]   moving 12 blocks
[   72.341117]   moving 12 blocks
[   72.343858]   moving 12 blocks
[   72.347722]   moving 17 blocks
[   72.352511]   moving 17 blocks
[   72.355753]   moving 17 blocks
[   72.361343]   moving 17 blocks
[   72.364631]   moving 17 blocks
[   72.369518]   moving 17 blocks
[   72.372748]   moving 17 blocks
[   72.379185]   moving 17 blocks
[   72.382328]   moving 15 blocks
[   72.387260]   moving 5 blocks
[   72.390857]   moving 17 blocks
[   72.395397]   moving 17 blocks
[   72.400867]   moving 15 blocks
[   72.405686]   moving 15 blocks
[   72.407879]   moving 5 blocks
[   72.411138]   moving 17 blocks
[   72.416022]   moving 15 blocks
[   72.420528]   moving 17 blocks
[   72.423587]   moving 15 blocks
[   72.428701]   moving 15 blocks
[   72.433260]   moving 12 blocks
[   72.436142]   moving 12 blocks
[   72.438727]   moving 12 blocks
[   72.442885]   moving 12 blocks
[   72.446795]   moving 12 blocks
[   72.449619]   moving 12 blocks
[   72.452801]   moving 17 blocks
[   72.458094]   moving 17 blocks
[   72.461629]   moving 17 blocks
[   72.466933]   moving 17 blocks
[   72.470055]   moving 15 blocks
[   72.477146]   moving 17 blocks
[   72.482175]   moving 15 blocks
[   72.485451]   moving 15 blocks
[   72.489542]   moving 12 blocks
[   72.493042]   moving 12 blocks
[   72.495205]   moving 12 blocks
[   72.497290]   moving 12 blocks
[   72.506793]   moving 5 blocks
[   72.508084]   moving 4 blocks
[   72.769519]   moving 2371 blocks
[   72.851093]   moving 5 blocks
[   72.854275]   moving 15 blocks
[   72.859756]   moving 15 blocks
[   72.865220]   moving 15 blocks
[   72.867375]   moving 5 blocks
[   72.870773]   moving 15 blocks
[   72.874597]   moving 17 blocks
[   72.879804]   moving 15 blocks
[   72.883602]   moving 17 blocks
[   72.890117]   moving 15 blocks
[   72.894808]   moving 8 blocks
[   72.897033]   moving 8 blocks
[   72.898594]   moving 2 blocks
[   72.900306]   moving 5 blocks
[   72.901831]   moving 1 blocks
[   73.057911]   moving 1383 blocks
[   73.107573] patching 2 blocks to 2
[   73.117622]   moving 3 blocks
[   73.123624] patching 34 blocks to 35
[   73.142517]   moving 6 blocks
[   73.150704]   moving 50 blocks
[   73.157801]   moving 4 blocks
[   73.160563]   moving 12 blocks
[   73.168928] patching 46 blocks to 46
[   73.286822] patching 43 blocks to 43
[   73.316301]   moving 77 blocks
[   73.371141] stashing 512 overlapping blocks to db0abf3dd157c8837290556ae45c4eb55039330a
[   73.371175] 119435264 bytes free on /cache (2097152 needed)
[   73.371669]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db0abf3dd157c8837290556ae45c4eb55039330a
[   73.397293] patching 512 blocks to 512
[   74.042040] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db0abf3dd157c8837290556ae45c4eb55039330a
[   74.160618] stashing 512 overlapping blocks to a32e25e52ae71694317a5e25ef83294f3cf88d1c
[   74.160657] 119435264 bytes free on /cache (2097152 needed)
[   74.160708]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a32e25e52ae71694317a5e25ef83294f3cf88d1c
[   74.185787] patching 512 blocks to 512
[   74.826754] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a32e25e52ae71694317a5e25ef83294f3cf88d1c
[   74.889584]   moving 24 blocks
[   74.896481]   moving 4 blocks
[   74.914541] patching 33 blocks to 33
[   74.936222]   moving 36 blocks
[   74.943574] patching 10 blocks to 10
[   74.995702] patching 350 blocks to 350
[   75.048236]   moving 4 blocks
[   75.069274]   moving 161 blocks
[   75.079699]   moving 5 blocks
[   75.082246]   moving 9 blocks
[   75.085467]   moving 1 blocks
[   75.088747]   moving 19 blocks
[   75.090929]   moving 4 blocks
[   75.094978] patching 24 blocks to 24
[   75.268101]   moving 1430 blocks
[   75.365040] patching 2 blocks to 2
[   75.396053]   moving 3 blocks
[   75.419167]   moving 4 blocks
[   75.442249]   moving 1 blocks
[   75.464583]   moving 4 blocks
[   75.503598]   moving 123 blocks
[   75.647684] patching 948 blocks to 948
[   76.603240]   moving 1 blocks
[   76.604715]   moving 4 blocks
[   76.606711]   moving 6 blocks
[   76.608735] patching 7 blocks to 7
[   76.619874]   moving 5 blocks
[   76.621710]   moving 4 blocks
[   76.624075]   moving 10 blocks
[   76.629787]   moving 25 blocks
[   76.637378]   moving 31 blocks
[   76.640838]   moving 1 blocks
[   76.683579]   moving 357 blocks
[   76.708565]   moving 78 blocks
[   76.717391]   moving 13 blocks
[   76.720841] patching 2 blocks to 2
[   76.730702]   moving 4 blocks
[   76.732390]   moving 1 blocks
[   76.746598]   moving 103 blocks
[   76.755044] patching 2 blocks to 2
[   76.765297]   moving 4 blocks
[   76.767344]   moving 5 blocks
[   76.768819]   moving 1 blocks
[   76.770515]   moving 5 blocks
[   76.782401]   moving 84 blocks
[   76.795792]   moving 51 blocks
[   76.803897] patching 28 blocks to 28
[   76.990623] patching 1530 blocks to 1530
[   77.986671]   moving 1 blocks
[   77.987893]   moving 1 blocks
[   78.079503] patching 786 blocks to 786
[   78.254501]   moving 8 blocks
[   78.257186]   moving 3 blocks
[   78.304867]   moving 307 blocks
[   78.320422]   moving 5 blocks
[   78.323596]   moving 6 blocks
[   78.325103]   moving 7 blocks
[   78.327626]   moving 5 blocks
[   78.338370]   moving 76 blocks
[   78.348076] patching 21 blocks to 21
[   78.376745] patching 109 blocks to 109
[   78.401277]   moving 8 blocks
[   78.435918]   moving 263 blocks
[   78.652852] patching 1836 blocks to 1842
[   78.942612]   moving 1 blocks
[   78.943984] patching 2 blocks to 2
[   78.953758]   moving 4 blocks
[   78.960542] patching 41 blocks to 41
[   78.977649]   moving 5 blocks
[   78.979442]   moving 3 blocks
[   78.985927]   moving 33 blocks
[   79.138804]   moving 1370 blocks
[   79.187218] patching 6 blocks to 6
[   79.226564]   moving 260 blocks
[   79.250204] patching 90 blocks to 90
[   79.282696]   moving 28 blocks
[   79.320889] patching 302 blocks to 302
[   79.390982]   moving 55 blocks
[   79.397911] stashing 2 overlapping blocks to 17b00bd4e25ccb1eda9b24126106f5eefe2f5af8
[   79.397950] 119435264 bytes free on /cache (8192 needed)
[   79.398004]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/17b00bd4e25ccb1eda9b24126106f5eefe2f5af8
[   79.403390] patching 2 blocks to 2
[   79.411758] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/17b00bd4e25ccb1eda9b24126106f5eefe2f5af8
[   79.431183]   moving 156 blocks
[   79.442786]   moving 12 blocks
[   79.445899]   moving 1 blocks
[   79.452327] patching 40 blocks to 40
[   79.467624] stashing 2 overlapping blocks to 3d9b8aab1bfedea4169eb30bd8bcffad7dcc08c3
[   79.467677] 119435264 bytes free on /cache (8192 needed)
[   79.467715]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3d9b8aab1bfedea4169eb30bd8bcffad7dcc08c3
[   79.471126] patching 2 blocks to 2
[   79.479364] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3d9b8aab1bfedea4169eb30bd8bcffad7dcc08c3
[   79.481044] stashing 2 overlapping blocks to ca78a76a120cf781f38567f167bec2bd67a54d66
[   79.481062] 119435264 bytes free on /cache (8192 needed)
[   79.481107]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ca78a76a120cf781f38567f167bec2bd67a54d66
[   79.484467] patching 2 blocks to 2
[   79.492659] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ca78a76a120cf781f38567f167bec2bd67a54d66
[   79.778163] patching 2548 blocks to 2548
[   82.221881]   moving 460 blocks
[   82.242109]   moving 2 blocks
[   82.243529]   moving 1 blocks
[   82.274341] patching 258 blocks to 258
[   82.346971]   moving 248 blocks
[   82.360167]   moving 3 blocks
[   82.383469]   moving 191 blocks
[   82.401272]   moving 62 blocks
[   82.407096]   moving 8 blocks
[   82.447162]   moving 329 blocks
[   82.465382] patching 24 blocks to 24
[   82.490887]   moving 74 blocks
[   82.529138] patching 274 blocks to 275
[   82.597914]   moving 17 blocks
[   82.624991]   moving 194 blocks
[   82.636720]   moving 1 blocks
[   82.683356]   moving 393 blocks
[   82.701026] stashing 2 overlapping blocks to 1cd99be05f3c813dd0fe276d8bd6f0461e24155f
[   82.701053] 119435264 bytes free on /cache (8192 needed)
[   82.701110]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1cd99be05f3c813dd0fe276d8bd6f0461e24155f
[   82.704621] patching 2 blocks to 2
[   82.712873] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1cd99be05f3c813dd0fe276d8bd6f0461e24155f
[   82.721000]   moving 34 blocks
[   82.725042]   moving 4 blocks
[   82.760318]   moving 298 blocks
[   82.801553] patching 104 blocks to 104
[   82.806561] I:current maximum temperature: 35961
[   82.831284] patching 33 blocks to 33
[   82.895717] stashing 512 overlapping blocks to 415910648236a6038f3d0907a76a3815348ba75b
[   82.895768] 119435264 bytes free on /cache (2097152 needed)
[   82.895808]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/415910648236a6038f3d0907a76a3815348ba75b
[   82.922026] patching 512 blocks to 512
[   83.562750] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/415910648236a6038f3d0907a76a3815348ba75b
[   83.619788] patching 2 blocks to 2
[   83.630114]   moving 3 blocks
[   83.679494] stashing 512 overlapping blocks to acdfce14a5363844b3a506c7ba076b70991b9434
[   83.679526] 119435264 bytes free on /cache (2097152 needed)
[   83.679590]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/acdfce14a5363844b3a506c7ba076b70991b9434
[   83.705505] patching 512 blocks to 512
[   84.345496] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/acdfce14a5363844b3a506c7ba076b70991b9434
[   84.453625]   moving 262 blocks
[   84.467278]   moving 4 blocks
[   84.470434] patching 16 blocks to 16
[   84.482788] patching 2 blocks to 2
[   84.492924]   moving 7 blocks
[   84.517924]   moving 202 blocks
[   84.529189] patching 2 blocks to 2
[   84.539425]   moving 4 blocks
[   84.541185]   moving 1 blocks
[   84.551497]   moving 70 blocks
[   84.558188] patching 22 blocks to 22
[   84.575301] patching 14 blocks to 14
[   84.587018]   moving 6 blocks
[   84.588134] patching 2 blocks to 2
[   84.597548]   moving 4 blocks
[   84.600676]   moving 15 blocks
[   84.633689]   moving 258 blocks
[   84.646197]   moving 1 blocks
[   84.659869]   moving 100 blocks
[   84.667745]   moving 4 blocks
[   84.670797]   moving 9 blocks
[   84.675277] patching 15 blocks to 15
[   84.688303]   moving 4 blocks
[   84.689537]   moving 1 blocks
[   84.690868] patching 2 blocks to 2
[   84.701295]   moving 5 blocks
[   84.702342]   moving 4 blocks
[   84.711036]   moving 62 blocks
[   84.718909]   moving 12 blocks
[   84.728485] patching 48 blocks to 48
[   84.755249]   moving 49 blocks
[   84.759919] patching 2 blocks to 2
[   84.770054]   moving 3 blocks
[   84.771824]   moving 3 blocks
[   84.774316]   moving 9 blocks
[   84.776439]   moving 7 blocks
[   84.781105] patching 27 blocks to 27
[   84.800562]   moving 28 blocks
[   84.804724] patching 2 blocks to 2
[   84.817364]   moving 29 blocks
[   84.822739]   moving 18 blocks
[   84.826885]   moving 1 blocks
[   84.828836]   moving 6 blocks
[   84.829931]   moving 5 blocks
[   84.837115] patching 38 blocks to 38
[   84.858991] patching 2 blocks to 2
[   84.868984]   moving 6 blocks
[   84.870638] patching 2 blocks to 2
[   84.881923]   moving 16 blocks
[   84.883733]   moving 3 blocks
[   84.886103] patching 22 blocks to 22
[   84.900235] patching 2 blocks to 2
[   84.909982]   moving 3 blocks
[   84.917152] patching 42 blocks to 42
[   84.936422]   moving 9 blocks
[   84.938181]   moving 10 blocks
[   84.944493]   moving 23 blocks
[   84.947956] patching 2 blocks to 2
[   84.958131]   moving 3 blocks
[   85.088830] patching 1160 blocks to 1160
[   85.913015]   moving 4 blocks
[   86.451984]   moving 4707 blocks
[   86.798155]   moving 17 blocks
[   86.799886] patching 2 blocks to 2
[   86.809678]   moving 3 blocks
[   86.811658] patching 12 blocks to 12
[   86.824566] patching 2 blocks to 2
[   86.834046]   moving 1 blocks
[   86.835192]   moving 1 blocks
[   86.836745]   moving 5 blocks
[   86.838839]   moving 13 blocks
[   86.843374]   moving 1 blocks
[   86.861899]   moving 149 blocks
[   86.874170] patching 22 blocks to 22
[   86.895410] patching 42 blocks to 42
[   86.956202]   moving 403 blocks
[   86.986060]   moving 94 blocks
[   86.993804]   moving 5 blocks
[   87.125681]   moving 1149 blocks
[   87.175200]   moving 64 blocks
[   87.180319]   moving 1 blocks
[   87.181457]   moving 1 blocks
[   87.194235]   moving 87 blocks
[   87.202863]   moving 6 blocks
[   87.204461]   moving 1 blocks
[   87.205458] stashing 2 overlapping blocks to 14b8d20ee4e5e4b1348fa6e13d87d729115ba500
[   87.205498] 119435264 bytes free on /cache (8192 needed)
[   87.205552]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/14b8d20ee4e5e4b1348fa6e13d87d729115ba500
[   87.210634] patching 2 blocks to 2
[   87.218899] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/14b8d20ee4e5e4b1348fa6e13d87d729115ba500
[   87.229703] patching 90 blocks to 90
[   87.252433]   moving 7 blocks
[   87.257283] patching 27 blocks to 27
[   87.297314]   moving 214 blocks
[   87.322387]   moving 112 blocks
[   87.333653]   moving 28 blocks
[   87.354947] patching 39 blocks to 40
[   87.373324]   moving 2 blocks
[   87.377354] patching 23 blocks to 23
[   87.399682] patching 50 blocks to 50
[   87.419688]   moving 4 blocks
[   87.423598] patching 25 blocks to 25
[   87.438607]   moving 1 blocks
[   87.462940]   moving 203 blocks
[   87.475179]   moving 7 blocks
[   87.479650]   moving 24 blocks
[   87.483294]   moving 4 blocks
[   87.484638] patching 2 blocks to 2
[   87.494352]   moving 3 blocks
[   87.496360]   moving 7 blocks
[   87.498660]   moving 4 blocks
[   87.499460]   moving 2 blocks
[   87.501618]   moving 9 blocks
[   87.512751]   moving 71 blocks
[   87.520768]   moving 23 blocks
[   87.524694] patching 2 blocks to 2
[   87.535806]   moving 4 blocks
[   87.536813]   moving 1 blocks
[   87.538723]   moving 8 blocks
[   87.539866]   moving 4 blocks
[   87.548587]   moving 64 blocks
[   87.555689]   moving 4 blocks
[   87.556423]   moving 1 blocks
[   87.593700] patching 307 blocks to 307
[   88.306996]   moving 2168 blocks
[   88.520463]   moving 280 blocks
[   88.615726]   moving 233 blocks
[   88.630726] patching 24 blocks to 24
[   88.649098]   moving 18 blocks
[   88.657398]   moving 48 blocks
[   88.663925]   moving 10 blocks
[   88.846894]   moving 1651 blocks
[   88.905487] stashing 2 overlapping blocks to 4e22ff619baeb8c0bf79119b23399b58af69458f
[   88.905567] 119435264 bytes free on /cache (8192 needed)
[   88.905639]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4e22ff619baeb8c0bf79119b23399b58af69458f
[   88.909165] patching 2 blocks to 2
[   88.917549] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4e22ff619baeb8c0bf79119b23399b58af69458f
[   88.919183] patching 2 blocks to 2
[   88.929243]   moving 4 blocks
[   88.930855] patching 2 blocks to 2
[   88.942198]   moving 17 blocks
[   88.946037]   moving 1 blocks
[   88.948024]   moving 1 blocks
[   88.949863] patching 3 blocks to 3
[   88.960778]   moving 4 blocks
[   88.965360]   moving 27 blocks
[   89.013585] patching 397 blocks to 397
[   89.074013]   moving 95 blocks
[   89.080817]   moving 4 blocks
[   89.084406] patching 27 blocks to 27
[   89.113464]   moving 102 blocks
[   89.123742]   moving 24 blocks
[   89.128870]   moving 24 blocks
[   89.132811]   moving 3 blocks
[   89.134653]   moving 5 blocks
[   89.183922]   moving 429 blocks
[   89.202741]   moving 14 blocks
[   89.204547]   moving 1 blocks
[   89.208576]   moving 27 blocks
[   89.253851]   moving 353 blocks
[   89.270014]   moving 1 blocks
[   89.384192]   moving 995 blocks
[   89.435935]   moving 2 blocks
[   89.437046]   moving 1 blocks
[   89.439506]   moving 12 blocks
[   89.440970]   moving 1 blocks
[   89.442215] patching 2 blocks to 2
[   89.452568]   moving 7 blocks
[   89.459873] patching 42 blocks to 42
[   89.478910]   moving 4 blocks
[   89.480174]   moving 1 blocks
[   89.482933]   moving 16 blocks
[   89.486750]   moving 10 blocks
[   89.488086]   moving 1 blocks
[   89.489603]   moving 5 blocks
[   89.491696] patching 3 blocks to 3
[   89.502749]   moving 5 blocks
[   89.510762]   moving 45 blocks
[   89.523733] patching 72 blocks to 72
[   89.544060]   moving 1 blocks
[   89.555341]   moving 86 blocks
[   89.560773]   moving 1 blocks
[   89.573431] patching 96 blocks to 96
[   89.594495]   moving 16 blocks
[   89.597909]   moving 6 blocks
[   89.602866] patching 29 blocks to 29
[   90.060969]   moving 4016 blocks
[   90.475549]   moving 2192 blocks
[   90.768417]   moving 1 blocks
[   90.769450]   moving 1 blocks
[   90.771301]   moving 6 blocks
[   90.773646]   moving 10 blocks
[   90.777178]   moving 8 blocks
[   90.778999]   moving 4 blocks
[   90.780987]   moving 6 blocks
[   90.785350] patching 26 blocks to 26
[   90.801344]   moving 6 blocks
[   90.802933]   moving 1 blocks
[   90.811900] patching 63 blocks to 63
[   90.835208] patching 5 blocks to 5
[   90.869387] patching 204 blocks to 205
[   90.930843] patching 50 blocks to 50
[   90.948716] patching 2 blocks to 2
[   90.959773]   moving 16 blocks
[   91.026844] patching 570 blocks to 571
[   91.191259]   moving 3 blocks
[   91.193338]   moving 8 blocks
[   91.196488] stashing 2 overlapping blocks to 34558a78673b280f82582bb9fc17390563b363f2
[   91.196528] 119435264 bytes free on /cache (8192 needed)
[   91.196584]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/34558a78673b280f82582bb9fc17390563b363f2
[   91.199866] patching 2 blocks to 2
[   91.208083] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/34558a78673b280f82582bb9fc17390563b363f2
[   91.210930]   moving 12 blocks
[   91.225401] patching 106 blocks to 107
[   91.258279]   moving 3 blocks
[   91.259776]   moving 1 blocks
[   91.285661]   moving 220 blocks
[   91.299814]   moving 21 blocks
[   91.303334]   moving 1 blocks
[   91.304399]   moving 1 blocks
[   91.312129] patching 52 blocks to 52
[   91.333986]   moving 1 blocks
[   91.341106] patching 45 blocks to 45
[   91.456534]   moving 2 blocks
[   91.457267]   moving 2 blocks
[   91.458455]   moving 1 blocks
[   91.464382]   moving 33 blocks
[   91.486759] patching 159 blocks to 159
[   92.080246] patching 27 blocks to 27
[   92.098717]   moving 2 blocks
[   92.105400]   moving 18 blocks
[   92.138718]   moving 230 blocks
[   92.152406]   moving 11 blocks
[   92.157387] patching 6 blocks to 6
[   92.171732] patching 37 blocks to 37
[   92.188539]   moving 4 blocks
[   92.192176] patching 19 blocks to 19
[   92.214483]   moving 9 blocks
[   92.215899]   moving 1 blocks
[   92.217655]   moving 14 blocks
[   92.221950]   moving 5 blocks
[   92.223521]   moving 4 blocks
[   92.225210]   moving 1 blocks
[   92.226208] stashing 2 overlapping blocks to f2dd46dc8360a95e0c91471974fd84dd8049c16a
[   92.226247] 119435264 bytes free on /cache (8192 needed)
[   92.226300]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f2dd46dc8360a95e0c91471974fd84dd8049c16a
[   92.229900] patching 2 blocks to 2
[   92.238161] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f2dd46dc8360a95e0c91471974fd84dd8049c16a
[   92.241964]   moving 12 blocks
[   92.245193]   moving 1 blocks
[   92.259637]   moving 107 blocks
[   92.268273]   moving 5 blocks
[   92.269685]   moving 1 blocks
[   92.271518]   moving 7 blocks
[   92.273410] patching 2 blocks to 2
[   92.283286]   moving 4 blocks
[   92.284836]   moving 1 blocks
[   92.301397]   moving 136 blocks
[   92.350832]   moving 358 blocks
[   92.366692] stashing 2 overlapping blocks to 19d167e68f2326f249c97fc855949567a013a928
[   92.366720] 119435264 bytes free on /cache (8192 needed)
[   92.366778]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19d167e68f2326f249c97fc855949567a013a928
[   92.370205] patching 2 blocks to 2
[   92.378426] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/19d167e68f2326f249c97fc855949567a013a928
[   92.380975]   moving 7 blocks
[   92.383155]   moving 5 blocks
[   92.387921]   moving 37 blocks
[   92.393464]   moving 13 blocks
[   92.400699] patching 28 blocks to 28
[   92.417684]   moving 25 blocks
[   92.422421]   moving 11 blocks
[   92.424458]   moving 2 blocks
[   92.432239]   moving 51 blocks
[   92.484714] stashing 512 overlapping blocks to 3a95dc0041be9627e2805262687fe9ddaf4e5f78
[   92.484749] 119435264 bytes free on /cache (2097152 needed)
[   92.484806]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3a95dc0041be9627e2805262687fe9ddaf4e5f78
[   92.510005] patching 512 blocks to 512
[   92.662644] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3a95dc0041be9627e2805262687fe9ddaf4e5f78
[   92.715269]   moving 1 blocks
[   92.766124] stashing 512 overlapping blocks to c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   92.766159] 119435264 bytes free on /cache (2097152 needed)
[   92.766211]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   92.792554] patching 512 blocks to 512
[   93.412499] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   93.555198] stashing 512 overlapping blocks to 01aa02c6fa236ee7147aadbcba239d3f7b50c776
[   93.555230] 119435264 bytes free on /cache (2097152 needed)
[   93.555288]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/01aa02c6fa236ee7147aadbcba239d3f7b50c776
[   93.581708] patching 512 blocks to 512
[   93.833094] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/01aa02c6fa236ee7147aadbcba239d3f7b50c776
[   93.886270]   moving 4 blocks
[   93.976607] stashing 512 overlapping blocks to c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   93.976638] 119435264 bytes free on /cache (2097152 needed)
[   93.976676]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   94.009033] patching 512 blocks to 512
[   94.623082] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c7d3e26afe426b27c1d6da95fff4ecfb843717b4
[   94.767358] stashing 512 overlapping blocks to 00c5d937e436f62d757541515e0a1597589ef60d
[   94.767388] 119435264 bytes free on /cache (2097152 needed)
[   94.767468]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/00c5d937e436f62d757541515e0a1597589ef60d
[   94.893177] patching 512 blocks to 512
[   95.225803] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/00c5d937e436f62d757541515e0a1597589ef60d
[   95.403137] stashing 512 overlapping blocks to 492c8d4cc9b08156a8f6dc9900280431d054521f
[   95.403169] 119435264 bytes free on /cache (2097152 needed)
[   95.403230]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/492c8d4cc9b08156a8f6dc9900280431d054521f
[   95.503059] patching 512 blocks to 512
[   95.961447] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/492c8d4cc9b08156a8f6dc9900280431d054521f
[   96.021026] patching 2 blocks to 2
[   96.030770]   moving 3 blocks
[   96.031758]  writing 1 blocks of new data
[   96.039780]   moving 51 blocks
[   96.045408]   moving 1 blocks
[   96.046942]   moving 4 blocks
[   96.048242] patching 2 blocks to 2
[   96.064811]   moving 44 blocks
[   96.079631] patching 90 blocks to 90
[   96.102034]   moving 10 blocks
[   96.104225] patching 5 blocks to 5
[   96.118876] patching 36 blocks to 36
[   96.135317]   moving 6 blocks
[   96.138850]   moving 11 blocks
[   96.142151]   moving 1 blocks
[   96.142804]   moving 1 blocks
[   96.144474]   moving 4 blocks
[   96.149977]   moving 36 blocks
[   96.155011] patching 2 blocks to 2
[   96.164882]   moving 3 blocks
[   96.165849]   moving 1 blocks
[   96.167574]   moving 6 blocks
[   96.172332] patching 29 blocks to 29
[   96.269893] patching 77 blocks to 77
[   96.324138] patching 318 blocks to 318
[   96.369638]   moving 3 blocks
[   96.373765]   moving 28 blocks
[   96.377635] patching 2 blocks to 2
[   96.387855]   moving 4 blocks
[   96.389685]   moving 4 blocks
[   96.439080] patching 418 blocks to 418
[   96.526755]   moving 346 blocks
[   96.540723]   moving 1 blocks
[   96.544226] patching 21 blocks to 21
[   96.619366] patching 528 blocks to 529
[   96.793388] patching 2 blocks to 2
[   96.803540]   moving 3 blocks
[   96.828571]   moving 80 blocks
[   96.845457]   moving 37 blocks
[   96.850626]   moving 5 blocks
[   96.874698]   moving 203 blocks
[   96.886772] patching 3 blocks to 3
[   96.898562]   moving 9 blocks
[   96.907644] patching 55 blocks to 55
[   96.978443] stashing 512 overlapping blocks to 0f723f68fc03d887d78113d9d6f7b5e39fe8e0cd
[   96.978476] 119435264 bytes free on /cache (2097152 needed)
[   96.978535]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0f723f68fc03d887d78113d9d6f7b5e39fe8e0cd
[   97.080047] patching 512 blocks to 512
[   97.118410] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0f723f68fc03d887d78113d9d6f7b5e39fe8e0cd
[   97.245855]   moving 1 blocks
[   97.319459] stashing 512 overlapping blocks to 675786b621e7453d5e70a9c8ff08ba18aa5c6d3d
[   97.319491] 119435264 bytes free on /cache (2097152 needed)
[   97.319550]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/675786b621e7453d5e70a9c8ff08ba18aa5c6d3d
[   97.356901] patching 512 blocks to 512
[   97.575821] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/675786b621e7453d5e70a9c8ff08ba18aa5c6d3d
[   97.639589] patching 83 blocks to 83
[   97.805888]   moving 41 blocks
[   97.814916]   moving 9 blocks
[   97.819758]   moving 3 blocks
[   97.826042]   moving 16 blocks
[   97.829648]   moving 4 blocks
[   97.837187]   moving 21 blocks
[   97.842790]   moving 5 blocks
[   97.851453] patching 26 blocks to 26
[   97.871463]   moving 4 blocks
[   97.873104]   moving 2 blocks
[   97.874330]   moving 5 blocks
[   97.875671] stashing 2 overlapping blocks to f1e8fb538a3c87fb74e759a6d4d0b0b3fcab777d
[   97.875709] 119435264 bytes free on /cache (8192 needed)
[   97.875761]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f1e8fb538a3c87fb74e759a6d4d0b0b3fcab777d
[   97.881473] patching 2 blocks to 2
[   97.890361] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f1e8fb538a3c87fb74e759a6d4d0b0b3fcab777d
[   97.892223]   moving 4 blocks
[   97.895489]   moving 15 blocks
[   97.903821] patching 35 blocks to 35
[   97.972354]   moving 453 blocks
[   97.990356] stashing 2 overlapping blocks to 8f05fdce79d9e9a2b5dffe2b863014237d5ee558
[   97.990382] 119435264 bytes free on /cache (8192 needed)
[   97.990441]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8f05fdce79d9e9a2b5dffe2b863014237d5ee558
[   97.993785] patching 2 blocks to 2
[   98.001963] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8f05fdce79d9e9a2b5dffe2b863014237d5ee558
[   98.003251] patching 2 blocks to 2
[   98.012839]   moving 5 blocks
[   98.019144]   moving 41 blocks
[   98.024591]   moving 1 blocks
[   98.072029]   moving 407 blocks
[   98.092937] patching 20 blocks to 21
[   98.108059] patching 2 blocks to 2
[   98.117551]   moving 3 blocks
[   98.125150]   moving 47 blocks
[   98.133800] patching 22 blocks to 22
[   98.148089]   moving 8 blocks
[   98.151233] patching 8 blocks to 8
[   98.168351]   moving 42 blocks
[   98.172449]   moving 4 blocks
[   98.176800]   moving 28 blocks
[   98.181200] patching 2 blocks to 2
[   98.192119]   moving 11 blocks
[   98.193860]   moving 6 blocks
[   98.194920]   moving 1 blocks
[   98.196122]   moving 2 blocks
[   98.197073]   moving 5 blocks
[   98.204154]   moving 56 blocks
[   98.211841] patching 22 blocks to 22
[   98.226350] patching 2 blocks to 2
[   98.315762]   moving 696 blocks
[   98.343936]   moving 6 blocks
[   98.346851]   moving 8 blocks
[   98.375607]   moving 261 blocks
[   98.388357]   moving 1 blocks
[   98.391180]   moving 11 blocks
[   98.606505]   moving 1915 blocks
[   98.769534] patching 3 blocks to 3
[   98.825401]   moving 4 blocks
[   98.890989]   moving 87 blocks
[   98.898651]   moving 6 blocks
[   98.965627]   moving 575 blocks
[   98.989354]   moving 6 blocks
[   98.991180]   moving 5 blocks
[   98.993600]   moving 10 blocks
[   98.996663] stashing 2 overlapping blocks to 9fd38d6be0872b1edad26cff1250b54144a3d55c
[   98.996703] 119435264 bytes free on /cache (8192 needed)
[   98.996756]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9fd38d6be0872b1edad26cff1250b54144a3d55c
[   99.000456] patching 2 blocks to 2
[   99.008651] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9fd38d6be0872b1edad26cff1250b54144a3d55c
[   99.012308]   moving 15 blocks
[   99.014058] stashing 3 overlapping blocks to 741ae8698fa64d2e1b3de357420ce0de39d810a4
[   99.014106] 119435264 bytes free on /cache (12288 needed)
[   99.014188]  writing 3 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/741ae8698fa64d2e1b3de357420ce0de39d810a4
[   99.017893] patching 3 blocks to 3
[   99.026892] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/741ae8698fa64d2e1b3de357420ce0de39d810a4
[   99.034622]   moving 51 blocks
[   99.041120]   moving 1 blocks
[   99.058133] patching 126 blocks to 126
[   99.101102] patching 52 blocks to 52
[   99.133806]   moving 128 blocks
[   99.142418]   moving 13 blocks
[   99.165592] patching 186 blocks to 186
[   99.235260]   moving 202 blocks
[   99.356220] patching 1011 blocks to 1011
[  102.817337] I:current maximum temperature: 35570
[  107.084957] patching 61 blocks to 62
[  107.115732] patching 12 blocks to 12
[  107.133700]   moving 19 blocks
[  107.137518] patching 2 blocks to 2
[  107.152561]   moving 4 blocks
[  107.154834] patching 6 blocks to 6
[  107.166162]   moving 3 blocks
[  107.505545]   moving 3079 blocks
[  107.644698]   moving 295 blocks
[  107.659366] patching 3 blocks to 3
[  107.672291]   moving 18 blocks
[  107.682527] patching 52 blocks to 52
[  107.703333]   moving 1 blocks
[  107.716621]   moving 101 blocks
[  107.724844] patching 2 blocks to 2
[  107.734597] patching 2 blocks to 2
[  107.744679]   moving 3 blocks
[  107.756943]   moving 93 blocks
[  107.782440]   moving 155 blocks
[  107.796375]   moving 28 blocks
[  107.803839]   moving 44 blocks
[  107.809955] stashing 2 overlapping blocks to ab872138b00f28d817be32297b45f951bc55bdef
[  107.809984] 119435264 bytes free on /cache (8192 needed)
[  107.810043]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ab872138b00f28d817be32297b45f951bc55bdef
[  107.812655] patching 2 blocks to 2
[  107.820967] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ab872138b00f28d817be32297b45f951bc55bdef
[  107.826175]   moving 27 blocks
[  107.921567]   moving 796 blocks
[  107.950898]   moving 4 blocks
[  107.994324]   moving 377 blocks
[  108.033732]   moving 47 blocks
[  108.038909]   moving 1 blocks
[  108.042611]   moving 24 blocks
[  108.046875]   moving 9 blocks
[  108.048274] patching 2 blocks to 2
[  108.058734]   moving 3 blocks
[  108.060526]   moving 4 blocks
[  108.062560]   moving 6 blocks
[  108.063988] stashing 2 overlapping blocks to f57f8fdde5cb3bcd830de76b201c6c369052c4c3
[  108.064028] 119435264 bytes free on /cache (8192 needed)
[  108.064104]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f57f8fdde5cb3bcd830de76b201c6c369052c4c3
[  108.067791] patching 2 blocks to 2
[  108.075992] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f57f8fdde5cb3bcd830de76b201c6c369052c4c3
[  108.078557]   moving 10 blocks
[  108.084384]   moving 19 blocks
[  108.088521]   moving 7 blocks
[  108.092882]   moving 25 blocks
[  108.096389] patching 2 blocks to 2
[  108.106036]   moving 9 blocks
[  108.107602] stashing 2 overlapping blocks to 03527610b5c00e345f9e80e6ea3d8bf8bf0a8dca
[  108.107641] 119435264 bytes free on /cache (8192 needed)
[  108.107695]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/03527610b5c00e345f9e80e6ea3d8bf8bf0a8dca
[  108.111365] patching 2 blocks to 2
[  108.119537] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/03527610b5c00e345f9e80e6ea3d8bf8bf0a8dca
[  108.120620] patching 2 blocks to 2
[  108.140104]   moving 93 blocks
[  108.155429]   moving 63 blocks
[  108.172563] patching 88 blocks to 88
[  108.313088]   moving 1 blocks
[  108.313739]   moving 1 blocks
[  108.316772]   moving 17 blocks
[  108.319666]   moving 1 blocks
[  108.320831]   moving 1 blocks
[  108.327848] patching 41 blocks to 41
[  108.408968]   moving 566 blocks
[  108.443518]   moving 89 blocks
[  108.480614] stashing 318 overlapping blocks to c27cd660074b4b7fadac65c9741621bea69b3a78
[  108.480649] 119435264 bytes free on /cache (1302528 needed)
[  108.480702]  writing 318 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c27cd660074b4b7fadac65c9741621bea69b3a78
[  108.499499] patching 318 blocks to 318
[  108.897121] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c27cd660074b4b7fadac65c9741621bea69b3a78
[  108.938444] stashing 2 overlapping blocks to a2789a4972a21bc67755906359cd3837499c5791
[  108.938463] 119435264 bytes free on /cache (8192 needed)
[  108.938512]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a2789a4972a21bc67755906359cd3837499c5791
[  108.943042] patching 2 blocks to 2
[  108.951212] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a2789a4972a21bc67755906359cd3837499c5791
[  108.953161] patching 2 blocks to 2
[  108.965372]   moving 17 blocks
[  109.017159] stashing 512 overlapping blocks to e58d2fe99b19d5be1eb118715482bc0f649046ed
[  109.017191] 119435264 bytes free on /cache (2097152 needed)
[  109.017251]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e58d2fe99b19d5be1eb118715482bc0f649046ed
[  109.043183] patching 512 blocks to 512
[  109.685109] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e58d2fe99b19d5be1eb118715482bc0f649046ed
[  109.748612] patching 24 blocks to 24
[  109.767016]   moving 1 blocks
[  109.772574] patching 2 blocks to 2
[  109.782447]   moving 4 blocks
[  109.788214]   moving 36 blocks
[  109.792735]   moving 6 blocks
[  109.795781]   moving 8 blocks
[  109.813224]   moving 113 blocks
[  109.933071]   moving 1033 blocks
[  110.018438] stashing 512 overlapping blocks to 0ead7b92b2fffa2438930d8c97c663274dbdc8a1
[  110.018471] 119435264 bytes free on /cache (2097152 needed)
[  110.018525]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0ead7b92b2fffa2438930d8c97c663274dbdc8a1
[  110.044515] patching 512 blocks to 512
[  110.075667] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0ead7b92b2fffa2438930d8c97c663274dbdc8a1
[  110.101437]   moving 1 blocks
[  110.103536]   moving 9 blocks
[  110.107505]   moving 11 blocks
[  110.464742]   moving 3165 blocks
[  110.672039]   moving 39 blocks
[  110.679599] patching 31 blocks to 31
[  110.874402] patching 1646 blocks to 1646
[  119.294949]   moving 16 blocks
[  119.317310]   moving 179 blocks
[  119.329790] patching 16 blocks to 16
[  119.358325]   moving 135 blocks
[  119.369251]   moving 13 blocks
[  119.411808] patching 353 blocks to 353
[  119.453832]   moving 4 blocks
[  119.456661]   moving 6 blocks
[  119.458624]   moving 4 blocks
[  119.460013] stashing 2 overlapping blocks to 05b8e6862549671cfa69482a340d5f07075294f5
[  119.460054] 119435264 bytes free on /cache (8192 needed)
[  119.460106]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/05b8e6862549671cfa69482a340d5f07075294f5
[  119.464737] patching 2 blocks to 2
[  119.472890] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/05b8e6862549671cfa69482a340d5f07075294f5
[  119.475464] patching 10 blocks to 10
[  119.495512]   moving 65 blocks
[  119.501006]   moving 3 blocks
[  119.502925]   moving 9 blocks
[  119.508091]   moving 34 blocks
[  119.512427] patching 2 blocks to 2
[  119.558830]   moving 303 blocks
[  119.573688]   moving 12 blocks
[  119.578146]   moving 4 blocks
[  119.699102]   moving 1086 blocks
[  119.939169]   moving 45 blocks
[  119.979670]   moving 8 blocks
[  120.006309]   moving 1 blocks
[  120.085384] patching 458 blocks to 458
[  120.281148]   moving 341 blocks
[  120.390960]   moving 2 blocks
[  120.454890]   moving 338 blocks
[  120.564550]   moving 1 blocks
[  120.590477]   moving 4 blocks
[  120.614816] patching 16 blocks to 16
[  120.648051]   moving 7 blocks
[  120.682007]   moving 45 blocks
[  120.772953]   moving 563 blocks
[  120.948900]   moving 9 blocks
[  120.976458]   moving 1 blocks
[  121.010921]   moving 87 blocks
[  121.052511] patching 55 blocks to 55
[  121.101779]   moving 1 blocks
[  121.133914]   moving 22 blocks
[  121.159461]   moving 123 blocks
[  121.192780]   moving 5 blocks
[  121.215576]   moving 4 blocks
[  121.243151] patching 34 blocks to 34
[  121.288300] patching 39 blocks to 39
[  121.330306]   moving 5 blocks
[  121.366299] patching 80 blocks to 81
[  121.425583] patching 34 blocks to 34
[  121.467756]   moving 5 blocks
[  121.495322]   moving 10 blocks
[  121.542841]   moving 262 blocks
[  121.557731] patching 30 blocks to 31
[  121.598230] patching 199 blocks to 199
[  122.048785]   moving 3 blocks
[  122.051819]   moving 5 blocks
[  122.060229] patching 26 blocks to 26
[  122.076499] patching 2 blocks to 2
[  122.086186]   moving 3 blocks
[  122.088311] patching 2 blocks to 2
[  122.098325]   moving 3 blocks
[  122.100243]   moving 9 blocks
[  122.104265]   moving 8 blocks
[  122.108586] patching 14 blocks to 14
[  122.134604]   moving 121 blocks
[  122.143606] patching 2 blocks to 2
[  122.155258]   moving 17 blocks
[  122.205110] stashing 512 overlapping blocks to b18eff487da1f1cb8230a6c84f42565e8f1f5af5
[  122.205143] 119435264 bytes free on /cache (2097152 needed)
[  122.205199]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b18eff487da1f1cb8230a6c84f42565e8f1f5af5
[  122.231962] patching 512 blocks to 512
[  122.828227] I:current maximum temperature: 35717
[  122.873608] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b18eff487da1f1cb8230a6c84f42565e8f1f5af5
[  123.018194] stashing 512 overlapping blocks to 8a70f5017e2e440e3b3f6b6a8fcfbb16681dd5de
[  123.018229] 119435264 bytes free on /cache (2097152 needed)
[  123.018282]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8a70f5017e2e440e3b3f6b6a8fcfbb16681dd5de
[  123.044707] patching 512 blocks to 512
[  123.688060] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8a70f5017e2e440e3b3f6b6a8fcfbb16681dd5de
[  123.747649]   moving 5 blocks
[  123.766245]   moving 58 blocks
[  123.859373] stashing 512 overlapping blocks to 39411b06cd5e21f60763fcbbd71f0acc28fa346b
[  123.859405] 119435264 bytes free on /cache (2097152 needed)
[  123.859470]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/39411b06cd5e21f60763fcbbd71f0acc28fa346b
[  123.885879] patching 512 blocks to 512
[  124.528446] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/39411b06cd5e21f60763fcbbd71f0acc28fa346b
[  124.671762] stashing 512 overlapping blocks to 9acb24659cf61603e7da5b16804f24467bfd4de7
[  124.671792] 119435264 bytes free on /cache (2097152 needed)
[  124.671856]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9acb24659cf61603e7da5b16804f24467bfd4de7
[  124.697910] patching 512 blocks to 512
[  125.339718] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9acb24659cf61603e7da5b16804f24467bfd4de7
[  125.487516] stashing 512 overlapping blocks to 6e44f33812786c594681a6e9da38249107dc9557
[  125.487548] 119435264 bytes free on /cache (2097152 needed)
[  125.487606]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6e44f33812786c594681a6e9da38249107dc9557
[  125.513826] patching 512 blocks to 512
[  126.153499] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6e44f33812786c594681a6e9da38249107dc9557
[  126.297892] stashing 512 overlapping blocks to 4e2917ca3e7ffe520d1f4bf642f58f019c324920
[  126.297941] 119435264 bytes free on /cache (2097152 needed)
[  126.298014]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4e2917ca3e7ffe520d1f4bf642f58f019c324920
[  126.324270] patching 512 blocks to 512
[  126.969006] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4e2917ca3e7ffe520d1f4bf642f58f019c324920
[  127.079704]   moving 5 blocks
[  127.103654]   moving 11 blocks
[  127.129991]   moving 9 blocks
[  127.157594]   moving 4 blocks
[  127.180262] patching 3 blocks to 3
[  127.223635]   moving 93 blocks
[  127.253937]   moving 12 blocks
[  127.280992]   moving 1 blocks
[  127.312712]   moving 68 blocks
[  127.346653]   moving 30 blocks
[  127.371969] patching 2 blocks to 2
[  127.402864]   moving 3 blocks
[  127.412765] patching 23 blocks to 23
[  127.469814]   moving 376 blocks
[  127.487102]   moving 1 blocks
[  127.489115] patching 8 blocks to 8
[  127.499442]   moving 5 blocks
[  127.529165]   moving 254 blocks
[  127.548158] patching 42 blocks to 42
[  127.572215]   moving 53 blocks
[  127.590918] patching 121 blocks to 121
[  127.626260]   moving 121 blocks
[  127.677817]   moving 375 blocks
[  127.696396]   moving 5 blocks
[  127.697710]   moving 1 blocks
[  127.699712]   moving 6 blocks
[  127.702067]   moving 3 blocks
[  127.708845] patching 38 blocks to 37
[  127.736487]   moving 8 blocks
[  127.744462]   moving 41 blocks
[  127.752844]   moving 24 blocks
[  127.770987]   moving 124 blocks
[  127.779231]   moving 1 blocks
[  128.043527] patching 2364 blocks to 2364
[  129.235446] patching 2029 blocks to 1794
[  130.937137] patching 47 blocks to 47
[  130.954284]   moving 5 blocks
[  130.972562]   moving 134 blocks
[  130.988292] patching 56 blocks to 56
[  131.019568]   moving 127 blocks
[  131.030108]   moving 16 blocks
[  131.031709]   moving 1 blocks
[  131.037708]   moving 38 blocks
[  131.042683]   moving 5 blocks
[  131.060205] patching 131 blocks to 131
[  131.187592]   moving 907 blocks
[  131.236486]   moving 8 blocks
[  131.248644]   moving 65 blocks
[  131.263241] patching 73 blocks to 73
[  131.288014]   moving 17 blocks
[  131.293902]   moving 18 blocks
[  131.297655] patching 10 blocks to 10
[  131.308275]   moving 5 blocks
[  131.311399]   moving 12 blocks
[  131.317709] patching 32 blocks to 33
[  131.336895]   moving 2 blocks
[  131.337638] patching 2 blocks to 2
[  131.347099]   moving 9 blocks
[  131.351153]   moving 8 blocks
[  131.367062]   moving 126 blocks
[  131.392973]   moving 148 blocks
[  131.403868]   moving 6 blocks
[  131.407035]   moving 27 blocks
[  131.415146]   moving 33 blocks
[  131.420451]   moving 7 blocks
[  131.457390]   moving 305 blocks
[  131.480031] patching 24 blocks to 24
[  131.543671]   moving 417 blocks
[  131.564017]   moving 21 blocks
[  131.570319]   moving 21 blocks
[  131.574269]   moving 6 blocks
[  131.577271] patching 26 blocks to 26
[  131.593318] patching 2 blocks to 2
[  131.607490]   moving 36 blocks
[  131.725330]   moving 1008 blocks
[  131.842676]   moving 85 blocks
[  132.202033] patching 3078 blocks to 3078
[  134.838539] patching 3 blocks to 3
[  134.848518]   moving 9 blocks
[  134.852107]   moving 12 blocks
[  134.855523] patching 2 blocks to 2
[  134.865681]   moving 3 blocks
[  134.867198]   moving 1 blocks
[  134.868377]   moving 1 blocks
[  134.869753] patching 9 blocks to 9
[  134.907536] patching 242 blocks to 242
[  134.968645]   moving 14 blocks
[  135.004434]   moving 279 blocks
[  135.018911]   moving 5 blocks
[  135.022938]   moving 22 blocks
[  135.026864]   moving 2 blocks
[  135.028442]   moving 4 blocks
[  135.032624]   moving 24 blocks
[  135.037934]   moving 10 blocks
[  135.041188]   moving 1 blocks
[  135.042883]   moving 5 blocks
[  135.052566] patching 68 blocks to 68
[  135.111663]   moving 305 blocks
[  135.127062]   moving 4 blocks
[  135.128194] stashing 2 overlapping blocks to c87635952a1c19296db8b020811e937c57c709ff
[  135.128235] 119435264 bytes free on /cache (8192 needed)
[  135.128289]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c87635952a1c19296db8b020811e937c57c709ff
[  135.131135] patching 2 blocks to 2
[  135.139440] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c87635952a1c19296db8b020811e937c57c709ff
[  135.141155] patching 2 blocks to 2
[  135.151253]   moving 3 blocks
[  135.152615] stashing 2 overlapping blocks to 9ba6371e48ff21f5c22ad6939692db4399135652
[  135.152654] 119435264 bytes free on /cache (8192 needed)
[  135.152707]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9ba6371e48ff21f5c22ad6939692db4399135652
[  135.156346] patching 2 blocks to 2
[  135.164579] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9ba6371e48ff21f5c22ad6939692db4399135652
[  135.167287]   moving 15 blocks
[  135.170940]   moving 1 blocks
[  135.172555]   moving 5 blocks
[  135.302258]   moving 1157 blocks
[  135.358230]   moving 4 blocks
[  135.390344]   moving 278 blocks
[  135.409928]   moving 42 blocks
[  135.415461]   moving 9 blocks
[  135.420619] patching 15 blocks to 15
[  135.440327]   moving 51 blocks
[  135.445539]   moving 5 blocks
[  135.471465]   moving 222 blocks
[  135.493388]   moving 81 blocks
[  135.501189]   moving 4 blocks
[  135.502714] stashing 2 overlapping blocks to 3b9c96aa74b278d41023338db6a9e13c961df885
[  135.502754] 119435264 bytes free on /cache (8192 needed)
[  135.502809]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3b9c96aa74b278d41023338db6a9e13c961df885
[  135.506549] patching 2 blocks to 2
[  135.514828] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3b9c96aa74b278d41023338db6a9e13c961df885
[  135.516108]   moving 1 blocks
[  135.517585]   moving 1 blocks
[  135.518942]   moving 2 blocks
[  135.520261]   moving 1 blocks
[  135.521389]   moving 1 blocks
[  135.522073]   moving 1 blocks
[  135.523235]   moving 1 blocks
[  135.524381]   moving 1 blocks
[  135.525704] patching 2 blocks to 2
[  135.535594]   moving 3 blocks
[  135.540654] patching 50 blocks to 50
[  135.562544]   moving 36 blocks
[  135.567129]   moving 4 blocks
[  135.568554] patching 2 blocks to 2
[  135.580664]   moving 35 blocks
[  135.593384]   moving 70 blocks
[  135.608314]   moving 73 blocks
[  135.621030]   moving 34 blocks
[  135.638896] patching 139 blocks to 139
[  135.825387]   moving 32 blocks
[  135.831537]   moving 1 blocks
[  135.834554]   moving 7 blocks
[  135.837841]   moving 4 blocks
[  135.839388]   moving 1 blocks
[  135.842636]   moving 8 blocks
[  135.846492] patching 2 blocks to 2
[  135.857827]   moving 4 blocks
[  135.875690]   moving 60 blocks
[  135.886499]   moving 28 blocks
[  135.892824] patching 25 blocks to 25
[  136.268924]   moving 3275 blocks
[  136.564031] patching 38 blocks to 38
[  136.606282]   moving 9 blocks
[  136.632987]   moving 1 blocks
[  136.655339] patching 2 blocks to 2
[  136.688295]   moving 3 blocks
[  136.712366]   moving 3 blocks
[  136.733476] patching 2 blocks to 2
[  136.744268]   moving 3 blocks
[  136.745686]   moving 1 blocks
[  136.748792]   moving 18 blocks
[  136.755365] patching 22 blocks to 22
[  136.770482]   moving 10 blocks
[  136.776957] patching 31 blocks to 31
[  136.812482]   moving 213 blocks
[  136.825227]   moving 10 blocks
[  136.829602] patching 25 blocks to 25
[  136.847084]   moving 16 blocks
[  136.849016]   moving 4 blocks
[  136.850352] patching 2 blocks to 2
[  136.861540]   moving 16 blocks
[  136.868021] patching 24 blocks to 24
[  136.885956]   moving 20 blocks
[  136.891997]   moving 29 blocks
[  136.896542] stashing 2 overlapping blocks to da3684fa499b7f64b49eda84e4bb6da135c30890
[  136.896581] 119435264 bytes free on /cache (8192 needed)
[  136.896634]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/da3684fa499b7f64b49eda84e4bb6da135c30890
[  136.900133] patching 2 blocks to 2
[  136.908394] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/da3684fa499b7f64b49eda84e4bb6da135c30890
[  136.912490]   moving 18 blocks
[  136.915088] patching 14 blocks to 14
[  136.930393] patching 40 blocks to 40
[  136.947439]   moving 1 blocks
[  136.959146]   moving 86 blocks
[  136.966480] patching 2 blocks to 2
[  136.976827]   moving 7 blocks
[  136.978472]   moving 1 blocks
[  136.980790]   moving 11 blocks
[  136.987258]   moving 26 blocks
[  136.997121]   moving 41 blocks
[  137.001389]   moving 6 blocks
[  137.004548]   moving 9 blocks
[  137.007240]   moving 3 blocks
[  137.009041]   moving 4 blocks
[  137.069132]   moving 526 blocks
[  137.119587]   moving 275 blocks
[  137.142591]   moving 70 blocks
[  137.152296] patching 26 blocks to 26
[  137.168827]   moving 5 blocks
[  137.171836]   moving 14 blocks
[  137.177161]   moving 20 blocks
[  137.181098]   moving 9 blocks
[  137.196387]   moving 113 blocks
[  137.205106]   moving 11 blocks
[  137.207340]   moving 2 blocks
[  137.208456]   moving 2 blocks
[  137.209720] stashing 2 overlapping blocks to 4deed1ec7a24c6cc32e28ec49c9ac2cc9145b591
[  137.209759] 119435264 bytes free on /cache (8192 needed)
[  137.209813]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4deed1ec7a24c6cc32e28ec49c9ac2cc9145b591
[  137.213475] patching 2 blocks to 2
[  137.221731] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4deed1ec7a24c6cc32e28ec49c9ac2cc9145b591
[  137.224406]   moving 10 blocks
[  137.231785]   moving 43 blocks
[  137.238175]   moving 18 blocks
[  137.243309] patching 21 blocks to 21
[  137.265562] patching 54 blocks to 54
[  137.293506]   moving 68 blocks
[  137.458827]   moving 1435 blocks
[  137.510066]   moving 5 blocks
[  137.511726]   moving 6 blocks
[  137.561345] stashing 512 overlapping blocks to 367da07dcabf39b4428999734a7b4fdf7aecfb9c
[  137.561377] 119435264 bytes free on /cache (2097152 needed)
[  137.561434]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/367da07dcabf39b4428999734a7b4fdf7aecfb9c
[  137.588506] patching 512 blocks to 512
[  138.236339] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/367da07dcabf39b4428999734a7b4fdf7aecfb9c
[  138.293838]   moving 5 blocks
[  138.298044]   moving 3 blocks
[  138.306137] patching 24 blocks to 25
[  138.337046]   moving 140 blocks
[  138.344594]   moving 5 blocks
[  138.367459] patching 180 blocks to 180
[  138.466885] patching 529 blocks to 529
[  138.559146]   moving 9 blocks
[  138.944913]   moving 3482 blocks
[  139.123520]   moving 433 blocks
[  139.152003]   moving 44 blocks
[  139.158515] patching 21 blocks to 21
[  139.173940]   moving 4 blocks
[  139.185316]   moving 84 blocks
[  139.191065]   moving 1 blocks
[  139.201546]   moving 83 blocks
[  139.212047] patching 21 blocks to 22
[  139.228498] patching 37 blocks to 37
[  139.243632]   moving 4 blocks
[  139.270243] patching 226 blocks to 227
[  139.324478]   moving 1 blocks
[  139.354363]   moving 253 blocks
[  139.370932]   moving 32 blocks
[  139.377495]   moving 23 blocks
[  139.383692]   moving 14 blocks
[  139.390089]   moving 26 blocks
[  139.394032]   moving 1 blocks
[  139.395289] patching 2 blocks to 2
[  139.406492]   moving 3 blocks
[  139.408133]   moving 4 blocks
[  139.414360] patching 35 blocks to 35
[  139.440720]   moving 79 blocks
[  139.448764]   moving 1 blocks
[  139.450714]   moving 8 blocks
[  139.452267] patching 2 blocks to 2
[  139.462361]   moving 3 blocks
[  139.469515] patching 45 blocks to 45
[  139.487288] patching 9 blocks to 9
[  139.511467]   moving 85 blocks
[  139.522064]   moving 23 blocks
[  139.526780] patching 5 blocks to 5
[  139.537661]   moving 4 blocks
[  139.655163] patching 1057 blocks to 1057
[  141.801218]   moving 4 blocks
[  141.804546] patching 5 blocks to 5
[  141.818653]   moving 13 blocks
[  141.844265]   moving 109 blocks
[  141.852753] stashing 2 overlapping blocks to 6c73843a08d2d1622c637127ecdd6e0a1b4cfa26
[  141.852792] 119435264 bytes free on /cache (8192 needed)
[  141.852846]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6c73843a08d2d1622c637127ecdd6e0a1b4cfa26
[  141.857573] patching 2 blocks to 2
[  141.865856] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6c73843a08d2d1622c637127ecdd6e0a1b4cfa26
[  141.872204] patching 35 blocks to 35
[  141.890179]   moving 4 blocks
[  141.891895]   moving 11 blocks
[  141.893258]   moving 1 blocks
[  141.894474]   moving 1 blocks
[  141.896144]   moving 5 blocks
[  141.901299] patching 31 blocks to 31
[  141.924920]   moving 9 blocks
[  141.933361] patching 61 blocks to 61
[  141.952142] patching 8 blocks to 8
[  141.963112] patching 4 blocks to 4
[  141.974675]   moving 19 blocks
[  141.984513] patching 52 blocks to 53
[  142.015036]   moving 93 blocks
[  142.025075]   moving 11 blocks
[  142.026385] patching 2 blocks to 2
[  142.036152]   moving 3 blocks
[  142.038936] patching 23 blocks to 23
[  142.063953]   moving 67 blocks
[  142.109626]   moving 350 blocks
[  142.124951]   moving 4 blocks
[  142.126004]   moving 1 blocks
[  142.128767]   moving 14 blocks
[  142.135718] patching 25 blocks to 25
[  142.151743]   moving 6 blocks
[  142.156686] patching 30 blocks to 31
[  142.188888]   moving 123 blocks
[  142.205087] patching 60 blocks to 60
[  142.223148]   moving 11 blocks
[  142.227659]   moving 12 blocks
[  142.229383]   moving 1 blocks
[  142.230116] patching 2 blocks to 2
[  142.239849]   moving 10 blocks
[  142.241586] patching 2 blocks to 2
[  142.251572]   moving 5 blocks
[  142.256256] patching 30 blocks to 30
[  142.828485]   moving 4230 blocks
[  142.838318] I:current maximum temperature: 36010
[  143.070424] patching 41 blocks to 41
[  143.093268] patching 61 blocks to 61
[  143.117211]   moving 6 blocks
[  143.119017] patching 2 blocks to 2
[  143.130006]   moving 11 blocks
[  143.134497]   moving 11 blocks
[  143.135824]   moving 4 blocks
[  143.143014]   moving 44 blocks
[  143.150382]   moving 22 blocks
[  143.180108]   moving 234 blocks
[  143.192527]   moving 8 blocks
[  143.193773] stashing 2 overlapping blocks to 760ccc19ea715263f6b31a4f07e124f52818bf86
[  143.193813] 119435264 bytes free on /cache (8192 needed)
[  143.193865]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/760ccc19ea715263f6b31a4f07e124f52818bf86
[  143.197554] patching 2 blocks to 2
[  143.205760] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/760ccc19ea715263f6b31a4f07e124f52818bf86
[  143.353500]   moving 1346 blocks
[  143.402030]   moving 5 blocks
[  143.555907]   moving 1346 blocks
[  143.838761]   moving 4 blocks
[  143.872087]   moving 92 blocks
[  143.907171]   moving 1 blocks
[  143.931353]   moving 9 blocks
[  143.955779]   moving 24 blocks
[  143.997761] patching 50 blocks to 50
[  144.041709]   moving 12 blocks
[  144.066408] patching 2 blocks to 2
[  144.098837]   moving 28 blocks
[  144.226315]   moving 920 blocks
[  144.695239]   moving 1756 blocks
[  144.944347]   moving 6 blocks
[  144.946302]   moving 7 blocks
[  145.038509]   moving 829 blocks
[  145.070094]   moving 4 blocks
[  145.071902] patching 3 blocks to 3
[  145.084449]   moving 18 blocks
[  145.087891] patching 6 blocks to 6
[  145.111677] patching 112 blocks to 112
[  145.134545]   moving 5 blocks
[  145.137225]   moving 10 blocks
[  145.141339] patching 8 blocks to 8
[  145.151434]   moving 1 blocks
[  145.152607]   moving 1 blocks
[  145.159556] patching 50 blocks to 50
[  145.179543] patching 20 blocks to 20
[  145.198405]   moving 37 blocks
[  145.206520]   moving 36 blocks
[  145.218719]   moving 62 blocks
[  145.225746]   moving 3 blocks
[  145.228111] patching 8 blocks to 8
[  145.238674] stashing 2 overlapping blocks to aa8040613681611c5689e20340a6575b3b84f605
[  145.238716] 119435264 bytes free on /cache (8192 needed)
[  145.238771]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aa8040613681611c5689e20340a6575b3b84f605
[  145.242451] patching 2 blocks to 2
[  145.250564] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aa8040613681611c5689e20340a6575b3b84f605
[  145.252961]   moving 4 blocks
[  145.257973]   moving 29 blocks
[  145.378644]   moving 1005 blocks
[  145.415599]   moving 13 blocks
[  145.434879]   moving 153 blocks
[  145.445815]   moving 4 blocks
[  145.448140] patching 7 blocks to 7
[  145.476408]   moving 156 blocks
[  145.493027]   moving 38 blocks
[  145.525294] patching 230 blocks to 231
[  145.581582]   moving 1 blocks
[  145.584561]   moving 16 blocks
[  145.586119]   moving 1 blocks
[  145.587487]   moving 6 blocks
[  145.589842]   moving 1 blocks
[  145.594199] patching 27 blocks to 27
[  145.610249]   moving 4 blocks
[  145.618301]   moving 53 blocks
[  145.626352] patching 25 blocks to 25
[  145.639629] stashing 2 overlapping blocks to ef60c5575158a6cc11ccb2caa26b0619bd9fcfa1
[  145.639674] 119435264 bytes free on /cache (8192 needed)
[  145.639727]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ef60c5575158a6cc11ccb2caa26b0619bd9fcfa1
[  145.642887] patching 2 blocks to 2
[  145.651047] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ef60c5575158a6cc11ccb2caa26b0619bd9fcfa1
[  145.658980]   moving 56 blocks
[  145.665337]   moving 15 blocks
[  145.667600]   moving 4 blocks
[  145.669584] patching 2 blocks to 2
[  145.682288] patching 29 blocks to 29
[  145.705185] patching 45 blocks to 45
[  145.719482]   moving 5 blocks
[  145.721028]   moving 4 blocks
[  145.722190] stashing 2 overlapping blocks to 68666d43be111e6f2cca131af230620612b6ed0a
[  145.722230] 119435264 bytes free on /cache (8192 needed)
[  145.722283]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/68666d43be111e6f2cca131af230620612b6ed0a
[  145.725513] patching 2 blocks to 2
[  145.733665] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/68666d43be111e6f2cca131af230620612b6ed0a
[  145.735977]   moving 8 blocks
[  145.740661] patching 9 blocks to 9
[  145.755042]   moving 23 blocks
[  145.757129]   moving 4 blocks
[  145.758495] patching 2 blocks to 2
[  145.768517]   moving 4 blocks
[  145.771345]   moving 12 blocks
[  145.776289]   moving 14 blocks
[  145.780076]   moving 5 blocks
[  146.094959]   moving 2824 blocks
[  146.277482]   moving 374 blocks
[  146.358485]   moving 1 blocks
[  146.381952] patching 10 blocks to 10
[  146.458773]   moving 4 blocks
[  146.628876] patching 1312 blocks to 1312
[  146.975994] patching 233 blocks to 233
[  147.010415] patching 1 blocks to 1
[  147.019520]   moving 2 blocks
[  147.022909]   moving 17 blocks
[  147.026743] stashing 2 overlapping blocks to e85b8581af801541f80a83d9bdc76234757707d5
[  147.026783] 119435264 bytes free on /cache (8192 needed)
[  147.026836]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e85b8581af801541f80a83d9bdc76234757707d5
[  147.030414] patching 2 blocks to 2
[  147.038705] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e85b8581af801541f80a83d9bdc76234757707d5
[  147.042658]   moving 19 blocks
[  147.048281]   moving 19 blocks
[  147.056134]   moving 34 blocks
[  147.061406]   moving 4 blocks
[  147.064254]   moving 22 blocks
[  147.192689]   moving 1137 blocks
[  147.239449]   moving 33 blocks
[  147.403151] patching 1421 blocks to 1421
[  147.681644] patching 298 blocks to 298
[  147.911256]   moving 19 blocks
[  147.964585]   moving 264 blocks
[  147.991614]   moving 7 blocks
[  147.996002] patching 24 blocks to 24
[  148.024073] patching 128 blocks to 128
[  148.263443]   moving 14 blocks
[  148.292819]   moving 95 blocks
[  148.305327] patching 2 blocks to 2
[  148.316244]   moving 3 blocks
[  148.397524] stashing 512 overlapping blocks to 323c3e3da658bccc8037efa9bf1c9bd5a5b18191
[  148.397558] 119435264 bytes free on /cache (2097152 needed)
[  148.397616]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/323c3e3da658bccc8037efa9bf1c9bd5a5b18191
[  148.423791] patching 512 blocks to 512
[  149.049849] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/323c3e3da658bccc8037efa9bf1c9bd5a5b18191
[  149.109618]   moving 13 blocks
[  149.113436] patching 2 blocks to 2
[  149.124781]   moving 7 blocks
[  149.184556]   moving 227 blocks
[  149.195316]   moving 10 blocks
[  149.196706]   moving 1 blocks
[  149.197947]   moving 1 blocks
[  149.199760]   moving 6 blocks
[  149.201707]   moving 3 blocks
[  149.204215]   moving 11 blocks
[  149.255027] stashing 512 overlapping blocks to 5353b883a5819d2f6c128ed341ce41d9b597c0d2
[  149.255062] 119435264 bytes free on /cache (2097152 needed)
[  149.255118]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5353b883a5819d2f6c128ed341ce41d9b597c0d2
[  149.281444] patching 512 blocks to 512
[  149.597245] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5353b883a5819d2f6c128ed341ce41d9b597c0d2
[  149.649801] stashing 2 overlapping blocks to db6c6a99ff72c99cb733c3b615f3a6cc3172d1b8
[  149.649819] 119435264 bytes free on /cache (8192 needed)
[  149.649869]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db6c6a99ff72c99cb733c3b615f3a6cc3172d1b8
[  149.654018] patching 2 blocks to 2
[  149.662261] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db6c6a99ff72c99cb733c3b615f3a6cc3172d1b8
[  149.719160] stashing 512 overlapping blocks to 738fab84142ffcfb50da022757cee8d3a0a138fe
[  149.719191] 119435264 bytes free on /cache (2097152 needed)
[  149.719250]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/738fab84142ffcfb50da022757cee8d3a0a138fe
[  149.745150] patching 512 blocks to 512
[  150.385289] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/738fab84142ffcfb50da022757cee8d3a0a138fe
[  150.442510]   moving 5 blocks
[  150.456100] patching 33 blocks to 33
[  150.641692] patching 1509 blocks to 1509
[  150.908227] patching 41 blocks to 41
[  150.930288]   moving 27 blocks
[  150.934934]   moving 19 blocks
[  150.937754] patching 2 blocks to 2
[  150.948245]   moving 5 blocks
[  150.952775] patching 25 blocks to 25
[  150.975824]   moving 58 blocks
[  150.982568]   moving 23 blocks
[  150.988883]   moving 23 blocks
[  150.990836]   moving 1 blocks
[  151.143408]   moving 1377 blocks
[  151.194214]   moving 4 blocks
[  151.243512] stashing 512 overlapping blocks to fdc624351c6bead95232641967aed4f6445d796c
[  151.243544] 119435264 bytes free on /cache (2097152 needed)
[  151.243601]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fdc624351c6bead95232641967aed4f6445d796c
[  151.270068] patching 512 blocks to 512
[  151.910266] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fdc624351c6bead95232641967aed4f6445d796c
[  152.097068]   moving 957 blocks
[  152.135660]   moving 19 blocks
[  152.141407]   moving 24 blocks
[  152.154486] patching 79 blocks to 79
[  152.185392]   moving 36 blocks
[  152.248285] patching 510 blocks to 510
[  152.302219]   moving 19 blocks
[  152.304117]   moving 1 blocks
[  152.305356]   moving 1 blocks
[  152.306453]   moving 1 blocks
[  152.307489] stashing 2 overlapping blocks to 179cc297c0f13f1570cc6e624190cc8f6a8836ac
[  152.307529] 119435264 bytes free on /cache (8192 needed)
[  152.307583]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/179cc297c0f13f1570cc6e624190cc8f6a8836ac
[  152.313289] patching 2 blocks to 2
[  152.321453] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/179cc297c0f13f1570cc6e624190cc8f6a8836ac
[  152.323557]   moving 1 blocks
[  152.325605]   moving 4 blocks
[  152.326928] patching 2 blocks to 2
[  152.336188]   moving 5 blocks
[  152.338341]   moving 4 blocks
[  152.367770] patching 245 blocks to 245
[  152.427697]   moving 10 blocks
[  152.446387]   moving 148 blocks
[  152.463030]   moving 46 blocks
[  152.515766] stashing 512 overlapping blocks to 0ff4078992711458b65a974d98f0d89763a60160
[  152.515797] 119435264 bytes free on /cache (2097152 needed)
[  152.515860]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0ff4078992711458b65a974d98f0d89763a60160
[  152.542072] patching 512 blocks to 512
[  152.766130] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0ff4078992711458b65a974d98f0d89763a60160
[  152.823855]   moving 14 blocks
[  152.876489] stashing 512 overlapping blocks to 220c81e7b93611cbcf61c1b578abfa92e4d8ebef
[  152.876527] 119435264 bytes free on /cache (2097152 needed)
[  152.876574]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/220c81e7b93611cbcf61c1b578abfa92e4d8ebef
[  152.902774] patching 512 blocks to 512
[  153.032127] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/220c81e7b93611cbcf61c1b578abfa92e4d8ebef
[  153.170024] stashing 512 overlapping blocks to 76d7f419d8bd5af83433eb1ec4f50f9aeaea728b
[  153.170056] 119435264 bytes free on /cache (2097152 needed)
[  153.170113]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/76d7f419d8bd5af83433eb1ec4f50f9aeaea728b
[  153.197518] patching 512 blocks to 512
[  153.382687] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/76d7f419d8bd5af83433eb1ec4f50f9aeaea728b
[  153.482955] stashing 512 overlapping blocks to 300184aaf42c0ada907671133d191db3dc371e63
[  153.482988] 119435264 bytes free on /cache (2097152 needed)
[  153.483043]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/300184aaf42c0ada907671133d191db3dc371e63
[  153.509512] patching 512 blocks to 512
[  153.548059] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/300184aaf42c0ada907671133d191db3dc371e63
[  153.573844]   moving 1 blocks
[  153.598697]   moving 12 blocks
[  153.636317] patching 120 blocks to 120
[  153.687899] patching 2 blocks to 2
[  153.720059]   moving 12 blocks
[  153.743305] patching 4 blocks to 4
[  153.777187]   moving 2 blocks
[  153.803805] patching 33 blocks to 33
[  153.853227]   moving 86 blocks
[  153.886688]   moving 9 blocks
[  153.919494]   moving 48 blocks
[  153.950646]   moving 1 blocks
[  153.975612]   moving 1 blocks
[  154.006134]   moving 54 blocks
[  154.065112]   moving 272 blocks
[  154.157832]   moving 252 blocks
[  154.172409]   moving 8 blocks
[  154.173699] stashing 2 overlapping blocks to b2043fa947e4640bfe14fd81eb6ed059dc014896
[  154.173736] 119435264 bytes free on /cache (8192 needed)
[  154.173792]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b2043fa947e4640bfe14fd81eb6ed059dc014896
[  154.177149] patching 2 blocks to 2
[  154.185492] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b2043fa947e4640bfe14fd81eb6ed059dc014896
[  154.192811] patching 43 blocks to 43
[  154.216704] patching 43 blocks to 43
[  154.236874]   moving 1 blocks
[  154.238488]   moving 5 blocks
[  154.239950] patching 2 blocks to 2
[  154.249748]   moving 3 blocks
[  154.260972]   moving 83 blocks
[  154.271390]   moving 33 blocks
[  154.281764]   moving 57 blocks
[  154.296437] patching 22 blocks to 23
[  154.313622] patching 2 blocks to 2
[  154.323710]   moving 3 blocks
[  154.325192]   moving 3 blocks
[  154.328017]   moving 24 blocks
[  154.331612] patching 2 blocks to 2
[  154.341611]   moving 3 blocks
[  154.342319]   moving 1 blocks
[  154.346492] patching 27 blocks to 27
[  154.363011]   moving 2 blocks
[  154.412349] stashing 512 overlapping blocks to c07c2eadb605bf247055ee15bdb23a4e62aaae37
[  154.412380] 119435264 bytes free on /cache (2097152 needed)
[  154.412441]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c07c2eadb605bf247055ee15bdb23a4e62aaae37
[  154.438386] patching 512 blocks to 512
[  155.079423] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c07c2eadb605bf247055ee15bdb23a4e62aaae37
[  155.142270]   moving 25 blocks
[  155.154953] patching 25 blocks to 26
[  155.218994] stashing 512 overlapping blocks to f8b5a5c9b80357ebca287005ccb60e8f3301a414
[  155.219029] 119435264 bytes free on /cache (2097152 needed)
[  155.219081]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f8b5a5c9b80357ebca287005ccb60e8f3301a414
[  155.243946] patching 512 blocks to 512
[  155.304166] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f8b5a5c9b80357ebca287005ccb60e8f3301a414
[  155.322559]   moving 5 blocks
[  155.324424]   moving 5 blocks
[  155.486990] patching 1459 blocks to 1459
[  155.692147]   moving 4 blocks
[  155.703620] patching 80 blocks to 81
[  155.736094]   moving 22 blocks
[  155.738690]   moving 4 blocks
[  155.753187] patching 112 blocks to 112
[  155.779951]   moving 12 blocks
[  155.783395]   moving 1 blocks
[  155.784564]   moving 1 blocks
[  155.786332]   moving 6 blocks
[  155.845288]   moving 489 blocks
[  155.865627]   moving 12 blocks
[  155.902390]   moving 307 blocks
[  155.922871] patching 30 blocks to 31
[  155.941961]   moving 2 blocks
[  155.946142] patching 24 blocks to 24
[  155.962043]   moving 20 blocks
[  155.966175] patching 2 blocks to 2
[  155.976313]   moving 3 blocks
[  156.005288]   moving 240 blocks
[  156.020180] patching 22 blocks to 22
[  156.035312]   moving 2 blocks
[  156.036477]   moving 1 blocks
[  156.037204] patching 2 blocks to 2
[  156.046495]   moving 4 blocks
[  156.070033]   moving 191 blocks
[  156.083444]   moving 20 blocks
[  156.087282]   moving 2 blocks
[  156.120302] patching 284 blocks to 284
[  156.486954]   moving 44 blocks
[  156.496730] stashing 2 overlapping blocks to 4472d43c871031680c687bb073cc007ab8de1d35
[  156.496760] 119435264 bytes free on /cache (8192 needed)
[  156.496815]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4472d43c871031680c687bb073cc007ab8de1d35
[  156.501130] patching 2 blocks to 2
[  156.509426] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4472d43c871031680c687bb073cc007ab8de1d35
[  156.533772] patching 80 blocks to 80
[  156.706869]   moving 1271 blocks
[  156.754234] patching 2 blocks to 2
[  156.764024]   moving 4 blocks
[  156.953251]   moving 1611 blocks
[  157.037572] patching 36 blocks to 36
[  157.062909]   moving 67 blocks
[  157.095635]   moving 275 blocks
[  157.110423]   moving 8 blocks
[  157.117307] patching 31 blocks to 31
[  157.132242] patching 3 blocks to 3
[  157.145208]   moving 27 blocks
[  157.148741]   moving 4 blocks
[  157.152343]   moving 19 blocks
[  157.156526]   moving 3 blocks
[  157.158503]   moving 4 blocks
[  157.159861] patching 2 blocks to 2
[  157.170597]   moving 10 blocks
[  157.177045] patching 46 blocks to 46
[  157.216529]   moving 189 blocks
[  157.227990]   moving 1 blocks
[  157.239128]   moving 78 blocks
[  157.246013]   moving 10 blocks
[  157.249557]   moving 1 blocks
[  157.250837]   moving 1 blocks
[  157.253362]   moving 18 blocks
[  157.259047] patching 21 blocks to 21
[  157.275515] patching 26 blocks to 26
[  157.289693] patching 2 blocks to 2
[  157.299983]   moving 4 blocks
[  157.301561]   moving 4 blocks
[  157.307682] patching 34 blocks to 34
[  157.336962] patching 116 blocks to 116
[  157.388468]   moving 248 blocks
[  157.401232] stashing 2 overlapping blocks to ce959929174bb6992f297ab5599f3c44f5eaa1c6
[  157.401260] 119435264 bytes free on /cache (8192 needed)
[  157.401316]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce959929174bb6992f297ab5599f3c44f5eaa1c6
[  157.404960] patching 2 blocks to 2
[  157.413174] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce959929174bb6992f297ab5599f3c44f5eaa1c6
[  157.414958]   moving 1 blocks
[  157.415943] stashing 2 overlapping blocks to 07b05eae38513e497df03261b33494aa8799cb31
[  157.415973] 119435264 bytes free on /cache (8192 needed)
[  157.416014]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/07b05eae38513e497df03261b33494aa8799cb31
[  157.419080] patching 2 blocks to 2
[  157.427237] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/07b05eae38513e497df03261b33494aa8799cb31
[  157.432170]   moving 25 blocks
[  157.438241]   moving 37 blocks
[  157.442187] patching 2 blocks to 2
[  157.451934]   moving 3 blocks
[  157.453535] patching 2 blocks to 2
[  157.463640]   moving 5 blocks
[  157.465560] patching 2 blocks to 2
[  157.475486]   moving 4 blocks
[  157.482068]   moving 42 blocks
[  157.487608]   moving 7 blocks
[  157.489335] patching 2 blocks to 2
[  157.500325]   moving 10 blocks
[  157.503502] patching 2 blocks to 2
[  157.513679]   moving 3 blocks
[  157.532324]   moving 152 blocks
[  157.549692] patching 73 blocks to 73
[  157.574993] stashing 10 overlapping blocks to 23f3bb977ee29ce91ef5e6e565967d549637d420
[  157.575021] 119435264 bytes free on /cache (40960 needed)
[  157.575078]  writing 10 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/23f3bb977ee29ce91ef5e6e565967d549637d420
[  157.578864] patching 10 blocks to 10
[  157.587520] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/23f3bb977ee29ce91ef5e6e565967d549637d420
[  157.589524]   moving 1 blocks
[  157.591276] patching 6 blocks to 6
[  157.602607] patching 3 blocks to 3
[  157.614423]   moving 11 blocks
[  157.622829] patching 52 blocks to 52
[  157.644561]   moving 3 blocks
[  157.663959]   moving 145 blocks
[  157.673214]   moving 1 blocks
[  157.674465]   moving 4 blocks
[  157.678021]   moving 15 blocks
[  157.684419] patching 29 blocks to 29
[  157.700031]   moving 1 blocks
[  157.701003] stashing 2 blocks to 048b66624c402bc88b8d2d9b883ce0ee0a466586
[  157.701013]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/048b66624c402bc88b8d2d9b883ce0ee0a466586
[  157.740723]   moving 305 blocks
[  157.759247]   moving 39 blocks
[  157.770292] patching 66 blocks to 66
[  157.789015]   moving 4 blocks
[  157.790359]   moving 5 blocks
[  157.792335]   moving 5 blocks
[  157.793846]   moving 6 blocks
[  157.796176]   moving 14 blocks
[  157.801376]   moving 36 blocks
[  157.805990]   moving 10 blocks
[  157.807709]   moving 8 blocks
[  157.812736]   moving 19 blocks
[  157.817039]   moving 15 blocks
[  157.820482] patching 19 blocks to 19
[  157.838501] patching 68 blocks to 68
[  157.860205]   moving 41 blocks
[  157.864689] patching 2 blocks to 2
[  157.874105]   moving 3 blocks
[  157.875492] patching 2 blocks to 2
[  157.907968]   moving 235 blocks
[  157.919877]   moving 6 blocks
[  157.921325] stashing 2 blocks to a5b7cda9265e4b7472b369d4cfc4b616de8e6d0f
[  157.921335]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a5b7cda9265e4b7472b369d4cfc4b616de8e6d0f
[  157.944568]   moving 143 blocks
[  157.954796]   moving 9 blocks
[  157.959699]   moving 19 blocks
[  157.964234]   moving 27 blocks
[  157.968628]   moving 9 blocks
[  157.971079]   moving 4 blocks
[  157.972290]   moving 4 blocks
[  157.974295]   moving 10 blocks
[  157.975982]   moving 5 blocks
[  157.977764]   moving 6 blocks
[  157.979890]   moving 6 blocks
[  157.984620]   moving 35 blocks
[  157.989136]   moving 5 blocks
[  157.990684]   moving 6 blocks
[  157.993073]   moving 7 blocks
[  157.994870]   moving 5 blocks
[  157.996819]   moving 5 blocks
[  157.999400]   moving 15 blocks
[  158.007282]   moving 37 blocks
[  158.025047]   moving 124 blocks
[  158.032713] patching 2 blocks to 2
[  158.042147]   moving 4 blocks
[  158.043536] stashing 3 blocks to fbee356493999d26e8a75bee0f7620a1cf439e02
[  158.043551]  writing 3 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fbee356493999d26e8a75bee0f7620a1cf439e02
[  158.048127] stashing 2 blocks to 6937682f39ba060e922e3f54467bf8cfcf08494a
[  158.048147]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6937682f39ba060e922e3f54467bf8cfcf08494a
[  159.036700] patching 7896 blocks to 7898
[  160.705731]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fbee356493999d26e8a75bee0f7620a1cf439e02
[  160.705930]   moving 3 blocks
[  160.706905] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fbee356493999d26e8a75bee0f7620a1cf439e02
[  160.707266]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6937682f39ba060e922e3f54467bf8cfcf08494a
[  160.707418] patching 2 blocks to 2
[  160.717395] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6937682f39ba060e922e3f54467bf8cfcf08494a
[  160.719031]   moving 11 blocks
[  160.773887]   moving 554 blocks
[  160.797952]   moving 13 blocks
[  160.799814]   moving 2 blocks
[  160.802994]   moving 23 blocks
[  160.811165]   moving 43 blocks
[  160.820385] patching 34 blocks to 34
[  160.834048] patching 7 blocks to 7
[  160.843772]   moving 1 blocks
[  160.845835] patching 13 blocks to 13
[  160.868453]   moving 127 blocks
[  160.878019]   moving 9 blocks
[  160.879700]   moving 6 blocks
[  160.885037]   moving 41 blocks
[  160.889115]   moving 1 blocks
[  160.890223] patching 7 blocks to 7
[  160.951190]   moving 540 blocks
[  160.972526]   moving 1 blocks
[  160.976379] patching 31 blocks to 31
[  160.991273] patching 2 blocks to 2
[  161.000952]   moving 3 blocks
[  161.002110] patching 3 blocks to 3
[  161.024857] patching 136 blocks to 136
[  161.121073] patching 774 blocks to 774
[  161.191548] patching 9 blocks to 9
[  161.202487]   moving 1 blocks
[  161.203761]   moving 9 blocks
[  161.210199]   moving 40 blocks
[  161.223999]   moving 102 blocks
[  161.298496] patching 709 blocks to 709
[  161.402453]   moving 32 blocks
[  161.406616]   moving 3 blocks
[  161.440825]   moving 342 blocks
[  161.455741]   moving 3 blocks
[  161.457330] patching 9 blocks to 9
[  161.468517]   moving 1 blocks
[  161.469548]   moving 2 blocks
[  161.471879] patching 21 blocks to 21
[  161.483560]   moving 1 blocks
[  161.484238]   moving 1 blocks
[  161.485556] patching 5 blocks to 5
[  161.507011]   moving 134 blocks
[  161.516532]   moving 4 blocks
[  161.517866]   moving 2 blocks
[  161.532993] patching 140 blocks to 140
[  161.558368] patching 7 blocks to 7
[  161.567840]   moving 1 blocks
[  161.568964] patching 7 blocks to 7
[  161.578510]   moving 1 blocks
[  161.581716] patching 32 blocks to 32
[  161.598517] patching 40 blocks to 40
[  161.615660]   moving 12 blocks
[  161.618407]   moving 1 blocks
[  161.619486]   moving 3 blocks
[  161.620724] patching 2 blocks to 2
[  161.630453]   moving 4 blocks
[  161.637924] patching 69 blocks to 69
[  161.656183]   moving 5 blocks
[  161.657237]   moving 1 blocks
[  161.658060]   moving 3 blocks
[  161.658782]   moving 1 blocks
[  161.659933] patching 8 blocks to 8
[  161.675318] patching 7 blocks to 7
[  161.685142]   moving 6 blocks
[  161.687655]   moving 13 blocks
[  161.689311] patching 7 blocks to 7
[  161.698853]   moving 1 blocks
[  161.699639] patching 3 blocks to 3
[  162.337539] stashing 5010 overlapping blocks to 2a54225913329787c7676f7189ecae024fd8530a
[  162.337572] 119418880 bytes free on /cache (20520960 needed)
[  162.337635]  writing 5010 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2a54225913329787c7676f7189ecae024fd8530a
[  162.805973] patching 5010 blocks to 5011
[  162.848565] I:current maximum temperature: 36206
[  163.519614] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2a54225913329787c7676f7189ecae024fd8530a
[  164.239916] stashing 5346 overlapping blocks to ef2e424ffb1405324049b88e6f173411f43c7876
[  164.239947] 119418880 bytes free on /cache (21897216 needed)
[  164.240010]  writing 5346 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ef2e424ffb1405324049b88e6f173411f43c7876
[  164.774159] patching 5346 blocks to 5347
[  165.417207] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ef2e424ffb1405324049b88e6f173411f43c7876
[  165.794202]   moving 1268 blocks
[  165.868193]   moving 260 blocks
[  165.992442] patching 1189 blocks to 1189
[  166.259822] patching 39 blocks to 39
[  166.278761] patching 60 blocks to 60
[  166.334451]   moving 354 blocks
[  166.370943] patching 63 blocks to 63
[  166.429608] patching 158 blocks to 158
[  166.508855]   moving 97 blocks
[  166.546863] patching 105 blocks to 105
[  166.630336] patching 425 blocks to 425
[  166.849276]   moving 762 blocks
[  167.079673] patching 1836 blocks to 1836
[  167.360406] patching 752 blocks to 752
[  167.438643] patching 104 blocks to 104
[  167.461968] patching 43 blocks to 43
[  167.983029]   moving 4560 blocks
[  168.240237] stashing 331 overlapping blocks to a31aad992efc43c61a97b3c682eaee1d4285e6b3
[  168.240270] 119418880 bytes free on /cache (1355776 needed)
[  168.240324]  writing 331 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a31aad992efc43c61a97b3c682eaee1d4285e6b3
[  168.260599]   moving 331 blocks
[  168.262147] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a31aad992efc43c61a97b3c682eaee1d4285e6b3
[  168.280327]   moving 3 blocks
[  168.281224]   moving 1 blocks
[  168.282025]   moving 1 blocks
[  168.282812]   moving 1 blocks
[  168.283625]   moving 1 blocks
[  168.284431]   moving 1 blocks
[  168.285209]   moving 1 blocks
[  168.286076]   moving 2 blocks
[  168.286925]   moving 1 blocks
[  168.291132]   moving 30 blocks
[  168.295105]   moving 1 blocks
[  168.298757]   moving 30 blocks
[  168.303214]   moving 1 blocks
[  168.304189]   moving 2 blocks
[  168.305267]   moving 1 blocks
[  168.306059]   moving 1 blocks
[  168.306841]   moving 1 blocks
[  168.307629]   moving 1 blocks
[  168.308415]   moving 1 blocks
[  168.309204]   moving 1 blocks
[  168.311820]   moving 13 blocks
[  168.314688]   moving 1 blocks
[  168.315716]   moving 3 blocks
[  168.317232]   moving 7 blocks
[  168.318650]   moving 2 blocks
[  168.319780]   moving 1 blocks
[  168.320517]   moving 1 blocks
[  168.321394]   moving 1 blocks
[  168.322275]   moving 1 blocks
[  168.323093]   moving 1 blocks
[  168.323951]   moving 2 blocks
[  168.324916]   moving 2 blocks
[  168.325781]   moving 2 blocks
[  168.326530]   moving 1 blocks
[  168.327420]   moving 2 blocks
[  168.328288]   moving 1 blocks
[  168.329041]   moving 1 blocks
[  168.329990]   moving 2 blocks
[  168.331207]   moving 2 blocks
[  168.332327]   moving 1 blocks
[  168.414455]   moving 850 blocks
[  168.445208]   moving 2 blocks
[  168.446111]   moving 1 blocks
[  168.449840]   moving 29 blocks
[  168.453473]   moving 2 blocks
[  168.454401]   moving 1 blocks
[  168.455304]   moving 1 blocks
[  168.456219]   moving 2 blocks
[  168.457048]   moving 1 blocks
[  168.460344]   moving 25 blocks
[  168.464478]   moving 1 blocks
[  168.467650]   moving 15 blocks
[  168.469144]   moving 1 blocks
[  168.469992]   moving 1 blocks
[  168.470910]   moving 2 blocks
[  168.471871]   moving 2 blocks
[  168.472803]   moving 2 blocks
[  168.473659]   moving 2 blocks
[  168.474526]   moving 2 blocks
[  168.475363]   moving 1 blocks
[  168.476155]   moving 1 blocks
[  168.476900]   moving 1 blocks
[  168.477698]   moving 1 blocks
[  168.478442]   moving 1 blocks
[  168.479215]   moving 1 blocks
[  168.480067]   moving 2 blocks
[  168.480848]   moving 1 blocks
[  168.481705]   moving 2 blocks
[  168.482625]   moving 1 blocks
[  168.483352]   moving 1 blocks
[  168.484189]   moving 1 blocks
[  168.485042]   moving 2 blocks
[  168.485819]   moving 1 blocks
[  168.486683]   moving 2 blocks
[  168.487650]   moving 2 blocks
[  168.488917]   moving 2 blocks
[  168.490038]   moving 1 blocks
[  168.490822]   moving 1 blocks
[  168.491729]   moving 2 blocks
[  168.492530]   moving 1 blocks
[  168.493255]   moving 1 blocks
[  168.494008]   moving 1 blocks
[  168.494859]   moving 2 blocks
[  168.495710]   moving 2 blocks
[  168.496462]   moving 1 blocks
[  168.497248]   moving 1 blocks
[  168.498098]   moving 2 blocks
[  168.498865]   moving 1 blocks
[  168.499618]   moving 1 blocks
[  168.500397]   moving 1 blocks
[  168.501163]   moving 1 blocks
[  168.501904]   moving 1 blocks
[  168.503037]   moving 4 blocks
[  168.504388]   moving 1 blocks
[  168.505113]   moving 1 blocks
[  168.505993]   moving 2 blocks
[  168.506944]   moving 2 blocks
[  168.508049]   moving 1 blocks
[  168.508934]   moving 2 blocks
[  168.509739]   moving 1 blocks
[  168.510650]   moving 2 blocks
[  168.511617]   moving 2 blocks
[  168.512839]   moving 2 blocks
[  168.513913]   moving 1 blocks
[  168.514840]   moving 2 blocks
[  168.515618]   moving 1 blocks
[  168.516534]   moving 2 blocks
[  168.517629]   moving 1 blocks
[  168.518502]   moving 2 blocks
[  168.519294]   moving 1 blocks
[  168.520180]   moving 2 blocks
[  168.521170]   moving 2 blocks
[  168.522320]   moving 1 blocks
[  168.523066]   moving 1 blocks
[  168.523984]   moving 2 blocks
[  168.525203]   moving 2 blocks
[  168.526133]   moving 2 blocks
[  168.527210]   moving 1 blocks
[  168.527968]   moving 1 blocks
[  168.530158]   moving 16 blocks
[  168.533301]   moving 1 blocks
[  168.534230]   moving 2 blocks
[  168.535190]   moving 1 blocks
[  168.535956]   moving 1 blocks
[  168.536958]   moving 2 blocks
[  168.537849]   moving 1 blocks
[  168.538805]   moving 2 blocks
[  168.539841]   moving 1 blocks
[  168.540574]   moving 1 blocks
[  168.541351]   moving 1 blocks
[  168.542095]   moving 1 blocks
[  168.542853]   moving 1 blocks
[  168.543739]   moving 2 blocks
[  168.544816]   moving 7 blocks
[  168.550480]   moving 45 blocks
[  168.556603]   moving 1 blocks
[  168.560042]   moving 25 blocks
[  168.566516]   moving 29 blocks
[  168.569623]   moving 2 blocks
[  168.570762]   moving 1 blocks
[  168.571553]   moving 1 blocks
[  168.572552]   moving 3 blocks
[  168.573889]   moving 2 blocks
[  168.574770]   moving 2 blocks
[  168.575593]   moving 1 blocks
[  168.576353]   moving 1 blocks
[  168.577097]   moving 1 blocks
[  168.577889]   moving 1 blocks
[  168.578649]   moving 1 blocks
[  168.579519]   moving 2 blocks
[  168.580570]   moving 1 blocks
[  168.581475]   moving 2 blocks
[  168.582238]   moving 1 blocks
[  168.582995]   moving 1 blocks
[  168.583766]   moving 1 blocks
[  168.584581]   moving 1 blocks
[  168.585362]   moving 1 blocks
[  168.586174]   moving 1 blocks
[  168.586948]   moving 1 blocks
[  168.589285]   moving 15 blocks
[  168.592551]   moving 1 blocks
[  168.593321]   moving 1 blocks
[  168.594855]   moving 9 blocks
[  168.597827]   moving 2 blocks
[  168.598753]   moving 2 blocks
[  168.599897]   moving 4 blocks
[  168.601334]   moving 1 blocks
[  168.602106]   moving 1 blocks
[  168.602898]   moving 1 blocks
[  168.603741]   moving 2 blocks
[  168.604511]   moving 1 blocks
[  168.605367]   moving 1 blocks
[  168.606094]   moving 1 blocks
[  168.606974]   moving 2 blocks
[  168.607832]   moving 1 blocks
[  168.610132]   moving 22 blocks
[  168.611950]   moving 1 blocks
[  168.612912]   moving 2 blocks
[  168.614093]   moving 2 blocks
[  168.615249]   moving 1 blocks
[  168.615977]   moving 1 blocks
[  168.617007]   moving 3 blocks
[  168.617878]   moving 1 blocks
[  168.618654]   moving 1 blocks
[  168.619388]   moving 1 blocks
[  168.620288]   moving 2 blocks
[  168.621357]   moving 1 blocks
[  168.622322]   moving 2 blocks
[  168.623139]   moving 1 blocks
[  168.623907]   moving 1 blocks
[  168.624646]   moving 1 blocks
[  168.625323]   moving 2 blocks
[  168.626450]   moving 2 blocks
[  168.627766]   moving 5 blocks
[  168.630109]   moving 1 blocks
[  168.630883]   moving 1 blocks
[  168.631727]   moving 2 blocks
[  168.632499]   moving 1 blocks
[  168.633322]   moving 1 blocks
[  168.634181]   moving 1 blocks
[  168.634960]   moving 1 blocks
[  168.635795]   moving 2 blocks
[  168.636555]   moving 1 blocks
[  168.637518]   moving 2 blocks
[  168.638621]   moving 1 blocks
[  168.639399]   moving 1 blocks
[  168.640174]   moving 1 blocks
[  168.643767]   moving 29 blocks
[  168.646660]   moving 2 blocks
[  168.647891]   moving 1 blocks
[  168.648501]   moving 1 blocks
[  168.649304]   moving 1 blocks
[  168.650502]   moving 2 blocks
[  168.651413]   moving 1 blocks
[  168.652254]   moving 1 blocks
[  168.653129]   moving 1 blocks
[  168.653995]   moving 1 blocks
[  168.656260]   moving 10 blocks
[  168.657525]   moving 2 blocks
[  168.658232]   moving 1 blocks
[  168.659420]   moving 2 blocks
[  168.660399]   moving 1 blocks
[  168.661221]   moving 1 blocks
[  168.662145]   moving 2 blocks
[  168.663048]   moving 1 blocks
[  168.663701]   moving 1 blocks
[  168.664509]   moving 1 blocks
[  168.665326]   moving 1 blocks
[  168.835374]   moving 1778 blocks
[  168.897333]   moving 2 blocks
[  168.898312]   moving 1 blocks
[  168.899249]   moving 2 blocks
[  168.900163]   moving 2 blocks
[  168.903815]   moving 29 blocks
[  168.907164]   moving 1 blocks
[  168.908169]   moving 2 blocks
[  168.909142]   moving 2 blocks
[  168.909949]   moving 1 blocks
[  168.910888]   moving 1 blocks
[  168.911655]   moving 1 blocks
[  168.912496]   moving 1 blocks
[  168.913579]   moving 3 blocks
[  168.914787]   moving 1 blocks
[  168.915640]   moving 1 blocks
[  168.916551]   moving 2 blocks
[  168.917515]   moving 1 blocks
[  168.918941]   moving 3 blocks
[  168.920031]   moving 1 blocks
[  168.921106]   moving 2 blocks
[  168.921997]   moving 1 blocks
[  168.922702]   moving 1 blocks
[  168.926901]   moving 33 blocks
[  168.930552]   moving 2 blocks
[  168.931820]   moving 1 blocks
[  168.932445]   moving 1 blocks
[  168.933329]   moving 2 blocks
[  168.934473]   moving 1 blocks
[  168.935121]   moving 1 blocks
[  168.936249]   moving 1 blocks
[  168.937014]   moving 1 blocks
[  168.937877]   moving 1 blocks
[  168.938786]   moving 2 blocks
[  168.940204]   moving 2 blocks
[  168.940862]   moving 1 blocks
[  168.941516]   moving 1 blocks
[  168.942311]   moving 1 blocks
[  168.943171]   moving 2 blocks
[  168.944087]   moving 2 blocks
[  168.945063]   moving 1 blocks
[  168.945645]   moving 1 blocks
[  168.946259]   moving 1 blocks
[  168.947004]   moving 1 blocks
[  168.947661]   moving 1 blocks
[  168.948249]   moving 1 blocks
[  168.948900]   moving 1 blocks
[  168.949512]   moving 1 blocks
[  168.950157]   moving 1 blocks
[  168.950948]   moving 1 blocks
[  168.951599]   moving 1 blocks
[  168.952375]   moving 1 blocks
[  168.953022]   moving 1 blocks
[  168.953789]   moving 1 blocks
[  168.954449]   moving 1 blocks
[  168.955286]   moving 2 blocks
[  168.955932]   moving 1 blocks
[  168.956893]   moving 1 blocks
[  168.957867]   moving 2 blocks
[  168.958868]   moving 2 blocks
[  168.959646]   moving 1 blocks
[  168.960451]   moving 1 blocks
[  168.961377]   moving 2 blocks
[  168.962187]   moving 1 blocks
[  168.962831]   moving 1 blocks
[  168.963437]   moving 1 blocks
[  168.964144]   moving 2 blocks
[  168.965177]   moving 1 blocks
[  168.965754]   moving 1 blocks
[  168.966523]   moving 1 blocks
[  168.967428]   moving 2 blocks
[  168.968227]   moving 1 blocks
[  168.969031]   moving 1 blocks
[  168.969646]   moving 1 blocks
[  168.970561]   moving 2 blocks
[  168.971677]   moving 1 blocks
[  168.972372]   moving 2 blocks
[  168.973413]   moving 1 blocks
[  168.974321]   moving 2 blocks
[  168.975359]   moving 2 blocks
[  168.976314]   moving 1 blocks
[  168.977153]   moving 1 blocks
[  168.978410]   moving 2 blocks
[  168.979087]   moving 1 blocks
[  168.979866]   moving 1 blocks
[  168.980633]   moving 1 blocks
[  168.981248]   moving 1 blocks
[  168.982025]   moving 1 blocks
[  168.982682]   moving 1 blocks
[  168.984129]   moving 1 blocks
[  168.984722]   moving 1 blocks
[  168.985586]   moving 2 blocks
[  168.986478]   moving 1 blocks
[  168.987646]   moving 8 blocks
[  168.990315]   moving 1 blocks
[  168.990967]   moving 1 blocks
[  168.991542]   moving 1 blocks
[  168.992145]   moving 1 blocks
[  168.992717]   moving 1 blocks
[  168.995232]   moving 17 blocks
[  168.997589]   moving 1 blocks
[  168.998553]   moving 2 blocks
[  168.999709]   moving 1 blocks
[  169.000393]   moving 2 blocks
[  169.001055]   moving 1 blocks
[  169.001774]   moving 2 blocks
[  169.002891]   moving 2 blocks
[  169.003957]   moving 1 blocks
[  169.004559]   moving 1 blocks
[  169.005679]   moving 4 blocks
[  169.006653]   moving 1 blocks
[  169.007234]   moving 1 blocks
[  169.007887]   moving 1 blocks
[  169.008629]   moving 1 blocks
[  169.009321]   moving 2 blocks
[  169.009985]   moving 1 blocks
[  169.010713]   moving 1 blocks
[  169.018390]   moving 78 blocks
[  169.023949]   moving 1 blocks
[  169.024585]   moving 1 blocks
[  169.025230]   moving 1 blocks
[  169.026011]   moving 1 blocks
[  169.042606]   moving 157 blocks
[  169.051898]   moving 2 blocks
[  169.052546]   moving 1 blocks
[  169.053169]   moving 1 blocks
[  169.053832]   moving 2 blocks
[  169.054539]   moving 2 blocks
[  169.055191]   moving 1 blocks
[  169.056979]   moving 1 blocks
[  169.057676]   moving 1 blocks
[  169.066453]   moving 1 blocks
[  169.067031]   moving 1 blocks
[  169.067894]   moving 1 blocks
[  169.068483]   moving 1 blocks
[  169.071407]   moving 29 blocks
[  169.074737]   moving 3 blocks
[  169.075521]   moving 2 blocks
[  169.076414]   moving 1 blocks
[  169.076996]   moving 1 blocks
[  169.077908]   moving 1 blocks
[  169.078572]   moving 2 blocks
[  169.079261]   moving 2 blocks
[  169.080290]   moving 6 blocks
[  169.081305]   moving 1 blocks
[  169.081930]   moving 1 blocks
[  169.082513]   moving 1 blocks
[  169.083624]   moving 4 blocks
[  169.084942]   moving 2 blocks
[  169.085684]   moving 2 blocks
[  169.086573]   moving 1 blocks
[  169.088119]   moving 2 blocks
[  169.088738]   moving 1 blocks
[  169.089364]   moving 1 blocks
[  169.090538]   moving 7 blocks
[  169.092053]   moving 1 blocks
[  169.092745]   moving 2 blocks
[  169.093363]   moving 1 blocks
[  169.094050]   moving 2 blocks
[  169.095002]   moving 1 blocks
[  169.095613]   moving 1 blocks
[  169.096365]   moving 1 blocks
[  169.097019]   moving 2 blocks
[  169.097636]   moving 1 blocks
[  169.098245]   moving 1 blocks
[  169.098898]   moving 2 blocks
[  169.099506]   moving 1 blocks
[  169.100139]   moving 1 blocks
[  169.100888]   moving 1 blocks
[  169.101547]   moving 1 blocks
[  169.102127]   moving 1 blocks
[  169.102890]   moving 1 blocks
[  169.103472]   moving 1 blocks
[  169.106941]   moving 27 blocks
[  169.110694]   moving 1 blocks
[  169.111407]   moving 2 blocks
[  169.112299]   moving 1 blocks
[  169.112881]   moving 1 blocks
[  169.113494]   moving 1 blocks
[  169.114235]   moving 2 blocks
[  169.114858]   moving 1 blocks
[  169.115555]   moving 2 blocks
[  169.116528]   moving 2 blocks
[  169.117670]   moving 4 blocks
[  169.118645]   moving 1 blocks
[  169.119228]   moving 1 blocks
[  169.120295]   moving 2 blocks
[  169.121419]   moving 1 blocks
[  169.122003]   moving 1 blocks
[  169.122690]   moving 2 blocks
[  169.123382]   moving 1 blocks
[  169.124195]   moving 2 blocks
[  169.124839]   moving 1 blocks
[  169.125525]   moving 2 blocks
[  169.126480]   moving 2 blocks
[  169.127288]   moving 3 blocks
[  169.128274]   moving 2 blocks
[  169.128893]   moving 1 blocks
[  169.129724]   moving 1 blocks
[  169.130301]   moving 1 blocks
[  169.131238]   moving 2 blocks
[  169.132205]   moving 2 blocks
[  169.133254]   moving 1 blocks
[  169.133836]   moving 1 blocks
[  169.134535]   moving 2 blocks
[  169.135714] patching 4 blocks to 4
[  169.146309]   moving 6 blocks
[  169.149138] patching 13 blocks to 13
[  169.162538] patching 10 blocks to 10
[  169.173514] patching 10 blocks to 10
[  169.183335]   moving 1 blocks
[  169.184611]   moving 4 blocks
[  169.186448]   moving 8 blocks
[  169.190469] patching 12 blocks to 12
[  169.211925] patching 84 blocks to 84
[  169.234670]   moving 9 blocks
[  169.243031]   moving 45 blocks
[  169.247312]   moving 4 blocks
[  169.248465]   moving 1 blocks
[  169.250565]   moving 14 blocks
[  169.252204]   moving 6 blocks
[  169.253822]   moving 5 blocks
[  169.255736]   moving 8 blocks
[  169.256817]   moving 1 blocks
[  169.257720]   moving 1 blocks
[  169.262618]   moving 47 blocks
[  169.268344]   moving 16 blocks
[  169.292072]   moving 207 blocks
[  169.303959]   moving 7 blocks
[  169.323817]   moving 186 blocks
[  169.336315]   moving 27 blocks
[  169.340858]   moving 13 blocks
[  169.343660]   moving 12 blocks
[  169.351109]   moving 12 blocks
[  169.375839]   moving 4 blocks
[  169.398379]   moving 1 blocks
[  169.420875]   moving 4 blocks
[  169.443037]   moving 4 blocks
[  169.466963]   moving 19 blocks
[  169.494963]   moving 20 blocks
[  169.510159]   moving 11 blocks
[  169.535135]   moving 1 blocks
[  169.557398]   moving 7 blocks
[  169.580105]   moving 5 blocks
[  169.603008]   moving 5 blocks
[  169.625556]   moving 1 blocks
[  169.649242]   moving 1 blocks
[  169.662314]   moving 4 blocks
[  169.690998]   moving 5 blocks
[  169.713750]   moving 1 blocks
[  169.736533]   moving 5 blocks
[  169.760258]   moving 9 blocks
[  169.783318]   moving 4 blocks
[  169.808804]   moving 4 blocks
[  169.826705]   moving 1 blocks
[  169.852012]   moving 1 blocks
[  169.875432]   moving 17 blocks
[  169.903607]   moving 31 blocks
[  169.928653]   moving 9 blocks
[  169.961198]   moving 63 blocks
[  169.990009]   moving 1 blocks
[  170.013946]   moving 4 blocks
[  170.015550]   moving 4 blocks
[  170.016495]   moving 1 blocks
[  170.017346]   moving 1 blocks
[  170.018545]   moving 3 blocks
[  170.019717]   moving 1 blocks
[  170.020530]   moving 1 blocks
[  170.021387]   moving 1 blocks
[  170.028577]   moving 63 blocks
[  170.033622]   moving 1 blocks
[  170.041164]   moving 63 blocks
[  170.046656]   moving 1 blocks
[  170.047508]   moving 1 blocks
[  170.048389]   moving 1 blocks
[  170.049239]   moving 1 blocks
[  170.052052]   moving 19 blocks
[  170.053697]   moving 1 blocks
[  170.054814]   moving 4 blocks
[  170.056245] patching 1 blocks to 1
[  170.066791]   moving 4 blocks
[  170.067771]   moving 1 blocks
[  170.085988] patching 178 blocks to 178
[  170.112258]   moving 1 blocks
[  170.113088]   moving 1 blocks
[  170.113738]   moving 1 blocks
[  170.114681]   moving 2 blocks
[  170.116056]   moving 6 blocks
[  170.118261]   moving 11 blocks
[  170.121254]   moving 15 blocks
[  170.128733]   moving 63 blocks
[  170.133424]   moving 1 blocks
[  170.134263]   moving 1 blocks
[  170.135155]   moving 1 blocks
[  170.135800]   moving 1 blocks
[  170.137261]   moving 6 blocks
[  170.144425]   moving 63 blocks
[  170.150422]   moving 7 blocks
[  170.151761]   moving 1 blocks
[  170.152931]   moving 4 blocks
[  170.154660]   moving 6 blocks
[  170.155970]   moving 4 blocks
[  170.157863]   moving 8 blocks
[  170.158945]   moving 1 blocks
[  170.160303]   moving 1 blocks
[  170.160998]   moving 2 blocks
[  170.161823]   moving 1 blocks
[  170.162649]   moving 1 blocks
[  170.163265]   moving 1 blocks
[  170.163910]   moving 1 blocks
[  170.170421]   moving 63 blocks
[  170.176088]   moving 7 blocks
[  170.177248]   moving 2 blocks
[  170.178118]   moving 1 blocks
[  170.178886]   moving 1 blocks
[  170.180588]   moving 4 blocks
[  170.185300]   moving 36 blocks
[  170.189324]   moving 3 blocks
[  170.190503]   moving 1 blocks
[  170.194451]   moving 27 blocks
[  170.197925]   moving 1 blocks
[  170.198770]   moving 1 blocks
[  170.200111]   moving 4 blocks
[  170.206373]   moving 50 blocks
[  170.217727]   moving 1 blocks
[  170.219939]   moving 16 blocks
[  170.223139]   moving 1 blocks
[  170.224001]   moving 1 blocks
[  170.230876]   moving 63 blocks
[  170.237124]   moving 3 blocks
[  170.238357]   moving 1 blocks
[  170.239256]   moving 1 blocks
[  170.240303]   moving 2 blocks
[  170.240951]   moving 1 blocks
[  170.241666]   moving 2 blocks
[  170.242766]   moving 3 blocks
[  170.243756]   moving 2 blocks
[  170.244553]   moving 3 blocks
[  170.245654]   moving 3 blocks
[  170.246808]   moving 1 blocks
[  170.247650]   moving 1 blocks
[  170.248245]   moving 1 blocks
[  170.249001]   moving 1 blocks
[  170.249730]   moving 1 blocks
[  170.250519]   moving 1 blocks
[  170.255621]   moving 45 blocks
[  170.262916]   moving 4 blocks
[  170.264302]   moving 2 blocks
[  170.265192]   moving 4 blocks
[  170.266719]   moving 2 blocks
[  170.268145]   moving 8 blocks
[  170.270934]   moving 1 blocks
[  170.271526]   moving 1 blocks
[  170.277349] patching 51 blocks to 51
[  170.294551]   moving 1 blocks
[  170.295487] patching 4 blocks to 4
[  170.305289]   moving 4 blocks
[  170.306796]   moving 1 blocks
[  170.307680]   moving 1 blocks
[  170.315276]   moving 72 blocks
[  170.322165]   moving 2 blocks
[  170.335455]   moving 135 blocks
[  170.344436]   moving 5 blocks
[  170.354404]   moving 87 blocks
[  170.362383]   moving 1 blocks
[  170.363073]   moving 1 blocks
[  170.363791]   moving 2 blocks
[  170.364529]   moving 1 blocks
[  170.365689]   moving 1 blocks
[  170.366334]   moving 1 blocks
[  170.367084]   moving 2 blocks
[  170.368009]   moving 1 blocks
[  170.368650]   moving 1 blocks
[  170.369441]   moving 3 blocks
[  170.370356]   moving 1 blocks
[  170.371426]   moving 3 blocks
[  170.372644]   moving 4 blocks
[  170.373369]   moving 1 blocks
[  170.374022]   moving 1 blocks
[  170.379812]   moving 55 blocks
[  170.386261]   moving 1 blocks
[  170.386871]   moving 1 blocks
[  170.387794]   moving 1 blocks
[  170.388376]   moving 1 blocks
[  170.388995]   moving 1 blocks
[  170.389576]   moving 1 blocks
[  170.390193]   moving 1 blocks
[  170.391043]   moving 1 blocks
[  170.391808]   moving 1 blocks
[  170.392392]   moving 1 blocks
[  170.393563]   moving 7 blocks
[  170.394975]   moving 1 blocks
[  170.396611]   moving 10 blocks
[  170.399903]   moving 4 blocks
[  170.401739]   moving 8 blocks
[  170.402875]   moving 1 blocks
[  170.404056]   moving 5 blocks
[  170.407286]   moving 23 blocks
[  170.416814]   moving 64 blocks
[  170.427668]   moving 47 blocks
[  170.440625]   moving 1 blocks
[  170.447033]   moving 55 blocks
[  170.458262]   moving 73 blocks
[  170.465361]   moving 4 blocks
[  170.467380]   moving 12 blocks
[  170.470490]   moving 4 blocks
[  170.472606]   moving 17 blocks
[  170.474589]   moving 12 blocks
[  170.477523]   moving 5 blocks
[  170.480172]   moving 13 blocks
[  170.483713]   moving 7 blocks
[  170.489917]   moving 51 blocks
[  170.494850]   moving 8 blocks
[  170.498951]   moving 18 blocks
[  170.509105]   moving 81 blocks
[  170.519699]   moving 30 blocks
[  170.524834]   moving 10 blocks
[  170.531496]   moving 49 blocks
[  170.540020] patching 28 blocks to 28
[  170.558694]   moving 44 blocks
[  170.566556] patching 40 blocks to 40
[  170.582353]   moving 1 blocks
[  170.583307] patching 2 blocks to 2
[  170.592886]   moving 3 blocks
[  170.594736] patching 7 blocks to 7
[  170.604750]   moving 2 blocks
[  170.895568] patching 1937 blocks to 1937
[  175.340197]   moving 7 blocks
[  175.341727]   moving 2 blocks
[  175.343102]   moving 3 blocks
[  175.344454] patching 2 blocks to 2
[  175.356889]   moving 30 blocks
[  175.360618]   moving 3 blocks
[  175.361650] patching 2 blocks to 2
[  175.372401]   moving 13 blocks
[  175.375131]   moving 3 blocks
[  175.376468] patching 2 blocks to 2
[  175.386595]   moving 5 blocks
[  175.388050]   moving 3 blocks
[  175.389301] patching 2 blocks to 2
[  175.400859]   moving 20 blocks
[  175.402766]   moving 3 blocks
[  175.403840] patching 2 blocks to 2
[  175.414025]   moving 14 blocks
[  175.417286]   moving 2 blocks
[  175.417949]   moving 1 blocks
[  175.418857]   moving 4 blocks
[  175.420722]   moving 8 blocks
[  175.423657]   moving 2 blocks
[  175.424736]   moving 2 blocks
[  175.426501] patching 15 blocks to 15
[  175.447646]   moving 1 blocks
[  175.448451]   moving 2 blocks
[  176.177646] patching 5472 blocks to 5472
[  177.214624] patching 47 blocks to 47
[  177.250536]   moving 3 blocks
[  177.251898]   moving 3 blocks
[  177.253135] patching 2 blocks to 2
[  177.278907]   moving 161 blocks
[  177.288867]   moving 5 blocks
[  177.290254] patching 2 blocks to 2
[  177.308017]   moving 80 blocks
[  177.313455]   moving 4 blocks
[  177.314715] patching 2 blocks to 2
[  177.330446]   moving 66 blocks
[  177.335553]   moving 4 blocks
[  177.336873] patching 2 blocks to 2
[  177.350413]   moving 41 blocks
[  177.355018]   moving 4 blocks
[  177.356646] patching 2 blocks to 2
[  177.367184]   moving 11 blocks
[  177.370225]   moving 3 blocks
[  177.371477] patching 2 blocks to 2
[  177.382909]   moving 17 blocks
[  177.384484]   moving 3 blocks
[  177.385771] patching 2 blocks to 2
[  177.398367]   moving 32 blocks
[  177.402268]   moving 4 blocks
[  177.403618] patching 2 blocks to 2
[  177.413687]   moving 4 blocks
[  177.415429]   moving 3 blocks
[  177.416440] patching 2 blocks to 2
[  177.425710]   moving 1 blocks
[  177.426819]   moving 3 blocks
[  177.428039] patching 2 blocks to 2
[  177.439102]   moving 18 blocks
[  177.442235]   moving 3 blocks
[  177.443605] patching 2 blocks to 2
[  177.453187]   moving 3 blocks
[  177.454551]   moving 3 blocks
[  177.455823] patching 2 blocks to 2
[  177.465177]   moving 2 blocks
[  177.466593]   moving 3 blocks
[  177.467858] patching 2 blocks to 2
[  177.477583]   moving 4 blocks
[  177.478878]   moving 4 blocks
[  177.479871] patching 2 blocks to 2
[  177.489647] patching 6 blocks to 6
[  177.500849]   moving 10 blocks
[  177.503617] patching 5 blocks to 5
[  177.513934]   moving 2 blocks
[  177.515074]   moving 3 blocks
[  177.516348] patching 2 blocks to 2
[  177.526096]   moving 5 blocks
[  177.527556]   moving 3 blocks
[  177.528841] patching 2 blocks to 2
[  177.539427]   moving 16 blocks
[  177.540976]   moving 3 blocks
[  177.542242] patching 2 blocks to 2
[  177.555512]   moving 36 blocks
[  177.560053]   moving 4 blocks
[  177.561740] patching 2 blocks to 2
[  177.572347] patching 12 blocks to 12
[  177.593474]   moving 30 blocks
[  177.597115]   moving 4 blocks
[  177.598734] patching 2 blocks to 2
[  177.608235]   moving 1 blocks
[  177.609340]   moving 3 blocks
[  177.610671] patching 2 blocks to 2
[  177.620276]   moving 1 blocks
[  177.621300]   moving 3 blocks
[  177.622427] patching 2 blocks to 2
[  177.632020]   moving 1 blocks
[  177.633129]   moving 3 blocks
[  177.634495] patching 2 blocks to 2
[  177.645783] patching 18 blocks to 18
[  177.658417]   moving 3 blocks
[  177.659677] patching 2 blocks to 2
[  177.669342]   moving 5 blocks
[  177.670875]   moving 3 blocks
[  177.672202] patching 2 blocks to 2
[  177.682836]   moving 13 blocks
[  177.684721]   moving 3 blocks
[  177.685780] patching 2 blocks to 2
[  177.695183]   moving 2 blocks
[  177.696277]   moving 3 blocks
[  177.697342] patching 2 blocks to 2
[  177.710457]   moving 33 blocks
[  177.714591]   moving 4 blocks
[  177.715687] patching 2 blocks to 2
[  177.727360]   moving 24 blocks
[  177.731705]   moving 4 blocks
[  177.733032] patching 2 blocks to 2
[  177.753289]   moving 104 blocks
[  177.761076]   moving 4 blocks
[  177.762123] patching 2 blocks to 2
[  177.771700]   moving 3 blocks
[  177.772766] patching 2 blocks to 2
[  177.782800]   moving 3 blocks
[  177.784210]   moving 3 blocks
[  177.785241] patching 2 blocks to 2
[  177.803634]   moving 8 blocks
[  177.805683]   moving 11 blocks
[  177.809053] patching 2 blocks to 2
[  177.818473]   moving 2 blocks
[  177.819601]   moving 3 blocks
[  177.820864] patching 2 blocks to 2
[  177.830039]  writing 1 blocks of new data
[  177.830616]  writing 1 blocks of new data
[  177.833846]   moving 24 blocks
[  177.837170]   moving 3 blocks
[  177.838538]   moving 3 blocks
[  177.844631]   moving 46 blocks
[  177.850520]   moving 2 blocks
[  177.852513]   moving 9 blocks
[  177.953332] patching 1024 blocks to 1024
[  178.084054]   moving 15 blocks
[  178.086164] patching 2 blocks to 2
[  178.095964]   moving 3 blocks
[  178.097920] patching 5 blocks to 5
[  178.108545]   moving 1 blocks
[  178.109758] patching 2 blocks to 2
[  178.119484]   moving 3 blocks
[  178.120841]   moving 2 blocks
[  178.121863]   moving 2 blocks
[  178.138619]   moving 135 blocks
[  178.147831]   moving 7 blocks
[  178.149314]   moving 2 blocks
[  178.155899]   moving 52 blocks
[  178.163580]   moving 7 blocks
[  178.165306] patching 2 blocks to 2
[  178.175229]   moving 5 blocks
[  178.176728]   moving 2 blocks
[  178.178756] patching 2 blocks to 2
[  178.188261]   moving 3 blocks
[  178.475136] patching 2444 blocks to 2444
[  178.822323]   moving 3 blocks
[  178.873178] patching 534 blocks to 534
[  178.935426] patching 1 blocks to 1
[  178.945020]   moving 2 blocks
[  178.945760] patching 2 blocks to 2
[  178.954968]   moving 3 blocks
[  178.960452]   moving 54 blocks
[  178.966726]   moving 3 blocks
[  178.967905] patching 3 blocks to 3
[  178.980376]   moving 17 blocks
[  178.988708]   moving 54 blocks
[  178.997480]   moving 23 blocks
[  179.001419]   moving 2 blocks
[  179.002566]   moving 3 blocks
[  179.003566]   moving 1 blocks
[  179.004524]   moving 5 blocks
[  179.005915] patching 2 blocks to 2
[  179.015267]   moving 3 blocks
[  179.016483]   moving 1 blocks
[  179.017320]   moving 1 blocks
[  179.018269] stashing 1 blocks to 58a68be431ebd3bda81ab6c97dd2cbaf5133ebd5
[  179.018281]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/58a68be431ebd3bda81ab6c97dd2cbaf5133ebd5
[  179.035867] stashing 136 overlapping blocks to 6280defd050588f8e6f018ef23b83a09ac7ffd4c
[  179.035897] 119414784 bytes free on /cache (557056 needed)
[  179.035952]  writing 136 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6280defd050588f8e6f018ef23b83a09ac7ffd4c
[  179.048328]   moving 136 blocks
[  179.048952] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6280defd050588f8e6f018ef23b83a09ac7ffd4c
[  179.057531]   moving 2 blocks
[  179.058327]   moving 1 blocks
[  179.059298]   moving 3 blocks
[  179.061650]   moving 13 blocks
[  179.063705]   moving 5 blocks
[  179.065608]   moving 8 blocks
[  179.066887]   moving 3 blocks
[  179.069768]   moving 17 blocks
[  179.073389] stashing 1 blocks to f4ee9c657597f4813bf42fc23af13cf52a1c600a
[  179.073404]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f4ee9c657597f4813bf42fc23af13cf52a1c600a
[  179.085521] stashing 74 overlapping blocks to 265068ccbecfa8a7c74808b1083a97341684ad6c
[  179.085541] 119410688 bytes free on /cache (303104 needed)
[  179.085588]  writing 74 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/265068ccbecfa8a7c74808b1083a97341684ad6c
[  179.095006]   moving 74 blocks
[  179.095363] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/265068ccbecfa8a7c74808b1083a97341684ad6c
[  179.110949] stashing 81 overlapping blocks to 46f66e3f0181f2521839252f6a0acce2109a97be
[  179.110970] 119410688 bytes free on /cache (331776 needed)
[  179.111021]  writing 81 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/46f66e3f0181f2521839252f6a0acce2109a97be
[  179.119864]   moving 81 blocks
[  179.120253] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/46f66e3f0181f2521839252f6a0acce2109a97be
[  179.136754] stashing 75 overlapping blocks to cb30b7dcf1aa7cba7fb158968d03be5d25e87a97
[  179.136774] 119410688 bytes free on /cache (307200 needed)
[  179.136820]  writing 75 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cb30b7dcf1aa7cba7fb158968d03be5d25e87a97
[  179.242467]   moving 75 blocks
[  179.242827] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cb30b7dcf1aa7cba7fb158968d03be5d25e87a97
[  179.276581] stashing 82 overlapping blocks to a35baeaa484e0ff51233ac946a38384e20f43ca3
[  179.276601] 119410688 bytes free on /cache (335872 needed)
[  179.276650]  writing 82 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a35baeaa484e0ff51233ac946a38384e20f43ca3
[  179.284942]   moving 82 blocks
[  179.285334] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a35baeaa484e0ff51233ac946a38384e20f43ca3
[  179.301050] stashing 74 overlapping blocks to 11b8d60e20360909729ae6008491eac34f079102
[  179.301112] 119410688 bytes free on /cache (303104 needed)
[  179.301175]  writing 74 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/11b8d60e20360909729ae6008491eac34f079102
[  179.310377]   moving 74 blocks
[  179.310731] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/11b8d60e20360909729ae6008491eac34f079102
[  179.325995] stashing 81 overlapping blocks to 0d6ee8741fc2c0356bb485446cf693da02c993b3
[  179.326015] 119410688 bytes free on /cache (331776 needed)
[  179.326065]  writing 81 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0d6ee8741fc2c0356bb485446cf693da02c993b3
[  179.335074]   moving 81 blocks
[  179.335467] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0d6ee8741fc2c0356bb485446cf693da02c993b3
[  179.350931] stashing 81 overlapping blocks to e6f68874ed77ff0eae0ff1938c1eebacf8823423
[  179.350950] 119410688 bytes free on /cache (331776 needed)
[  179.350993]  writing 81 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e6f68874ed77ff0eae0ff1938c1eebacf8823423
[  179.360692]   moving 81 blocks
[  179.361126] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e6f68874ed77ff0eae0ff1938c1eebacf8823423
[  179.375298] stashing 75 overlapping blocks to 200737d485af1c818acf095524e48d3b4d350418
[  179.375318] 119410688 bytes free on /cache (307200 needed)
[  179.375366]  writing 75 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/200737d485af1c818acf095524e48d3b4d350418
[  179.385369]   moving 75 blocks
[  179.385735] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/200737d485af1c818acf095524e48d3b4d350418
[  179.401185] stashing 81 overlapping blocks to 043d0522c34c3404911e529b92809713c8d0e0a3
[  179.401204] 119410688 bytes free on /cache (331776 needed)
[  179.401255]  writing 81 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/043d0522c34c3404911e529b92809713c8d0e0a3
[  179.411038]   moving 81 blocks
[  179.411423] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/043d0522c34c3404911e529b92809713c8d0e0a3
[  179.425519] stashing 76 overlapping blocks to dd56720361676486d3c0983d86ed814fa8b9efcf
[  179.425538] 119410688 bytes free on /cache (311296 needed)
[  179.425586]  writing 76 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/dd56720361676486d3c0983d86ed814fa8b9efcf
[  179.435607]   moving 76 blocks
[  179.435986] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/dd56720361676486d3c0983d86ed814fa8b9efcf
[  179.450380] stashing 75 overlapping blocks to 3f2b1b9d08ffc61ef9134026643d59eb4dfb523a
[  179.450401] 119410688 bytes free on /cache (307200 needed)
[  179.450444]  writing 75 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3f2b1b9d08ffc61ef9134026643d59eb4dfb523a
[  179.459800]   moving 75 blocks
[  179.460166] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3f2b1b9d08ffc61ef9134026643d59eb4dfb523a
[  179.474969] stashing 81 overlapping blocks to 69bda86faba9e1b15c1398792ce57a78f48dc0e7
[  179.474986] 119410688 bytes free on /cache (331776 needed)
[  179.475037]  writing 81 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69bda86faba9e1b15c1398792ce57a78f48dc0e7
[  179.483993]   moving 81 blocks
[  179.484424] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69bda86faba9e1b15c1398792ce57a78f48dc0e7
[  179.499114] stashing 75 overlapping blocks to 434874ea89b30356b96dde04bb801703a439d677
[  179.499133] 119410688 bytes free on /cache (307200 needed)
[  179.499180]  writing 75 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/434874ea89b30356b96dde04bb801703a439d677
[  179.508432]   moving 75 blocks
[  179.508798] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/434874ea89b30356b96dde04bb801703a439d677
[  179.523515] stashing 82 overlapping blocks to 0b33ddd28f0db67f4832c6e02a7e475d4488de97
[  179.523548] 119410688 bytes free on /cache (335872 needed)
[  179.523620]  writing 82 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0b33ddd28f0db67f4832c6e02a7e475d4488de97
[  179.532960]   moving 82 blocks
[  179.533359] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0b33ddd28f0db67f4832c6e02a7e475d4488de97
[  179.553812] stashing 75 overlapping blocks to d57ab0cd607b9b6253d865246bbedb3679046ca8
[  179.553832] 119410688 bytes free on /cache (307200 needed)
[  179.553881]  writing 75 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d57ab0cd607b9b6253d865246bbedb3679046ca8
[  179.563545]   moving 75 blocks
[  179.563912] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/d57ab0cd607b9b6253d865246bbedb3679046ca8
[  179.579092] stashing 81 overlapping blocks to 9c6be7a95cac9f23553d8cf7f95523e0cf88ecf1
[  179.579113] 119410688 bytes free on /cache (331776 needed)
[  179.579157]  writing 81 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9c6be7a95cac9f23553d8cf7f95523e0cf88ecf1
[  179.588281]   moving 81 blocks
[  179.588673] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9c6be7a95cac9f23553d8cf7f95523e0cf88ecf1
[  179.603851] stashing 82 overlapping blocks to 8019750c7952c8aef458a732d20e6f2d21597af9
[  179.603872] 119410688 bytes free on /cache (335872 needed)
[  179.603920]  writing 82 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8019750c7952c8aef458a732d20e6f2d21597af9
[  179.613836]   moving 82 blocks
[  179.614265] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8019750c7952c8aef458a732d20e6f2d21597af9
[  179.628539] stashing 75 overlapping blocks to aecacd50d750ffff75ef20368ed1c9966e70dce5
[  179.628559] 119410688 bytes free on /cache (307200 needed)
[  179.628602]  writing 75 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aecacd50d750ffff75ef20368ed1c9966e70dce5
[  179.638727]   moving 75 blocks
[  179.639096] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aecacd50d750ffff75ef20368ed1c9966e70dce5
[  179.654062] stashing 81 overlapping blocks to 9cc7c842ccf89132c4cedc73d56be1db58bfa20c
[  179.654116] 119410688 bytes free on /cache (331776 needed)
[  179.654172]  writing 81 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9cc7c842ccf89132c4cedc73d56be1db58bfa20c
[  179.664449]   moving 81 blocks
[  179.664843] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9cc7c842ccf89132c4cedc73d56be1db58bfa20c
[  179.679854] stashing 75 overlapping blocks to 91eba2993127a04a4347625b3536f8314ceaf5c2
[  179.679874] 119410688 bytes free on /cache (307200 needed)
[  179.679924]  writing 75 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/91eba2993127a04a4347625b3536f8314ceaf5c2
[  179.688072]   moving 75 blocks
[  179.688442] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/91eba2993127a04a4347625b3536f8314ceaf5c2
[  179.701819] stashing 61 overlapping blocks to 1f6b68783dd1855cc527bed3eb8dc56be7adbc58
[  179.701839] 119410688 bytes free on /cache (249856 needed)
[  179.701886]  writing 61 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1f6b68783dd1855cc527bed3eb8dc56be7adbc58
[  179.711000]   moving 61 blocks
[  179.711305] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1f6b68783dd1855cc527bed3eb8dc56be7adbc58
[  179.722485] stashing 61 overlapping blocks to 9ada88b8c179c299d32944ee5339833727d97439
[  179.722504] 119410688 bytes free on /cache (249856 needed)
[  179.722553]  writing 61 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9ada88b8c179c299d32944ee5339833727d97439
[  179.729768]   moving 61 blocks
[  179.730074] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9ada88b8c179c299d32944ee5339833727d97439
[  179.741422] stashing 65 overlapping blocks to 199ff9a557bcdf18d4e91201c3d62dc0a51f3288
[  179.741442] 119410688 bytes free on /cache (266240 needed)
[  179.741490]  writing 65 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/199ff9a557bcdf18d4e91201c3d62dc0a51f3288
[  179.749359]   moving 65 blocks
[  179.749681] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/199ff9a557bcdf18d4e91201c3d62dc0a51f3288
[  179.760560] stashing 61 overlapping blocks to a674b7456cfd694b87acb944d6b7b7addc22eb14
[  179.760580] 119410688 bytes free on /cache (249856 needed)
[  179.760628]  writing 61 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a674b7456cfd694b87acb944d6b7b7addc22eb14
[  179.768023]   moving 61 blocks
[  179.768325] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a674b7456cfd694b87acb944d6b7b7addc22eb14
[  179.776350]   moving 31 blocks
[  179.781310]   moving 4 blocks
[  179.783661]   moving 16 blocks
[  179.785154]   moving 2 blocks
[  179.796037] stashing 99 overlapping blocks to a0aadfb07276665b93ec181db4e870d3c858722b
[  179.796057] 119410688 bytes free on /cache (405504 needed)
[  179.796104]  writing 99 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a0aadfb07276665b93ec181db4e870d3c858722b
[  179.806941]   moving 99 blocks
[  179.807429] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a0aadfb07276665b93ec181db4e870d3c858722b
[  179.823986] stashing 92 overlapping blocks to 01fcd6efcebcc583a236d8ef85e3f19b3681168a
[  179.824009] 119410688 bytes free on /cache (376832 needed)
[  179.824067]  writing 92 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/01fcd6efcebcc583a236d8ef85e3f19b3681168a
[  179.834557]   moving 92 blocks
[  179.835007] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/01fcd6efcebcc583a236d8ef85e3f19b3681168a
[  179.842781]   moving 4 blocks
[  179.844350]   moving 6 blocks
[  179.846014]   moving 5 blocks
[  179.847707]   moving 5 blocks
[  179.849470]   moving 6 blocks
[  179.851052]   moving 4 blocks
[  179.854767]   moving 28 blocks
[  179.860527]   moving 28 blocks
[  179.867231]   moving 28 blocks
[  179.873764]   moving 27 blocks
[  179.877775]   moving 9 blocks
[  179.881842]   moving 9 blocks
[  179.885509]   moving 9 blocks
[  179.887352]   moving 9 blocks
[  179.888981]   moving 4 blocks
[  179.891292]   moving 12 blocks
[  179.894284]   moving 3 blocks
[  179.895371]   moving 2 blocks
[  179.896288]   moving 2 blocks
[  179.898344]   moving 13 blocks
[  179.901819]   moving 12 blocks
[  179.904735]   moving 13 blocks
[  179.906654]   moving 9 blocks
[  179.927284] stashing 174 overlapping blocks to 53ebd6f92cddff7ca6f27546c1ff1637203c26f4
[  179.927303] 119410688 bytes free on /cache (712704 needed)
[  179.927352]  writing 174 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/53ebd6f92cddff7ca6f27546c1ff1637203c26f4
[  179.941268]   moving 174 blocks
[  179.942047] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/53ebd6f92cddff7ca6f27546c1ff1637203c26f4
[  179.953010]   moving 3 blocks
[  179.954174]   moving 4 blocks
[  179.959175]   moving 40 blocks
[  179.968705]   moving 38 blocks
[  179.972914]   moving 2 blocks
[  179.973860]   moving 2 blocks
[  179.974896]   moving 2 blocks
[  179.975995]   moving 3 blocks
[  179.977823]   moving 7 blocks
[  179.979556]   moving 5 blocks
[  179.980795]   moving 3 blocks
[  179.982134]   moving 2 blocks
[  179.984956]   moving 20 blocks
[  179.989642]   moving 20 blocks
[  179.995356]   moving 21 blocks
[  180.000453]   moving 21 blocks
[  180.003727]   moving 2 blocks
[  180.004623]   moving 2 blocks
[  180.005526]   moving 2 blocks
[  180.006677]   moving 4 blocks
[  180.007971]   moving 4 blocks
[  180.009265]   moving 2 blocks
[  180.010202]   moving 2 blocks
[  180.011495]   moving 5 blocks
[  180.015540]   moving 26 blocks
[  180.020981]   moving 26 blocks
[  180.027661]   moving 27 blocks
[  180.033621]   moving 27 blocks
[  180.038205]   moving 4 blocks
[  180.042262]   moving 28 blocks
[  180.046335]   moving 4 blocks
[  180.049140]   moving 14 blocks
[  180.054105]   moving 14 blocks
[  180.058399]   moving 14 blocks
[  180.062102]   moving 14 blocks
[  180.063871]   moving 2 blocks
[  180.065087]   moving 2 blocks
[  180.066093]   moving 2 blocks
[  180.068677]   moving 15 blocks
[  180.072244]   moving 3 blocks
[  180.075822]   moving 25 blocks
[  180.079695]   moving 8 blocks
[  180.083173]   moving 8 blocks
[  180.086379]   moving 8 blocks
[  180.088323]   moving 10 blocks
[  180.089985]   moving 8 blocks
[  180.094021]   moving 9 blocks
[  180.100513]   moving 36 blocks
[  180.104669]   moving 6 blocks
[  180.108724]   moving 20 blocks
[  180.110842]   moving 2 blocks
[  180.113664]   moving 20 blocks
[  180.119129]   moving 21 blocks
[  180.122822]   moving 20 blocks
[  180.127603]   moving 15 blocks
[  180.129903]   moving 5 blocks
[  180.131222]   moving 2 blocks
[  180.132140]   moving 2 blocks
[  180.133810]   moving 10 blocks
[  180.135176]   moving 2 blocks
[  180.136488]   moving 5 blocks
[  180.138101]   moving 2 blocks
[  180.139625]   moving 8 blocks
[  180.143556]   moving 8 blocks
[  180.147231]   moving 8 blocks
[  180.148943]   moving 8 blocks
[  180.152913]   moving 29 blocks
[  180.159478]   moving 28 blocks
[  180.166596]   moving 30 blocks
[  180.170424]   moving 5 blocks
[  180.174597]   moving 29 blocks
[  180.177456]   moving 2 blocks
[  180.178551]   moving 4 blocks
[  180.179885]   moving 5 blocks
[  180.184448]   moving 35 blocks
[  180.191481]   moving 36 blocks
[  180.207991] stashing 124 overlapping blocks to 56e219b9e63c9500bdc3011188a6b41fead31985
[  180.208029] 119410688 bytes free on /cache (507904 needed)
[  180.208083]  writing 124 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/56e219b9e63c9500bdc3011188a6b41fead31985
[  180.219843]   moving 124 blocks
[  180.220396] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/56e219b9e63c9500bdc3011188a6b41fead31985
[  180.231887]   moving 31 blocks
[  180.238715]   moving 30 blocks
[  180.245562]   moving 34 blocks
[  180.254393]   moving 33 blocks
[  180.258296]   moving 2 blocks
[  180.259405]   moving 3 blocks
[  180.273366] stashing 123 overlapping blocks to 9e90f46627daf3d996eb330408bbf7ba27c0491b
[  180.273386] 119410688 bytes free on /cache (503808 needed)
[  180.273431]  writing 123 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9e90f46627daf3d996eb330408bbf7ba27c0491b
[  180.285575]   moving 123 blocks
[  180.286128] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9e90f46627daf3d996eb330408bbf7ba27c0491b
[  180.293918]   moving 6 blocks
[  180.296194]   moving 15 blocks
[  180.298978]   moving 2 blocks
[  180.300017]   moving 6 blocks
[  180.301457]   moving 6 blocks
[  180.302883]   moving 2 blocks
[  180.304310]   moving 6 blocks
[  180.305730]   moving 2 blocks
[  180.307235]   moving 12 blocks
[  180.313299]   moving 26 blocks
[  180.319805]   moving 27 blocks
[  180.322860] stashing 3 blocks to bb206cf3172cbf3b4805863bd5242b1da29466fa
[  180.322880]  writing 3 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bb206cf3172cbf3b4805863bd5242b1da29466fa
[  180.328724] stashing 15 blocks to 0228ab590c1d8de8bcfd993e74ca906e69952e51
[  180.328756]  writing 15 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0228ab590c1d8de8bcfd993e74ca906e69952e51
[  180.334721] stashing 8 blocks to 0acc5615c714ed4f1614be41dcecbac95e1ec0e1
[  180.334745]  writing 8 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0acc5615c714ed4f1614be41dcecbac95e1ec0e1
[  180.421058]   moving 759 blocks
[  180.463111]   moving 90 blocks
[  180.517677]   moving 358 blocks
[  180.544102]   moving 6 blocks
[  180.545427] patching 2 blocks to 2
[  180.555751]   moving 11 blocks
[  180.557362]   moving 3 blocks
[  180.558622] patching 2 blocks to 2
[  180.568503]   moving 6 blocks
[  180.570390]   moving 7 blocks
[  180.572192]   moving 3 blocks
[  180.573212] patching 2 blocks to 2
[  180.583011]   moving 5 blocks
[  180.584137]   moving 2 blocks
[  180.585475]   moving 3 blocks
[  180.586704] patching 2 blocks to 2
[  180.596196]   moving 2 blocks
[  180.597341]   moving 3 blocks
[  180.598815]   moving 3 blocks
[  180.600082] patching 2 blocks to 2
[  180.609603]   moving 3 blocks
[  180.611405]   moving 7 blocks
[  180.612849] patching 2 blocks to 2
[  180.623405] stashing 18 blocks to 9eee8e019963b4cf7884b208373dc55569f12b24
[  180.623433]  writing 18 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9eee8e019963b4cf7884b208373dc55569f12b24
[  180.628810] stashing 9 blocks to ad4acfb47fc7e3d099f6fb8b4fd75207ada4a02d
[  180.628821]  writing 9 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad4acfb47fc7e3d099f6fb8b4fd75207ada4a02d
[  180.632348] stashing 21 blocks to bec1e2f3221530dcb5c3e9437cf926a757887bdb
[  180.632359]  writing 21 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bec1e2f3221530dcb5c3e9437cf926a757887bdb
[  180.661463]   moving 245 blocks
[  180.764177] stashing 837 overlapping blocks to e379f7291edacf4cfff8abb3a159d658205ea7bf
[  180.764211] 119107584 bytes free on /cache (3428352 needed)
[  180.764269]  writing 837 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e379f7291edacf4cfff8abb3a159d658205ea7bf
[  180.804092]   moving 837 blocks
[  180.808087] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e379f7291edacf4cfff8abb3a159d658205ea7bf
[  180.851608]   moving 140 blocks
[  180.863223]   moving 21 blocks
[  180.866837] patching 2 blocks to 2
[  180.913423] stashing 755 blocks to b7d904aad9bcd2f1aa1365df4a4dd337d8a427e1
[  180.913444]  writing 755 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b7d904aad9bcd2f1aa1365df4a4dd337d8a427e1
[  181.069413] stashing 12 blocks to 3cd854c9c55faeeb1fa284e998a95b8b37ca3073
[  181.069443]  writing 12 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3cd854c9c55faeeb1fa284e998a95b8b37ca3073
[  181.171199] stashing 130 blocks to 14258552e5d5a91d4f49b31fc65ecbc6c1dafbe9
[  181.171265]  writing 130 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/14258552e5d5a91d4f49b31fc65ecbc6c1dafbe9
[  181.317182] stashing 23 blocks to 1355fb8d2d241c774ce046cf08ca12aa5902f226
[  181.317212]  writing 23 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1355fb8d2d241c774ce046cf08ca12aa5902f226
[  181.437585] stashing 8 blocks to fd3fdca8271d8b6102272c79ee0c538f9201d3ff
[  181.437646]  writing 8 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fd3fdca8271d8b6102272c79ee0c538f9201d3ff
[  181.442371] stashing 1 blocks to a326225f2c15039f89757464306536e7d86f8c1c
[  181.442429]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a326225f2c15039f89757464306536e7d86f8c1c
[  181.703528]   moving 2586 blocks
[  181.849037]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b7d904aad9bcd2f1aa1365df4a4dd337d8a427e1
[  181.898686]   moving 1106 blocks
[  181.938068] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b7d904aad9bcd2f1aa1365df4a4dd337d8a427e1
[  181.940430]   moving 1 blocks
[  181.941851]   moving 8 blocks
[  181.942881]   moving 1 blocks
[  181.945304]   moving 17 blocks
[  181.948495]   moving 1 blocks
[  181.949346]   moving 1 blocks
[  181.950560]   moving 4 blocks
[  181.951862]   moving 4 blocks
[  181.956496]   moving 35 blocks
[  181.961854]   moving 1 blocks
[  181.962979]   moving 8 blocks
[  181.966705]   moving 11 blocks
[  181.968679]   moving 6 blocks
[  181.970861]   moving 7 blocks
[  181.972829]   moving 5 blocks
[  181.978320]   moving 35 blocks
[  181.983766]   moving 25 blocks
[  181.988257]   moving 9 blocks
[  181.989393]   moving 1 blocks
[  181.992570]   moving 24 blocks
[  181.996955]   moving 10 blocks
[  181.998568] stashing 8 blocks to ce7beb3bf165ef884e9a65e57f477195ab654926
[  181.998580]  writing 8 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce7beb3bf165ef884e9a65e57f477195ab654926
[  182.011158]   moving 69 blocks
[  182.019049]   moving 13 blocks
[  182.021972]   moving 1 blocks
[  182.023076]   moving 4 blocks
[  182.026446]   moving 25 blocks
[  182.030904]   moving 7 blocks
[  182.032404]   moving 6 blocks
[  182.034103]   moving 4 blocks
[  182.043124]   moving 4 blocks
[  182.044711]   moving 12 blocks
[  182.047627]   moving 4 blocks
[  182.048571]   moving 1 blocks
[  182.049868]   moving 6 blocks
[  182.051145]   moving 1 blocks
[  182.053009]   moving 12 blocks
[  182.056784] patching 22 blocks to 22
[  182.071661]   moving 3 blocks
[  182.073794]   moving 6 blocks
[  182.075376]   moving 7 blocks
[  182.076824]   moving 1 blocks
[  182.078242]   moving 11 blocks
[  182.081149]   moving 1 blocks
[  182.082645]   moving 8 blocks
[  182.089119]   moving 57 blocks
[  182.114937]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9eee8e019963b4cf7884b208373dc55569f12b24
[  182.123953] stashing 291 overlapping blocks to 35dfdc4cb14c854030ebf7cc760348c8d6b571f4
[  182.123975] 118362112 bytes free on /cache (1191936 needed)
[  182.124026]  writing 291 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/35dfdc4cb14c854030ebf7cc760348c8d6b571f4
[  182.141960] patching 291 blocks to 291
[  182.163436] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/35dfdc4cb14c854030ebf7cc760348c8d6b571f4
[  182.174992] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9eee8e019963b4cf7884b208373dc55569f12b24
[  182.176227]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bec1e2f3221530dcb5c3e9437cf926a757887bdb
[  182.177334]   moving 21 blocks
[  182.178814] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bec1e2f3221530dcb5c3e9437cf926a757887bdb
[  182.179467]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad4acfb47fc7e3d099f6fb8b4fd75207ada4a02d
[  182.179946]   moving 9 blocks
[  182.182440] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ad4acfb47fc7e3d099f6fb8b4fd75207ada4a02d
[  182.183175]   moving 3 blocks
[  182.184688]   moving 3 blocks
[  182.185685] patching 2 blocks to 2
[  182.196701]   moving 16 blocks
[  182.205014]   moving 3 blocks
[  182.206311] patching 2 blocks to 2
[  182.215806]   moving 3 blocks
[  182.217081] patching 2 blocks to 2
[  182.226981]   moving 6 blocks
[  182.229051]   moving 5 blocks
[  182.230461] patching 2 blocks to 2
[  182.239979]   moving 3 blocks
[  182.241255]   moving 2 blocks
[  182.242630]   moving 3 blocks
[  182.243663] patching 2 blocks to 2
[  182.258318] stashing 116 blocks to 7d1f381f05c01993f7b64aef963ed4c6d211baa3
[  182.258347]  writing 116 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d1f381f05c01993f7b64aef963ed4c6d211baa3
[  182.273906] stashing 71 blocks to c3a30e60a2900f13a3a408b77a9d4931ea4555f7
[  182.273935]  writing 71 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c3a30e60a2900f13a3a408b77a9d4931ea4555f7
[  182.283460] stashing 18 blocks to ea626ae59a8c1a283767ba378a2eee4cf8b11f57
[  182.283482]  writing 18 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ea626ae59a8c1a283767ba378a2eee4cf8b11f57
[  182.287958] stashing 8 blocks to 5e27bd52b755b0f4c7bce6adcac3f722293c5683
[  182.287968]  writing 8 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5e27bd52b755b0f4c7bce6adcac3f722293c5683
[  182.294307] stashing 14 blocks to 2eccccffeb6e6884506a0e757e696cba8fabf68e
[  182.294324]  writing 14 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2eccccffeb6e6884506a0e757e696cba8fabf68e
[  182.300175] stashing 8 blocks to f35fdfd7313a6330395e0a16a7e2ccbcb5bdc92f
[  182.300198]  writing 8 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f35fdfd7313a6330395e0a16a7e2ccbcb5bdc92f
[  182.305006] stashing 10 blocks to 764f6ab930ba089390449f0b090510646437a84f
[  182.305023]  writing 10 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/764f6ab930ba089390449f0b090510646437a84f
[  182.309243] stashing 9 blocks to c3ff40fb3c134d07ea327a3b715ff955023522a0
[  182.309258]  writing 9 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c3ff40fb3c134d07ea327a3b715ff955023522a0
[  182.314898] stashing 21 blocks to 0f5b4d331a12d73260fd0434463a3d9f9b8e5c26
[  182.314914]  writing 21 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0f5b4d331a12d73260fd0434463a3d9f9b8e5c26
[  182.319963] stashing 10 blocks to ce382aa0ee32c0e24b494f891e6f24737eecb28d
[  182.319978]  writing 10 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce382aa0ee32c0e24b494f891e6f24737eecb28d
[  182.326438] stashing 7 blocks to 49ffc34ca4c5129aef2c6d4a8149b3f6467509c0
[  182.326454]  writing 7 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/49ffc34ca4c5129aef2c6d4a8149b3f6467509c0
[  182.336011] stashing 64 blocks to e94974da4bfe30e3387258ec0fb6dec8d4bc4d9d
[  182.336029]  writing 64 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e94974da4bfe30e3387258ec0fb6dec8d4bc4d9d
[  182.348372] stashing 46 blocks to c18720663bc9028acf98322fa5108f980741f3a0
[  182.348390]  writing 46 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c18720663bc9028acf98322fa5108f980741f3a0
[  182.357848] stashing 55 blocks to 80028959f1ec5153873d1ca3223430090811c4b7
[  182.357864]  writing 55 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/80028959f1ec5153873d1ca3223430090811c4b7
[  182.859737] I:current maximum temperature: 36647
[  183.036736] stashing 5940 overlapping blocks to 843149e137bc0460e6f8be938a0176c4ed570589
[  183.036775] 116686848 bytes free on /cache (24330240 needed)
[  183.036824]  writing 5940 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/843149e137bc0460e6f8be938a0176c4ed570589
[  183.361963]   moving 5940 blocks
[  183.389116] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/843149e137bc0460e6f8be938a0176c4ed570589
[  183.564416]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e94974da4bfe30e3387258ec0fb6dec8d4bc4d9d
[  183.567863]   moving 64 blocks
[  183.573917] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e94974da4bfe30e3387258ec0fb6dec8d4bc4d9d
[  183.577038]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/80028959f1ec5153873d1ca3223430090811c4b7
[  183.579960] patching 55 blocks to 55
[  183.598084] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/80028959f1ec5153873d1ca3223430090811c4b7
[  183.600663]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c18720663bc9028acf98322fa5108f980741f3a0
[  183.603015]   moving 46 blocks
[  183.606894] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c18720663bc9028acf98322fa5108f980741f3a0
[  183.608279]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0f5b4d331a12d73260fd0434463a3d9f9b8e5c26
[  183.609375]   moving 21 blocks
[  183.612521] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0f5b4d331a12d73260fd0434463a3d9f9b8e5c26
[  183.613406]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2eccccffeb6e6884506a0e757e696cba8fabf68e
[  183.614154] patching 14 blocks to 14
[  183.625473] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2eccccffeb6e6884506a0e757e696cba8fabf68e
[  183.626244]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/764f6ab930ba089390449f0b090510646437a84f
[  183.626763] patching 10 blocks to 10
[  183.637387] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/764f6ab930ba089390449f0b090510646437a84f
[  183.638171]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce382aa0ee32c0e24b494f891e6f24737eecb28d
[  183.638690]   moving 10 blocks
[  183.641211] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce382aa0ee32c0e24b494f891e6f24737eecb28d
[  183.641821]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c3ff40fb3c134d07ea327a3b715ff955023522a0
[  183.642269]   moving 9 blocks
[  183.644411] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c3ff40fb3c134d07ea327a3b715ff955023522a0
[  183.644959]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5e27bd52b755b0f4c7bce6adcac3f722293c5683
[  183.645391] patching 8 blocks to 8
[  183.655578] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5e27bd52b755b0f4c7bce6adcac3f722293c5683
[  183.656251]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f35fdfd7313a6330395e0a16a7e2ccbcb5bdc92f
[  183.656674]   moving 8 blocks
[  183.657497] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f35fdfd7313a6330395e0a16a7e2ccbcb5bdc92f
[  183.658016]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/49ffc34ca4c5129aef2c6d4a8149b3f6467509c0
[  183.658403]   moving 7 blocks
[  183.659402] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/49ffc34ca4c5129aef2c6d4a8149b3f6467509c0
[  183.882794]   moving 1391 blocks
[  184.237489] stashing 5838 blocks to 84306e2183d6b9d913f22491854d1b721ba69e2f
[  184.237523]  writing 5838 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/84306e2183d6b9d913f22491854d1b721ba69e2f
[  185.676527]   moving 8192 blocks
[  186.576575]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/84306e2183d6b9d913f22491854d1b721ba69e2f
[  186.874916]   moving 5838 blocks
[  187.169275] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/84306e2183d6b9d913f22491854d1b721ba69e2f
[  187.196801]   moving 152 blocks
[  187.250658] patching 2 blocks to 2
[  187.661774]   moving 2861 blocks
[  187.800303]   moving 7 blocks
[  187.801885]   moving 3 blocks
[  187.803431]   moving 7 blocks
[  187.805089] patching 2 blocks to 2
[  187.815145]   moving 3 blocks
[  187.816420] patching 2 blocks to 2
[  187.834593]   moving 67 blocks
[  187.841595] patching 2 blocks to 2
[  187.852899]   moving 19 blocks
[  187.865904]   moving 101 blocks
[  188.316310] stashing 4762 overlapping blocks to af3fa1a0f7b0fa5e5b1591f246a90d5e0e00b841
[  188.316343] 117719040 bytes free on /cache (19505152 needed)
[  188.316402]  writing 4762 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/af3fa1a0f7b0fa5e5b1591f246a90d5e0e00b841
[  188.572971]   moving 4762 blocks
[  188.595873] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/af3fa1a0f7b0fa5e5b1591f246a90d5e0e00b841
[  189.223613]   moving 3 blocks
[  189.227104]   moving 20 blocks
[  189.231418]   moving 2 blocks
[  189.233718]   moving 10 blocks
[  189.236601] patching 2 blocks to 2
[  189.246115]   moving 3 blocks
[  189.247241] patching 2 blocks to 2
[  189.256847]   moving 7 blocks
[  189.258327] patching 2 blocks to 2
[  189.267871] patching 2 blocks to 2
[  189.344846]   moving 713 blocks
[  189.671252] patching 3203 blocks to 3203
[  190.142828]   moving 5 blocks
[  190.143976] patching 2 blocks to 2
[  190.153464] patching 2 blocks to 2
[  190.181938]   moving 168 blocks
[  190.190426]   moving 2 blocks
[  190.191846] stashing 9 blocks to 45d97b46b36446e8466ef48c4bd286308973302c
[  190.191857]  writing 9 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/45d97b46b36446e8466ef48c4bd286308973302c
[  190.197827] stashing 15 blocks to 09fb2e5498df026706649df88114885e1c06fbfe
[  190.197840]  writing 15 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/09fb2e5498df026706649df88114885e1c06fbfe
[  190.208483]   moving 59 blocks
[  190.213036] stashing 1 blocks to cd8f04b548ad0f53bc6e8255ad3b6e5994cc2dc4
[  190.213051]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cd8f04b548ad0f53bc6e8255ad3b6e5994cc2dc4
[  190.218452] stashing 5 blocks to aa3a969a538ea9d2207e649f58753b7c82426f50
[  190.218473]  writing 5 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aa3a969a538ea9d2207e649f58753b7c82426f50
[  190.223010] stashing 2 blocks to 29d21b1d97ae546ea082d68679ba810d66bc57fa
[  190.223027]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/29d21b1d97ae546ea082d68679ba810d66bc57fa
[  190.327102]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/45d97b46b36446e8466ef48c4bd286308973302c
[  190.342461] stashing 512 overlapping blocks to 810d2e905f212a134af53a03a48ba73909df96e4
[  190.342496] 117587968 bytes free on /cache (2097152 needed)
[  190.342547]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/810d2e905f212a134af53a03a48ba73909df96e4
[  190.367692] patching 512 blocks to 512
[  190.431164] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/810d2e905f212a134af53a03a48ba73909df96e4
[  190.475813] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/45d97b46b36446e8466ef48c4bd286308973302c
[  190.476768]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/09fb2e5498df026706649df88114885e1c06fbfe
[  190.477552]   moving 15 blocks
[  190.480542] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/09fb2e5498df026706649df88114885e1c06fbfe
[  190.480876]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cd8f04b548ad0f53bc6e8255ad3b6e5994cc2dc4
[  190.480979]   moving 1 blocks
[  190.481525] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cd8f04b548ad0f53bc6e8255ad3b6e5994cc2dc4
[  190.482105]   moving 5 blocks
[  190.494275]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aa3a969a538ea9d2207e649f58753b7c82426f50
[  190.499126]   moving 156 blocks
[  190.506923] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/aa3a969a538ea9d2207e649f58753b7c82426f50
[  190.507668]   moving 6 blocks
[  190.524450]   moving 155 blocks
[  190.533726] patching 2 blocks to 2
[  190.542978]   moving 2 blocks
[  190.544173]   moving 4 blocks
[  190.545561] patching 2 blocks to 2
[  190.555202]   moving 4 blocks
[  190.557048]   moving 10 blocks
[  190.559901] patching 2 blocks to 2
[  190.585495]   moving 171 blocks
[  190.596463]   moving 7 blocks
[  190.597829] patching 2 blocks to 2
[  191.559965]   moving 8192 blocks
[  191.955914]   moving 418 blocks
[  192.979359]   moving 8192 blocks
[  193.489923]   moving 1418 blocks
[  193.545005] patching 41 blocks to 42
[  193.579688] stashing 286 blocks to db3e3c18f09493605f713479ef3518ed09faebc1
[  193.579718]  writing 286 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db3e3c18f09493605f713479ef3518ed09faebc1
[  193.610370] stashing 278 blocks to 40c1279da049af5a334719435402a87d925431e9
[  193.610400]  writing 278 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/40c1279da049af5a334719435402a87d925431e9
[  193.643936] stashing 306 blocks to f48f25d21132caeae3f63f4887fc733bf4b47eac
[  193.643968]  writing 306 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f48f25d21132caeae3f63f4887fc733bf4b47eac
[  194.266245] stashing 5444 overlapping blocks to 8e2800440b967b644c3c430a520db88c1512a5db
[  194.266279] 114147328 bytes free on /cache (22298624 needed)
[  194.266336]  writing 5444 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8e2800440b967b644c3c430a520db88c1512a5db
[  195.530149]   moving 5444 blocks
[  195.555209] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8e2800440b967b644c3c430a520db88c1512a5db
[  195.798185]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f48f25d21132caeae3f63f4887fc733bf4b47eac
[  195.814249]   moving 306 blocks
[  195.891913] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f48f25d21132caeae3f63f4887fc733bf4b47eac
[  195.912754]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db3e3c18f09493605f713479ef3518ed09faebc1
[  195.927882]   moving 286 blocks
[  196.011336] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/db3e3c18f09493605f713479ef3518ed09faebc1
[  196.028226]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/40c1279da049af5a334719435402a87d925431e9
[  196.042823]   moving 278 blocks
[  196.121934] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/40c1279da049af5a334719435402a87d925431e9
[  196.468941]   moving 2928 blocks
[  196.627871]   moving 553 blocks
[  197.163942] stashing 4606 overlapping blocks to 605e5b3461fc7274b7a7e14bcb793361467f10e1
[  197.163974] 117710848 bytes free on /cache (18866176 needed)
[  197.164034]  writing 4606 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/605e5b3461fc7274b7a7e14bcb793361467f10e1
[  197.381511]   moving 4606 blocks
[  197.402718] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/605e5b3461fc7274b7a7e14bcb793361467f10e1
[  197.873581]   moving 31 blocks
[  197.883113]   moving 64 blocks
[  197.887743] patching 2 blocks to 2
[  198.367353]   moving 3764 blocks
[  198.614342] patching 1043 blocks to 1043
[  199.346238] stashing 5881 overlapping blocks to e4af8909528cb12524f4b952705b1a46c3f0c520
[  199.346270] 117710848 bytes free on /cache (24088576 needed)
[  199.346327]  writing 5881 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e4af8909528cb12524f4b952705b1a46c3f0c520
[  199.673385] patching 5881 blocks to 5881
[  201.076551] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e4af8909528cb12524f4b952705b1a46c3f0c520
[  201.622535]   moving 162 blocks
[  201.893993] stashing 3978 blocks to e30d86ebfbf8bcb5fe34fe415717c39e5bd5e8cc
[  201.894024]  writing 3978 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e30d86ebfbf8bcb5fe34fe415717c39e5bd5e8cc
[  202.869881] I:current maximum temperature: 36500
[  202.957682]   moving 6715 blocks
[  203.714019] stashing 783 blocks to c934eccf1610b7c8feb097009c7dc40bf7a408ac
[  203.714049]  writing 783 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c934eccf1610b7c8feb097009c7dc40bf7a408ac
[  203.784052] stashing 604 blocks to 85b5786075b8970de750126ef74f42afd593ea12
[  203.784107]  writing 604 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/85b5786075b8970de750126ef74f42afd593ea12
[  203.816980] stashing 88 blocks to f92b7c58fd95ba5f20d24806ef164cb15b624f6d
[  203.817011]  writing 88 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f92b7c58fd95ba5f20d24806ef164cb15b624f6d
[  203.826784] stashing 2 blocks to edf968708071cd680e9404a7da22421c5ccc8a63
[  203.826807]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/edf968708071cd680e9404a7da22421c5ccc8a63
[  204.691260]   moving 8192 blocks
[  205.095467] stashing 1188 blocks to c4891dcfeb5e80e6db651b5fb5ba06291022b407
[  205.095500]  writing 1188 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c4891dcfeb5e80e6db651b5fb5ba06291022b407
[  206.024423]   moving 8192 blocks
[  207.430630]   moving 8192 blocks
[  207.813484]   moving 235 blocks
[  207.829267]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f92b7c58fd95ba5f20d24806ef164cb15b624f6d
[  207.833831]   moving 88 blocks
[  207.840873] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f92b7c58fd95ba5f20d24806ef164cb15b624f6d
[  207.841494]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/edf968708071cd680e9404a7da22421c5ccc8a63
[  207.841642] patching 2 blocks to 2
[  207.850609] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/edf968708071cd680e9404a7da22421c5ccc8a63
[  207.851356] patching 2 blocks to 2
[  207.860847]   moving 3 blocks
[  207.861910] patching 2 blocks to 2
[  207.871704]   moving 3 blocks
[  207.877155]   moving 34 blocks
[  207.888153] stashing 57 overlapping blocks to 73b25e8c285c62b8f889029e0136478a9ddd3490
[  207.888178] 90869760 bytes free on /cache (233472 needed)
[  207.888241]  writing 57 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/73b25e8c285c62b8f889029e0136478a9ddd3490
[  207.897319]   moving 57 blocks
[  207.897587] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/73b25e8c285c62b8f889029e0136478a9ddd3490
[  207.901946] patching 2 blocks to 2
[  207.913324]   moving 20 blocks
[  207.917327] patching 2 blocks to 2
[  207.926749]   moving 2 blocks
[  207.927843]   moving 3 blocks
[  207.929112] patching 2 blocks to 2
[  207.939455]   moving 10 blocks
[  207.941703]   moving 2 blocks
[  207.995941]   moving 427 blocks
[  208.042064]   moving 296 blocks
[  208.097326]   moving 436 blocks
[  208.116658]   moving 16 blocks
[  208.120142] patching 3 blocks to 3
[  208.356084]   moving 2123 blocks
[  208.428683]   moving 9 blocks
[  208.431259] patching 2 blocks to 2
[  208.520061]   moving 800 blocks
[  208.725171]   moving 1871 blocks
[  209.150886]   moving 3321 blocks
[  209.704805]   moving 1081 blocks
[  210.201146]   moving 1842 blocks
[  210.926934]   moving 2705 blocks
[  211.288756] stashing 3 blocks to 8bb91e574a9341c8e62f2b7b8987af2903a9595e
[  211.288817]  writing 3 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8bb91e574a9341c8e62f2b7b8987af2903a9595e
[  211.297244]   moving 23 blocks
[  211.300404]   moving 5 blocks
[  211.301914] stashing 1 blocks to c55094a5bb60d6f9e78dab6b9e94f8fe2074d9b1
[  211.301960]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c55094a5bb60d6f9e78dab6b9e94f8fe2074d9b1
[  211.424789]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f4ee9c657597f4813bf42fc23af13cf52a1c600a
[  211.424920]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/58a68be431ebd3bda81ab6c97dd2cbaf5133ebd5
[  211.424981]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8bb91e574a9341c8e62f2b7b8987af2903a9595e
[  211.425074]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3cd854c9c55faeeb1fa284e998a95b8b37ca3073
[  211.425324]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/048b66624c402bc88b8d2d9b883ce0ee0a466586
[  211.425400]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a5b7cda9265e4b7472b369d4cfc4b616de8e6d0f
[  211.440547] stashing 512 overlapping blocks to e2493fcdcbb3dc6e3cce35f68ab25be2d5ba4869
[  211.440581] 90853376 bytes free on /cache (2097152 needed)
[  211.440631]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e2493fcdcbb3dc6e3cce35f68ab25be2d5ba4869
[  211.467046] patching 512 blocks to 512
[  211.520195] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e2493fcdcbb3dc6e3cce35f68ab25be2d5ba4869
[  211.551394] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f4ee9c657597f4813bf42fc23af13cf52a1c600a
[  211.551594] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/58a68be431ebd3bda81ab6c97dd2cbaf5133ebd5
[  211.551747] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8bb91e574a9341c8e62f2b7b8987af2903a9595e
[  211.551904] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3cd854c9c55faeeb1fa284e998a95b8b37ca3073
[  211.552080] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/048b66624c402bc88b8d2d9b883ce0ee0a466586
[  211.552235] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a5b7cda9265e4b7472b369d4cfc4b616de8e6d0f
[  211.607531]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c934eccf1610b7c8feb097009c7dc40bf7a408ac
[  211.656259]   moving 1047 blocks
[  211.694385] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c934eccf1610b7c8feb097009c7dc40bf7a408ac
[  211.878358]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/85b5786075b8970de750126ef74f42afd593ea12
[  211.956819]   moving 2163 blocks
[  212.315332] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/85b5786075b8970de750126ef74f42afd593ea12
[  212.539031]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c4891dcfeb5e80e6db651b5fb5ba06291022b407
[  212.646554]   moving 2747 blocks
[  212.897889] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c4891dcfeb5e80e6db651b5fb5ba06291022b407
[  212.912607]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/14258552e5d5a91d4f49b31fc65ecbc6c1dafbe9
[  212.915470]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bb206cf3172cbf3b4805863bd5242b1da29466fa
[  212.919732] patching 133 blocks to 133
[  212.945881] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/14258552e5d5a91d4f49b31fc65ecbc6c1dafbe9
[  212.946435] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bb206cf3172cbf3b4805863bd5242b1da29466fa
[  212.955310]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d1f381f05c01993f7b64aef963ed4c6d211baa3
[  212.961332]   moving 116 blocks
[  212.969545] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7d1f381f05c01993f7b64aef963ed4c6d211baa3
[  212.977696]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c3a30e60a2900f13a3a408b77a9d4931ea4555f7
[  212.981339]   moving 71 blocks
[  212.988079] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c3a30e60a2900f13a3a408b77a9d4931ea4555f7
[  212.989693]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1355fb8d2d241c774ce046cf08ca12aa5902f226
[  212.991011]   moving 23 blocks
[  212.994611] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1355fb8d2d241c774ce046cf08ca12aa5902f226
[  212.996836]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ea626ae59a8c1a283767ba378a2eee4cf8b11f57
[  212.997837] patching 18 blocks to 19
[  213.014046] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ea626ae59a8c1a283767ba378a2eee4cf8b11f57
[  213.016631]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0228ab590c1d8de8bcfd993e74ca906e69952e51
[  213.017366]   moving 15 blocks
[  213.018963] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0228ab590c1d8de8bcfd993e74ca906e69952e51
[  213.020038]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce7beb3bf165ef884e9a65e57f477195ab654926
[  213.020472]   moving 8 blocks
[  213.023485] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce7beb3bf165ef884e9a65e57f477195ab654926
[  213.024654]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0acc5615c714ed4f1614be41dcecbac95e1ec0e1
[  213.025051]   moving 8 blocks
[  213.027763] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0acc5615c714ed4f1614be41dcecbac95e1ec0e1
[  213.028312]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fd3fdca8271d8b6102272c79ee0c538f9201d3ff
[  213.028705]   moving 8 blocks
[  213.029498] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fd3fdca8271d8b6102272c79ee0c538f9201d3ff
[  213.030022]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/29d21b1d97ae546ea082d68679ba810d66bc57fa
[  213.030150]   moving 2 blocks
[  213.030750] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/29d21b1d97ae546ea082d68679ba810d66bc57fa
[  213.031690]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c55094a5bb60d6f9e78dab6b9e94f8fe2074d9b1
[  213.032001]   moving 9 blocks
[  213.032747] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c55094a5bb60d6f9e78dab6b9e94f8fe2074d9b1
[  213.032969]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a326225f2c15039f89757464306536e7d86f8c1c
[  213.033061]   moving 1 blocks
[  213.033633] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a326225f2c15039f89757464306536e7d86f8c1c
[  213.204615]   moving 1559 blocks
[  213.448436]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e30d86ebfbf8bcb5fe34fe415717c39e5bd5e8cc
[  213.652761]   moving 3978 blocks
[  213.967037] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e30d86ebfbf8bcb5fe34fe415717c39e5bd5e8cc
[  213.977588]   moving 17 blocks
[  214.004682] patching 2 blocks to 2
[  214.049510] patching 93 blocks to 93
[  214.103805]   moving 29 blocks
[  214.131244] patching 2 blocks to 2
[  214.165310]   moving 2 blocks
[  214.187678]   moving 3 blocks
[  214.213232] patching 2 blocks to 2
[  214.280228]   moving 293 blocks
[  214.347864] patching 50 blocks to 50
[  214.692076]   moving 2949 blocks
[  214.810961] patching 187 blocks to 187
[  215.071963]   zeroing 1024 blocks
[  215.178674]   zeroing 1024 blocks
[  215.219790]   zeroing 1024 blocks
[  215.262421]   zeroing 1024 blocks
[  215.326182]   zeroing 1024 blocks
[  215.369184]   zeroing 1024 blocks
[  215.522804]   zeroing 1024 blocks
[  215.645124]   zeroing 1024 blocks
[  215.686762]   zeroing 1024 blocks
[  215.727941]   zeroing 1024 blocks
[  215.768563]   zeroing 1024 blocks
[  215.809304]   zeroing 1024 blocks
[  215.850031]   zeroing 24 blocks
[  215.853770]   zeroing 183548 blocks
[  222.879973] I:current maximum temperature: 38018
[  223.123034] wrote 523919 blocks; expected 523919
[  223.123094] stashed 73224 blocks
[  223.123125] max alloc needed was 33554432
[  223.123211] deleting stash 2bdde8504898ccfcd2c59f20bb8c9c25f73bb524
[  223.551881] system update is complete...
[  223.551983] Patching boot image...
[  223.893485] patch EMMC:/dev/block/bootdevice/by-name/boot:16777216:7fe68c444188a7d1cd8f20244f1da7a6dc4aa81d:16777216:7668dce8be3405ba6d090c62cce564563f7acecc: partition read matched size 16777216 SHA-1 7fe68c444188a7d1cd8f20244f1da7a6dc4aa81d
[  223.897570] 119439360 bytes free on /cache (16777216 needed)
[  223.897579] Using cache to copy content
[  227.463136] now 7668dce8
[  227.862647]   caches dropped
[  229.043957] verification read succeeded (attempt 1)
[  229.058220] system update is complete...
[  229.058239] updating rpm ...
[  229.062665] boot prop value is 7824900.sdhci 
[  229.062676] Storage type is emmc 
[  229.063367] Applying /tmp/rpm.mbn of size 262144 onto rpm of size 262144
[  229.084533] wrote rpm partition
[  229.086743] updating tz ...
[  229.105103] boot prop value is 7824900.sdhci 
[  229.105122] Storage type is emmc 
[  229.105212] Applying /tmp/tz.mbn of size 1835008 onto tz of size 1835008
[  229.208832] wrote tz partition
[  229.209452] updating devcfg ...
[  229.210512] boot prop value is 7824900.sdhci 
[  229.210533] Storage type is emmc 
[  229.210635] Applying /tmp/devcfg.mbn of size 65536 onto devcfg of size 65536
[  229.215644] wrote devcfg partition
[  229.215976] updating cmnlib ...
[  229.219898] boot prop value is 7824900.sdhci 
[  229.219925] Storage type is emmc 
[  229.220028] Applying /tmp/cmnlib.mbn of size 257152 onto cmnlib of size 262144
[  229.235657] wrote cmnlib partition
[  229.235862] updating cmnlib64 ...
[  229.240027] boot prop value is 7824900.sdhci 
[  229.240051] Storage type is emmc 
[  229.240151] Applying /tmp/cmnlib64.mbn of size 257152 onto cmnlib64 of size 262144
[  229.256386] wrote cmnlib64 partition
[  229.256599] updating keymaster ...
[  229.260280] boot prop value is 7824900.sdhci 
[  229.260298] Storage type is emmc 
[  229.260409] Applying /tmp/keymaster.mbn of size 257152 onto keymaster of size 262144
[  229.278165] wrote keymaster partition
[  229.278367] updating prov ...
[  229.280899] boot prop value is 7824900.sdhci 
[  229.280915] Storage type is emmc 
[  229.281028] Applying /tmp/prov.mbn of size 191616 onto prov of size 196608
[  229.294502] wrote prov partition
[  229.294695] Removing unneeded files from modem...
[  229.297999] Patching modem files...
[  229.310920] patch /modem/image/cmnlib.b00: now 757403f9
[  229.326402] patch /modem/image/cmnlib.b01: now 30e1296b
[  229.361343] patch /modem/image/cmnlib.b02: now cb2643ba
[  229.375174] patch /modem/image/cmnlib.b03: now ac6432f6
[  229.391932] patch /modem/image/cmnlib.b05: now ac00edab
[  229.406584] patch /modem/image/cmnlib.mdt: now cc236b92
[  229.422317] patch /modem/image/cmnlib64.b00: now 0e384454
[  229.438661] patch /modem/image/cmnlib64.b01: now 30ac4fd9
[  229.471546] patch /modem/image/cmnlib64.b02: now d1d5bbec
[  229.485604] patch /modem/image/cmnlib64.b03: now 0e266084
[  229.502397] patch /modem/image/cmnlib64.b04: now f321753e
[  229.517282] patch /modem/image/cmnlib64.b05: now dfa96a69
[  229.533891] patch /modem/image/cmnlib64.mdt: now 1a27e8de
[  229.549942] patch /modem/image/fpctzappfingerprint.b01: now c2aa21e4
[  229.590577] patch /modem/image/fpctzappfingerprint.b02: now 47947a71
[  229.605407] patch /modem/image/fpctzappfingerprint.mdt: now 3fb99a3e
[  229.633825] patch /modem/image/mba.mbn: now 4fb1c944
[  229.649518] patch /modem/image/modem.b00: now 310b271a
[  229.665789] patch /modem/image/modem.b01: now 50f63356
[  229.679833] patch /modem/image/modem.b02: now 3da6ed5b
[  229.716973] patch /modem/image/modem.b05: now 4332eab8
[  229.763495] patch /modem/image/modem.b06: now eab7ef99
[  229.795355] patch /modem/image/modem.b07: now 2e0592a9
[  229.884163] patch /modem/image/modem.b08: now dd8da055
[  229.998713] patch /modem/image/modem.b09: now 394a7bd8
[  231.610072] patch /modem/image/modem.b10: now 0d4f78c5
[  231.944815] patch /modem/image/modem.b12: now d45b9898
[  232.093384] patch /modem/image/modem.b13: now 55b14065
[  232.120846] patch /modem/image/modem.b16: now e66b15a4
[  235.697907] patch /modem/image/modem.b18: now e24d2958
[  235.730632] patch /modem/image/modem.b19: now e5d99c98
[  235.868898] patch /modem/image/modem.b20: now ad056f6c
[  235.886735] patch /modem/image/modem.mdt: now f249e590
[  235.900556] patch /modem/image/wcnss.b00: now 0fdaddbd
[  235.917471] patch /modem/image/wcnss.b01: now b1188cc0
[  235.934777] patch /modem/image/wcnss.b02: now 14998f60
[  236.177310] patch /modem/image/wcnss.b06: now 68967ef3
[  236.201985] patch /modem/image/wcnss.b10: now 92b3d7b6
[  236.226380] patch /modem/image/wcnss.b12: now 881a88e2
[  236.242016] patch /modem/image/wcnss.mdt: now 7fbdd093
[  236.257558] patch /modem/image/widevine.b00: now 52129636
[  236.273790] patch /modem/image/widevine.b01: now 4edfd978
[  236.314324] patch /modem/image/widevine.b02: now f7dfd95b
[  236.332464] patch /modem/image/widevine.b03: now b428618c
[  236.348043] patch /modem/image/widevine.b04: now edfc08e8
[  236.363111] patch /modem/image/widevine.b05: now 0b82109e
[  236.377271] patch /modem/image/widevine.b06: now df128445
[  236.391596] patch /modem/image/widevine.mdt: now 133d6d18
[  236.398810] Unpacking new files in modem ...
[  236.411881] Extracted file "/modem/image/modem.b17"
[  236.417514] Extracted file "/modem/image/cmnlib.b04"
[  236.485762] Extracted file "/modem/image/qdsp6m.qdb"
[  236.485816] Extracted 3 file(s)
[  236.485870] Symlinks and permissions in modem ...
[  236.491193] Updating dsp ...
[  236.491219] Patching dsp image unconditionally...
[  236.491225] performing update
[  236.491630] blockimg version is 4
[  236.491641] maximum stash entries 0
[  236.492016] creating stash /cache/recovery/677d9ad4e05073580617d8de2a8bf397effe0443/
[  236.492762] 119435264 bytes free on /cache (0 needed)
[  236.492808]  writing 1024 blocks of new data
[  236.685915]  writing 1024 blocks of new data
[  236.832188]  writing 36 blocks of new data
[  236.841170]   zeroing 1024 blocks
[  236.896150]   zeroing 988 blocks
[  236.948246] wrote 4096 blocks; expected 4096
[  236.948294] stashed 0 blocks
[  236.948301] max alloc needed was 4096
[  236.948307] deleting stash 677d9ad4e05073580617d8de2a8bf397effe0443
[  236.957514] updating fsg ...
[  236.985406] boot prop value is 7824900.sdhci 
[  236.985435] Storage type is emmc 
[  236.985550] Applying /tmp/fsg.mbn of size 3096576 onto fsg of size 8388608
[  237.149310] wrote fsg partition
[  237.150158] Erasing modemst1 ...
[  237.150389] unknown volume for path [/cache]
[  237.150408] Unable to mount /cache!
[  237.150902] blk: partition "" size 1835008 not a multiple of io_buffer_size 524288
[  237.150978] blk: partition "" size 1835008 not a multiple of io_buffer_size 524288
[  237.151116] blk: partition "" size 21073920 not a multiple of io_buffer_size 524288
[  237.151224] blk: partition "" size 56883133440 not a multiple of io_buffer_size 524288
[  237.153597] Trying to access file /sys/block/mmcblk0/mmcblk0p27/start
[  237.153837] /dev/block/bootdevice/by-name/modemst1 starts at: 135135232
[  237.153868] Formatting partition /dev/block/bootdevice/by-name/modemst1 of length 2097152 starting at 135135232
[  237.174210] Format complete for partition 
[  237.175786] Erasing modemst2 ...
[  237.175846] unknown volume for path [/cache]
[  237.175855] Unable to mount /cache!
[  237.175948] Trying to access file /sys/block/mmcblk0/mmcblk0p28/start
[  237.176109] /dev/block/bootdevice/by-name/modemst2 starts at: 137232384
[  237.176139] Formatting partition /dev/block/bootdevice/by-name/modemst2 of length 2097152 starting at 137232384
[  237.176153] Aligning offset to 4194304 boundary by moving 1179648 bytes
[  237.201046] Format complete for partition 
[  237.202625] Patching oem image...
[  237.202671] 119439360 bytes free on /cache (33554432 needed)
[  237.202698] Patching oem image after verification.
[  237.202707] performing update
[  237.203140] blockimg version is 4
[  237.203150] maximum stash entries 0
[  237.203201] creating stash /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/
[  237.203397] 119435264 bytes free on /cache (33554432 needed)
[  237.203453]   zeroing 150600 blocks
[  242.232464] stashing 8192 overlapping blocks to 6688a7cc1926be7ec0489882794e37c7793ccf12
[  242.232501] 119435264 bytes free on /cache (33554432 needed)
[  242.232561]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/6688a7cc1926be7ec0489882794e37c7793ccf12
[  242.618344] patching 8192 blocks to 8192
[  242.891010] I:current maximum temperature: 37088
[  243.020349] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/6688a7cc1926be7ec0489882794e37c7793ccf12
[  244.159280] stashing 8192 overlapping blocks to 13d55ec3574b06eda3a367f792381c885d57e9f7
[  244.159312] 119435264 bytes free on /cache (33554432 needed)
[  244.159376]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/13d55ec3574b06eda3a367f792381c885d57e9f7
[  244.554168] patching 8192 blocks to 8192
[  244.946607] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/13d55ec3574b06eda3a367f792381c885d57e9f7
[  246.111400] stashing 8192 overlapping blocks to 6bc36b202a382f37c9218d4f2f0252145af742b8
[  246.111432] 119435264 bytes free on /cache (33554432 needed)
[  246.111494]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/6bc36b202a382f37c9218d4f2f0252145af742b8
[  246.490910] patching 8192 blocks to 8192
[  246.885970] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/6bc36b202a382f37c9218d4f2f0252145af742b8
[  247.206860]   zeroing 1024 blocks
[  247.246996]   zeroing 1024 blocks
[  247.286884]   zeroing 1024 blocks
[  247.326999]   zeroing 1024 blocks
[  247.382198]   zeroing 1024 blocks
[  247.426634]   zeroing 512 blocks
[  247.448996] wrote 30208 blocks; expected 30208
[  247.449044] stashed 24576 blocks
[  247.449051] max alloc needed was 33554432
[  247.449057] deleting stash d42c8ee5b06118b9bb530644a3c2f4943bb98d8f
[  247.454255] Updating gpt_main0.bin...
[  247.455600] boot prop value is 7824900.sdhci 
[  247.455644] Storage type is emmc 
[  247.455655]
[  247.455663]  LBA SIZE for this device is : 512 bytes
[  247.456418] Changed ending_lba to 122142686
[  247.456430] growth partition is userdata
[  247.456484] Partition size(in blocks)  = Partition(/dev/block/mmcblk0) size : 62537072640 bytes 
[  247.456514] 122142720 for the partition /dev/block/mmcblk0
[  247.456556] Partition(/dev/block/mmcblk0) size : 62537072640 bytes 
[  247.463408] Writing header for backup partition 
[  247.468875] GPT upgrade successful
[  247.468970] Entered IsHardwareSecuredFn
[  247.469052] It is a Secure Hardware
[  247.469077] Leaving IsHardwareSecuredFn: t
[  247.469115] Erasing DDR..
[  247.469177] unknown volume for path [/cache]
[  247.469186] Unable to mount /cache!
[  247.469251] Trying to access file /sys/block/mmcblk0/mmcblk0p23/start
[  247.469391] /dev/block/bootdevice/by-name/DDR starts at: 133955584
[  247.469421] Formatting partition /dev/block/bootdevice/by-name/DDR of length 32768 starting at 133955584
[  247.470236] Format complete for partition 
[  247.470866] Erased DDR successfully..
[  247.755355]
[  247.787044] W:failed to read uncrypt status: No such file or directory
[  247.797085] I:current maximum temperature: 36794
[  247.797203] I:/cache/OTA_Package_OPSS28.65-36-9.zip
[  247.797210] I:1
[  247.797219] I:time_total: 246
[  247.797224] I:retry: 0
[  247.797230] I:target_build: 431cb
[  247.797236] I:source_build: 03c05
[  247.797244] I:bytes_written_system: 2145972224
[  247.797250] I:bytes_stashed_system: 299925504
[  247.797256] I:bytes_written_dsp: 16777216
[  247.797262] I:bytes_stashed_dsp: 0
[  247.797267] I:bytes_written_oem: 123731968
[  247.797273] I:bytes_stashed_oem: 100663296
[  247.797278] I:temperature_start: 35080
[  247.797286] I:temperature_end: 36794
[  247.797291] I:temperature_max: 38018
[  247.797297] I:
[  247.797302] I:Saving locale "en-IN"
