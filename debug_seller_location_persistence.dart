/// Debug script for seller location persistence issue
/// 
/// This script directly tests the database operations to identify
/// where the seller location data persistence is failing.

import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  print('🔍 DEBUG - Seller Location Persistence Investigation');
  print('=' * 60);

  // Initialize Supabase (replace with actual credentials)
  await Supabase.initialize(
    url: 'https://oaynfzqjielnsipttzbs.supabase.co',
    anonKey: 'your-anon-key', // Replace with actual anon key
  );

  final supabase = Supabase.instance.client;

  // Test data
  const testSellerId = 'test-seller-id'; // Replace with actual seller ID
  final testLocationData = {
    'latitude': 12.9716,
    'longitude': 77.5946,
    'delivery_radius_km': 10,
    'location_verified': true,
    'location_updated_at': DateTime.now().toIso8601String(),
    'business_address': 'Test Address, Bangalore, Karnataka, India',
  };

  print('\n📊 Step 1: Check Sellers Table Schema');
  try {
    // Get table schema information
    final schemaResult = await supabase
        .from('sellers')
        .select('*')
        .limit(1);
    
    if (schemaResult.isNotEmpty) {
      final firstSeller = schemaResult.first;
      print('✅ Sellers table accessible');
      print('📋 Available columns: ${firstSeller.keys.toList()}');
      
      // Check if location columns exist
      final locationColumns = ['latitude', 'longitude', 'delivery_radius_km', 'location_verified', 'location_updated_at'];
      final missingColumns = locationColumns.where((col) => !firstSeller.containsKey(col)).toList();
      
      if (missingColumns.isEmpty) {
        print('✅ All location columns exist in sellers table');
      } else {
        print('❌ Missing location columns: $missingColumns');
      }
    } else {
      print('⚠️ No sellers found in table');
    }
  } catch (e) {
    print('❌ Error accessing sellers table: $e');
  }

  print('\n🔍 Step 2: Test Direct Database Update');
  try {
    print('Attempting to update seller with ID: $testSellerId');
    print('Update data: $testLocationData');

    final updateResult = await supabase
        .from('sellers')
        .update(testLocationData)
        .eq('id', testSellerId)
        .select()
        .single();

    print('✅ Direct database update successful');
    print('📄 Updated seller data: $updateResult');
    
    // Verify the update by reading back
    final verifyResult = await supabase
        .from('sellers')
        .select('id, latitude, longitude, delivery_radius_km, location_verified, location_updated_at, business_address')
        .eq('id', testSellerId)
        .single();
    
    print('✅ Verification read successful');
    print('📄 Current seller location data: $verifyResult');
    
  } catch (e) {
    print('❌ Direct database update failed: $e');
    
    // Check if it's a permission issue
    if (e.toString().contains('permission') || e.toString().contains('RLS')) {
      print('🔒 This appears to be a Row Level Security (RLS) policy issue');
      print('💡 Check if the seller can update their own location fields');
    }
    
    // Check if it's a column issue
    if (e.toString().contains('column') || e.toString().contains('does not exist')) {
      print('📋 This appears to be a database schema issue');
      print('💡 Check if the location columns exist in the sellers table');
    }
  }

  print('\n🔍 Step 3: Test Individual Field Updates');
  final individualFields = {
    'delivery_radius_km': 15,
    'latitude': 12.9352,
    'longitude': 77.6245,
    'location_verified': false,
  };

  for (final entry in individualFields.entries) {
    try {
      await supabase
          .from('sellers')
          .update({entry.key: entry.value})
          .eq('id', testSellerId);
      
      print('✅ Successfully updated ${entry.key}: ${entry.value}');
    } catch (e) {
      print('❌ Failed to update ${entry.key}: $e');
    }
  }

  print('\n🔍 Step 4: Check Current Authentication');
  try {
    final user = supabase.auth.currentUser;
    if (user != null) {
      print('✅ User authenticated: ${user.id}');
      print('📧 User email: ${user.email}');
      
      // Check if this user is associated with the test seller
      final sellerCheck = await supabase
          .from('sellers')
          .select('id, seller_name, user_id')
          .eq('user_id', user.id);
      
      if (sellerCheck.isNotEmpty) {
        print('✅ User is associated with seller(s): ${sellerCheck.length}');
        for (final seller in sellerCheck) {
          print('   Seller: ${seller['seller_name']} (ID: ${seller['id']})');
        }
      } else {
        print('⚠️ User is not associated with any sellers');
      }
    } else {
      print('❌ No user authenticated');
      print('💡 This could be why updates are failing');
    }
  } catch (e) {
    print('❌ Error checking authentication: $e');
  }

  print('\n🔍 Step 5: Test RLS Policies');
  try {
    // Try to read seller data (should work if RLS allows)
    final readTest = await supabase
        .from('sellers')
        .select('id, seller_name, latitude, longitude')
        .eq('id', testSellerId)
        .maybeSingle();
    
    if (readTest != null) {
      print('✅ Can read seller data - RLS read policy working');
      print('📄 Seller data: $readTest');
    } else {
      print('❌ Cannot read seller data - RLS read policy blocking');
    }
  } catch (e) {
    print('❌ Error testing RLS read policy: $e');
  }

  print('\n' + '=' * 60);
  print('🎯 INVESTIGATION COMPLETE');
  print('');
  print('Next Steps Based on Results:');
  print('1. If schema issues: Add missing columns to sellers table');
  print('2. If RLS issues: Update policies to allow seller location updates');
  print('3. If auth issues: Ensure proper user authentication in app');
  print('4. If successful: The issue is in the Flutter app logic');
}

/// Helper function to test specific seller ID
Future<void> testWithSpecificSeller(String sellerId) async {
  final supabase = Supabase.instance.client;
  
  print('\n🧪 Testing with specific seller ID: $sellerId');
  
  try {
    // First, check if seller exists
    final seller = await supabase
        .from('sellers')
        .select('*')
        .eq('id', sellerId)
        .maybeSingle();
    
    if (seller != null) {
      print('✅ Seller found: ${seller['seller_name']}');
      print('📍 Current location data:');
      print('   Latitude: ${seller['latitude']}');
      print('   Longitude: ${seller['longitude']}');
      print('   Delivery radius: ${seller['delivery_radius_km']}');
      print('   Location verified: ${seller['location_verified']}');
      print('   Last updated: ${seller['location_updated_at']}');
    } else {
      print('❌ Seller not found with ID: $sellerId');
    }
  } catch (e) {
    print('❌ Error testing specific seller: $e');
  }
}
