/// Apply Seller RLS Policy Fix
/// 
/// This script applies the critical RLS policy fix to allow sellers
/// to update their own location data in the Supabase database.
/// 
/// CRITICAL: This must be run to fix the seller location persistence bug

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

void main() async {
  print('🔧 APPLYING SELLER RLS POLICY FIX');
  print('=' * 50);

  // Supabase project configuration
  const projectId = 'oaynfzqjielnsipttzbs';
  const supabaseUrl = 'https://$projectId.supabase.co';
  
  // You'll need to get these from Supabase dashboard
  const serviceRoleKey = 'YOUR_SERVICE_ROLE_KEY'; // Replace with actual key
  const managementApiKey = 'YOUR_MANAGEMENT_API_KEY'; // Replace with actual key

  print('📊 Project: $projectId');
  print('🌐 URL: $supabaseUrl');

  // SQL to create the missing RLS policies
  const rlsPolicySQL = '''
-- Enable RLS on sellers table (if not already enabled)
ALTER TABLE sellers ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Sellers can view their own profile" ON sellers;
DROP POLICY IF EXISTS "Sellers can update their own profile" ON sellers;
DROP POLICY IF EXISTS "Allow seller registration" ON sellers;
DROP POLICY IF EXISTS "Public can view approved sellers" ON sellers;

-- Policy 1: Allow seller registration (INSERT)
CREATE POLICY "Allow seller registration" ON sellers
FOR INSERT WITH CHECK (true);

-- Policy 2: Sellers can view their own profile (SELECT)
CREATE POLICY "Sellers can view their own profile" ON sellers
FOR SELECT USING (user_id = auth.uid());

-- Policy 3: CRITICAL - Sellers can update their own profile (UPDATE)
CREATE POLICY "Sellers can update their own profile" ON sellers
FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());

-- Policy 4: Public can view approved sellers (for customer browsing)
CREATE POLICY "Public can view approved sellers" ON sellers
FOR SELECT USING (approval_status = 'approved');

-- Policy 5: Service role has full access (for admin operations)
CREATE POLICY "Service role full access to sellers" ON sellers
FOR ALL TO service_role USING (true);
''';

  print('\n🔍 Step 1: Test Database Connection');
  try {
    final testResponse = await http.get(
      Uri.parse('$supabaseUrl/rest/v1/sellers?select=id&limit=1'),
      headers: {
        'apikey': serviceRoleKey,
        'Authorization': 'Bearer $serviceRoleKey',
        'Content-Type': 'application/json',
      },
    );

    if (testResponse.statusCode == 200) {
      print('✅ Database connection successful');
    } else {
      print('❌ Database connection failed: ${testResponse.statusCode}');
      print('Response: ${testResponse.body}');
      return;
    }
  } catch (e) {
    print('❌ Database connection error: $e');
    return;
  }

  print('\n🔧 Step 2: Apply RLS Policy Fix');
  try {
    // Use Supabase Management API to execute SQL
    final sqlResponse = await http.post(
      Uri.parse('https://api.supabase.com/v1/projects/$projectId/database/query'),
      headers: {
        'Authorization': 'Bearer $managementApiKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'query': rlsPolicySQL,
      }),
    );

    if (sqlResponse.statusCode == 200) {
      print('✅ RLS policies applied successfully');
      final responseData = jsonDecode(sqlResponse.body);
      print('📄 Response: $responseData');
    } else {
      print('❌ Failed to apply RLS policies: ${sqlResponse.statusCode}');
      print('Response: ${sqlResponse.body}');
    }
  } catch (e) {
    print('❌ Error applying RLS policies: $e');
  }

  print('\n🧪 Step 3: Test Seller Update Permission');
  try {
    // Test if a seller can now update their location
    // This would need to be done with actual seller authentication
    print('⚠️ Manual testing required:');
    print('1. Login as a seller in the app');
    print('2. Try to update location in seller profile');
    print('3. Verify location data persists after navigation');
  } catch (e) {
    print('❌ Error testing permissions: $e');
  }

  print('\n🔍 Step 4: Verify Policies Created');
  try {
    const verifySQL = '''
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'sellers' 
ORDER BY policyname;
''';

    final verifyResponse = await http.post(
      Uri.parse('https://api.supabase.com/v1/projects/$projectId/database/query'),
      headers: {
        'Authorization': 'Bearer $managementApiKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'query': verifySQL,
      }),
    );

    if (verifyResponse.statusCode == 200) {
      final responseData = jsonDecode(verifyResponse.body);
      print('✅ Policy verification successful');
      print('📋 Current sellers table policies:');
      
      if (responseData['result'] != null && responseData['result'].isNotEmpty) {
        for (final policy in responseData['result']) {
          print('   - ${policy['policyname']}: ${policy['cmd']}');
        }
      } else {
        print('   No policies found (this might indicate an issue)');
      }
    } else {
      print('❌ Policy verification failed: ${verifyResponse.statusCode}');
    }
  } catch (e) {
    print('❌ Error verifying policies: $e');
  }

  print('\n' + '=' * 50);
  print('🎯 RLS POLICY FIX COMPLETE');
  print('');
  print('Next Steps:');
  print('1. Test seller location updates in the app');
  print('2. Verify location data persists in database');
  print('3. Enable feature flag: kEnableSellerLocationDeliveryFee = true');
  print('4. Test delivery fee calculation with actual seller coordinates');
}

/// Alternative method using direct SQL execution
/// Use this if the Management API method doesn't work
void applyRLSFixDirectly() async {
  print('\n🔧 ALTERNATIVE: Direct SQL Execution Method');
  print('If the Management API method fails, you can:');
  print('');
  print('1. Open Supabase Dashboard → SQL Editor');
  print('2. Copy and paste the following SQL:');
  print('');
  print('-- Enable RLS and create policies for sellers table');
  print('ALTER TABLE sellers ENABLE ROW LEVEL SECURITY;');
  print('');
  print('-- Drop existing policies to avoid conflicts');
  print('DROP POLICY IF EXISTS "Sellers can update their own profile" ON sellers;');
  print('');
  print('-- Create the critical update policy');
  print('CREATE POLICY "Sellers can update their own profile" ON sellers');
  print('FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid());');
  print('');
  print('3. Execute the SQL');
  print('4. Test seller location updates in the app');
}

/// Test function to verify the fix works
Future<void> testSellerLocationUpdate() async {
  print('\n🧪 TESTING SELLER LOCATION UPDATE');
  
  // This would need to be integrated into the actual app for testing
  print('Manual Test Steps:');
  print('1. Login as a seller in the Flutter app');
  print('2. Go to Seller Profile → Business Location section');
  print('3. Use GPS capture or pin drop to set location');
  print('4. Save the profile');
  print('5. Navigate away and return to profile');
  print('6. Verify location data is still visible');
  print('7. Check Supabase database directly to confirm data persistence');
}
