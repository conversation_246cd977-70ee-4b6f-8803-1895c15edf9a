[    0.000155] __bionic_open_tzdata: couldn't find any tzdata when looking for GMT!
[    0.000213] Starting recovery (pid 409) on Sun Feb 22 07:14:11 1970
[    0.000358] recovery filesystem table
[    0.000366] =========================
[    0.000373]   0 /system ext4 /dev/block/bootdevice/by-name/system 0
[    0.000379]   1 /cache ext4 /dev/block/bootdevice/by-name/cache 0
[    0.000384]   2 /data ext4 /dev/block/bootdevice/by-name/userdata -16384
[    0.000391]   3 /sdcard vfat /dev/block/mmcblk1p1 0
[    0.000397]   4 /boot emmc /dev/block/bootdevice/by-name/boot 0
[    0.000402]   5 /recovery emmc /dev/block/bootdevice/by-name/recovery 0
[    0.000407]   6 /misc emmc /dev/block/bootdevice/by-name/misc 0
[    0.000414]   7 /oem ext4 /dev/block/bootdevice/by-name/oem 0
[    0.000420]   8 /modem ext4 /dev/block/bootdevice/by-name/modem 0
[    0.000426]   9 /dsp ext4 /dev/block/bootdevice/by-name/dsp 0
[    0.000432]   10 /tmp ramdisk ramdisk 0
[    0.000443]
[    0.001200] I:Boot command: boot-recovery
[    0.001210] I:Got arguments from boot message
[    0.002082] locale is [en_US]
[    0.002089] stage is []
[    0.002098] reason is [(null)]
[    0.003814] cannot find/open a drm device: No such file or directory
[    0.004042] fb0 reports (possibly inaccurate):
[    0.004049]   vi.bits_per_pixel = 32
[    0.004054]   vi.red.offset   =   0   .length =   8
[    0.004060]   vi.green.offset =   8   .length =   8
[    0.004065]   vi.blue.offset  =  16   .length =   8
[    0.017463] framebuffer: 0 (1080 x 1920)
[    0.058847]           erasing_text: en_US (133 x 57 @ 1740)
[    0.064532]        no_command_text: en_US (248 x 57 @ 1740)
[    0.069088]             error_text: en_US (94 x 57 @ 1740)
[    0.989649]        installing_text: en_US (453 x 57 @ 1740)
[    1.020651] Command: "recovery" "--update_package=@/cache/recovery/block.map" "--locale=en_US"
[    1.124190] Filesystem            1K-blocks    Used Available Use% Mounted on
[    1.124224] rootfs                  1735912    7976   1727936   1% /
[    1.124230] tmpfs                   1843412     408   1843004   1% /dev
[    1.124236] tmpfs                   1843412       4   1843408   1% /tmp
[    1.124275] /dev/block/mmcblk0p53   3999372 3052428    946944  77% /system
[    1.145983] 0	/cache
[    1.148139]
[    1.148685] persist.mot.gps.conf.from.sim=true
[    1.148692] persist.mot.gps.smart_battery=1
[    1.148958] ro.am.reschedule_service=true
[    1.148999] ro.sys.fw.bservice_age=5000
[    1.149009] ro.sys.fw.bservice_limit=5
[    1.149017] ro.sys.fw.bservice_enable=true
[    1.149044] ro.hwui.texture_cache_size=72
[    1.149252] debug.gralloc.enable_fb_ubwc=1
[    1.149346] persist.dpm.feature=0
[    1.149492] persist.rmnet.mux=enabled
[    1.149600] ro.opengles.version=196610
[    1.149681] camera.aux.packagelist=org.codeaurora.snapcam,com.motorola.camera2,com.motorola.motocit
[    1.149688] camera.hal1.packagelist=com.skype.raider,com.google.android.talk
[    1.149695] camera.display.lmax=1280x720
[    1.149701] camera.display.umax=1920x1080
[    1.149706] camera.disable_zsl_mode=1
[    1.149711] persist.camera.expose.aux=1
[    1.149883] ro.dalvik.vm.native.bridge=0
[    1.149889] dalvik.vm.isa.arm.variant=cortex-a53
[    1.149897] dalvik.vm.isa.arm.features=default
[    1.149903] dalvik.vm.usejit=true
[    1.149908] dalvik.vm.heapsize=384m
[    1.149913] dalvik.vm.dex2oat-Xms=64m
[    1.149921] dalvik.vm.dex2oat-Xmx=512m
[    1.149927] dalvik.vm.heapmaxfree=8m
[    1.149932] dalvik.vm.heapminfree=512k
[    1.149937] dalvik.vm.heapstartsize=8m
[    1.149942] dalvik.vm.appimageformat=lz4
[    1.149948] dalvik.vm.usejitprofiles=true
[    1.149953] dalvik.vm.heapgrowthlimit=192m
[    1.149960] dalvik.vm.stack-trace-file=/data/anr/traces.txt
[    1.149966] dalvik.vm.image-dex2oat-Xms=64m
[    1.149971] dalvik.vm.image-dex2oat-Xmx=64m
[    1.149976] dalvik.vm.heaptargetutilization=0.75
[    1.149984] ro.config.zram=true
[    1.149989] ro.config.ringtone=Moto.ogg
[    1.149995] ro.config.wallpaper=system/media/wallpapers/default_moto_wallpaper.jpg
[    1.150000] ro.config.ringtone_2=Moto.ogg
[    1.150015] ro.config.alarm_alert=Oxygen.ogg
[    1.150023] ro.config.max_starting_bg=8
[    1.150028] ro.config.vc_call_vol_steps=8
[    1.150035] ro.config.notification_sound=Moto.ogg
[    1.150099] af.fast_track_multiplier=1
[    1.150105] av.debug.disable.pers.cache=1
[    1.150113] av.offload.enable=false
[    1.150119] mm.enable.sec.smoothstreaming=false
[    1.150124] mm.enable.qcom_parser=135715
[    1.150129] mm.enable.smoothstreaming=false
[    1.150137] pm.dexopt.boot=verify-profile
[    1.150143] pm.dexopt.ab-ota=speed-profile
[    1.150148] pm.dexopt.install=interpret-only
[    1.150153] pm.dexopt.core-app=speed
[    1.150158] pm.dexopt.bg-dexopt=speed-profile
[    1.150163] pm.dexopt.first-boot=interpret-only
[    1.150168] pm.dexopt.shared-apk=speed
[    1.150177] pm.dexopt.nsys-library=speed
[    1.150182] pm.dexopt.forced-dexopt=speed
[    1.150187] ro.fm.transmitter=false
[    1.150193] ro.qc.sdk.audio.ssr=false
[    1.150198] ro.qc.sdk.audio.fluencetype=none
[    1.150203] ro.adb.secure=1
[    1.150208] ro.com.google.ime.theme_id=4
[    1.150213] ro.com.google.gmsversion=7.1_r3
[    1.150219] ro.com.google.clientidbase=android-motorola
[    1.150226] ro.com.google.clientidbase.am=android-motorola
[    1.150231] ro.com.google.clientidbase.ms=android-motorola-rev2
[    1.150237] ro.com.google.clientidbase.yt=android-motorola
[    1.150242] ro.com.google.clientidbase.gmm=android-motorola
[    1.150247] ro.com.google.rlzbrandcode=MOTC
[    1.150252] ro.com.google.rlz_ap_whitelist=y0,y5,y6,y7,y8
[    1.150257] ro.frp.pst=/dev/block/bootdevice/by-name/frp
[    1.150262] ro.mot.build.product.increment=366
[    1.150268] ro.mot.build.version.release=26.366
[    1.150273] ro.mot.build.version.sdk_int=26
[    1.150278] ro.mot.build.customerid=retail
[    1.150284] ro.mot.sensors.glance_approach=false
[    1.150289] ro.mot.security.enable=true
[    1.150294] ro.mot.base_buildid=NPS26.116-61/74
[    1.150302] ro.mot.ignore_csim_appid=true
[    1.150308] ro.oem.key1=retin
[    1.150313] ro.opa.eligible_device=true
[    1.150318] ro.sys.fw.empty_app_percent=50
[    1.150323] ro.sys.fw.use_trim_settings=true
[    1.150328] ro.sys.fw.trim_cache_percent=100
[    1.150333] ro.sys.fw.trim_empty_percent=100
[    1.150339] ro.sys.fw.trim_enable_memory=2147483648
[    1.150344] ro.sys.fw.dex2oat_thread_count=4
[    1.150349] ro.url.legal=http://www.google.com/intl/%s/mobile/android/basic/phone-legal.html
[    1.150355] ro.url.legal.android_privacy=http://www.google.com/intl/%s/mobile/android/basic/privacy.html
[    1.150360] ro.usb.bpt=2ee5
[    1.150365] ro.usb.mtp=2e82
[    1.150370] ro.usb.ptp=2e83
[    1.150375] ro.usb.bpteth=2ee7
[    1.150380] ro.usb.bpt_adb=2ee6
[    1.150385] ro.usb.mtp_adb=2e76
[    1.150390] ro.usb.ptp_adb=2e84
[    1.150395] ro.usb.bpteth_adb=2ee8
[    1.150401] ro.boot.cid=0x32
[    1.150406] ro.boot.uid=C035992300000000000000000000
[    1.150411] ro.boot.emmc=true
[    1.150418] ro.boot.mode=normal
[    1.150424] ro.boot.flash.locked=1
[    1.150429] ro.boot.hwrev=0x8400
[    1.150434] ro.boot.radio=INDIA
[    1.150439] ro.boot.device=sanders
[    1.150444] ro.boot.fsg-id=
[    1.150449] ro.boot.carrier=retin
[    1.150455] ro.boot.dualsim=true
[    1.150460] ro.boot.baseband=msm
[    1.150465] ro.boot.bl_state=1
[    1.150470] ro.boot.hardware=qcom
[    1.150475] ro.boot.hardware.sku=XT1804
[    1.150480] ro.boot.serialno=ZY32286WPB
[    1.150486] ro.boot.btmacaddr=A8:96:75:05:41:08
[    1.150491] ro.boot.bootdevice=7824900.sdhci
[    1.150496] ro.boot.bootloader=0xC207
[    1.150501] ro.boot.bootreason=reboot
[    1.150506] ro.boot.veritymode=enforcing
[    1.150512] ro.boot.wifimacaddr=A8:96:75:05:41:09,A8:96:75:05:41:0A
[    1.150517] ro.boot.write_protect=1
[    1.150522] ro.boot.poweroff_alarm=0
[    1.150527] ro.boot.powerup_reason=0x00004000
[    1.150532] ro.boot.secure_hardware=1
[    1.150537] ro.boot.verifiedbootstate=green
[    1.150542] ro.hwui.path_cache_size=32
[    1.150547] ro.hwui.layer_cache_size=48
[    1.150552] ro.hwui.gradient_cache_size=1
[    1.150558] ro.hwui.r_buffer_cache_size=8
[    1.150567] ro.hwui.drop_shadow_cache_size=6
[    1.150572] ro.hwui.text_large_cache_width=2048
[    1.150577] ro.hwui.text_small_cache_width=1024
[    1.150583] ro.hwui.text_large_cache_height=1024
[    1.150588] ro.hwui.text_small_cache_height=1024
[    1.150597] ro.hwui.texture_cache_flushrate=0.4
[    1.150602] ro.mdtp.package_name2=com.qualcomm.qti.securemsm.mdtp.MdtpDemo
[    1.150607] ro.wifi.channels=
[    1.150613] ro.allow.mock.location=0
[    1.150618] ro.board.platform=msm8953
[    1.150623] ro.build.id=NPSS26.116-61-11
[    1.150628] ro.build.date=Tue May 29 20:49:44 CDT 2018
[    1.150634] ro.build.date.utc=1527644984
[    1.150639] ro.build.host=ilclbld33
[    1.150644] ro.build.tags=release-keys
[    1.150649] ro.build.type=user
[    1.150654] ro.build.user=hudsoncm
[    1.150659] ro.build.product=sanders
[    1.150664] ro.build.version.sdk=25
[    1.150669] ro.build.version.qcom=LA.UM.5.6.r1-03800-89xx.0
[    1.150674] ro.build.version.release=7.1.1
[    1.150679] ro.build.version.codename=REL
[    1.150685] ro.build.version.incremental=18
[    1.150690] ro.build.version.preview_sdk=0
[    1.150695] ro.build.version.all_codenames=REL
[    1.150700] ro.build.version.security_patch=2018-06-01
[    1.150706] ro.build.thumbprint=7.1.1/NPSS26.116-61-11/18:user/release-keys
[    1.150711] ro.build.characteristics=default
[    1.150716] ro.media.enc.aud.ch=1
[    1.150722] ro.media.enc.aud.hz=8000
[    1.150727] ro.media.enc.aud.bps=13300
[    1.150732] ro.media.enc.aud.codec=qcelp
[    1.150737] ro.media.enc.aud.fileformat=qcp
[    1.150742] ro.radio.imei.sv=13
[    1.150747] ro.bug2go.magickeys=24,26
[    1.150752] ro.lenovo.single_hand=1
[    1.150757] ro.secure=1
[    1.150763] ro.vendor.at_library=libqti-at.so
[    1.150768] ro.vendor.gt_library=libqti-gt.so
[    1.150773] ro.vendor.extension_library=libqti-perfd-client.so
[    1.150778] ro.zygote=zygote32
[    1.150783] ro.carrier=retin
[    1.150788] ro.memperf.lib=libmemperf.so
[    1.150793] ro.memperf.enable=false
[    1.150799] ro.product.cpu.abi=armeabi-v7a
[    1.150804] ro.product.cpu.abi2=armeabi
[    1.150809] ro.product.cpu.abilist=armeabi-v7a,armeabi
[    1.150814] ro.product.cpu.abilist32=armeabi-v7a,armeabi
[    1.150819] ro.product.cpu.abilist64=
[    1.150824] ro.product.name=sanders_retail
[    1.150829] ro.product.board=msm8953
[    1.150834] ro.product.brand=motorola
[    1.150839] ro.product.model=Moto G (5S) Plus
[    1.150844] ro.product.device=sanders
[    1.150850] ro.product.locale=en-US
[    1.150855] ro.product.manufacturer=motorola
[    1.150861] ro.product.first_api_level=25
[    1.150869] ro.baseband=msm
[    1.150874] ro.bootmode=normal
[    1.150879] ro.hardware=qcom
[    1.150884] ro.hardware.sensors=sanders
[    1.150889] ro.logdumpd.enabled=0
[    1.150895] ro.qualcomm.cabl=0
[    1.150900] ro.revision=p400
[    1.150905] ro.serialno=ZY32286WPB
[    1.150911] ro.bootimage.build.date=Tue May 29 20:49:44 CDT 2018
[    1.150916] ro.bootimage.build.date.utc=1527644984
[    1.150921] ro.bootimage.build.fingerprint=motorola/sanders_retail/sanders:7.1.1/NPSS26.116-61-11/18:user/release-keys
[    1.150926] ro.emmc_size=16GB
[    1.150931] ro.telephony.default_network=10,0
[    1.150936] ro.bootloader=0xC207
[    1.150941] ro.bootreason=reboot
[    1.150946] ro.debuggable=0
[    1.150952] ro.setupwizard.mode=OPTIONAL
[    1.150957] ro.core_ctl_max_cpu=4
[    1.150962] ro.core_ctl_min_cpu=2
[    1.150967] ro.use_data_netmgrd=true
[    1.150972] ro.cutoff_voltage_mv=3400
[    1.150977] ro.oem_unlock_supported=1
[    1.150982] drm.service.enabled=true
[    1.150987] mmp.enable.3g2=true
[    1.150993] use.qti.sw.ape.decoder=true
[    1.150998] use.qti.sw.alac.decoder=true
[    1.151003] use.voice.path.for.pcm.voip=false
[    1.151008] init.svc.healthd=running
[    1.151013] init.svc.ueventd=running
[    1.151018] init.svc.recovery=running
[    1.151023] qcom.bt.le_dev_pwr_class=1
[    1.151028] qcom.hw.aac.encoder=false
[    1.151033] rild.libargs=-d /dev/smd0
[    1.151038] rild.libpath=/system/vendor/lib/libril-qc-qmi-1.so
[    1.151048] vidc.dec.disable.split.cpu=1
[    1.151053] vidc.dec.downscalar_width=1920
[    1.151080] vidc.dec.downscalar_height=1088
[    1.151087] vidc.enc.dcvs.extra-buff-count=2
[    1.151093] vidc.enc.disable.pq=true
[    1.151098] vidc.enc.disable_bframes=1
[    1.151103] vidc.enc.disable_pframes=1
[    1.151108] vidc.disable.split.mode=1
[    1.151113] audio.pp.asphere.enabled=false
[    1.151118] audio.safx.pbe.enabled=true
[    1.151125] audio.dolby.ds2.enabled=true
[    1.151130] audio.parser.ip.buffer.size=262144
[    1.151135] audio.offload.min.duration.secs=60
[    1.151140] audio.offload.pcm.16bit.enable=false
[    1.151146] audio.offload.pcm.24bit.enable=false
[    1.151151] audio.offload.track.enable=true
[    1.151156] audio.offload.video=false
[    1.151161] audio.offload.buffer.size.kb=64
[    1.151166] audio.offload.disable=false
[    1.151171] audio.offload.gapless.enabled=false
[    1.151176] audio.offload.multiple.enabled=false
[    1.151182] audio.playback.mch.downsample=true
[    1.151187] audio.deep_buffer.media=true
[    1.151192] media.msm8956hw=0
[    1.151197] media.aac_51_output_enabled=true
[    1.151202] voice.conc.fallbackpath=deep-buffer
[    1.151208] voice.voip.conc.disabled=true
[    1.151213] voice.record.conc.disabled=false
[    1.151218] voice.playback.conc.disabled=true
[    1.151223] tunnel.audio.encode=false
[    1.151228] persist.mm.sta.enable=0
[    1.151233] persist.vt.supported=0
[    1.151238] persist.cne.rat.wlan.chip.oem=WCN
[    1.151243] persist.cne.feature=1
[    1.151248] persist.cne.logging.qxdm=3974
[    1.151253] persist.eab.supported=0
[    1.151258] persist.hwc.mdpcomp.enable=true
[    1.151263] persist.hwc.enable_vds=1
[    1.151268] persist.ims.vt=false
[    1.151273] persist.ims.vt.epdg=false
[    1.151278] persist.ims.rcs=false
[    1.151283] persist.ims.volte=true
[    1.151290] persist.ims.disableADBLogs=2
[    1.151295] persist.ims.disableIMSLogs=0
[    1.151300] persist.ims.disableQXDMLogs=0
[    1.151309] persist.ims.disableDebugLogs=0
[    1.151315] persist.lte.pco_supported=true
[    1.151320] persist.qfp=false
[    1.151325] persist.rcs.presence.provision=0
[    1.151330] persist.rcs.supported=0
[    1.151335] persist.data.qmi.adb_logmask=0
[    1.151340] persist.data.mode=concurrent
[    1.151345] persist.data.iwlan.enable=true
[    1.151351] persist.data.netmgrd.qos.enable=true
[    1.151356] persist.demo.hdmirotationlock=false
[    1.151361] persist.rild.nitz_plmn=
[    1.151366] persist.rild.nitz_long_ons_0=
[    1.151371] persist.rild.nitz_long_ons_1=
[    1.151376] persist.rild.nitz_long_ons_2=
[    1.151381] persist.rild.nitz_long_ons_3=
[    1.151386] persist.rild.nitz_short_ons_0=
[    1.151391] persist.rild.nitz_short_ons_1=
[    1.151396] persist.rild.nitz_short_ons_2=
[    1.151401] persist.rild.nitz_short_ons_3=
[    1.151407] persist.vold.ecryptfs_supported=true
[    1.151412] persist.timed.enable=true
[    1.151417] persist.speaker.prot.enable=false
[    1.151422] persist.fuse_sdcard=true
[    1.151427] persist.esdfs_sdcard=true
[    1.151432] persist.qcril_uim_vcc_feature=1
[    1.151437] keyguard.no_require_sim=true
[    1.151442] audio_hal.period_size=240
[    1.151447] telephony.lteOnCdmaDevice=1
[    1.151452] DEVICE_PROVISIONED=1
[    1.151457] mdc_initial_max_retry=10
[    1.151462] persist.debug.coresight.config=stm-events
[    1.151467] persist.audio.cal.sleeptime=6000
[    1.151473] persist.audio.dualmic.config=endfire
[    1.151478] persist.audio.endcall.delay=250
[    1.151483] persist.audio.fluence.speaker=false
[    1.151488] persist.audio.fluence.voicerec=false
[    1.151493] persist.audio.fluence.voicecall=true
[    1.151498] persist.audio.fluence.voicecomm=true
[    1.151503] persist.audio.calfile0=/etc/acdbdata/Bluetooth_cal.acdb
[    1.151508] persist.audio.calfile1=/etc/acdbdata/General_cal.acdb
[    1.151514] persist.audio.calfile2=/etc/acdbdata/Global_cal.acdb
[    1.151519] persist.audio.calfile3=/etc/acdbdata/Handset_cal.acdb
[    1.151524] persist.audio.calfile4=/etc/acdbdata/Hdmi_cal.acdb
[    1.151534] persist.audio.calfile5=/etc/acdbdata/Headset_cal.acdb
[    1.151540] persist.audio.calfile6=/etc/acdbdata/Speaker_cal.acdb
[    1.151545] security.perf_harden=1
[    1.151552] debug.sf.hw=1
[    1.151557] debug.egl.hw=1
[    1.151563] debug.atrace.tags.enableflags=0
[    1.151568] debug.enable.gamed=0
[    1.151573] debug.enable.sglscale=1
[    1.151578] debug.mdpcomp.logs=0
[    1.151583] ro.qualcomm.bt.hci_transport=smd
[    1.151588] ro.bluetooth.hfp.ver=1.6
[    1.151593] bluetooth.hfp.client=1
[    1.151691] ro.hw.hwrev=0x8400
[    1.151697] ro.hw.radio=INDIA
[    1.151707] ro.hw.device=sanders
[    1.151712] ro.hw.dualsim=true
[    1.151717] dev.pm.dyn_samplingrate=1
[    1.151722] net.bt.name=Android
[    1.151730] net.change=net.bt.name
[    1.151735] sys.mod.platformsdkversion=200
[    1.151740] persist.sys.qc.sub.rdump.on=1
[    1.151745] persist.sys.qc.sub.rdump.max=0
[    1.151751] persist.sys.cnd.iwlan=1
[    1.151756] persist.sys.ssr.restart_level=ALL_ENABLE
[    1.151761] persist.sys.media.use-awesome=false
[    1.151768] persist.sys.dalvik.vm.lib.2=libart.so
[    1.151773] ril.subscription.types=NV,RUIM
[    1.151781] persist.radio.call.audio.output=0
[    1.151786] persist.radio.calls.on.ims=true
[    1.151792] persist.radio.jbims=1
[    1.151799] persist.radio.domain.ps=0
[    1.151805] persist.radio.VT_ENABLE=1
[    1.151810] persist.radio.apn_delay=5000
[    1.151815] persist.radio.msgtunnel.start=true
[    1.151820] persist.radio.custom_ecc=1
[    1.151828] persist.radio.mt_sms_ack=30
[    1.151833] persist.radio.sar_sensor=1
[    1.151838] persist.radio.videopause.mode=0
[    1.151844] persist.radio.REVERSE_QMI=0
[    1.151849] persist.radio.dfr_mode_set=1
[    1.151854] persist.radio.sib16_support=1
[    1.151859] persist.radio.sw_mbn_update=1
[    1.151864] persist.radio.force_get_pref=1
[    1.151871] persist.radio.is_wps_enabled=true
[    1.151877] persist.radio.snapshot_timer=22
[    1.151882] persist.radio.ROTATION_ENABLE=1
[    1.151887] persist.radio.VT_USE_MDM_TIME=0
[    1.151892] persist.radio.oem_ind_to_both=0
[    1.151897] persist.radio.VT_HYBRID_ENABLE=1
[    1.151903] persist.radio.apm_sim_not_pwdn=1
[    1.151908] persist.radio.no_wait_for_card=1
[    1.151913] persist.radio.snapshot_enabled=1
[    1.151918] persist.radio.0x9e_not_callname=1
[    1.151923] persist.radio.RATE_ADAPT_ENABLE=1
[    1.151928] persist.radio.relay_oprt_change=1
[    1.151935]
[    1.151942] Supported API: 3
[    1.171964] Fixing execute permissions for /cache
[    1.172657] charge_status 3, charged 0, status 0, capacity 59
[    1.204592] Finding update package...
[    1.270229] I:Update location: @/cache/recovery/block.map
[    1.270264] Opening update package...
[    1.310707] sysutil: mmapped 762 ranges
[    1.310949] I:read key e=65537 hash=32
[    1.310958] I:1 key(s) loaded from /res/keys
[    1.310964] Verifying update package...
[    1.340221] I:comment is 1465 bytes; signature 1447 bytes from end
[   14.180901] I:signature (offset: 0x4b34df2a, length: 1441): 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
[   14.181416] I:whole-file signature verified against RSA key 0
[   14.181424] I:verify_file returned 0
[   14.185704] Installing update...
[   14.215058] E:Failed to parse build number in post-build-incremental=9fea.
[   14.266405] mmapped 762 ranges
[   14.269088] installing gptupgrade updater extensions
[   14.282847] selinux_android_file_context: Error getting file context handle (No such file or directory)
[   14.287405] Source: motorola/sanders/sanders:7.1.1/NPSS26.116-61-11/18:user/release-keys
[   14.287427] Target: motorola/sanders/sanders:8.1.0/OPS28.65-36/9fea:user/release-keys
[   14.287435] Verifying current system...
[   14.605256] partition read matched size 16777216 SHA-1 02a96aef194b97e0803da22abca04c31a2766ef7
[   18.994707] Verified oem partition...
[   18.994803] Checking for stash directory /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ for the device /dev/block/bootdevice/by-name/system upgrade 
[   18.994900] Checking for stash directory /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/ for the device /dev/block/bootdevice/by-name/oem upgrade 
[   18.994969] 254484480 bytes free on /cache (121466880 needed)
[   45.524200] Verified system partition...
[   45.524239] Patching system image after verification.
[   45.524267] performing update
[   45.548710] blockimg version is 4
[   45.548752] maximum stash entries 0
[   45.548759] creating stash /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/
[   45.549676] 254480384 bytes free on /cache (121466880 needed)
[   45.552962]   zeroing 153401 blocks
[   55.233597] patching 717 blocks to 12
[   55.250139] patching 59 blocks to 6
[   55.260355] patching 1 blocks to 1
[   55.271075]   moving 14 blocks
[   55.273151] patching 3 blocks to 5
[   55.330438] patching 31 blocks to 43
[   55.412977] patching 2 blocks to 2
[   55.549403] stashing 1626 overlapping blocks to 4fc7d81699db4d3f326326ff04225e1530eb5cab
[   55.549441] 254480384 bytes free on /cache (6660096 needed)
[   55.549503]  writing 1626 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4fc7d81699db4d3f326326ff04225e1530eb5cab
[   55.918061] patching 1626 blocks to 17
[   55.932701] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/4fc7d81699db4d3f326326ff04225e1530eb5cab
[   55.993820] patching 322 blocks to 328
[   57.783008] patching 3151 blocks to 16930
[   74.883332] patching 2501 blocks to 21
[   76.832498] stashing 29655 overlapping blocks to be1108e2559d85092ece0f996cdba4b770d39944
[   76.832532] 254480384 bytes free on /cache (121466880 needed)
[   76.832612]  writing 29655 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/be1108e2559d85092ece0f996cdba4b770d39944
[   79.618397] patching 29655 blocks to 237
[   79.792340] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/be1108e2559d85092ece0f996cdba4b770d39944
[   79.923565] patching 8 blocks to 5
[   79.963257] patching 22 blocks to 56
[   80.409586] stashing 3876 overlapping blocks to 023c8ac19dad0b98032bdceaeab5460042086460
[   80.409624] 254480384 bytes free on /cache (15876096 needed)
[   80.409669]  writing 3876 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/023c8ac19dad0b98032bdceaeab5460042086460
[   80.632889] patching 3876 blocks to 25
[   80.653258] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/023c8ac19dad0b98032bdceaeab5460042086460
[   80.668071] patching 25 blocks to 45
[   80.729770] patching 2 blocks to 2
[   80.803739] patching 256 blocks to 258
[   81.929726] patching 185 blocks to 6
[   81.941664] patching 13 blocks to 5
[   81.957228] patching 21 blocks to 20
[   81.984801] patching 2 blocks to 3
[   82.079415] stashing 512 overlapping blocks to b6dd58a29e12ef67a46f7788c8ae4447201a7bb9
[   82.079449] 254480384 bytes free on /cache (2097152 needed)
[   82.079505]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b6dd58a29e12ef67a46f7788c8ae4447201a7bb9
[   82.105003] patching 512 blocks to 512
[   82.163562] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/b6dd58a29e12ef67a46f7788c8ae4447201a7bb9
[   82.185114] patching 5 blocks to 21
[   82.335329] patching 1445 blocks to 18
[   82.470071] patching 1164 blocks to 1271
[   86.551594] patching 65 blocks to 83
[   86.892885] patching 1357 blocks to 19
[   87.020971] patching 2331 blocks to 19
[   87.047227] patching 107 blocks to 6
[   87.057688] patching 14 blocks to 5
[   87.067611] patching 5 blocks to 5
[   87.146161] patching 296 blocks to 263
[   88.951687] patching 2 blocks to 4
[   89.004074] patching 7 blocks to 5
[   89.078302] patching 758 blocks to 11
[   89.100240] patching 112 blocks to 86
[   89.407988] patching 5298 blocks to 74
[   89.488009] patching 142 blocks to 5
[   89.503471] patching 51 blocks to 5
[   89.554673] patching 351 blocks to 6
[   89.567296] patching 4 blocks to 51
[   89.631465] patching 11 blocks to 9
[   89.645752] patching 4 blocks to 5
[   89.707446] patching 187 blocks to 6
[   89.760432] patching 896 blocks to 12
[   89.810327] patching 226 blocks to 526
[   92.259575] patching 2205 blocks to 30
[   92.534741] patching 1432 blocks to 2548
[  106.449150] patching 1578 blocks to 18
[  106.519299] patching 231 blocks to 248
[  106.714209] patching 6 blocks to 7
[  106.750225] patching 9 blocks to 10
[  106.791603] patching 40 blocks to 55
[  106.842276] patching 5 blocks to 6
[  106.858980] patching 87 blocks to 7
[  106.870672] patching 10 blocks to 5
[  106.881374] patching 12 blocks to 5
[  106.891963] patching 10 blocks to 5
[  107.554844] patching 5957 blocks to 5471
[  115.323787] patching 10 blocks to 5
[  115.350724] patching 349 blocks to 7
[  115.361690] patching 6 blocks to 5
[  115.371928] patching 4 blocks to 4
[  115.438198] patching 274 blocks to 7
[  115.460940] patching 122 blocks to 127
[  115.952173] patching 10 blocks to 5
[  115.962177] patching 3 blocks to 4
[  116.045455] patching 10 blocks to 5
[  116.055991] patching 6 blocks to 6
[  116.123867] patching 7 blocks to 5
[  116.249855] patching 1069 blocks to 12
[  116.266214] patching 2 blocks to 24
[  116.554015] patching 655 blocks to 3165
[  121.681409] patching 12 blocks to 5
[  121.712595] patching 4 blocks to 6
[  121.791380] patching 17 blocks to 6
[  121.823460] patching 2 blocks to 4
[  122.031466] patching 2858 blocks to 50
[  122.070340] patching 7 blocks to 5
[  122.081405] patching 9 blocks to 10
[  122.100760] patching 5 blocks to 5
[  122.143634] patching 234 blocks to 6
[  122.156632] patching 2 blocks to 33
[  122.234289] patching 219 blocks to 7
[  122.248060] patching 4 blocks to 41
[  122.309337] patching 10 blocks to 5
[  122.328040] patching 45 blocks to 46
[  122.410976] patching 34 blocks to 5
[  122.422134] patching 5 blocks to 10
[  122.482120] patching 27 blocks to 5
[  122.845332] patching 3245 blocks to 3482
[  127.293294] patching 3401 blocks to 29
[  127.380880] patching 366 blocks to 376
[  131.657406] patching 1344 blocks to 1756
[  133.733277] patching 1462 blocks to 38
[  133.766114] patching 7 blocks to 14
[  133.872072] patching 79 blocks to 9
[  134.021772] patching 549 blocks to 1418
[  135.629038] patching 695 blocks to 8
[  135.646861] patching 31 blocks to 134
[  135.877358] patching 816 blocks to 1842
[  138.050911] patching 685 blocks to 9
[  138.066002] patching 9 blocks to 10
[  138.084498] patching 13 blocks to 19
[  138.118313] patching 68 blocks to 80
[  138.192411] patching 8 blocks to 9
[  138.212788] patching 36 blocks to 39
[  138.244483] patching 9 blocks to 12
[  138.263819] patching 55 blocks to 6
[  138.275154] patching 12 blocks to 15
[  138.501985] patching 22 blocks to 25
[  138.740908] patching 994 blocks to 995
[  138.859327] patching 17 blocks to 16
[  138.883855] patching 5 blocks to 6
[  138.897706] patching 5 blocks to 5
[  138.912380] patching 26 blocks to 27
[  138.935839] patching 11 blocks to 17
[  138.958517] patching 12 blocks to 5
[  139.018121] patching 234 blocks to 597
[  139.617047] patching 5 blocks to 4
[  139.629862] patching 5 blocks to 4
[  139.650109] patching 67 blocks to 70
[  139.715263]   moving 1 blocks
[  139.716091] patching 3 blocks to 3
[  139.727125] patching 35 blocks to 5
[  139.742254] patching 34 blocks to 35
[  139.765791] patching 2 blocks to 2
[  139.779155] patching 34 blocks to 39
[  139.818292] patching 8 blocks to 8
[  139.834628] patching 2 blocks to 3
[  139.880022] patching 2 blocks to 3
[  139.927180] patching 15 blocks to 22
[  139.973937] patching 156 blocks to 7
[  139.991404] patching 23 blocks to 67
[  140.480398] patching 120 blocks to 6
[  140.491892] patching 2 blocks to 21
[  140.552263] patching 2 blocks to 6
[  140.613091] patching 39 blocks to 5
[  140.623762] patching 5 blocks to 9
[  140.682536] patching 13 blocks to 5
[  140.695242] patching 8 blocks to 10
[  140.750879] patching 2 blocks to 10
[  140.973379] patching 1191 blocks to 1651
[  151.748275] patching 109 blocks to 5
[  151.759345] patching 5 blocks to 13
[  151.825875] patching 33 blocks to 5
[  151.843557] patching 29 blocks to 27
[  151.867481]   moving 1 blocks
[  151.870975] patching 9 blocks to 10
[  151.887638] patching 5 blocks to 4
[  151.902720] patching 9 blocks to 10
[  151.922438] patching 11 blocks to 14
[  151.966772] patching 118 blocks to 132
[  152.117432] patching 6 blocks to 6
[  152.136143] patching 21 blocks to 46
[  152.214791] patching 5 blocks to 6
[  152.230683] patching 12 blocks to 11
[  152.255063] patching 27 blocks to 28
[  152.290783] patching 22 blocks to 5
[  152.304450] patching 40 blocks to 42
[  152.672007] patching 1852 blocks to 1915
[  161.257011] patching 1 blocks to 1
[  161.267340] patching 4 blocks to 4
[  161.280757] patching 7 blocks to 7
[  161.295653] patching 4 blocks to 4
[  161.308247] patching 4 blocks to 4
[  161.321941] patching 14 blocks to 11
[  161.337916] patching 11 blocks to 12
[  161.354887] patching 4 blocks to 4
[  161.366969] patching 6 blocks to 7
[  161.380592] patching 6 blocks to 6
[  161.397011] patching 27 blocks to 27
[  161.428416] patching 65 blocks to 70
[  161.494151] patching 4 blocks to 4
[  161.507640] patching 22 blocks to 19
[  161.542343] patching 120 blocks to 120
[  161.640967] patching 8 blocks to 9
[  161.658446] patching 8 blocks to 8
[  161.705915] patching 246 blocks to 350
[  162.067312] patching 21 blocks to 25
[  162.093804] patching 6 blocks to 7
[  162.108062] patching 6 blocks to 6
[  162.121926] patching 7 blocks to 8
[  162.139098] patching 4 blocks to 4
[  162.157591] patching 61 blocks to 50
[  162.209321] patching 58 blocks to 62
[  162.264937] patching 39 blocks to 40
[  162.321598] patching 116 blocks to 305
[  162.497064] patching 11 blocks to 15
[  162.519873] patching 5 blocks to 5
[  162.532730] patching 5 blocks to 5
[  162.546758] patching 14 blocks to 19
[  162.577049] patching 63 blocks to 68
[  162.647770] patching 64 blocks to 66
[  162.709380] patching 8 blocks to 8
[  162.723663] patching 4 blocks to 4
[  162.737902] patching 14 blocks to 16
[  162.760650] patching 7 blocks to 8
[  162.775958] patching 4 blocks to 4
[  162.788441] patching 4 blocks to 4
[  162.801384] patching 4 blocks to 4
[  162.814132] patching 6 blocks to 7
[  162.833499] patching 29 blocks to 56
[  162.893359] patching 8 blocks to 17
[  162.913380] patching 6 blocks to 7
[  162.926607] patching 4 blocks to 6
[  162.948562] patching 65 blocks to 78
[  163.028719] patching 6 blocks to 6
[  163.054408] patching 98 blocks to 114
[  163.175437] patching 55 blocks to 93
[  163.289894] patching 597 blocks to 11
[  163.389788] patching 618 blocks to 907
[  165.975149] patching 106 blocks to 6
[  166.006476] patching 58 blocks to 93
[  166.426159] patching 284 blocks to 7
[  166.464117] patching 83 blocks to 128
[  166.554678] patching 8 blocks to 5
[  166.573514] patching 52 blocks to 88
[  170.491019] patching 13 blocks to 5
[  170.502734] patching 3 blocks to 5
[  170.600035] patching 347 blocks to 7
[  170.614065] patching 10 blocks to 10
[  170.633698] patching 5 blocks to 6
[  170.649228] patching 13 blocks to 5
[  170.662717] patching 11 blocks to 11
[  170.681608] patching 6 blocks to 7
[  170.702808] patching 20 blocks to 30
[  170.737238] patching 8 blocks to 16
[  170.764137] patching 26 blocks to 5
[  170.777028] patching 9 blocks to 9
[  170.806496] patching 35 blocks to 44
[  170.898104] patching 195 blocks to 202
[  171.155409] patching 90 blocks to 109
[  171.252032] patching 55 blocks to 203
[  171.448452]   moving 1 blocks
[  171.451893] patching 9 blocks to 9
[  171.483267] patching 53 blocks to 65
[  171.553849] patching 9 blocks to 9
[  171.591255] patching 1 blocks to 1
[  171.627812] patching 25 blocks to 81
[  171.720251]   moving 8 blocks
[  171.742729]   moving 1 blocks
[  171.765419]   moving 5 blocks
[  171.788391] patching 2 blocks to 2
[  171.819001] patching 1 blocks to 1
[  171.851797] patching 12 blocks to 12
[  171.887091]   moving 1 blocks
[  171.910900]  writing 1024 blocks of new data
[  172.306773]  writing 359 blocks of new data
[  172.453470] patching 7 blocks to 7
[  172.489342] patching 17 blocks to 17
[  172.533807] patching 101 blocks to 5
[  172.567795] patching 5 blocks to 6
[  172.603460]   moving 4 blocks
[  172.626060]  writing 7 blocks of new data
[  172.627261]  writing 27 blocks of new data
[  172.664653] patching 5 blocks to 5
[  172.699058]  writing 12 blocks of new data
[  172.728170] patching 12 blocks to 12
[  172.764543]  writing 77 blocks of new data
[  172.818010] patching 133 blocks to 133
[  172.966423] patching 14 blocks to 15
[  173.002896]  writing 7 blocks of new data
[  173.027042]  writing 4 blocks of new data
[  173.052549] patching 6 blocks to 7
[  173.091998] patching 23 blocks to 24
[  173.145772]  writing 2 blocks of new data
[  173.159761]   moving 4 blocks
[  173.185338] patching 14 blocks to 15
[  173.219135]  writing 1 blocks of new data
[  173.243020] patching 14 blocks to 15
[  173.279286] patching 5 blocks to 5
[  173.317812]   moving 36 blocks
[  173.345149]  writing 23 blocks of new data
[  173.373608] patching 12 blocks to 12
[  173.415347] patching 9 blocks to 4
[  173.452236]   moving 1 blocks
[  173.477519]   moving 1 blocks
[  173.502667]  writing 5 blocks of new data
[  173.511806]  writing 1 blocks of new data
[  173.512511]  writing 61 blocks of new data
[  173.526641] patching 14 blocks to 15
[  173.540990] patching 12 blocks to 12
[  173.560142]  writing 4 blocks of new data
[  173.561362]  writing 1 blocks of new data
[  173.561916]  writing 3 blocks of new data
[  173.565578]   moving 4 blocks
[  173.569098] patching 18 blocks to 17
[  173.598452]   moving 1 blocks
[  173.599005]  writing 7 blocks of new data
[  173.600924]   moving 4 blocks
[  173.603255] patching 12 blocks to 12
[  173.625548] patching 77 blocks to 123
[  173.755907] patching 16 blocks to 17
[  173.769768]  writing 5 blocks of new data
[  173.858771] patching 650 blocks to 948
[  176.081549]   moving 6 blocks
[  176.083866]   moving 1 blocks
[  176.085482] patching 5 blocks to 4
[  176.098595] patching 6 blocks to 6
[  176.112701]   moving 2 blocks
[  176.113404]  writing 1 blocks of new data
[  176.120767] patching 16 blocks to 17
[  176.135243] patching 1 blocks to 1
[  176.145312] patching 8 blocks to 10
[  176.162354] patching 5 blocks to 6
[  176.202384]   moving 280 blocks
[  176.214806] patching 5 blocks to 5
[  176.228236]   moving 10 blocks
[  176.230896]  writing 25 blocks of new data
[  176.235796]  writing 54 blocks of new data
[  176.246772]  writing 31 blocks of new data
[  176.254013]   moving 1 blocks
[  176.256824] patching 12 blocks to 12
[  176.268974]  writing 357 blocks of new data
[  176.329204]   moving 78 blocks
[  176.336653] patching 9 blocks to 13
[  176.357308] patching 4 blocks to 4
[  176.370235]   moving 6 blocks
[  176.372208]   moving 1 blocks
[  176.385052] patching 134 blocks to 103
[  176.481787]  writing 21 blocks of new data
[  176.487721] patching 6 blocks to 6
[  176.497305]   moving 1 blocks
[  176.498377]   moving 1 blocks
[  176.501483] patching 17 blocks to 20
[  176.525383]  writing 4 blocks of new data
[  176.528122] patching 14 blocks to 15
[  176.541380]   moving 1 blocks
[  176.542430] patching 1 blocks to 1
[  176.551991] patching 6 blocks to 7
[  176.566038]   moving 3 blocks
[  176.583495] patching 107 blocks to 161
[  176.749936]   moving 307 blocks
[  176.763935]  writing 2 blocks of new data
[  176.766674] patching 12 blocks to 12
[  176.783207] patching 16 blocks to 17
[  176.797993] patching 4 blocks to 4
[  176.809513]  writing 1 blocks of new data
[  176.812217] patching 14 blocks to 15
[  176.824509]  writing 102 blocks of new data
[  176.848067]   moving 1 blocks
[  176.881114] patching 674 blocks to 5
[  176.940188]  writing 3 blocks of new data
[  176.943519] patching 16 blocks to 17
[  176.957758]  writing 1 blocks of new data
[  176.960189] patching 12 blocks to 12
[  176.972124]  writing 226 blocks of new data
[  177.023836]   moving 260 blocks
[  177.036485]  writing 28 blocks of new data
[  177.042546]  writing 2 blocks of new data
[  177.043402]  writing 12 blocks of new data
[  177.046848]  writing 1 blocks of new data
[  177.047448]  writing 1 blocks of new data
[  177.048735]   moving 2 blocks
[  177.050014]   moving 1 blocks
[  177.052241] patching 12 blocks to 12
[  177.065994] patching 12 blocks to 12
[  177.101363] patching 125 blocks to 248
[  177.443196] patching 17 blocks to 17
[  177.457388]  writing 3 blocks of new data
[  177.463327] patching 16 blocks to 17
[  177.476602]  writing 62 blocks of new data
[  177.495794] patching 5 blocks to 5
[  177.510186] patching 10 blocks to 8
[  177.530578] patching 14 blocks to 15
[  177.552478]   moving 5 blocks
[  177.553142]  writing 49 blocks of new data
[  177.560267] patching 6 blocks to 6
[  177.574594] patching 5 blocks to 5
[  177.595116] patching 87 blocks to 74
[  177.660655] patching 8 blocks to 9
[  177.678411]   moving 1 blocks
[  177.680968] patching 14 blocks to 15
[  177.695911]   moving 1 blocks
[  177.696977]   moving 1 blocks
[  177.699901] patching 16 blocks to 17
[  177.712001]  writing 393 blocks of new data
[  177.774941] patching 16 blocks to 17
[  177.788296]  writing 7 blocks of new data
[  177.791636] patching 14 blocks to 15
[  177.806905] patching 24 blocks to 23
[  177.835438] patching 16 blocks to 17
[  177.851202] patching 32 blocks to 34
[  177.883303]  writing 10 blocks of new data
[  177.892769] patching 39 blocks to 44
[  177.932926]  writing 6 blocks of new data
[  177.934519] patching 3 blocks to 3
[  177.945840] patching 14 blocks to 15
[  177.958186]  writing 2 blocks of new data
[  177.959828] patching 5 blocks to 5
[  177.974096] patching 12 blocks to 12
[  178.011778]   moving 262 blocks
[  178.024877]  writing 4 blocks of new data
[  178.027822]  writing 30 blocks of new data
[  178.033268]  writing 4 blocks of new data
[  178.035165]   moving 1 blocks
[  178.035682]  writing 1 blocks of new data
[  178.036269]  writing 2 blocks of new data
[  178.037284] patching 2 blocks to 2
[  178.048179] patching 12 blocks to 12
[  178.060484]  writing 1 blocks of new data
[  178.061085]  writing 29 blocks of new data
[  178.068416]  writing 5 blocks of new data
[  178.071653] patching 16 blocks to 17
[  178.086208]  writing 54 blocks of new data
[  178.095106] patching 7 blocks to 6
[  178.110297] patching 8 blocks to 15
[  178.129088]   moving 1 blocks
[  178.130687]   moving 4 blocks
[  178.131672]  writing 9 blocks of new data
[  178.137033] patching 16 blocks to 17
[  178.152429] patching 14 blocks to 15
[  178.172157] patching 14 blocks to 15
[  178.186473] patching 3 blocks to 3
[  178.196366]   moving 1 blocks
[  178.197789] patching 4 blocks to 4
[  178.209624]  writing 1 blocks of new data
[  178.210738]   moving 1 blocks
[  178.212355] patching 1 blocks to 3
[  178.225168] patching 14 blocks to 15
[  178.238325]  writing 12 blocks of new data
[  178.241313] patching 12 blocks to 12
[  178.253545]  writing 2 blocks of new data
[  178.256498]   moving 1 blocks
[  178.257020]  writing 41 blocks of new data
[  178.262704]  writing 1 blocks of new data
[  178.263259]  writing 4 blocks of new data
[  178.263923]  writing 49 blocks of new data
[  178.278438]  writing 3 blocks of new data
[  178.281116] patching 12 blocks to 12
[  178.292800]  writing 40 blocks of new data
[  178.300216]  writing 1 blocks of new data
[  178.300853]  writing 3 blocks of new data
[  178.306439] patching 16 blocks to 17
[  178.320174]  writing 358 blocks of new data
[  178.375921] patching 14 blocks to 15
[  178.396369]   moving 1 blocks
[  178.396924]  writing 38 blocks of new data
[  178.405028] patching 3 blocks to 3
[  178.414304]  writing 116 blocks of new data
[  178.433660] patching 12 blocks to 12
[  178.449965] patching 19 blocks to 21
[  178.471400] patching 14 blocks to 15
[  178.633850] patching 3041 blocks to 5
[  178.649994] patching 106 blocks to 5
[  178.659826]  writing 7 blocks of new data
[  178.661706] patching 3 blocks to 3
[  178.674089]   moving 23 blocks
[  178.679161] patching 14 blocks to 15
[  179.570783] stashing 6206 overlapping blocks to 75391a4a1de9becb5d9e432ea34efb77fb83bec2
[  179.570817] 254480384 bytes free on /cache (25419776 needed)
[  179.570893]  writing 6206 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/75391a4a1de9becb5d9e432ea34efb77fb83bec2
[  180.207857] patching 6206 blocks to 9351
[  186.520791] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/75391a4a1de9becb5d9e432ea34efb77fb83bec2
[  187.310912] patching 4 blocks to 4
[  187.322423]  writing 1 blocks of new data
[  187.341104] patching 367 blocks to 10
[  187.353288]  writing 2 blocks of new data
[  187.354983] patching 3 blocks to 3
[  187.364872]   moving 2 blocks
[  187.365424]  writing 48 blocks of new data
[  187.377400] patching 17 blocks to 17
[  187.391875] patching 3 blocks to 3
[  187.401120]  writing 1 blocks of new data
[  187.402265]   moving 1 blocks
[  187.403307]   moving 1 blocks
[  187.403869]  writing 30 blocks of new data
[  187.408260]  writing 7 blocks of new data
[  187.409304]  writing 11 blocks of new data
[  187.414150]  writing 1 blocks of new data
[  187.414710]  writing 149 blocks of new data
[  187.458349] patching 173 blocks to 190
[  187.507780]  writing 10 blocks of new data
[  187.512511] patching 14 blocks to 15
[  187.524779]  writing 22 blocks of new data
[  187.574926]   moving 403 blocks
[  187.592117] patching 12 blocks to 12
[  187.606205]  writing 14 blocks of new data
[  187.619369] patching 78 blocks to 94
[  187.713388]  writing 4 blocks of new data
[  187.716895]   moving 5 blocks
[  187.850056] patching 1167 blocks to 1149
[  189.028592] patching 12 blocks to 12
[  189.041217] patching 1 blocks to 1
[  189.049915]  writing 64 blocks of new data
[  189.061815] patching 1 blocks to 1
[  189.070917]   moving 1 blocks
[  189.071446]  writing 1 blocks of new data
[  189.074096]   moving 13 blocks
[  189.077142] patching 1 blocks to 1
[  189.085863]  writing 3 blocks of new data
[  189.086826]  writing 6 blocks of new data
[  189.088121]   moving 1 blocks
[  189.090758] patching 14 blocks to 15
[  189.104550]  writing 1 blocks of new data
[  189.106240] patching 5 blocks to 5
[  189.119501] patching 5 blocks to 5
[  189.130984]  writing 16 blocks of new data
[  189.133885] patching 12 blocks to 12
[  189.147782]  writing 4 blocks of new data
[  189.149632]  writing 214 blocks of new data
[  189.185189]  writing 112 blocks of new data
[  189.202146]  writing 1 blocks of new data
[  189.202695]  writing 76 blocks of new data
[  189.218456]   moving 2 blocks
[  189.219433]   moving 1 blocks
[  189.221085] patching 3 blocks to 3
[  189.230261] patching 1 blocks to 1
[  189.240875] patching 12 blocks to 12
[  189.253930] patching 4 blocks to 4
[  189.266764] patching 4 blocks to 4
[  189.278286]  writing 2 blocks of new data
[  189.279241]  writing 4 blocks of new data
[  189.281968] patching 7 blocks to 8
[  189.296262] patching 3 blocks to 3
[  189.307667] patching 14 blocks to 15
[  189.322209] patching 4 blocks to 4
[  189.333123]   moving 2 blocks
[  189.335262]   moving 9 blocks
[  189.338478] patching 4 blocks to 4
[  189.350409]  writing 1 blocks of new data
[  189.351027]  writing 33 blocks of new data
[  189.365077] patching 29 blocks to 71
[  189.474109]  writing 418 blocks of new data
[  189.539971]   moving 1 blocks
[  189.541543] patching 5 blocks to 4
[  189.552954]  writing 1 blocks of new data
[  189.558419] patching 30 blocks to 40
[  189.594649] patching 7 blocks to 7
[  189.608142]   moving 1 blocks
[  189.610861] patching 14 blocks to 15
[  189.646426] patching 39 blocks to 306
[  190.071478] patching 14 blocks to 15
[  190.086497] patching 3 blocks to 3
[  190.095785]  writing 1 blocks of new data
[  190.103196] patching 12 blocks to 12
[  190.116347]  writing 233 blocks of new data
[  190.283267] patching 1103 blocks to 1132
[  191.054941] patching 15 blocks to 10
[  191.070861]  writing 1 blocks of new data
[  191.076453] patching 70 blocks to 6
[  191.086467]  writing 1 blocks of new data
[  191.087666]   moving 1 blocks
[  191.089612] patching 8 blocks to 9
[  191.105456]   moving 1 blocks
[  191.106697]   moving 1 blocks
[  191.110712]   moving 27 blocks
[  191.116582] patching 16 blocks to 17
[  191.129877] patching 5 blocks to 6
[  191.142493] patching 3 blocks to 3
[  191.153650] patching 12 blocks to 12
[  191.168744]   moving 12 blocks
[  191.170912]  writing 95 blocks of new data
[  191.191270]   moving 4 blocks
[  191.191913]  writing 1 blocks of new data
[  191.192510]  writing 102 blocks of new data
[  191.210668] patching 7 blocks to 7
[  191.223943] patching 8 blocks to 5
[  191.234565]   moving 5 blocks
[  191.235988] patching 1 blocks to 1
[  191.246638] patching 14 blocks to 15
[  191.260203]   moving 1 blocks
[  191.260773]  writing 8 blocks of new data
[  191.266000] patching 16 blocks to 17
[  191.280350]  writing 1 blocks of new data
[  191.282020] patching 5 blocks to 5
[  191.293752]  writing 5 blocks of new data
[  191.295791]   moving 3 blocks
[  191.296394]  writing 353 blocks of new data
[  191.350654]   moving 1 blocks
[  191.352869] patching 12 blocks to 12
[  191.368149] patching 14 blocks to 15
[  191.381296]   moving 2 blocks
[  191.385335] patching 28 blocks to 26
[  191.412150] patching 1 blocks to 1
[  191.421379]   moving 1 blocks
[  191.422442]   moving 1 blocks
[  191.422958]  writing 1 blocks of new data
[  191.423564]  writing 8 blocks of new data
[  191.426200]   moving 1 blocks
[  191.426714]  writing 4 blocks of new data
[  191.429175]   moving 10 blocks
[  191.430447]   moving 1 blocks
[  191.431001]  writing 10 blocks of new data
[  191.434702]   moving 5 blocks
[  191.438724] patching 19 blocks to 24
[  191.456944] patching 41 blocks to 45
[  191.490689]   moving 1 blocks
[  191.493564] patching 16 blocks to 17
[  191.509254] patching 16 blocks to 17
[  191.526747] patching 21 blocks to 27
[  191.556378]  writing 7 blocks of new data
[  191.559479]   moving 1 blocks
[  191.560043]  writing 4 blocks of new data
[  191.561356]   moving 1 blocks
[  191.564195] patching 16 blocks to 17
[  191.578545] patching 3 blocks to 3
[  191.587635]  writing 7 blocks of new data
[  191.588779]  writing 1 blocks of new data
[  191.589914]   moving 1 blocks
[  191.590979]   moving 1 blocks
[  191.592120] patching 1 blocks to 1
[  191.601974]   moving 2 blocks
[  191.604005]   moving 6 blocks
[  191.604989]  writing 8 blocks of new data
[  191.606899] patching 10 blocks to 8
[  191.621989] patching 5 blocks to 4
[  191.635130]   moving 6 blocks
[  191.636580]  writing 4 blocks of new data
[  191.639891] patching 16 blocks to 17
[  191.654961]   moving 3 blocks
[  191.656659]   moving 6 blocks
[  191.658153]   moving 1 blocks
[  191.658673]  writing 4 blocks of new data
[  191.660199] patching 17 blocks to 5
[  191.670075]  writing 5 blocks of new data
[  191.674766] patching 16 blocks to 17
[  191.687423]   moving 1 blocks
[  191.687945]  writing 18 blocks of new data
[  191.691398]  writing 1 blocks of new data
[  191.691992]  writing 571 blocks of new data
[  191.838671] patching 8 blocks to 8
[  191.875880]   moving 3 blocks
[  191.898144]  writing 5 blocks of new data
[  191.922239] patching 13 blocks to 17
[  191.965835]  writing 4 blocks of new data
[  191.990782] patching 2 blocks to 2
[  192.022388]  writing 1 blocks of new data
[  192.048844] patching 12 blocks to 12
[  192.071894] patching 12 blocks to 12
[  192.085656]  writing 1 blocks of new data
[  192.086306]  writing 1 blocks of new data
[  192.087388]   moving 1 blocks
[  192.088892]   moving 3 blocks
[  192.089999]   moving 1 blocks
[  192.090553]  writing 52 blocks of new data
[  192.122826]   moving 220 blocks
[  192.133877]  writing 14 blocks of new data
[  192.137533]   moving 1 blocks
[  192.138041]  writing 1 blocks of new data
[  192.138996]   moving 1 blocks
[  192.141559] patching 14 blocks to 15
[  192.164047] patching 14 blocks to 15
[  192.179651] patching 12 blocks to 24
[  192.206206]   moving 1 blocks
[  192.208339] patching 10 blocks to 9
[  192.224584] patching 5 blocks to 5
[  192.236138]  writing 1 blocks of new data
[  192.243478]   moving 48 blocks
[  192.246899]   moving 1 blocks
[  192.247453]  writing 33 blocks of new data
[  192.254304] patching 12 blocks to 12
[  192.278034] patching 2 blocks to 159
[  192.347850] patching 5 blocks to 5
[  192.360556]   moving 2 blocks
[  192.361418]   moving 1 blocks
[  192.363009] patching 5 blocks to 5
[  192.374212]  writing 230 blocks of new data
[  192.401866] patching 11 blocks to 13
[  192.419506]  writing 5 blocks of new data
[  192.421807]  writing 20 blocks of new data
[  192.429919] patching 8 blocks to 9
[  192.446737]   moving 11 blocks
[  192.455990] patching 106 blocks to 6
[  192.465949]  writing 37 blocks of new data
[  192.474609] patching 5 blocks to 4
[  192.486441]  writing 4 blocks of new data
[  192.489078] patching 12 blocks to 12
[  192.504072]   moving 9 blocks
[  192.505548]   moving 1 blocks
[  192.507870] patching 12 blocks to 12
[  192.520378]   moving 1 blocks
[  192.520900]  writing 4 blocks of new data
[  192.524113] patching 16 blocks to 17
[  192.538410]  writing 8 blocks of new data
[  192.539277]  writing 1 blocks of new data
[  192.540923]   moving 5 blocks
[  192.542438] patching 1 blocks to 1
[  192.551108]  writing 25 blocks of new data
[  192.559076] patching 16 blocks to 17
[  192.573035]   moving 12 blocks
[  192.574964]   moving 1 blocks
[  192.575945]   moving 1 blocks
[  192.576464]  writing 4 blocks of new data
[  192.579443] patching 16 blocks to 17
[  192.593868]  writing 107 blocks of new data
[  192.605806] patching 3 blocks to 3
[  192.621590] patching 6 blocks to 90
[  192.716194] patching 12 blocks to 12
[  192.729227]   moving 1 blocks
[  192.729727]  writing 5 blocks of new data
[  192.730720]  writing 5 blocks of new data
[  192.731659]  writing 74 blocks of new data
[  192.744008]  writing 22 blocks of new data
[  192.748929]   moving 1 blocks
[  192.749479]  writing 7 blocks of new data
[  192.751486] patching 5 blocks to 5
[  192.773150] patching 2 blocks to 136
[  192.834519]  writing 358 blocks of new data
[  192.900561] patching 5 blocks to 5
[  192.913611] patching 5 blocks to 5
[  192.927442] patching 11 blocks to 11
[  192.944277]  writing 2 blocks of new data
[  192.946140] patching 6 blocks to 6
[  192.959583]   moving 1 blocks
[  192.960168]  writing 51 blocks of new data
[  192.970560]   moving 1 blocks
[  192.971997]   moving 4 blocks
[  192.973368] patching 6 blocks to 5
[  192.985503]  writing 1 blocks of new data
[  193.019778] patching 316 blocks to 308
[  193.253199] patching 8 blocks to 5
[  193.277154] patching 28 blocks to 51
[  193.325563]  writing 1 blocks of new data
[  193.327089]   moving 1 blocks
[  193.329485] patching 5 blocks to 5
[  193.346587] patching 15 blocks to 17
[  193.370844]   moving 14 blocks
[  193.376720] patching 19 blocks to 19
[  193.402965] patching 12 blocks to 12
[  193.415682]  writing 6 blocks of new data
[  193.417144]  writing 26 blocks of new data
[  193.427834] patching 16 blocks to 17
[  193.441186]  writing 6 blocks of new data
[  193.442750]  writing 28 blocks of new data
[  193.451838]   moving 1 blocks
[  193.452467]  writing 4 blocks of new data
[  193.454641] patching 1 blocks to 1
[  193.469186] patching 14 blocks to 15
[  193.489115]   moving 24 blocks
[  193.497262] patching 17 blocks to 17
[  193.513123]   moving 1 blocks
[  193.513724]  writing 4 blocks of new data
[  193.518754]   moving 4 blocks
[  193.522807] patching 12 blocks to 12
[  193.540138]   moving 36 blocks
[  193.546954]  writing 1 blocks of new data
[  193.548083] patching 1 blocks to 1
[  193.557543]   moving 1 blocks
[  193.558599] patching 3 blocks to 3
[  193.568914] patching 5 blocks to 6
[  193.581655]   moving 2 blocks
[  193.582209]  writing 77 blocks of new data
[  193.595009] patching 4 blocks to 4
[  193.606596]  writing 318 blocks of new data
[  193.655789]  writing 2 blocks of new data
[  193.657230] patching 1 blocks to 1
[  193.666954]   moving 3 blocks
[  193.671511]   moving 28 blocks
[  193.675775]  writing 10 blocks of new data
[  193.683292] patching 16 blocks to 17
[  193.697416] patching 4 blocks to 4
[  193.710639] patching 12 blocks to 12
[  193.726345] patching 17 blocks to 17
[  193.739533]  writing 418 blocks of new data
[  193.798260] patching 2 blocks to 2
[  193.838534] patching 171 blocks to 346
[  194.189509]  writing 1 blocks of new data
[  194.190502]   moving 1 blocks
[  194.193985]   moving 1 blocks
[  194.199226] patching 12 blocks to 12
[  194.211859]  writing 528 blocks of new data
[  194.309335]  writing 1 blocks of new data
[  194.309922]  writing 25 blocks of new data
[  194.315389]  writing 80 blocks of new data
[  194.336602] patching 37 blocks to 37
[  194.376675]  writing 10 blocks of new data
[  194.380234] patching 16 blocks to 17
[  194.394116]  writing 1 blocks of new data
[  194.437974] patching 915 blocks to 12
[  194.452186]  writing 1 blocks of new data
[  194.455243] patching 15 blocks to 19
[  194.476167] patching 1 blocks to 1
[  194.484843]  writing 37 blocks of new data
[  194.492499]  writing 38 blocks of new data
[  194.498108]  writing 16 blocks of new data
[  194.503103]  writing 17 blocks of new data
[  194.506270]  writing 2 blocks of new data
[  194.507211]   moving 1 blocks
[  194.509133] patching 12 blocks to 12
[  194.531358] patching 51 blocks to 83
[  195.010430]  writing 3 blocks of new data
[  195.015759] patching 14 blocks to 16
[  195.039816] patching 5 blocks to 4
[  195.057103] patching 16 blocks to 17
[  195.071706]  writing 5 blocks of new data
[  195.075364] patching 7 blocks to 7
[  195.092392] patching 17 blocks to 17
[  195.108835] patching 4 blocks to 4
[  195.121804]   moving 2 blocks
[  195.127324] patching 16 blocks to 17
[  195.143081] patching 6 blocks to 5
[  195.159840] patching 12 blocks to 12
[  195.172653]  writing 10 blocks of new data
[  195.178676]  writing 5 blocks of new data
[  195.181931] patching 6 blocks to 5
[  195.195418]  writing 10 blocks of new data
[  195.200315] patching 4 blocks to 4
[  195.212614]  writing 82 blocks of new data
[  195.229532] patching 14 blocks to 15
[  195.245992] patching 1 blocks to 13
[  195.268145]   moving 8 blocks
[  195.293332] patching 246 blocks to 7
[  195.303552]  writing 7 blocks of new data
[  195.305003]   moving 1 blocks
[  195.306375] patching 4 blocks to 3
[  195.341292] patching 215 blocks to 215
[  195.380606] patching 203 blocks to 5
[  195.390131]  writing 47 blocks of new data
[  195.400267] patching 16 blocks to 17
[  195.414340] patching 14 blocks to 15
[  195.428461]  writing 45 blocks of new data
[  195.436697]  writing 1 blocks of new data
[  195.438729]   moving 8 blocks
[  195.440024]   moving 1 blocks
[  195.442947] patching 16 blocks to 17
[  195.459403] patching 14 blocks to 15
[  195.473786] patching 4 blocks to 6
[  195.487908] patching 3 blocks to 3
[  195.498517] patching 14 blocks to 15
[  195.511113]   moving 1 blocks
[  195.512397]   moving 2 blocks
[  195.512955]  writing 33 blocks of new data
[  195.518837] patching 1 blocks to 1
[  195.529443] patching 14 blocks to 15
[  195.608204] patching 509 blocks to 698
[  196.518164]  writing 6 blocks of new data
[  196.521964] patching 17 blocks to 17
[  196.567349] patching 300 blocks to 261
[  196.862960]   moving 1 blocks
[  196.867939] patching 14 blocks to 15
[  196.941405]   moving 408 blocks
[  196.958476]  writing 575 blocks of new data
[  197.043444] patching 6 blocks to 6
[  197.056379]  writing 1 blocks of new data
[  197.057962] patching 7 blocks to 6
[  197.073293] patching 16 blocks to 17
[  197.096483]   moving 65 blocks
[  197.105098] patching 14 blocks to 15
[  197.119882]   moving 5 blocks
[  197.125729] patching 31 blocks to 44
[  197.250023]   moving 896 blocks
[  197.285375] patching 4 blocks to 5
[  197.297061]  writing 10 blocks of new data
[  197.301180] patching 7 blocks to 7
[  197.313491]   moving 1 blocks
[  197.316473]   moving 15 blocks
[  197.317672]  writing 39 blocks of new data
[  197.326291]   moving 1 blocks
[  197.328309] patching 10 blocks to 9
[  197.338342]  writing 32 blocks of new data
[  197.345515] patching 14 blocks to 15
[  197.361611] patching 14 blocks to 15
[  197.382227]  writing 52 blocks of new data
[  197.395377]  writing 128 blocks of new data
[  197.417912]   moving 1 blocks
[  197.420443] patching 12 blocks to 13
[  197.437826]  writing 5 blocks of new data
[  197.440696] patching 12 blocks to 12
[  197.454199] patching 3 blocks to 3
[  197.463320]  writing 4 blocks of new data
[  197.466918] patching 18 blocks to 19
[  197.490416] patching 4 blocks to 6
[  197.503961]  writing 20 blocks of new data
[  197.509904]  writing 9 blocks of new data
[  197.512505]  writing 3 blocks of new data
[  197.515025] patching 10 blocks to 10
[  197.564320]   moving 295 blocks
[  197.578362]   moving 1 blocks
[  197.591257]   moving 101 blocks
[  197.613554] patching 127 blocks to 134
[  197.729090] patching 12 blocks to 12
[  197.785382]   moving 155 blocks
[  197.816997]   moving 28 blocks
[  197.825668] patching 33 blocks to 35
[  197.861250] patching 16 blocks to 17
[  197.877583] patching 12 blocks to 12
[  197.891093]  writing 38 blocks of new data
[  197.896878]  writing 5 blocks of new data
[  197.901269]   moving 8 blocks
[  197.903752] patching 12 blocks to 12
[  197.915744]  writing 2 blocks of new data
[  197.918695] patching 16 blocks to 17
[  197.930711]  writing 135 blocks of new data
[  197.957141] patching 12 blocks to 12
[  197.969955] patching 3 blocks to 3
[  197.983307]   moving 27 blocks
[  197.986549]  writing 796 blocks of new data
[  198.093663] patching 16 blocks to 17
[  198.109229] patching 4 blocks to 4
[  198.121902]   moving 4 blocks
[  198.123048]  writing 5 blocks of new data
[  198.124165]   moving 1 blocks
[  198.124753]  writing 1 blocks of new data
[  198.125337]  writing 1024 blocks of new data
[  198.252162]  writing 1024 blocks of new data
[  198.380073]  writing 1024 blocks of new data
[  198.549115]  writing 1024 blocks of new data
[  198.699417]  writing 1024 blocks of new data
[  198.846492]  writing 718 blocks of new data
[  198.968456]   moving 9 blocks
[  198.971492]  writing 6 blocks of new data
[  198.975891] patching 7 blocks to 8
[  198.991258]  writing 4 blocks of new data
[  198.993639]   moving 6 blocks
[  199.000857] patching 47 blocks to 51
[  199.032212] patching 12 blocks to 12
[  199.047164] patching 7 blocks to 7
[  199.062041] patching 18 blocks to 19
[  199.084575]  writing 2 blocks of new data
[  199.085202]  writing 5 blocks of new data
[  199.087254] patching 5 blocks to 5
[  199.099287] patching 1 blocks to 1
[  199.108244]  writing 11 blocks of new data
[  199.109368]  writing 1 blocks of new data
[  199.112325] patching 16 blocks to 17
[  199.128079]   moving 13 blocks
[  199.129216]  writing 63 blocks of new data
[  199.142497] patching 14 blocks to 15
[  199.156473] patching 14 blocks to 15
[  199.168997]   moving 1 blocks
[  199.170953] patching 5 blocks to 5
[  199.183091]   moving 1 blocks
[  199.183662]  writing 1 blocks of new data
[  199.187492] patching 12 blocks to 12
[  199.201851]   moving 1 blocks
[  199.202401]  writing 18 blocks of new data
[  199.205906]   moving 1 blocks
[  199.206457]  writing 566 blocks of new data
[  199.289770]  writing 81 blocks of new data
[  199.300126]  writing 9 blocks of new data
[  199.304122] patching 12 blocks to 12
[  199.316844]   moving 3 blocks
[  199.317995]   moving 1 blocks
[  199.318507]  writing 4 blocks of new data
[  199.324291] patching 76 blocks to 6
[  199.334247]  writing 8 blocks of new data
[  199.338915] patching 8 blocks to 8
[  199.353618]  writing 113 blocks of new data
[  199.372697] patching 16 blocks to 17
[  199.387348] patching 3 blocks to 3
[  199.396701]  writing 1024 blocks of new data
[  199.537667]  writing 9 blocks of new data
[  199.539028]  writing 30 blocks of new data
[  199.546011]   moving 1 blocks
[  199.550334] patching 30 blocks to 41
[  199.586509] patching 14 blocks to 15
[  199.607736] patching 63 blocks to 78
[  199.672219] patching 7 blocks to 9
[  199.688880]  writing 11 blocks of new data
[  199.707838]   moving 152 blocks
[  199.720022] patching 23 blocks to 24
[  199.750215] patching 6 blocks to 7
[  199.764605] patching 12 blocks to 12
[  199.799148] patching 172 blocks to 179
[  199.957785] patching 25 blocks to 16
[  200.019864]   moving 135 blocks
[  200.039148]  writing 13 blocks of new data
[  200.042999] patching 4 blocks to 4
[  200.056483] patching 12 blocks to 12
[  200.069287]   moving 1 blocks
[  200.069812]  writing 1 blocks of new data
[  200.072270]   moving 12 blocks
[  200.076978] patching 12 blocks to 12
[  200.091474] patching 5 blocks to 5
[  200.104582] patching 5 blocks to 5
[  200.121816] patching 36 blocks to 48
[  200.165687] patching 3 blocks to 5
[  200.179456]  writing 8 blocks of new data
[  200.183771] patching 12 blocks to 12
[  200.198640] patching 12 blocks to 12
[  200.210786]  writing 3 blocks of new data
[  200.219981] patching 46 blocks to 49
[  200.280162] patching 5 blocks to 5
[  200.314763] patching 5 blocks to 5
[  201.361334] patching 20710 blocks to 305
[  201.720972] patching 14 blocks to 15
[  201.754931]  writing 4 blocks of new data
[  201.787786]   moving 45 blocks
[  201.820410] patching 17 blocks to 17
[  201.853491]  writing 2 blocks of new data
[  201.875503]  writing 8 blocks of new data
[  201.897859]  writing 10 blocks of new data
[  201.922223]   moving 1 blocks
[  201.944962]  writing 458 blocks of new data
[  202.141181]  writing 341 blocks of new data
[  202.276619]   moving 2 blocks
[  202.338695]   moving 338 blocks
[  202.444090]   moving 1 blocks
[  202.468793] patching 16 blocks to 17
[  202.508587] patching 12 blocks to 12
[  202.546773] patching 12 blocks to 12
[  202.587659] patching 21 blocks to 23
[  202.629243] patching 1 blocks to 2
[  202.665570] patching 25 blocks to 16
[  202.712957]  writing 563 blocks of new data
[  202.929371]   moving 1 blocks
[  202.953595] patching 15 blocks to 17
[  202.998249]  writing 47 blocks of new data
[  203.029055]  writing 3 blocks of new data
[  203.053244]   moving 1 blocks
[  203.100656] patching 175 blocks to 267
[  203.489331]  writing 29 blocks of new data
[  203.521383]   moving 22 blocks
[  203.549793]   moving 24 blocks
[  203.566376]   moving 123 blocks
[  203.574507]  writing 5 blocks of new data
[  203.576823] patching 5 blocks to 4
[  203.590485] patching 12 blocks to 12
[  203.604201] patching 17 blocks to 17
[  203.618632]  writing 5 blocks of new data
[  203.621973] patching 16 blocks to 17
[  203.635933] patching 14 blocks to 15
[  203.649985]  writing 124 blocks of new data
[  203.671304]  writing 2 blocks of new data
[  203.673743] patching 9 blocks to 10
[  203.692153] patching 16 blocks to 17
[  203.736275]   moving 262 blocks
[  203.750024]   moving 3 blocks
[  203.753237] patching 16 blocks to 17
[  203.767993]  writing 1 blocks of new data
[  203.768624]  writing 55 blocks of new data
[  203.784591] patching 66 blocks to 71
[  203.859129] patching 37 blocks to 5
[  203.868454]  writing 41 blocks of new data
[  203.876512] patching 16 blocks to 17
[  203.891499] patching 5 blocks to 5
[  203.903386]  writing 29 blocks of new data
[  203.911855] patching 1 blocks to 1
[  203.922136]   moving 8 blocks
[  203.925386]   moving 1 blocks
[  203.940027] patching 111 blocks to 121
[  204.066446] patching 3 blocks to 5
[  204.104479] patching 75 blocks to 74
[  204.177889] patching 8 blocks to 9
[  204.194573] patching 4 blocks to 6
[  204.209131] patching 5 blocks to 4
[  204.221558]  writing 12 blocks of new data
[  204.225383] patching 5 blocks to 5
[  204.239728]   moving 12 blocks
[  204.243461]   moving 1 blocks
[  204.245187] patching 5 blocks to 6
[  204.260455] patching 10 blocks to 5
[  204.271299]   moving 1 blocks
[  204.271931]  writing 31 blocks of new data
[  204.286406] patching 17 blocks to 17
[  204.304228] patching 16 blocks to 17
[  204.321232] patching 12 blocks to 12
[  204.348704] patching 46 blocks to 53
[  204.402734]  writing 121 blocks of new data
[  204.432601] patching 111 blocks to 121
[  204.544971]  writing 29 blocks of new data
[  204.552192]  writing 1 blocks of new data
[  204.553955] patching 5 blocks to 5
[  204.566957]   moving 1 blocks
[  204.568514] patching 5 blocks to 5
[  204.579704]  writing 2 blocks of new data
[  204.580314]  writing 6 blocks of new data
[  204.582201]   moving 3 blocks
[  204.583066]  writing 1 blocks of new data
[  204.583638]  writing 22 blocks of new data
[  204.586422]  writing 21 blocks of new data
[  204.592839]   moving 24 blocks
[  204.596542]   moving 1 blocks
[  204.597057]  writing 39 blocks of new data
[  204.606551]   moving 3 blocks
[  204.622460] patching 125 blocks to 124
[  204.744043]   moving 1 blocks
[  204.777778] patching 32 blocks to 46
[  204.831873]  writing 1 blocks of new data
[  205.065922] patching 1431 blocks to 2364
[  207.683717]  writing 2 blocks of new data
[  207.685083]   moving 1 blocks
[  207.685633]  writing 5 blocks of new data
[  207.686350]  writing 1 blocks of new data
[  207.687933]   moving 5 blocks
[  207.688830]  writing 1 blocks of new data
[  207.689418]  writing 4 blocks of new data
[  207.691317] patching 2 blocks to 2
[  207.700799]  writing 58 blocks of new data
[  207.709386]   moving 1 blocks
[  207.714658] patching 42 blocks to 26
[  207.756828] patching 142 blocks to 148
[  207.846157] patching 7 blocks to 7
[  207.859420] patching 7 blocks to 7
[  207.872379] patching 5 blocks to 5
[  207.884844]   moving 4 blocks
[  207.924134] patching 326 blocks to 331
[  208.092390] patching 17 blocks to 18
[  208.114141] patching 3 blocks to 3
[  208.126206] patching 16 blocks to 17
[  208.141151] patching 1 blocks to 1
[  208.149843]  writing 1 blocks of new data
[  208.151748] patching 5 blocks to 5
[  208.163151]  writing 5 blocks of new data
[  208.164363]  writing 1 blocks of new data
[  208.167303] patching 17 blocks to 17
[  208.179468]  writing 1 blocks of new data
[  208.180066]  writing 4 blocks of new data
[  208.181900]   moving 2 blocks
[  208.197573] patching 123 blocks to 126
[  208.301098] patching 7 blocks to 6
[  208.316120] patching 12 blocks to 12
[  208.331630]   moving 27 blocks
[  208.334293]  writing 16 blocks of new data
[  208.339729]  writing 23 blocks of new data
[  208.362021] patching 61 blocks to 242
[  208.548033] patching 12 blocks to 12
[  208.562237]  writing 21 blocks of new data
[  208.569448]   moving 6 blocks
[  208.570577]  writing 1 blocks of new data
[  208.574819] patching 12 blocks to 12
[  208.590460] patching 5 blocks to 5
[  208.605377] patching 10 blocks to 11
[  208.623111]  writing 10 blocks of new data
[  209.029170] patching 2092 blocks to 3318
[  211.310673]  writing 1008 blocks of new data
[  211.554641] patching 82 blocks to 85
[  211.614874] patching 16 blocks to 17
[  211.912176] patching 2087 blocks to 3078
[  223.891784]  writing 38 blocks of new data
[  223.900042]   moving 8 blocks
[  223.958901] patching 1161 blocks to 12
[  223.974694] patching 3 blocks to 3
[  223.984010]  writing 12 blocks of new data
[  223.987248]  writing 1 blocks of new data
[  224.009592] patching 191 blocks to 199
[  224.052047]  writing 2 blocks of new data
[  224.052687]  writing 1 blocks of new data
[  224.054248] patching 4 blocks to 4
[  224.066254]   moving 1 blocks
[  224.067897] patching 5 blocks to 5
[  224.078015]   moving 1 blocks
[  224.078534]  writing 9 blocks of new data
[  224.079413]  writing 279 blocks of new data
[  224.130958]   moving 22 blocks
[  224.134893] patching 16 blocks to 17
[  224.149572] patching 1 blocks to 2
[  224.159371]  writing 4 blocks of new data
[  224.164213] patching 12 blocks to 12
[  224.176241]  writing 50 blocks of new data
[  224.184717]  writing 10 blocks of new data
[  224.191540] patching 16 blocks to 17
[  224.203610]  writing 12 blocks of new data
[  224.206687]   moving 1 blocks
[  224.208436] patching 9 blocks to 10
[  224.226488] patching 5 blocks to 5
[  224.240038] patching 7 blocks to 7
[  224.253755] patching 14 blocks to 15
[  224.267096]   moving 1 blocks
[  224.268549] patching 8 blocks to 5
[  224.278281]  writing 5 blocks of new data
[  224.279290]  writing 5 blocks of new data
[  224.280228]  writing 45 blocks of new data
[  224.290005]   moving 1 blocks
[  224.293150] patching 16 blocks to 17
[  224.309467] patching 16 blocks to 17
[  224.321471]  writing 3 blocks of new data
[  224.322412]  writing 1 blocks of new data
[  224.322966]  writing 1 blocks of new data
[  224.324091]   moving 1 blocks
[  224.348002] stashing 42 overlapping blocks to 670d1617ba338b5528439faca306ac3bf293b701
[  224.348034] 254480384 bytes free on /cache (172032 needed)
[  224.348414]  writing 42 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/670d1617ba338b5528439faca306ac3bf293b701
[  224.355294] patching 42 blocks to 333
[  224.793838] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/670d1617ba338b5528439faca306ac3bf293b701
[  224.831354]  writing 4 blocks of new data
[  224.845986] patching 42 blocks to 42
[  224.873413]  writing 9 blocks of new data
[  224.892723]   moving 51 blocks
[  224.902344] patching 12 blocks to 12
[  224.914473]  writing 3 blocks of new data
[  224.916696]  writing 1 blocks of new data
[  224.918320] patching 4 blocks to 5
[  224.952146] patching 174 blocks to 193
[  225.027256]   moving 222 blocks
[  226.250202] patching 6896 blocks to 11974
[  239.635498] patching 8192 blocks to 8192
[  247.687295] patching 16 blocks to 17
[  247.701311]   moving 1 blocks
[  247.704186] patching 16 blocks to 17
[  247.719209]   moving 1 blocks
[  247.719762]  writing 161 blocks of new data
[  247.746267] patching 16 blocks to 17
[  247.760463]   moving 2 blocks
[  247.763372] patching 14 blocks to 15
[  247.786391]   moving 1 blocks
[  247.786930]  writing 1 blocks of new data
[  247.788064] patching 3 blocks to 3
[  247.797381]  writing 17 blocks of new data
[  247.802766]   moving 1 blocks
[  247.803786]   moving 1 blocks
[  247.804315]  writing 1 blocks of new data
[  247.805836] patching 4 blocks to 4
[  247.819703] patching 10 blocks to 12
[  247.835653] patching 1 blocks to 1
[  247.845152] patching 8 blocks to 5
[  247.855025]   moving 1 blocks
[  247.860110] patching 32 blocks to 34
[  247.910869] patching 123 blocks to 139
[  248.459202]  writing 32 blocks of new data
[  248.476741]   moving 1 blocks
[  248.477361]  writing 7 blocks of new data
[  248.480760] patching 4 blocks to 4
[  248.493991]   moving 1 blocks
[  248.498141] patching 12 blocks to 12
[  248.513700] patching 2 blocks to 8
[  248.560685] patching 12 blocks to 12
[  248.579479] patching 59 blocks to 60
[  248.633295]  writing 28 blocks of new data
[  248.641757] patching 5 blocks to 6
[  248.654186]   moving 1 blocks
[  248.656554] patching 12 blocks to 12
[  248.670027]  writing 5 blocks of new data
[  248.670813]  writing 9 blocks of new data
[  248.671672]  writing 28 blocks of new data
[  248.677796]   moving 1 blocks
[  248.678974] patching 2 blocks to 2
[  248.689652]   moving 13 blocks
[  248.690561]  writing 34 blocks of new data
[  248.697492] patching 6 blocks to 5
[  248.710115]  writing 27 blocks of new data
[  248.718712] patching 16 blocks to 17
[  248.730678]  writing 3 blocks of new data
[  248.731613]  writing 1 blocks of new data
[  248.733753] patching 9 blocks to 10
[  248.752235] patching 12 blocks to 12
[  248.766221]   moving 1 blocks
[  248.768496] patching 14 blocks to 16
[  248.790323]   moving 4 blocks
[  248.793271] patching 14 blocks to 15
[  248.807750] patching 1 blocks to 1
[  248.818130] patching 7 blocks to 11
[  248.830576]   moving 8 blocks
[  248.831371]  writing 1024 blocks of new data
[  248.991962]  writing 1024 blocks of new data
[  249.129822]  writing 396 blocks of new data
[  249.191932]  writing 18 blocks of new data
[  249.197523]  writing 14 blocks of new data
[  249.203457] patching 1 blocks to 1
[  249.212262]  writing 1 blocks of new data
[  249.216330]   moving 26 blocks
[  249.219432]  writing 769 blocks of new data
[  249.332432] patching 20 blocks to 41
[  249.434486] patching 2 blocks to 9
[  249.482900]  writing 837 blocks of new data
[  249.664159]  writing 3 blocks of new data
[  249.665978] patching 5 blocks to 4
[  249.678752]   moving 1 blocks
[  249.680336] patching 5 blocks to 6
[  249.692617]  writing 9 blocks of new data
[  249.693505]  writing 18 blocks of new data
[  249.698032]  writing 6 blocks of new data
[  249.699655] patching 1 blocks to 1
[  249.708549]  writing 1 blocks of new data
[  249.711476] patching 16 blocks to 17
[  249.724662] patching 5 blocks to 5
[  249.736921]  writing 70 blocks of new data
[  249.750837] patching 14 blocks to 15
[  249.765174]  writing 5 blocks of new data
[  249.768901] patching 16 blocks to 17
[  249.784754]   moving 14 blocks
[  249.785885]  writing 20 blocks of new data
[  249.791058]  writing 41 blocks of new data
[  249.799097] patching 9 blocks to 9
[  249.810135] patching 5 blocks to 9
[  249.827061]   moving 1 blocks
[  249.838061] patching 44 blocks to 113
[  249.901189]   moving 11 blocks
[  249.902896]   moving 2 blocks
[  249.905150]   moving 10 blocks
[  249.909718] patching 14 blocks to 15
[  249.926232]   moving 18 blocks
[  249.929921] patching 16 blocks to 17
[  249.946665] patching 16 blocks to 17
[  249.958755]  writing 4 blocks of new data
[  249.959966]  writing 5 blocks of new data
[  249.973025] patching 70 blocks to 68
[  250.027374] patching 5 blocks to 6
[  250.039479]  writing 29 blocks of new data
[  250.043685]   moving 3 blocks
[  250.046329] patching 12 blocks to 12
[  250.058039]  writing 1 blocks of new data
[  250.075872] patching 138 blocks to 140
[  250.192828]  writing 4 blocks of new data
[  250.198206] patching 14 blocks to 15
[  250.213097] patching 4 blocks to 4
[  250.224463]  writing 433 blocks of new data
[  250.293305]   moving 44 blocks
[  250.298028] patching 3 blocks to 3
[  250.309006] patching 12 blocks to 12
[  250.323044] patching 1 blocks to 1
[  250.331828]  writing 9 blocks of new data
[  250.334634]   moving 1 blocks
[  250.339562] patching 35 blocks to 37
[  250.372522] patching 12 blocks to 12
[  250.385139] patching 4 blocks to 4
[  250.438552] patching 358 blocks to 375
[  250.478263]  writing 3 blocks of new data
[  250.482681] patching 14 blocks to 15
[  250.497527] patching 1 blocks to 1
[  250.506270]  writing 43 blocks of new data
[  250.512067]  writing 1 blocks of new data
[  250.513444] patching 3 blocks to 3
[  250.522630]  writing 3 blocks of new data
[  250.524127]   moving 1 blocks
[  250.529808] patching 30 blocks to 38
[  250.565075] patching 6 blocks to 6
[  250.580759] patching 22 blocks to 23
[  251.310181] patching 5421 blocks to 5694
[  253.732551] patching 14 blocks to 15
[  253.771943] patching 16 blocks to 17
[  253.825596]   moving 156 blocks
[  253.888480]  writing 6 blocks of new data
[  253.900479]  writing 5 blocks of new data
[  253.928698]   moving 26 blocks
[  253.956153]   moving 1 blocks
[  253.978668] patching 2 blocks to 2
[  254.011340] patching 16 blocks to 17
[  254.049945] patching 12 blocks to 12
[  254.089076]   moving 1 blocks
[  254.113017] patching 10 blocks to 18
[  254.157263] patching 16 blocks to 17
[  254.194484]  writing 1024 blocks of new data
[  254.585313]  writing 1024 blocks of new data
[  254.961856]  writing 323 blocks of new data
[  255.096927] patching 9 blocks to 10
[  255.140444] patching 4 blocks to 4
[  255.172966]  writing 4 blocks of new data
[  255.197303] patching 3 blocks to 3
[  255.228726]  writing 5 blocks of new data
[  255.251178]  writing 1 blocks of new data
[  255.275822]   moving 13 blocks
[  255.298357]  writing 52 blocks of new data
[  255.347799] patching 140 blocks to 109
[  255.480665]   moving 4 blocks
[  255.506165] patching 1 blocks to 1
[  255.537132]   moving 11 blocks
[  255.563034]   moving 1 blocks
[  255.586896] patching 1 blocks to 1
[  255.598665] patching 12 blocks to 12
[  255.611779]  writing 42 blocks of new data
[  255.626234] patching 65 blocks to 61
[  255.682278] patching 14 blocks to 15
[  255.699082]   moving 19 blocks
[  255.701263]  writing 11 blocks of new data
[  255.702727]   moving 1 blocks
[  255.704785] patching 7 blocks to 11
[  255.720462]  writing 26 blocks of new data
[  255.764908]   moving 350 blocks
[  255.780681] patching 4 blocks to 4
[  255.792903]   moving 1 blocks
[  255.795418] patching 13 blocks to 14
[  255.816807] patching 14 blocks to 15
[  255.830865]  writing 1 blocks of new data
[  255.846330] patching 123 blocks to 123
[  255.875467] patching 12 blocks to 12
[  255.889265]  writing 12 blocks of new data
[  255.892540]   moving 1 blocks
[  255.893060]  writing 4 blocks of new data
[  255.921931] patching 189 blocks to 293
[  256.112603]  writing 2 blocks of new data
[  256.113651]  writing 1 blocks of new data
[  256.116085] patching 5 blocks to 5
[  256.132628]   moving 6 blocks
[  256.135283] patching 4 blocks to 4
[  256.148233] patching 1 blocks to 1
[  256.161196] patching 14 blocks to 15
[  256.180406] patching 9 blocks to 9
[  256.196597] patching 20 blocks to 25
[  256.227210] patching 12 blocks to 12
[  256.239637]  writing 5 blocks of new data
[  256.240620]  writing 1 blocks of new data
[  256.242367] patching 6 blocks to 7
[  256.257746] patching 12 blocks to 12
[  256.298522]   moving 234 blocks
[  256.310860]   moving 5 blocks
[  256.311844]  writing 4 blocks of new data
[  256.315171] patching 16 blocks to 17
[  256.327221]  writing 3 blocks of new data
[  256.331622] patching 49 blocks to 5
[  256.341897]   moving 1 blocks
[  256.342482]  writing 9 blocks of new data
[  256.349331] patching 7 blocks to 24
[  256.376826] patching 29 blocks to 31
[  256.401726] patching 4 blocks to 4
[  256.413769]  writing 24 blocks of new data
[  256.417628]   moving 2 blocks
[  256.552969] patching 1627 blocks to 920
[  257.314634] patching 137 blocks to 159
[  257.481025]  writing 1 blocks of new data
[  257.481708]  writing 6 blocks of new data
[  257.483208]  writing 829 blocks of new data
[  257.606302]  writing 6 blocks of new data
[  257.608117] patching 5 blocks to 5
[  257.747090] patching 2636 blocks to 21
[  257.766815]  writing 1 blocks of new data
[  257.768885] patching 7 blocks to 9
[  257.779748]  writing 1 blocks of new data
[  257.781166] patching 5 blocks to 5
[  257.796246]  writing 2 blocks of new data
[  257.797457]   moving 1 blocks
[  257.798089]  writing 1 blocks of new data
[  257.834729] patching 107 blocks to 146
[  257.956000]  writing 1 blocks of new data
[  257.956724]  writing 833 blocks of new data
[  258.120164]   moving 36 blocks
[  258.123979]   moving 3 blocks
[  258.126574] patching 12 blocks to 12
[  258.139999]  writing 1 blocks of new data
[  258.140612]  writing 1 blocks of new data
[  258.141205]  writing 4 blocks of new data
[  258.282875] patching 1004 blocks to 1160
[  258.421865]   moving 3 blocks
[  258.424138] patching 9 blocks to 10
[  258.440563] patching 6 blocks to 6
[  258.454478]  writing 1 blocks of new data
[  258.458931]   moving 29 blocks
[  258.464214] patching 8 blocks to 13
[  258.498239] patching 144 blocks to 153
[  258.657354] patching 7 blocks to 8
[  258.672083]  writing 7 blocks of new data
[  258.710196] patching 152 blocks to 156
[  258.886343] patching 1 blocks to 1
[  258.907376] patching 36 blocks to 38
[  258.947374]   moving 1 blocks
[  258.952220] patching 14 blocks to 15
[  258.965719]  writing 16 blocks of new data
[  258.973452]   moving 3 blocks
[  258.974844] patching 1 blocks to 1
[  258.984871] patching 5 blocks to 6
[  258.997721] patching 3 blocks to 3
[  259.008622]   moving 1 blocks
[  259.009265]  writing 13 blocks of new data
[  259.015547] patching 14 blocks to 15
[  259.032185] patching 4 blocks to 4
[  259.043780]  writing 15 blocks of new data
[  259.049382] patching 2 blocks to 2
[  259.063639] patching 16 blocks to 17
[  259.078190]  writing 2 blocks of new data
[  259.079591]   moving 2 blocks
[  259.080332]  writing 1 blocks of new data
[  259.096950]   moving 56 blocks
[  259.110471] patching 3 blocks to 15
[  259.214993] patching 4 blocks to 4
[  259.250055]   moving 12 blocks
[  259.272386]  writing 45 blocks of new data
[  259.301878]  writing 4 blocks of new data
[  259.328293] patching 16 blocks to 17
[  259.366613] patching 14 blocks to 15
[  259.409981]  writing 4 blocks of new data
[  259.442718]   moving 8 blocks
[  259.469432] patching 12 blocks to 12
[  259.508122]  writing 15 blocks of new data
[  259.536111]  writing 1 blocks of new data
[  259.567722] patching 76 blocks to 96
[  259.622035]  writing 65 blocks of new data
[  259.635171] patching 4 blocks to 4
[  259.647491] patching 5 blocks to 5
[  259.661638] patching 16 blocks to 17
[  259.674151] patching 1 blocks to 1
[  259.685542] patching 18 blocks to 18
[  259.708755]  writing 7 blocks of new data
[  259.710631] patching 3 blocks to 3
[  259.719927]  writing 2 blocks of new data
[  259.723677] patching 5 blocks to 4
[  259.737390] patching 10 blocks to 11
[  259.754597]  writing 1 blocks of new data
[  259.755238]  writing 5 blocks of new data
[  259.757030] patching 3 blocks to 3
[  259.808270]   moving 374 blocks
[  259.826997] patching 16 blocks to 17
[  259.841400]   moving 1 blocks
[  259.843182] patching 7 blocks to 7
[  259.855496] patching 4 blocks to 4
[  259.866642]  writing 1024 blocks of new data
[  260.007136]  writing 288 blocks of new data
[  260.062417] patching 20 blocks to 21
[  260.089709] patching 14 blocks to 15
[  260.103840]   moving 2 blocks
[  260.106718] patching 16 blocks to 17
[  260.172880]  writing 8 blocks of new data
[  260.175545]  writing 3 blocks of new data
[  260.178112]  writing 137 blocks of new data
[  260.194053]  writing 7 blocks of new data
[  260.195621]   moving 1 blocks
[  260.196810] patching 1 blocks to 2
[  260.209637] patching 16 blocks to 17
[  260.222384]   moving 1 blocks
[  260.222930]  writing 4 blocks of new data
[  260.226295]  writing 1024 blocks of new data
[  260.367954]  writing 113 blocks of new data
[  260.392474]  writing 1024 blocks of new data
[  260.532976]  writing 397 blocks of new data
[  260.602407] patching 12 blocks to 12
[  260.615567]  writing 102 blocks of new data
[  260.628489]   moving 7 blocks
[  260.631847] patching 17 blocks to 17
[  260.644766] patching 1 blocks to 1
[  260.770708] patching 1028 blocks to 1056
[  261.371879] patching 37 blocks to 48
[  261.447928] patching 752 blocks to 9
[  261.485964]   moving 227 blocks
[  261.498029]  writing 1 blocks of new data
[  261.499464] patching 3 blocks to 3
[  261.509333]   moving 10 blocks
[  261.510470] patching 1 blocks to 1
[  261.520853] patching 8 blocks to 9
[  261.538843] patching 14 blocks to 15
[  261.552342]   moving 1 blocks
[  261.552867]  writing 6 blocks of new data
[  261.555992] patching 14 blocks to 15
[  261.569054]   moving 3 blocks
[  261.569914]  writing 11 blocks of new data
[  261.572221]   moving 5 blocks
[  261.575273] patching 12 blocks to 12
[  261.591474] patching 16 blocks to 17
[  261.605563]  writing 1024 blocks of new data
[  261.763583]  writing 485 blocks of new data
[  261.834069]  writing 19 blocks of new data
[  261.838558] patching 7 blocks to 7
[  261.855922] patching 70 blocks to 58
[  261.910961]   moving 1 blocks
[  261.911519]  writing 81 blocks of new data
[  262.015744] patching 600 blocks to 957
[  263.537960]   moving 813 blocks
[  263.574041]  writing 1 blocks of new data
[  263.603256]   moving 8 blocks
[  263.730275] patching 734 blocks to 1173
[  265.029840]   moving 3 blocks
[  265.030758]  writing 4 blocks of new data
[  265.034166]  writing 510 blocks of new data
[  265.112769] patching 49 blocks to 36
[  265.143608] patching 10 blocks to 19
[  265.177330]   moving 102 blocks
[  265.184731]   moving 1 blocks
[  265.272610] patching 725 blocks to 859
[  265.645090]   moving 1 blocks
[  265.645727]  writing 1 blocks of new data
[  265.647197]   moving 1 blocks
[  265.648755]   moving 4 blocks
[  265.653832] patching 12 blocks to 12
[  265.666771]  writing 1 blocks of new data
[  265.669782] patching 12 blocks to 12
[  265.683635]  writing 1 blocks of new data
[  265.688109] patching 19 blocks to 21
[  265.710895]  writing 5 blocks of new data
[  265.713295]  writing 1 blocks of new data
[  265.715709] patching 12 blocks to 12
[  265.728276]   moving 2 blocks
[  265.728831]  writing 12 blocks of new data
[  265.732574]   moving 1 blocks
[  265.735190] patching 14 blocks to 15
[  265.749200]  writing 5 blocks of new data
[  265.749972]  writing 1024 blocks of new data
[  265.924453]  writing 94 blocks of new data
[  265.946299]   moving 12 blocks
[  265.949928] patching 3 blocks to 3
[  265.959305]  writing 1 blocks of new data
[  265.960437]   moving 1 blocks
[  265.961382] patching 1 blocks to 1
[  265.971145]   moving 4 blocks
[  265.972282]  writing 1 blocks of new data
[  265.974525] patching 12 blocks to 12
[  265.986948]  writing 143 blocks of new data
[  266.006705] patching 14 blocks to 15
[  266.050088]   moving 272 blocks
[  266.065587] patching 16 blocks to 17
[  266.108027]   moving 252 blocks
[  266.119998]   moving 1 blocks
[  266.120531]  writing 8 blocks of new data
[  266.123705]   moving 1 blocks
[  266.125742] patching 1 blocks to 15
[  266.148973]   moving 5 blocks
[  266.150250]   moving 1 blocks
[  266.153502] patching 34 blocks to 5
[  266.165009] patching 14 blocks to 15
[  266.177540]  writing 27 blocks of new data
[  266.189866]  writing 7 blocks of new data
[  266.190879]  writing 35 blocks of new data
[  266.194250]  writing 83 blocks of new data
[  266.207300]  writing 1 blocks of new data
[  266.207901]  writing 57 blocks of new data
[  266.217605]  writing 24 blocks of new data
[  266.253541] patching 169 blocks to 186
[  266.343420] patching 321 blocks to 412
[  266.830217]   moving 5 blocks
[  266.832336]   moving 3 blocks
[  266.833440]  writing 4 blocks of new data
[  266.838911]   moving 24 blocks
[  266.842501] patching 1 blocks to 1
[  266.852224] patching 3 blocks to 3
[  266.861911]  writing 6 blocks of new data
[  266.870509] patching 12 blocks to 12
[  266.883279]  writing 1 blocks of new data
[  266.884568]   moving 2 blocks
[  266.886085] patching 3 blocks to 3
[  266.902683] patching 24 blocks to 25
[  266.933310] patching 8 blocks to 18
[  266.948790] patching 5 blocks to 5
[  266.961335] patching 5 blocks to 4
[  266.972990]  writing 4 blocks of new data
[  266.975526] patching 12 blocks to 12
[  266.989579] patching 4 blocks to 4
[  267.014830] patching 98 blocks to 112
[  267.111121]   moving 12 blocks
[  267.113917]   moving 1 blocks
[  267.114799]   moving 1 blocks
[  267.115321]  writing 46 blocks of new data
[  267.122792]  writing 4 blocks of new data
[  267.124833] patching 11 blocks to 12
[  267.176165]   moving 307 blocks
[  267.190328]  writing 30 blocks of new data
[  267.197349]   moving 5 blocks
[  267.198992]   moving 2 blocks
[  267.201851] patching 16 blocks to 17
[  267.219716] patching 25 blocks to 24
[  267.246898]  writing 3 blocks of new data
[  267.250249] patching 16 blocks to 17
[  267.266173] patching 16 blocks to 17
[  267.281100]   moving 1 blocks
[  267.283230] patching 12 blocks to 12
[  267.296993] patching 1 blocks to 1
[  267.307491] patching 2 blocks to 3
[  267.363118] patching 16 blocks to 17
[  267.375969]   moving 2 blocks
[  267.377033]   moving 1 blocks
[  267.399657]   moving 191 blocks
[  267.411286] patching 5 blocks to 5
[  267.422997] patching 7 blocks to 20
[  267.497551]  writing 55 blocks of new data
[  267.513388] patching 16 blocks to 17
[  267.527789]  writing 2 blocks of new data
[  267.528500]  writing 1 blocks of new data
[  267.529083]  writing 6 blocks of new data
[  267.530830]   moving 2 blocks
[  267.531384]  writing 1 blocks of new data
[  267.532527] patching 3 blocks to 3
[  267.542322]  writing 21 blocks of new data
[  267.550182] patching 14 blocks to 15
[  267.562982]   moving 1 blocks
[  267.563570]  writing 1 blocks of new data
[  267.564867]   moving 1 blocks
[  267.565453]  writing 275 blocks of new data
[  267.606292] patching 12 blocks to 12
[  267.619828]   moving 8 blocks
[  267.620618]  writing 286 blocks of new data
[  267.670967] patching 27 blocks to 27
[  267.696511] patching 12 blocks to 12
[  267.709228] patching 1 blocks to 4
[  267.721891]   moving 3 blocks
[  267.724846] patching 14 blocks to 15
[  267.737463]  writing 5 blocks of new data
[  267.738574]   moving 1 blocks
[  267.748704]   moving 78 blocks
[  267.758185] patching 14 blocks to 15
[  267.772613]   moving 1 blocks
[  267.773167]  writing 1 blocks of new data
[  267.773745]  writing 21 blocks of new data
[  267.779154] patching 16 blocks to 17
[  267.792956]  writing 36 blocks of new data
[  267.799118]   moving 1 blocks
[  267.800280]   moving 4 blocks
[  267.802963] patching 12 blocks to 12
[  267.817311] patching 14 blocks to 15
[  267.830020] patching 1 blocks to 1
[  267.867389]   moving 248 blocks
[  267.880870]  writing 1024 blocks of new data
[  268.021217]  writing 594 blocks of new data
[  268.111004] patching 1 blocks to 1
[  268.119850]  writing 25 blocks of new data
[  268.167650] patching 358 blocks to 375
[  268.211361] patching 37 blocks to 37
[  268.258309] patching 45 blocks to 52
[  268.297473]   moving 8 blocks
[  268.299707] patching 6 blocks to 7
[  268.315823] patching 16 blocks to 17
[  268.332442] patching 16 blocks to 17
[  268.344542]  writing 10 blocks of new data
[  268.347011]  writing 7 blocks of new data
[  268.350465] patching 3 blocks to 3
[  268.360670]   moving 3 blocks
[  268.362123]   moving 1 blocks
[  268.362676]  writing 10 blocks of new data
[  268.365675]   moving 1 blocks
[  268.368639] patching 16 blocks to 17
[  268.382820] patching 14 blocks to 15
[  268.437268] patching 867 blocks to 14
[  268.452779]   moving 3 blocks
[  268.453613]  writing 1 blocks of new data
[  268.454912] patching 4 blocks to 4
[  268.467234]   moving 1 blocks
[  268.468135] stashing 1 blocks to 90b7452d163e8a84baa02b07be2b18aca7f0514a
[  268.468146]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/90b7452d163e8a84baa02b07be2b18aca7f0514a
[  268.703365] patching 4804 blocks to 37
[  268.738643] patching 11 blocks to 17
[  268.795749] patching 15 blocks to 22
[  268.848386] patching 58 blocks to 62
[  268.920830] patching 39 blocks to 40
[  268.994346] patching 116 blocks to 305
[  269.252005] patching 31 blocks to 33
[  269.298989]  writing 161 blocks of new data
[  269.366238]  writing 4 blocks of new data
[  269.397161] patching 5 blocks to 5
[  269.419954]  writing 76 blocks of new data
[  269.429901]  writing 21 blocks of new data
[  269.434391] patching 6 blocks to 6
[  269.461483] patching 77 blocks to 258
[  269.752260]  writing 191 blocks of new data
[  269.786543]  writing 24 blocks of new data
[  269.825416] patching 191 blocks to 195
[  270.047338]  writing 22 blocks of new data
[  270.052830] patching 4 blocks to 4
[  270.065168]  writing 27 blocks of new data
[  270.070268] patching 5 blocks to 5
[  270.083222] patching 5 blocks to 4
[  270.095741]  writing 17 blocks of new data
[  270.099970] patching 1 blocks to 2
[  270.110352]  writing 13 blocks of new data
[  270.115433]  writing 23 blocks of new data
[  270.119329]  writing 7 blocks of new data
[  270.121156]  writing 64 blocks of new data
[  270.182133]   moving 280 blocks
[  270.194893]  writing 27 blocks of new data
[  270.198831]  writing 24 blocks of new data
[  270.206677] patching 24 blocks to 24
[  270.229867]  writing 12 blocks of new data
[  270.236339] patching 48 blocks to 50
[  270.284220]  writing 8 blocks of new data
[  270.286660]  writing 12 blocks of new data
[  270.287570]  writing 106 blocks of new data
[  270.300230]  writing 52 blocks of new data
[  270.312360] patching 1 blocks to 14
[  270.332153]  writing 2 blocks of new data
[  270.332773]  writing 7 blocks of new data
[  270.333813]  writing 1 blocks of new data
[  270.339317] patching 23 blocks to 28
[  270.369388] patching 4 blocks to 5
[  270.380499] patching 6 blocks to 5
[  270.391588]  writing 22 blocks of new data
[  270.395023]  writing 5 blocks of new data
[  270.395995]  writing 51 blocks of new data
[  270.404960] patching 11 blocks to 12
[  270.419701] patching 2 blocks to 2
[  270.428353]  writing 41 blocks of new data
[  270.435400]  writing 24 blocks of new data
[  270.439407] patching 9 blocks to 10
[  270.459055]   moving 65 blocks
[  270.465231]  writing 55 blocks of new data
[  270.475019] patching 10 blocks to 11
[  270.491568] patching 3 blocks to 3
[  270.500673]  writing 68 blocks of new data
[  270.509832]  writing 23 blocks of new data
[  270.514474] patching 8 blocks to 8
[  270.525145] patching 5 blocks to 5
[  270.537264]  writing 375 blocks of new data
[  270.581802]  writing 4 blocks of new data
[  270.584902] patching 8 blocks to 8
[  270.604501] patching 41 blocks to 41
[  270.645587] patching 56 blocks to 56
[  270.662726]  writing 73 blocks of new data
[  270.672783] patching 10 blocks to 10
[  270.682887]  writing 32 blocks of new data
[  270.686156]  writing 33 blocks of new data
[  270.692334]  writing 26 blocks of new data
[  270.696216] patching 9 blocks to 9
[  270.711303] patching 49 blocks to 50
[  270.749202]  writing 36 blocks of new data
[  270.756579]  writing 22 blocks of new data
[  270.757870]  writing 213 blocks of new data
[  270.784289]  writing 24 blocks of new data
[  270.789100]  writing 11 blocks of new data
[  270.792121] patching 6 blocks to 5
[  270.804439]  writing 21 blocks of new data
[  270.808467] patching 4 blocks to 5
[  270.820728]  writing 226 blocks of new data
[  270.842731] patching 7 blocks to 8
[  270.858635] patching 5 blocks to 5
[  270.868117]  writing 5 blocks of new data
[  270.869696] patching 4 blocks to 4
[  270.878917]  writing 23 blocks of new data
[  270.883165]  writing 30 blocks of new data
[  270.886767]  writing 6 blocks of new data
[  270.892742] patching 59 blocks to 4
[  270.904771] patching 2 blocks to 2
[  270.914240] patching 9 blocks to 9
[  270.927462] patching 22 blocks to 23
[  270.956557] patching 25 blocks to 24
[  270.982443]  writing 25 blocks of new data
[  270.988224]   moving 23 blocks
[  270.993816] patching 18 blocks to 19
[  271.012111]  writing 10 blocks of new data
[  271.016059]   moving 14 blocks
[  271.020504] patching 2 blocks to 2
[  271.051245] patching 4 blocks to 4
[  271.062220] patching 9 blocks to 9
[  271.074662]  writing 54 blocks of new data
[  271.080148]  writing 36 blocks of new data
[  271.086823] patching 1 blocks to 8
[  271.103619] patching 3 blocks to 3
[  271.113088] patching 4 blocks to 4
[  271.125375]  writing 46 blocks of new data
[  271.135322] patching 23 blocks to 26
[  271.177800]   moving 152 blocks
[  271.185968]  writing 52 blocks of new data
[  271.193243]  writing 29 blocks of new data
[  271.199325] stashing 3 blocks to 98ae8d8f28a613f46386d37dfcd4d760c736d9d1
[  271.199336]  writing 3 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/98ae8d8f28a613f46386d37dfcd4d760c736d9d1
[  271.206743] patching 37 blocks to 5
[  271.217101] patching 2 blocks to 2
[  271.279583] patching 2 blocks to 3
[  271.386576] patching 1087 blocks to 12
[  271.401699] stashing 5 blocks to 28c85e002685d08d0c70af35ff78ef3f73ecfcdb
[  271.401726]  writing 5 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/28c85e002685d08d0c70af35ff78ef3f73ecfcdb
[  271.419106] patching 115 blocks to 94
[  271.584888] patching 1520 blocks to 13
[  271.602916] stashing 56 blocks to 745ec3745ab3879e486a9b0e50964796582c26fd
[  271.602944]  writing 56 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/745ec3745ab3879e486a9b0e50964796582c26fd
[  271.899158] patching 6053 blocks to 56
[  271.951645] stashing 11 blocks to 8298112540eccd5f29540910bd3020fd14760e14
[  271.951674]  writing 11 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8298112540eccd5f29540910bd3020fd14760e14
[  272.465174]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/745ec3745ab3879e486a9b0e50964796582c26fd
[  272.642654] stashing 6007 overlapping blocks to ebf921e3b27c7ecfefd77644e53777ad0e1153c8
[  272.642688] 254169088 bytes free on /cache (24604672 needed)
[  272.642741]  writing 6007 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ebf921e3b27c7ecfefd77644e53777ad0e1153c8
[  272.960951] patching 6007 blocks to 4716
[  297.324580] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ebf921e3b27c7ecfefd77644e53777ad0e1153c8
[  297.666499] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/745ec3745ab3879e486a9b0e50964796582c26fd
[  298.607582] patching 8192 blocks to 8192
[  308.069139] patching 1597 blocks to 1203
[  313.805149] patching 2248 blocks to 19
[  313.836786] patching 11 blocks to 5
[  313.852056] patching 47 blocks to 49
[  314.005684] patching 1624 blocks to 18
[  314.211050] patching 3863 blocks to 25
[  314.233642] patching 2 blocks to 2
[  314.338238] patching 189 blocks to 784
[  317.701717] patching 29 blocks to 5
[  317.722820] patching 69 blocks to 73
[  317.825811] patching 14 blocks to 5
[  317.836990] patching 13 blocks to 16
[  317.930175] patching 39 blocks to 50
[  318.158984] patching 944 blocks to 1430
[  319.862676] patching 76 blocks to 84
[  319.935182] patching 5 blocks to 6
[  319.948084] patching 6 blocks to 7
[  319.961210]  writing 329 blocks of new data
[  320.000704] patching 5 blocks to 4
[  320.012693]  writing 14 blocks of new data
[  320.022402] patching 40 blocks to 42
[  320.064053] patching 51 blocks to 90
[  320.147982] patching 7 blocks to 7
[  320.161723]  writing 24 blocks of new data
[  320.204389] patching 370 blocks to 397
[  320.627283] patching 16 blocks to 14
[  320.645883] patching 16 blocks to 16
[  320.667040] patching 20 blocks to 21
[  320.689968] patching 4 blocks to 4
[  320.704544] patching 35 blocks to 37
[  320.745440] patching 86 blocks to 90
[  320.805377]  writing 11 blocks of new data
[  320.809629] patching 8 blocks to 9
[  320.835501] patching 86 blocks to 87
[  320.915026]   moving 377 blocks
[  320.940728] patching 85 blocks to 89
[  321.001101] patching 35 blocks to 36
[  321.031254]  writing 1024 blocks of new data
[  321.310082]  writing 1024 blocks of new data
[  321.577271]  writing 118 blocks of new data
[  321.603480] patching 32 blocks to 34
[  321.636976] patching 17 blocks to 24
[  321.663686]  writing 602 blocks of new data
[  321.734546]  writing 5 blocks of new data
[  321.735494]  writing 9 blocks of new data
[  321.737895]  writing 11 blocks of new data
[  321.743718] patching 27 blocks to 29
[  321.769695]  writing 5 blocks of new data
[  321.772148]  writing 5 blocks of new data
[  321.774078] patching 6 blocks to 5
[  321.790174] patching 31 blocks to 32
[  321.821178]  writing 6 blocks of new data
[  321.822933] patching 4 blocks to 4
[  321.834266]  writing 1024 blocks of new data
[  321.947524]  writing 90 blocks of new data
[  321.964148] patching 8 blocks to 9
[  321.981921] patching 21 blocks to 22
[  322.011557] patching 83 blocks to 92
[  322.093449] patching 11 blocks to 12
[  322.112771] patching 9 blocks to 10
[  322.128463]  writing 20 blocks of new data
[  322.135494] patching 7 blocks to 8
[  322.150519]  writing 25 blocks of new data
[  322.158954]  writing 5 blocks of new data
[  322.161194] patching 5 blocks to 5
[  322.177670] patching 19 blocks to 19
[  322.200874] patching 18 blocks to 19
[  322.227793]  writing 4 blocks of new data
[  322.228790]  writing 6 blocks of new data
[  322.230634] patching 4 blocks to 4
[  322.245531] patching 35 blocks to 36
[  322.322670] patching 240 blocks to 245
[  322.645505] patching 25 blocks to 33
[  322.682029]  writing 1024 blocks of new data
[  322.804548]  writing 435 blocks of new data
[  322.857599] patching 30 blocks to 31
[  322.902504] patching 121 blocks to 116
[  322.985034] stashing 95 blocks to 69df30e93577ec79a2f681fa330a2f520dbddf87
[  322.985066]  writing 95 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69df30e93577ec79a2f681fa330a2f520dbddf87
[  323.410231] patching 8441 blocks to 95
[  323.491727] stashing 38 blocks to 35243b975f7a45d23b806d19fd9e34ac90981855
[  323.491758]  writing 38 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/35243b975f7a45d23b806d19fd9e34ac90981855
[  323.680268]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69df30e93577ec79a2f681fa330a2f520dbddf87
[  323.819734] patching 4693 blocks to 38
[  323.852797] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69df30e93577ec79a2f681fa330a2f520dbddf87
[  324.822933] patching 8192 blocks to 8192
[  332.375601] patching 1124 blocks to 14
[  332.404232] patching 270 blocks to 7
[  332.419949] patching 69 blocks to 5
[  332.432173] patching 21 blocks to 29
[  332.493943] patching 11 blocks to 23
[  332.758029] patching 564 blocks to 786
[  334.781154] patching 66 blocks to 6
[  334.799416] patching 113 blocks to 7
[  335.035905] patching 1249 blocks to 2949
[  338.271289] patching 7 blocks to 6
[  338.282093] patching 10 blocks to 10
[  338.338092] patching 50 blocks to 6
[  338.350376] patching 5 blocks to 37
[  338.453892] patching 89 blocks to 6
[  338.472022] patching 33 blocks to 53
[  338.586091] patching 10 blocks to 5
[  338.597128] patching 2 blocks to 3
[  338.801593] patching 998 blocks to 1005
[  341.235030] patching 4 blocks to 12
[  341.325682] patching 72 blocks to 95
[  341.539344] patching 55 blocks to 5
[  341.572120] patching 7 blocks to 19
[  341.657243] patching 76 blocks to 5
[  341.693072] patching 16 blocks to 28
[  341.773861] patching 7 blocks to 45
[  341.936499] patching 1727 blocks to 18
[  341.996969] patching 190 blocks to 189
[  342.229386] patching 141 blocks to 148
[  342.347951] patching 6 blocks to 7
[  342.360861]  writing 28 blocks of new data
[  342.369931] patching 23 blocks to 25
[  342.498322] patching 2138 blocks to 19
[  342.515917]  writing 3 blocks of new data
[  342.535715] patching 383 blocks to 8
[  342.548250]  writing 1024 blocks of new data
[  342.687187]  writing 1024 blocks of new data
[  342.826303]  writing 1024 blocks of new data
[  342.971602]  writing 1024 blocks of new data
[  343.120301]  writing 1024 blocks of new data
[  343.260056]  writing 1024 blocks of new data
[  343.400942]  writing 1024 blocks of new data
[  343.554467]  writing 1024 blocks of new data
[  343.676249]  writing 1024 blocks of new data
[  343.837841]  writing 1024 blocks of new data
[  344.180113]  writing 144 blocks of new data
[  344.214912]  writing 19 blocks of new data
[  344.219806]  writing 46 blocks of new data
[  344.227298]  writing 41 blocks of new data
[  344.234224]  writing 28 blocks of new data
[  344.241596]  writing 1024 blocks of new data
[  344.344965]  writing 1024 blocks of new data
[  344.447173]  writing 1024 blocks of new data
[  344.570874]  writing 7 blocks of new data
[  344.572048]  writing 47 blocks of new data
[  344.582373] patching 10 blocks to 10
[  344.597262]  writing 93 blocks of new data
[  344.633842] patching 205 blocks to 254
[  344.877530]  writing 8 blocks of new data
[  344.882848] patching 4 blocks to 4
[  344.894447]  writing 5 blocks of new data
[  344.908395] patching 87 blocks to 73
[  344.975487]  writing 18 blocks of new data
[  344.999388] patching 92 blocks to 79
[  345.096996] patching 60 blocks to 60
[  345.154056] patching 38 blocks to 44
[  345.199154]  writing 1024 blocks of new data
[  345.280357]  writing 322 blocks of new data
[  345.314900]  writing 1024 blocks of new data
[  345.421934]  writing 322 blocks of new data
[  345.460692]  writing 30 blocks of new data
[  345.484522] patching 174 blocks to 233
[  345.714833]  writing 1 blocks of new data
[  345.715569]  writing 1024 blocks of new data
[  345.832457]  writing 1024 blocks of new data
[  346.038020]  writing 1024 blocks of new data
[  346.253803]  writing 1024 blocks of new data
[  346.393367]  writing 1024 blocks of new data
[  346.514797]  writing 1024 blocks of new data
[  346.642980]  writing 1024 blocks of new data
[  346.760264]  writing 1024 blocks of new data
[  346.895654]  writing 1024 blocks of new data
[  347.011441]  writing 1024 blocks of new data
[  347.134182]  writing 1024 blocks of new data
[  347.260591]  writing 1024 blocks of new data
[  347.403740]  writing 663 blocks of new data
[  347.512035]  writing 24 blocks of new data
[  347.518821]  writing 48 blocks of new data
[  347.528534]  writing 489 blocks of new data
[  347.610307] stashing 161 blocks to 2925d73a1ed14a9d53c0feff09b45e976ff8766b
[  347.610345]  writing 161 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2925d73a1ed14a9d53c0feff09b45e976ff8766b
[  348.487324] patching 18188 blocks to 161
[  348.810493]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2925d73a1ed14a9d53c0feff09b45e976ff8766b
[  349.051403] patching 8079 blocks to 81
[  349.116810] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2925d73a1ed14a9d53c0feff09b45e976ff8766b
[  349.770759] patching 6462 blocks to 6329
[  365.680410] patching 28 blocks to 5
[  366.312145] patching 12669 blocks to 132
[  366.510267] patching 402 blocks to 439
[  367.211053] patching 1890 blocks to 1923
[  368.902303] stashing 328 overlapping blocks to 1c9c00d48c131b6de04ce480641b96b69840db5c
[  368.902338] 254242816 bytes free on /cache (1343488 needed)
[  368.902402]  writing 328 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1c9c00d48c131b6de04ce480641b96b69840db5c
[  369.100372] patching 328 blocks to 342
[  370.822214] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1c9c00d48c131b6de04ce480641b96b69840db5c
[  371.458858] patching 4421 blocks to 4886
[  396.849997] patching 1816 blocks to 31
[  396.880964] patching 19 blocks to 23
[  397.111198] patching 964 blocks to 845
[  397.484387] patching 3250 blocks to 2747
[  400.073742] patching 281 blocks to 306
[  400.447373] patching 143 blocks to 278
[  401.457898] patching 11 blocks to 9
[  401.472665] patching 12 blocks to 9
[  401.488931] patching 10 blocks to 5
[  401.506458] patching 42 blocks to 5
[  401.517167] patching 2 blocks to 3
[  401.563338] patching 15 blocks to 5
[  401.573253] patching 4 blocks to 6
[  401.676598] patching 282 blocks to 311
[  403.115965] patching 9701 blocks to 7603
[  412.356480] patching 5541 blocks to 6962
[  444.073301] patching 8192 blocks to 8192
[  452.365426] patching 829 blocks to 931
[  463.785933] patching 8192 blocks to 8192
[  470.000133] patching 1919 blocks to 2214
[  473.119577] patching 8192 blocks to 8192
[  480.277013] patching 3014 blocks to 3926
[  484.783221] patching 8 blocks to 5
[  484.815878] patching 4 blocks to 5
[  484.892313] patching 8 blocks to 8
[  485.126287] patching 679 blocks to 13
[  485.854798] patching 7324 blocks to 6552
[  494.190226] patching 24 blocks to 19
[  495.086787] patching 8192 blocks to 8192
[  498.493243] patching 2332 blocks to 22
[  499.351072] patching 8192 blocks to 8192
[  509.325291] patching 4655 blocks to 59
[  509.487064] patching 1600 blocks to 19
[  509.505732] patching 2 blocks to 5
[  509.617110] patching 596 blocks to 592
[  514.466001] patching 1031 blocks to 19
[  514.848093] patching 2720 blocks to 2928
[  516.437425] patching 571 blocks to 3195
[  523.431854] patching 3434 blocks to 4906
[  544.232900] patching 994 blocks to 1047
[  545.355049] patching 3528 blocks to 1871
[  557.295883] patching 15 blocks to 9
[  557.311364] patching 6 blocks to 5
[  557.331764] patching 11 blocks to 101
[  557.599596] patching 10 blocks to 57
[  557.851342] patching 436 blocks to 436
[  558.540988] patching 3224 blocks to 3321
[  586.398912] patching 1394 blocks to 18
[  587.116722] patching 5466 blocks to 5508
[  602.064108] patching 139 blocks to 296
[  603.541617]  writing 2 blocks of new data
[  603.551226]   moving 4 blocks
[  603.551835]  writing 2 blocks of new data
[  603.560800] patching 75 blocks to 76
[  603.630453]  writing 3 blocks of new data
[  603.632215]   moving 2 blocks
[  603.633110]  writing 2 blocks of new data
[  603.635271]   moving 8 blocks
[  603.637477]  writing 1024 blocks of new data
[  603.750617]  writing 1024 blocks of new data
[  603.863659]  writing 1024 blocks of new data
[  603.968056]  writing 1024 blocks of new data
[  604.068156]  writing 1024 blocks of new data
[  604.200491]  writing 594 blocks of new data
[  604.299359]   moving 4 blocks
[  604.301781]   moving 5 blocks
[  604.303450]   moving 5 blocks
[  604.304183]  writing 2 blocks of new data
[  604.311642]   moving 40 blocks
[  604.317984]   moving 4 blocks
[  604.319209]  writing 837 blocks of new data
[  604.442762]   moving 3 blocks
[  604.443601]  writing 2 blocks of new data
[  604.444252]  writing 19 blocks of new data
[  604.449014]  writing 1024 blocks of new data
[  604.611924]  writing 57 blocks of new data
[  604.629444]   moving 29 blocks
[  604.636696]   moving 31 blocks
[  604.640740]   moving 5 blocks
[  604.641965]  writing 2 blocks of new data
[  604.643674]   moving 6 blocks
[  604.644624]  writing 632 blocks of new data
[  604.753027] patching 71 blocks to 174
[  604.903063]   moving 27 blocks
[  604.909622]   moving 8 blocks
[  604.911047]  writing 2 blocks of new data
[  604.913826]   moving 6 blocks
[  604.915708]  writing 2 blocks of new data
[  604.916528]  writing 124 blocks of new data
[  604.953212]  writing 5 blocks of new data
[  604.960384]   moving 10 blocks
[  604.962652]  writing 5 blocks of new data
[  604.963431]  writing 14 blocks of new data
[  604.966204]   moving 4 blocks
[  604.967217]  writing 5 blocks of new data
[  604.969947]  writing 5 blocks of new data
[  604.977774]   moving 30 blocks
[  604.991083] patching 75 blocks to 75
[  605.059013]  writing 759 blocks of new data
[  605.147467]   moving 12 blocks
[  605.150777]  writing 13 blocks of new data
[  605.154006]  writing 13 blocks of new data
[  605.161501]   moving 30 blocks
[  605.164810]  writing 1024 blocks of new data
[  605.439162]  writing 1024 blocks of new data
[  605.753531]  writing 1024 blocks of new data
[  606.115539]  writing 906 blocks of new data
[  606.409790] patching 536 blocks to 8
[  606.448867]   moving 28 blocks
[  606.476747]  writing 2 blocks of new data
[  606.501320]  writing 5 blocks of new data
[  606.522517]  writing 1024 blocks of new data
[  606.676797]  writing 1024 blocks of new data
[  606.810600]  writing 1024 blocks of new data
[  606.949679]  writing 935 blocks of new data
[  607.083973]  writing 5 blocks of new data
[  607.095343] patching 75 blocks to 75
[  607.168951]   moving 20 blocks
[  607.171774]  writing 1024 blocks of new data
[  607.321611]  writing 212 blocks of new data
[  607.366558] patching 81 blocks to 81
[  607.438165]  writing 7 blocks of new data
[  607.440840]   moving 8 blocks
[  607.441632]  writing 1024 blocks of new data
[  607.665809]  writing 1024 blocks of new data
[  607.892030]  writing 1024 blocks of new data
[  608.039776]  writing 1024 blocks of new data
[  608.194973]  writing 1024 blocks of new data
[  608.350092]  writing 324 blocks of new data
[  608.411684]   moving 9 blocks
[  608.416398] patching 9 blocks to 15
[  608.437787]  writing 123 blocks of new data
[  608.458619]  writing 2 blocks of new data
[  608.459939]   moving 2 blocks
[  608.460488]  writing 21 blocks of new data
[  608.465513]  writing 4 blocks of new data
[  608.466581]  writing 1024 blocks of new data
[  608.616169]  writing 1024 blocks of new data
[  608.747775]  writing 1024 blocks of new data
[  608.884890]  writing 316 blocks of new data
[  608.945133]   moving 34 blocks
[  608.948437]  writing 3 blocks of new data
[  608.949058]  writing 2 blocks of new data
[  608.952719]   moving 21 blocks
[  608.958088]   moving 20 blocks
[  608.969340] patching 74 blocks to 75
[  609.041622]   moving 9 blocks
[  609.046975]   moving 14 blocks
[  609.049588]   moving 2 blocks
[  609.050799]   moving 2 blocks
[  609.052511]   moving 4 blocks
[  609.053658]  writing 853 blocks of new data
[  609.183584]   moving 14 blocks
[  609.185715]  writing 2 blocks of new data
[  609.187388]   moving 5 blocks
[  609.196054] patching 81 blocks to 81
[  609.267975]   moving 20 blocks
[  609.269319]  writing 3 blocks of new data
[  609.273666]   moving 27 blocks
[  609.281752]   moving 10 blocks
[  609.316123]   moving 8 blocks
[  609.340210]   moving 12 blocks
[  609.362750] patching 4 blocks to 9
[  609.396832]  writing 10 blocks of new data
[  609.425328]   moving 29 blocks
[  609.451079]  writing 9 blocks of new data
[  609.476356]  writing 2 blocks of new data
[  609.501308]  writing 6 blocks of new data
[  609.528695]   moving 14 blocks
[  609.554119]  writing 2 blocks of new data
[  609.579495]  writing 11 blocks of new data
[  609.612380]   moving 9 blocks
[  609.636132]  writing 286 blocks of new data
[  609.684825]   moving 25 blocks
[  609.687474]  writing 2 blocks of new data
[  609.697267] patching 80 blocks to 81
[  609.764007]  writing 11 blocks of new data
[  609.767255]  writing 121 blocks of new data
[  609.789607]   moving 6 blocks
[  609.790832]  writing 90 blocks of new data
[  609.812367]   moving 8 blocks
[  609.814765]   moving 8 blocks
[  609.820063]   moving 21 blocks
[  609.823935]   moving 8 blocks
[  609.827568]   moving 21 blocks
[  609.831917]   moving 4 blocks
[  609.832845]  writing 2 blocks of new data
[  609.833691]  writing 1024 blocks of new data
[  609.974101]  writing 1024 blocks of new data
[  610.117599]  writing 1024 blocks of new data
[  610.261245]  writing 1024 blocks of new data
[  610.389656]  writing 1024 blocks of new data
[  610.529063]  writing 1024 blocks of new data
[  610.655140]  writing 649 blocks of new data
[  610.768138]   moving 2 blocks
[  610.769696]   moving 5 blocks
[  610.770667]  writing 2 blocks of new data
[  610.771541]  writing 2 blocks of new data
[  610.772166]  writing 15 blocks of new data
[  610.781672]   moving 38 blocks
[  610.785189]  writing 3 blocks of new data
[  610.785864]  writing 1024 blocks of new data
[  610.926729]  writing 1024 blocks of new data
[  611.039573]  writing 1024 blocks of new data
[  611.153097]  writing 1024 blocks of new data
[  611.289824]  writing 1024 blocks of new data
[  611.445113]  writing 1024 blocks of new data
[  611.601131]  writing 571 blocks of new data
[  611.683105]   moving 33 blocks
[  611.692161]   moving 36 blocks
[  611.694976]  writing 7 blocks of new data
[  611.698644]   moving 5 blocks
[  611.699618]  writing 6 blocks of new data
[  611.700946]  writing 7 blocks of new data
[  611.701997]  writing 36 blocks of new data
[  611.713325]   moving 26 blocks
[  611.718984]   moving 35 blocks
[  611.723204]  writing 514 blocks of new data
[  611.806324]   moving 3 blocks
[  611.810024]   moving 26 blocks
[  611.813379]  writing 3 blocks of new data
[  611.814355]  writing 713 blocks of new data
[  611.914558]   moving 12 blocks
[  611.915425]  writing 488 blocks of new data
[  611.983366]  writing 15 blocks of new data
[  611.990882]   moving 20 blocks
[  611.993602]   moving 8 blocks
[  611.996742]  writing 245 blocks of new data
[  612.033252]   moving 28 blocks
[  612.037366]  writing 20 blocks of new data
[  612.041248]  writing 3 blocks of new data
[  612.042873]   moving 2 blocks
[  612.045854]   moving 14 blocks
[  612.049484]   moving 3 blocks
[  612.053458]   moving 20 blocks
[  612.055778]  writing 2 blocks of new data
[  612.057936]  writing 2 blocks of new data
[  612.058500]  writing 2 blocks of new data
[  612.060920] stashing 1 blocks to 528c243697fe30f781b2e3e3b529d5585b2f8b4c
[  612.060936]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/528c243697fe30f781b2e3e3b529d5585b2f8b4c
[  612.073426] patching 149 blocks to 7
[  612.091082] patching 5 blocks to 140
[  612.269732] patching 2301 blocks to 23
[  612.904404] patching 12832 blocks to 154
[  613.040043] stashing 1 blocks to 9ede2539c05b03c2b1d67e477e3d10edd725c830
[  613.040068]  writing 1 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9ede2539c05b03c2b1d67e477e3d10edd725c830
[  613.116202] patching 709 blocks to 11
[  613.675713] patching 11126 blocks to 66
[  613.727466] patching 1 blocks to 1
[  613.736830] patching 1 blocks to 1
[  613.746678] patching 4 blocks to 4
[  613.763878] patching 31 blocks to 31
[  613.832778] patching 5 blocks to 5
[  613.948006] patching 1168 blocks to 12
[  613.965956] patching 16 blocks to 21
[  613.995150] patching 59 blocks to 10
[  614.020339] patching 48 blocks to 84
[  614.119134] patching 5 blocks to 4
[  614.132454] patching 4 blocks to 4
[  614.146159] patching 7 blocks to 8
[  614.161535] patching 1 blocks to 1
[  614.171918] patching 8 blocks to 9
[  614.194236] patching 30 blocks to 12
[  614.215515] patching 8 blocks to 12
[  614.243269] patching 45 blocks to 45
[  614.315228] patching 129 blocks to 116
[  614.465507] patching 85 blocks to 162
[  614.619664] patching 6 blocks to 7
[  614.638737] patching 22 blocks to 40
[  614.693999] patching 53 blocks to 64
[  614.860992] patching 1072 blocks to 9
[  614.873987] patching 11 blocks to 11
[  614.894839] patching 18 blocks to 14
[  614.920734] patching 38 blocks to 47
[  614.972090] patching 61 blocks to 71
[  615.044197] patching 63 blocks to 73
[  615.116795] patching 55 blocks to 55
[  615.178176] patching 27 blocks to 51
[  615.232126] patching 8 blocks to 8
[  615.264709] patching 154 blocks to 207
[  615.481575] patching 8 blocks to 16
[  615.504500] patching 9 blocks to 4
[  615.518592] patching 9 blocks to 10
[  615.536886] patching 5 blocks to 5
[  615.559292] patching 44 blocks to 47
[  615.610152] patching 13 blocks to 15
[  615.629876]   moving 1 blocks
[  615.630608]   moving 1 blocks
[  615.631608] patching 5 blocks to 5
[  615.644977] patching 11 blocks to 12
[  615.671567] patching 56 blocks to 8
[  615.689497] patching 5 blocks to 4
[  615.702887] patching 5 blocks to 5
[  615.716943] patching 7 blocks to 7
[  615.730825]   moving 1 blocks
[  615.758132] patching 138 blocks to 186
[  615.973785] patching 5 blocks to 5
[  615.988653] patching 18 blocks to 27
[  616.022531] patching 12 blocks to 10
[  616.039739] patching 9 blocks to 9
[  616.058400] patching 9 blocks to 20
[  616.085273]   moving 2 blocks
[  616.086679]   moving 1 blocks
[  616.087617]   moving 1 blocks
[  616.088771]   moving 2 blocks
[  616.090277]   moving 2 blocks
[  616.091056]  writing 1 blocks of new data
[  616.093044] patching 6 blocks to 7
[  616.110068]   moving 1 blocks
[  616.111126]   moving 1 blocks
[  616.111763]  writing 1 blocks of new data
[  616.112440]  writing 1 blocks of new data
[  616.113140]  writing 2 blocks of new data
[  616.114730] patching 3 blocks to 3
[  616.125111] patching 4 blocks to 4
[  616.137404]   moving 2 blocks
[  616.138638]   moving 2 blocks
[  616.139753]   moving 1 blocks
[  616.141316]   moving 4 blocks
[  616.143072]   moving 2 blocks
[  616.144484] patching 1 blocks to 1
[  616.153303]  writing 2 blocks of new data
[  616.154077]  writing 1 blocks of new data
[  616.154717]  writing 1 blocks of new data
[  616.155766]   moving 1 blocks
[  616.156975]   moving 2 blocks
[  616.157994]  writing 1 blocks of new data
[  616.159042]   moving 1 blocks
[  616.159662]  writing 31 blocks of new data
[  616.167456]   moving 1 blocks
[  616.168105]  writing 1 blocks of new data
[  616.169664] patching 4 blocks to 4
[  616.181559]  writing 1 blocks of new data
[  616.182248]  writing 2 blocks of new data
[  616.183285]  writing 8 blocks of new data
[  616.186089]   moving 6 blocks
[  616.188169]   moving 1 blocks
[  616.189339]   moving 2 blocks
[  616.190456]   moving 1 blocks
[  616.191081]  writing 23 blocks of new data
[  616.199197]   moving 2 blocks
[  616.200232]   moving 1 blocks
[  616.200843]  writing 1 blocks of new data
[  616.201804]   moving 1 blocks
[  616.202919]   moving 2 blocks
[  616.204287]   moving 1 blocks
[  616.205213]   moving 1 blocks
[  616.210756]   moving 27 blocks
[  616.214446]   moving 1 blocks
[  616.215057]  writing 1 blocks of new data
[  616.216063] patching 1 blocks to 1
[  616.225132]  writing 1 blocks of new data
[  616.236316]   moving 55 blocks
[  616.244208]  writing 1 blocks of new data
[  616.245233]   moving 1 blocks
[  616.245829]  writing 1 blocks of new data
[  616.246424]  writing 1 blocks of new data
[  616.247056]  writing 1 blocks of new data
[  616.248071] patching 1 blocks to 1
[  616.257396]   moving 2 blocks
[  616.258749]   moving 1 blocks
[  616.259700]   moving 1 blocks
[  616.260679]   moving 1 blocks
[  616.261746]   moving 2 blocks
[  616.262813]   moving 1 blocks
[  616.263736]   moving 1 blocks
[  616.264781]   moving 1 blocks
[  616.266271]   moving 4 blocks
[  616.267956]   moving 2 blocks
[  616.269512] patching 2 blocks to 3
[  616.279998]   moving 1 blocks
[  616.281156]   moving 2 blocks
[  616.282282]   moving 1 blocks
[  616.284256]   moving 7 blocks
[  616.286178]   moving 1 blocks
[  616.286740]  writing 12 blocks of new data
[  616.361075] patching 375 blocks to 455
[  616.815532]   moving 1 blocks
[  616.816506]   moving 1 blocks
[  616.819220] patching 5 blocks to 5
[  616.833151]   moving 2 blocks
[  616.833995]   moving 1 blocks
[  616.834865]   moving 2 blocks
[  616.835811]   moving 2 blocks
[  616.836620]  writing 24 blocks of new data
[  616.842566] patching 4 blocks to 4
[  616.856054] patching 26 blocks to 28
[  616.877540] patching 2 blocks to 3
[  616.888144] patching 1 blocks to 1
[  616.897130]   moving 1 blocks
[  616.898155]   moving 2 blocks
[  616.899526] patching 6 blocks to 6
[  616.912546]   moving 1 blocks
[  616.913099]  writing 1 blocks of new data
[  616.914005]   moving 1 blocks
[  616.914557]  writing 6 blocks of new data
[  616.915906]   moving 1 blocks
[  616.916755]   moving 1 blocks
[  616.917773]   moving 2 blocks
[  616.918904] patching 1 blocks to 1
[  616.928115]   moving 2 blocks
[  616.929005]   moving 1 blocks
[  616.929559]  writing 1 blocks of new data
[  616.930132]  writing 1 blocks of new data
[  616.931153]   moving 2 blocks
[  616.938559] patching 57 blocks to 43
[  616.967151]  writing 3 blocks of new data
[  616.971231]   moving 29 blocks
[  616.974781]   moving 2 blocks
[  616.975668]   moving 1 blocks
[  616.976553]   moving 1 blocks
[  616.984263] patching 65 blocks to 65
[  617.001169]   moving 1 blocks
[  617.002061]   moving 1 blocks
[  617.002944]   moving 1 blocks
[  617.003496]  writing 1 blocks of new data
[  617.005657]  writing 1 blocks of new data
[  617.006209]  writing 1 blocks of new data
[  617.007130]   moving 1 blocks
[  617.008106]   moving 2 blocks
[  617.009113]   moving 2 blocks
[  617.009664]  writing 1 blocks of new data
[  617.010623] patching 1 blocks to 1
[  617.028741] patching 83 blocks to 87
[  617.075598] patching 6 blocks to 10
[  617.090375]  writing 2 blocks of new data
[  617.091621]   moving 1 blocks
[  617.103774]   moving 2 blocks
[  617.105165]   moving 5 blocks
[  617.121307]   moving 157 blocks
[  617.135709]   moving 1 blocks
[  617.159020]  writing 1 blocks of new data
[  617.193738] patching 110 blocks to 135
[  617.405560] patching 2 blocks to 2
[  617.436614]  writing 1 blocks of new data
[  617.473038] patching 67 blocks to 72
[  617.581925] patching 9 blocks to 9
[  617.623423] patching 1 blocks to 1
[  617.633482]   moving 1 blocks
[  617.642338]   moving 78 blocks
[  617.649336]  writing 1 blocks of new data
[  617.650272] patching 1 blocks to 1
[  617.659508]  writing 1 blocks of new data
[  617.660752] patching 3 blocks to 4
[  617.671862] patching 4 blocks to 4
[  617.682643]   moving 2 blocks
[  618.190442] patching 4560 blocks to 4560
[  618.601366] patching 1 blocks to 1
[  618.610454]  writing 1 blocks of new data
[  618.611058]  writing 133 blocks of new data
[  618.636179] patching 1 blocks to 1
[  618.644920]  writing 1 blocks of new data
[  618.646187] patching 4 blocks to 4
[  618.658110]  writing 17 blocks of new data
[  618.663638]  writing 8 blocks of new data
[  618.666426]  writing 1 blocks of new data
[  618.667610] patching 4 blocks to 4
[  618.679995]   moving 1 blocks
[  618.680530]  writing 106 blocks of new data
[  618.692425]  writing 51 blocks of new data
[  618.701197]   moving 1 blocks
[  618.701986] patching 1 blocks to 1
[  618.711140]   moving 2 blocks
[  618.712016]   moving 1 blocks
[  618.713505]   moving 8 blocks
[  618.716069]   moving 2 blocks
[  618.716763]   moving 2 blocks
[  618.717280]  writing 1 blocks of new data
[  618.718185]   moving 2 blocks
[  618.719061]   moving 1 blocks
[  618.719548]  writing 2 blocks of new data
[  618.720438] patching 4 blocks to 4
[  618.730644]   moving 1 blocks
[  618.731172]  writing 8 blocks of new data
[  618.733840]   moving 17 blocks
[  618.737451]  writing 1 blocks of new data
[  618.738018]  writing 1 blocks of new data
[  618.739001]   moving 2 blocks
[  618.739849]   moving 1 blocks
[  618.740991]   moving 4 blocks
[  618.742192]   moving 1 blocks
[  618.742676]  writing 1 blocks of new data
[  618.747697] patching 45 blocks to 45
[  618.770026] patching 76 blocks to 75
[  618.839489]   moving 8 blocks
[  618.840287]  writing 1 blocks of new data
[  618.840908]  writing 19 blocks of new data
[  618.845550]  writing 1 blocks of new data
[  618.846603]   moving 2 blocks
[  618.847189]  writing 1 blocks of new data
[  618.848120] patching 1 blocks to 1
[  618.863194] patching 61 blocks to 61
[  618.880639]   moving 1 blocks
[  618.881495]   moving 1 blocks
[  618.891579]   moving 92 blocks
[  618.898759]  writing 1 blocks of new data
[  618.899365]  writing 75 blocks of new data
[  618.915138]   moving 9 blocks
[  618.917621]   moving 1 blocks
[  618.918417]   moving 1 blocks
[  618.918920]  writing 4 blocks of new data
[  618.919708] patching 1 blocks to 1
[  618.929764]   moving 1 blocks
[  618.931041]   moving 5 blocks
[  618.931963]  writing 1 blocks of new data
[  618.933955]  writing 1 blocks of new data
[  618.934842]   moving 1 blocks
[  618.935770]   moving 2 blocks
[  618.936294]  writing 1 blocks of new data
[  618.937168]   moving 1 blocks
[  618.937657]  writing 1 blocks of new data
[  618.938661]   moving 2 blocks
[  618.940006]   moving 3 blocks
[  618.941307]   moving 3 blocks
[  618.942496]   moving 2 blocks
[  618.943765]   moving 3 blocks
[  618.944997]   moving 2 blocks
[  618.945981] patching 2 blocks to 2
[  618.955054]   moving 1 blocks
[  618.956036]   moving 2 blocks
[  618.956930]  writing 1 blocks of new data
[  618.957927]   moving 2 blocks
[  618.958809] patching 1 blocks to 1
[  618.967638]   moving 1 blocks
[  618.970691]   moving 16 blocks
[  618.972011]   moving 1 blocks
[  618.972984]   moving 2 blocks
[  618.988550] patching 139 blocks to 154
[  619.106588]   moving 1 blocks
[  619.107114]  writing 2 blocks of new data
[  619.108061]   moving 1 blocks
[  619.108887]   moving 1 blocks
[  619.109536]   moving 1 blocks
[  619.110505]   moving 2 blocks
[  619.111444] patching 1 blocks to 1
[  619.120439] patching 1 blocks to 1
[  619.129561]   moving 1 blocks
[  619.130081]  writing 1 blocks of new data
[  619.130692]  writing 44 blocks of new data
[  619.141071]   moving 1 blocks
[  619.142008]   moving 2 blocks
[  619.142560]  writing 1 blocks of new data
[  619.143140]  writing 1 blocks of new data
[  619.143687]  writing 3 blocks of new data
[  619.144757]   moving 2 blocks
[  619.145978]   moving 2 blocks
[  619.146806]  writing 1 blocks of new data
[  619.147716]   moving 1 blocks
[  619.148597]   moving 1 blocks
[  619.149479]   moving 2 blocks
[  619.157340] patching 82 blocks to 82
[  619.234570]   moving 63 blocks
[  619.241648]   moving 1 blocks
[  619.242176]  writing 1 blocks of new data
[  619.243109]   moving 1 blocks
[  619.243624]  writing 1 blocks of new data
[  619.244549]   moving 1 blocks
[  619.245066]  writing 1 blocks of new data
[  619.245930]   moving 1 blocks
[  619.246758] patching 1 blocks to 1
[  619.255589]  writing 1 blocks of new data
[  619.256160]  writing 1 blocks of new data
[  619.257107]   moving 1 blocks
[  619.257651]  writing 1 blocks of new data
[  619.258719]   moving 1 blocks
[  619.259537]   moving 1 blocks
[  619.260086]  writing 1 blocks of new data
[  619.260766]   moving 1 blocks
[  619.264550] patching 31 blocks to 30
[  619.296564] patching 2 blocks to 3
[  619.307101]   moving 16 blocks
[  619.310153]   moving 1 blocks
[  619.311148]   moving 2 blocks
[  619.312445]   moving 2 blocks
[  619.313320]   moving 1 blocks
[  619.313841]  writing 1 blocks of new data
[  619.315205]   moving 5 blocks
[  619.316102]  writing 1 blocks of new data
[  619.317118]   moving 2 blocks
[  619.318015]  writing 13 blocks of new data
[  619.321274]   moving 2 blocks
[  619.321860]  writing 1 blocks of new data
[  619.322824]   moving 1 blocks
[  619.323387]  writing 1 blocks of new data
[  619.324046]  writing 1 blocks of new data
[  619.324960]   moving 1 blocks
[  619.329711] patching 51 blocks to 50
[  619.346498]   moving 1 blocks
[  619.347052]  writing 5 blocks of new data
[  619.348729]   moving 2 blocks
[  619.349979]   moving 4 blocks
[  619.351389]   moving 1 blocks
[  619.352184]   moving 1 blocks
[  619.353128]   moving 2 blocks
[  619.357987]   moving 33 blocks
[  619.362986]  writing 1 blocks of new data
[  619.363623]  writing 1 blocks of new data
[  619.364178]  writing 1 blocks of new data
[  619.365224]   moving 2 blocks
[  619.366383]   moving 1 blocks
[  619.367358] patching 2 blocks to 3
[  619.377194]  writing 1 blocks of new data
[  619.378231]   moving 2 blocks
[  619.379028]   moving 1 blocks
[  619.379565]  writing 1 blocks of new data
[  619.380664]   moving 3 blocks
[  619.381832]   moving 1 blocks
[  619.382679]   moving 1 blocks
[  619.383497]   moving 1 blocks
[  619.384037]  writing 1 blocks of new data
[  619.384921]   moving 1 blocks
[  619.385799]   moving 2 blocks
[  619.386653]   moving 1 blocks
[  619.387562]   moving 2 blocks
[  619.388379]  writing 1 blocks of new data
[  619.392115] patching 29 blocks to 29
[  619.407159] patching 17 blocks to 27
[  619.438862] patching 5 blocks to 9
[  619.454676]   moving 2 blocks
[  619.455704]   moving 2 blocks
[  619.456860]   moving 1 blocks
[  619.458021]   moving 2 blocks
[  619.677339] patching 1445 blocks to 1778
[  622.070769]   moving 1 blocks
[  622.071816]   moving 1 blocks
[  622.072808]   moving 3 blocks
[  622.073651]  writing 1 blocks of new data
[  622.074529]   moving 1 blocks
[  622.077984]   moving 27 blocks
[  622.080322]  writing 1 blocks of new data
[  622.085183] patching 36 blocks to 36
[  622.112666] patching 5 blocks to 136
[  622.290551]   moving 4 blocks
[  622.292269] patching 1 blocks to 1
[  622.301749]   moving 2 blocks
[  622.302859]   moving 1 blocks
[  622.303555]  writing 1 blocks of new data
[  622.304654]   moving 1 blocks
[  622.305856]   moving 2 blocks
[  622.306833]  writing 5 blocks of new data
[  622.308191]  writing 1 blocks of new data
[  622.309438] patching 2 blocks to 2
[  622.319230]   moving 2 blocks
[  622.322174]   moving 10 blocks
[  622.324980]  writing 7 blocks of new data
[  622.327025] patching 1 blocks to 1
[  622.350188] patching 81 blocks to 81
[  622.421953] patching 1 blocks to 1
[  622.430827]  writing 1 blocks of new data
[  622.432162]   moving 1 blocks
[  622.433381]   moving 2 blocks
[  622.434783]   moving 1 blocks
[  622.435718]   moving 1 blocks
[  622.436337]  writing 1 blocks of new data
[  622.437552]   moving 2 blocks
[  622.443464]   moving 29 blocks
[  622.448156]  writing 1 blocks of new data
[  622.449201] patching 1 blocks to 1
[  622.457961]  writing 1 blocks of new data
[  622.459193]   moving 2 blocks
[  622.471023]   moving 63 blocks
[  622.480416]   moving 1 blocks
[  622.481382]   moving 1 blocks
[  622.483051] patching 5 blocks to 4
[  622.495150]  writing 13 blocks of new data
[  622.499131]  writing 1 blocks of new data
[  622.499837]  writing 1 blocks of new data
[  622.501819] patching 6 blocks to 7
[  622.516659]   moving 2 blocks
[  622.531027] patching 70 blocks to 81
[  622.609477]  writing 1 blocks of new data
[  622.610463] patching 1 blocks to 1
[  622.619511] patching 1 blocks to 1
[  622.628554]   moving 1 blocks
[  622.629529]   moving 2 blocks
[  622.630084]  writing 1 blocks of new data
[  622.631010]   moving 1 blocks
[  622.631548]  writing 5 blocks of new data
[  622.633278]   moving 2 blocks
[  622.634241]   moving 2 blocks
[  622.635440]   moving 2 blocks
[  622.636263]  writing 96 blocks of new data
[  622.648651]   moving 1 blocks
[  622.649441]   moving 1 blocks
[  622.650092]   moving 1 blocks
[  622.651072]   moving 2 blocks
[  622.651623]  writing 1 blocks of new data
[  622.655176]   moving 28 blocks
[  622.658479]   moving 2 blocks
[  622.659380]   moving 1 blocks
[  622.660139]   moving 1 blocks
[  622.663700]   moving 28 blocks
[  622.666468]  writing 6 blocks of new data
[  622.667895]   moving 1 blocks
[  622.668690] patching 3 blocks to 3
[  622.678532]   moving 1 blocks
[  622.679054]  writing 1 blocks of new data
[  622.680055]   moving 2 blocks
[  622.683407]   moving 23 blocks
[  622.687606]   moving 2 blocks
[  622.688163]  writing 1 blocks of new data
[  622.689082]   moving 1 blocks
[  622.691652] patching 14 blocks to 22
[  622.719476] patching 1 blocks to 1
[  622.728284]  writing 2 blocks of new data
[  622.729299]   moving 1 blocks
[  622.729850]  writing 1 blocks of new data
[  622.730919]   moving 2 blocks
[  622.731833]  writing 6 blocks of new data
[  622.733437]   moving 1 blocks
[  622.734411]   moving 2 blocks
[  622.735287]   moving 1 blocks
[  622.736103]   moving 1 blocks
[  622.736890]   moving 1 blocks
[  622.738113] patching 4 blocks to 4
[  622.750481]   moving 2 blocks
[  622.751729]   moving 2 blocks
[  622.752706] patching 1 blocks to 2
[  622.763408]   moving 9 blocks
[  622.764439]   moving 1 blocks
[  622.765001]  writing 52 blocks of new data
[  622.775890] patching 6 blocks to 8
[  622.789865]  writing 1 blocks of new data
[  622.792178]   moving 15 blocks
[  622.793393]  writing 1 blocks of new data
[  622.801369] patching 75 blocks to 75
[  622.871692]   moving 1 blocks
[  622.872219]  writing 465 blocks of new data
[  622.927288]  writing 1 blocks of new data
[  622.936470] patching 81 blocks to 81
[  623.008904] patching 6 blocks to 8
[  623.025468]   moving 1 blocks
[  623.026350] patching 1 blocks to 1
[  623.035155]  writing 1 blocks of new data
[  623.035832]   moving 1 blocks
[  623.036407]  writing 1 blocks of new data
[  623.037316]   moving 1 blocks
[  623.038572]   moving 4 blocks
[  623.039482]  writing 1 blocks of new data
[  623.040490]   moving 2 blocks
[  623.042218] patching 7 blocks to 8
[  623.055981]  writing 1 blocks of new data
[  623.057065]   moving 2 blocks
[  623.058017]   moving 1 blocks
[  623.058894]   moving 1 blocks
[  623.059444]  writing 9 blocks of new data
[  623.060582]   moving 1 blocks
[  623.062049] patching 6 blocks to 6
[  623.072070] patching 1 blocks to 1
[  623.081401]   moving 4 blocks
[  623.082361]   moving 1 blocks
[  623.086197]   moving 31 blocks
[  623.089106]   moving 1 blocks
[  623.090107]   moving 2 blocks
[  623.091347]   moving 2 blocks
[  623.092442] patching 3 blocks to 3
[  623.102432]   moving 1 blocks
[  623.103345]   moving 1 blocks
[  623.104345]   moving 2 blocks
[  623.108048]   moving 29 blocks
[  623.111593]  writing 1024 blocks of new data
[  623.352432]  writing 19 blocks of new data
[  623.483270]   moving 1106 blocks
[  623.738106]   moving 25 blocks
[  623.756416]  writing 1 blocks of new data
[  623.761689] patching 38 blocks to 45
[  623.800022]   moving 7 blocks
[  623.801482]   moving 2 blocks
[  623.802362]   moving 1 blocks
[  623.803244] patching 1 blocks to 1
[  623.812181]   moving 1 blocks
[  623.812837]   moving 1 blocks
[  623.813692]   moving 1 blocks
[  623.814718]   moving 2 blocks
[  623.815881]   moving 1 blocks
[  623.816819]   moving 2 blocks
[  623.817885] patching 5 blocks to 7
[  623.831532]  writing 1 blocks of new data
[  623.832465]   moving 1 blocks
[  623.833385]   moving 2 blocks
[  623.834219]  writing 1 blocks of new data
[  623.836357]   moving 9 blocks
[  623.840199] patching 14 blocks to 16
[  623.851461]  writing 1 blocks of new data
[  623.858581]   moving 63 blocks
[  623.863165]  writing 1 blocks of new data
[  623.864226]   moving 2 blocks
[  623.864780]  writing 2 blocks of new data
[  623.865370]  writing 14 blocks of new data
[  623.868342]   moving 2 blocks
[  623.868892]  writing 1 blocks of new data
[  623.869486]  writing 1 blocks of new data
[  623.870425]   moving 2 blocks
[  623.871438]   moving 2 blocks
[  623.872256]   moving 1 blocks
[  623.874454]   moving 14 blocks
[  623.877907]   moving 2 blocks
[  623.879918] patching 2 blocks to 2
[  623.889886] patching 5 blocks to 6
[  623.900345]   moving 1 blocks
[  623.900872]  writing 1 blocks of new data
[  623.901959]   moving 2 blocks
[  623.903122] patching 1 blocks to 1
[  623.912136]   moving 1 blocks
[  623.913154]   moving 2 blocks
[  623.913756]  writing 17 blocks of new data
[  623.917968]  writing 1 blocks of new data
[  623.918895]   moving 1 blocks
[  623.919408]  writing 1 blocks of new data
[  623.925137] patching 46 blocks to 49
[  623.990026] patching 61 blocks to 61
[  624.004966]   moving 2 blocks
[  624.006213]   moving 2 blocks
[  624.007047]  writing 2 blocks of new data
[  624.008015]   moving 1 blocks
[  624.008912] patching 1 blocks to 1
[  624.018205]   moving 2 blocks
[  624.025097]   moving 63 blocks
[  624.029696]  writing 1 blocks of new data
[  624.030732]   moving 2 blocks
[  624.032232]   moving 4 blocks
[  624.033143]  writing 15 blocks of new data
[  624.036847] patching 2 blocks to 2
[  624.045996]   moving 1 blocks
[  624.046549]  writing 171 blocks of new data
[  624.067768] patching 1 blocks to 1
[  624.077472]   moving 11 blocks
[  624.080020]   moving 4 blocks
[  624.080994]   moving 1 blocks
[  624.081556]  writing 4 blocks of new data
[  624.084626] patching 5 blocks to 6
[  624.096122]   moving 1 blocks
[  624.096643]  writing 1 blocks of new data
[  624.106615]   moving 99 blocks
[  624.112873]  writing 1 blocks of new data
[  624.113509]  writing 1 blocks of new data
[  624.114539]   moving 2 blocks
[  624.115094]  writing 18 blocks of new data
[  624.117560]  writing 12 blocks of new data
[  624.120762] patching 1 blocks to 2
[  624.130120]   moving 1 blocks
[  624.130698]  writing 1 blocks of new data
[  624.131626]   moving 1 blocks
[  624.132210]  writing 1 blocks of new data
[  624.132789]  writing 1 blocks of new data
[  624.133807]   moving 2 blocks
[  624.135028]   moving 2 blocks
[  624.145866] patching 82 blocks to 82
[  624.217677]   moving 1 blocks
[  624.221101]   moving 27 blocks
[  624.225456]   moving 1 blocks
[  624.226309]   moving 1 blocks
[  624.227301]   moving 2 blocks
[  624.228439]   moving 1 blocks
[  624.228961]  writing 23 blocks of new data
[  624.233460]  writing 1 blocks of new data
[  624.251292] patching 164 blocks to 178
[  624.293327]  writing 1 blocks of new data
[  624.294648]   moving 4 blocks
[  624.296216]   moving 2 blocks
[  624.297154] patching 1 blocks to 1
[  624.306634]   moving 4 blocks
[  624.307931]   moving 2 blocks
[  624.308849]   moving 2 blocks
[  624.309404]  writing 1 blocks of new data
[  624.310456]   moving 2 blocks
[  624.311652]   moving 1 blocks
[  624.312529]   moving 1 blocks
[  624.313347]   moving 1 blocks
[  624.314264]   moving 2 blocks
[  624.315098]   moving 1 blocks
[  624.316079]   moving 2 blocks
[  624.319124] patching 22 blocks to 18
[  624.339443] patching 1 blocks to 1
[  624.348045]  writing 19 blocks of new data
[  624.351631] patching 1 blocks to 1
[  624.360629]   moving 1 blocks
[  624.364105] patching 23 blocks to 23
[  624.392658] patching 4 blocks to 4
[  624.404428] patching 1 blocks to 1
[  624.413515]   moving 1 blocks
[  624.414407]   moving 1 blocks
[  624.415293]   moving 1 blocks
[  624.416238]   moving 2 blocks
[  624.424136] patching 75 blocks to 75
[  624.505694]   moving 28 blocks
[  624.510628]   moving 2 blocks
[  624.512136]   moving 2 blocks
[  624.513408]   moving 2 blocks
[  624.514945]   moving 2 blocks
[  624.520303]   moving 26 blocks
[  624.524133]  writing 25 blocks of new data
[  624.533210]   moving 6 blocks
[  624.535017]   moving 1 blocks
[  624.535666]  writing 1 blocks of new data
[  624.539172]   moving 15 blocks
[  624.542163]   moving 1 blocks
[  624.544806]   moving 25 blocks
[  624.549206]   moving 1 blocks
[  624.550344]   moving 2 blocks
[  624.551050]  writing 5 blocks of new data
[  624.552586]   moving 1 blocks
[  624.554105]   moving 4 blocks
[  624.555425] patching 1 blocks to 1
[  624.564710]   moving 1 blocks
[  624.565916]   moving 2 blocks
[  624.572440] patching 63 blocks to 29
[  624.606880]  writing 1 blocks of new data
[  624.607939]   moving 2 blocks
[  624.692886] patching 916 blocks to 850
[  625.872941]   moving 1 blocks
[  625.874246]   moving 2 blocks
[  625.875255]  writing 1 blocks of new data
[  625.886009] patching 81 blocks to 82
[  625.957654]  writing 1 blocks of new data
[  625.958770]   moving 2 blocks
[  625.959598]  writing 1 blocks of new data
[  625.960148]  writing 1 blocks of new data
[  625.961190]   moving 2 blocks
[  625.961905]   moving 1 blocks
[  625.962798]   moving 2 blocks
[  625.963753]   moving 2 blocks
[  625.969160] patching 61 blocks to 61
[  625.985355] patching 1 blocks to 1
[  625.994480]   moving 2 blocks
[  626.001075]   moving 63 blocks
[  626.007908] patching 1 blocks to 1
[  626.022462]   moving 63 blocks
[  626.029345]   moving 1 blocks
[  626.030204]   moving 1 blocks
[  626.030755]  writing 1 blocks of new data
[  626.031666] patching 1 blocks to 1
[  626.040995]   moving 1 blocks
[  626.041866]   moving 1 blocks
[  626.042842]   moving 2 blocks
[  626.043701]  writing 1 blocks of new data
[  626.044893]   moving 7 blocks
[  626.046432]   moving 3 blocks
[  626.047596]   moving 1 blocks
[  626.048200]  writing 8 blocks of new data
[  626.049042]  writing 13 blocks of new data
[  626.052428]   moving 1 blocks
[  626.053312]   moving 1 blocks
[  626.056757] patching 29 blocks to 25
[  626.081555]  writing 3 blocks of new data
[  626.082563]   moving 1 blocks
[  626.083462] patching 1 blocks to 1
[  626.092542]   moving 1 blocks
[  626.093423]   moving 1 blocks
[  626.094283] patching 1 blocks to 1
[  626.103987]   moving 1 blocks
[  626.104548]  writing 1 blocks of new data
[  626.105615]   moving 2 blocks
[  626.107212]   moving 4 blocks
[  626.115752]   moving 4 blocks
[  626.116995]  writing 1 blocks of new data
[  626.117568]  writing 1 blocks of new data
[  626.123936]   moving 63 blocks
[  626.132119] patching 29 blocks to 30
[  626.146783]  writing 1 blocks of new data
[  626.150612] patching 29 blocks to 30
[  626.164909]  writing 1 blocks of new data
[  626.165980]   moving 2 blocks
[  626.166906] patching 1 blocks to 1
[  626.175745]  writing 1 blocks of new data
[  626.177092] patching 5 blocks to 5
[  626.189821]   moving 1 blocks
[  626.191774] patching 13 blocks to 10
[  626.205804]   moving 1 blocks
[  626.206722]   moving 1 blocks
[  626.207272]  writing 1 blocks of new data
[  626.208210]   moving 1 blocks
[  626.208723]  writing 60 blocks of new data
[  626.222550]   moving 3 blocks
[  626.223948] stashing 2 blocks to 50fb19bd478e4bc220f42f01031be9ee603c4610
[  626.223959]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/50fb19bd478e4bc220f42f01031be9ee603c4610
[  626.256077] patching 568 blocks to 9
[  626.269539] patching 3 blocks to 4
[  627.064977] patching 16003 blocks to 157
[  627.252377] patching 364 blocks to 8
[  627.266253] patching 8 blocks to 6
[  627.294234] patching 87 blocks to 156
[  627.657858] patching 4 blocks to 2
[  627.703279] patching 14 blocks to 14
[  627.728871] patching 21 blocks to 22
[  627.754164] patching 7 blocks to 6
[  627.769954] patching 7 blocks to 11
[  627.789281] patching 5 blocks to 5
[  627.828965] patching 144 blocks to 143
[  627.862490]  writing 19 blocks of new data
[  627.868366] patching 9 blocks to 10
[  627.888375]   moving 14 blocks
[  627.911115] patching 106 blocks to 93
[  628.024308] patching 5 blocks to 5
[  628.038212] patching 8 blocks to 9
[  628.055546]   moving 5 blocks
[  628.072306] patching 88 blocks to 87
[  628.177066] patching 7 blocks to 7
[  628.255512]   moving 355 blocks
[  628.294063] patching 6 blocks to 7
[  628.309459] patching 6 blocks to 5
[  628.327886]   moving 6 blocks
[  628.328910]  writing 9 blocks of new data
[  628.334052] patching 7 blocks to 25
[  628.363401] patching 8 blocks to 8
[  628.379206] patching 7 blocks to 7
[  628.407088] patching 143 blocks to 143
[  628.435444] patching 19 blocks to 20
[  628.450082] patching 12 blocks to 10
[  628.467631]   moving 5 blocks
[  628.470667] patching 19 blocks to 19
[  628.501943] patching 27 blocks to 31
[  628.537012] patching 19 blocks to 18
[  628.565136] patching 143 blocks to 143
[  628.591571] patching 5 blocks to 5
[  628.614426] patching 115 blocks to 97
[  628.705475] patching 17 blocks to 17
[  628.729359] patching 18 blocks to 18
[  628.756103] patching 7 blocks to 7
[  628.769671]  writing 291 blocks of new data
[  628.827297] patching 143 blocks to 143
[  628.852491]   moving 6 blocks
[  628.854601] patching 6 blocks to 6
[  628.871710] patching 33 blocks to 34
[  628.910895]   moving 26 blocks
[  628.914752] patching 5 blocks to 4
[  628.927442] patching 7 blocks to 8
[  628.943766] patching 5 blocks to 4
[  628.956587] patching 6 blocks to 5
[  628.969834]   moving 11 blocks
[  628.972105] patching 7 blocks to 12
[  628.989432] patching 7 blocks to 7
[  629.003667] patching 9 blocks to 11
[  629.025429] patching 25 blocks to 29
[  629.049346] patching 10 blocks to 9
[  629.067580] patching 11 blocks to 11
[  629.085637] patching 4 blocks to 4
[  629.098352] patching 9 blocks to 9
[  629.115667] patching 19 blocks to 20
[  629.131489]  writing 64 blocks of new data
[  629.142079]  writing 11 blocks of new data
[  629.147477] patching 5 blocks to 6
[  629.161118] patching 6 blocks to 8
[  629.175437]  writing 1024 blocks of new data
[  629.281980]  writing 1024 blocks of new data
[  629.392822]  writing 606 blocks of new data
[  629.457691] patching 14 blocks to 12
[  629.474826] patching 6 blocks to 7
[  629.495625] patching 61 blocks to 61
[  629.558758]   moving 1 blocks
[  629.567546] patching 82 blocks to 81
[  629.654609] patching 56 blocks to 56
[  629.711124] patching 9 blocks to 10
[  629.729575] patching 22 blocks to 8
[  629.744654] patching 4 blocks to 4
[  629.757135]   moving 6 blocks
[  629.759434] patching 13 blocks to 14
[  629.776653] patching 5 blocks to 5
[  629.789200]  writing 553 blocks of new data
[  629.934424] patching 7 blocks to 6
[  629.971726] patching 19 blocks to 25
[  630.024799] patching 9 blocks to 10
[  630.063206] patching 34 blocks to 35
[  630.101696] patching 12 blocks to 5
[  630.114579]  writing 4 blocks of new data
[  630.120783] patching 34 blocks to 35
[  630.159609] patching 10 blocks to 11
[  630.178780]   moving 1 blocks
[  630.180045]   moving 5 blocks
[  630.184716] patching 32 blocks to 32
[  630.220812] patching 5 blocks to 5
[  630.233392] patching 6 blocks to 6
[  630.250350] patching 42 blocks to 42
[  630.280427] patching 6 blocks to 6
[  630.294145] patching 17 blocks to 7
[  630.308859] patching 8 blocks to 8
[  630.323729]   moving 5 blocks
[  630.326501] patching 14 blocks to 14
[  630.361565] patching 144 blocks to 142
[  630.558322] patching 144 blocks to 143
[  630.599189] patching 44 blocks to 44
[  630.634740] patching 11 blocks to 12
[  630.961582] patching 2604 blocks to 2599
[  633.221154] patching 10 blocks to 12
[  633.240730] patching 5 blocks to 6
[  633.253614] patching 5 blocks to 5
[  633.267933] patching 8 blocks to 9
[  633.298890] patching 144 blocks to 143
[  633.325098] patching 6 blocks to 6
[  633.339732] patching 8 blocks to 9
[  633.368773] patching 144 blocks to 143
[  633.393902]  writing 17 blocks of new data
[  633.400292] patching 21 blocks to 56
[  633.453520] patching 4 blocks to 6
[  633.467999] patching 14 blocks to 14
[  633.516611]   moving 305 blocks
[  633.535839]   moving 40 blocks
[  633.541079]  writing 2 blocks of new data
[  633.542808] patching 5 blocks to 6
[  633.558312] patching 20 blocks to 21
[  633.573794] patching 20 blocks to 20
[  633.590046] patching 19 blocks to 20
[  633.604439] patching 7 blocks to 8
[  633.621086] patching 9 blocks to 10
[  633.639207] patching 6 blocks to 7
[  633.655347] patching 21 blocks to 27
[  633.686139]  writing 24 blocks of new data
[  633.692160] patching 5 blocks to 6
[  633.718433] patching 143 blocks to 143
[  633.744995] patching 6 blocks to 7
[  633.761080] patching 20 blocks to 21
[  633.778156] patching 12 blocks to 12
[  633.797356] patching 5 blocks to 6
[  633.811359] patching 4 blocks to 4
[  633.823917] patching 4 blocks to 4
[  633.837268] patching 14 blocks to 13
[  633.855198] patching 7 blocks to 8
[  633.871664]   moving 6 blocks
[  633.874448] patching 19 blocks to 18
[  633.897897] patching 5 blocks to 6
[  633.911705] patching 19 blocks to 20
[  633.928152] patching 13 blocks to 14
[  633.958964] patching 143 blocks to 143
[  633.985878] patching 13 blocks to 13
[  634.005107] patching 6 blocks to 5
[  634.031512] patching 145 blocks to 145
[  634.060329] patching 6 blocks to 7
[  634.074430]  writing 7 blocks of new data
[  634.078686] patching 30 blocks to 30
[  634.116116] patching 17 blocks to 17
[  634.137644] patching 6 blocks to 7
[  634.151542] patching 8 blocks to 9
[  634.168935]   moving 29 blocks
[  634.172297] patching 2 blocks to 2
[  634.182001]   moving 7 blocks
[  634.183013]  writing 6 blocks of new data
[  634.184538]  writing 44 blocks of new data
[  634.204881] patching 143 blocks to 143
[  634.231261] patching 7 blocks to 8
[  634.248823] patching 19 blocks to 20
[  634.272752] patching 114 blocks to 118
[  634.345805] patching 6 blocks to 7
[  634.362460] patching 29 blocks to 29
[  634.398572]  writing 7 blocks of new data
[  634.403454] patching 16 blocks to 18
[  634.425731]  writing 3 blocks of new data
[  634.428002] patching 10 blocks to 11
[  634.448648] patching 33 blocks to 34
[  634.480683]  writing 8 blocks of new data
[  634.486901] patching 30 blocks to 28
[  634.515248] patching 6 blocks to 7
[  634.529673] patching 8 blocks to 9
[  634.545416] patching 6 blocks to 7
[  634.569215] patching 117 blocks to 117
[  634.706667] patching 5 blocks to 7
[  634.725571] patching 24 blocks to 27
[  634.755021] patching 11 blocks to 12
[  634.772874]  writing 25 blocks of new data
[  634.784304] patching 9 blocks to 22
[  634.812154] patching 20 blocks to 20
[  634.834297] patching 37 blocks to 36
[  634.873035]  writing 6 blocks of new data
[  634.877235] patching 5 blocks to 6
[  634.903967] patching 81 blocks to 69
[  634.972624] patching 6 blocks to 5
[  634.986549] patching 5 blocks to 5
[  634.999552] patching 5 blocks to 6
[  635.013840] patching 2 blocks to 2
[  635.023589] patching 4 blocks to 4
[  635.047411] patching 73 blocks to 73
[  635.131809] patching 37 blocks to 37
[  635.160703]  writing 168 blocks of new data
[  635.192718] patching 5 blocks to 4
[  635.205565] patching 2 blocks to 2
[  635.216070] patching 6 blocks to 7
[  635.230406]   moving 1 blocks
[  635.232114] patching 5 blocks to 4
[  635.244507]  writing 10 blocks of new data
[  635.249570]   moving 1 blocks
[  635.251607] patching 7 blocks to 7
[  635.267129] patching 4 blocks to 4
[  635.280527] patching 7 blocks to 7
[  635.297989] patching 50 blocks to 57
[  635.368502] patching 144 blocks to 143
[  635.403508] patching 6 blocks to 5
[  635.420067] patching 18 blocks to 16
[  635.453444] patching 103 blocks to 112
[  635.558666] patching 11 blocks to 20
[  635.583329] patching 12 blocks to 13
[  635.601805]   moving 1 blocks
[  635.604998] patching 21 blocks to 24
[  635.631693] patching 21 blocks to 21
[  635.661852] patching 17 blocks to 17
[  635.686286]  writing 4 blocks of new data
[  635.700167] patching 143 blocks to 143
[  635.727280] patching 22 blocks to 22
[  635.745405] patching 7 blocks to 8
[  635.760024]   moving 7 blocks
[  635.774379] patching 144 blocks to 143
[  635.800033]   moving 7 blocks
[  635.805259] patching 12 blocks to 66
[  635.862974] patching 37 blocks to 41
[  635.908072] patching 13 blocks to 14
[  635.942693] patching 144 blocks to 143
[  635.969847] patching 7 blocks to 8
[  635.983535]  writing 2 blocks of new data
[  636.274301] patching 2196 blocks to 2578
[  638.660206] patching 4 blocks to 4
[  638.672579] patching 6 blocks to 6
[  638.686303] patching 11 blocks to 12
[  638.704619]   moving 8 blocks
[  638.705392]  writing 23 blocks of new data
[  638.713692] patching 31 blocks to 34
[  638.745531]  writing 4 blocks of new data
[  638.748885] patching 30 blocks to 31
[  638.788264] patching 143 blocks to 143
[  638.813214]  writing 5 blocks of new data
[  638.824767] stashing 194 blocks to 451a78aec5529926d3894caa6397ebd8c5d87d18
[  638.824792]  writing 194 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/451a78aec5529926d3894caa6397ebd8c5d87d18
[  638.842762] stashing 17 blocks to efa09f134df12d6b160284a77cdabdb26457e1bd
[  638.842787]  writing 17 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/efa09f134df12d6b160284a77cdabdb26457e1bd
[  638.953144] patching 807 blocks to 1086
[  642.592681] stashing 760 blocks to 773058b03fd49b689383c8b1e864f1d052b52eca
[  642.592717]  writing 760 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/773058b03fd49b689383c8b1e864f1d052b52eca
[  643.629060] patching 8192 blocks to 8192
[  652.104121] patching 2087 blocks to 31
[  652.156432]  writing 1024 blocks of new data
[  652.348500]  writing 1024 blocks of new data
[  652.464476]  writing 1024 blocks of new data
[  652.580745]  writing 203 blocks of new data
[  652.616392]  writing 1024 blocks of new data
[  652.728678]  writing 587 blocks of new data
[  652.797288] stashing 5 blocks to 37fb031c89a1c5759b11a5e113bb0d4a415d73d7
[  652.797351]  writing 5 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/37fb031c89a1c5759b11a5e113bb0d4a415d73d7
[  653.232986] patching 2709 blocks to 4707
[  658.864134]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/528c243697fe30f781b2e3e3b529d5585b2f8b4c
[  658.864238]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/28c85e002685d08d0c70af35ff78ef3f73ecfcdb
[  658.864446]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/50fb19bd478e4bc220f42f01031be9ee603c4610
[  658.864533]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/37fb031c89a1c5759b11a5e113bb0d4a415d73d7
[  658.879690] stashing 512 overlapping blocks to 8595d260aa10fb66f9848b283f0e9d52ac5d5b0d
[  658.879723] 250228736 bytes free on /cache (2097152 needed)
[  658.879775]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8595d260aa10fb66f9848b283f0e9d52ac5d5b0d
[  658.952572] patching 512 blocks to 512
[  659.032064] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8595d260aa10fb66f9848b283f0e9d52ac5d5b0d
[  659.058223] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/528c243697fe30f781b2e3e3b529d5585b2f8b4c
[  659.058411] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/28c85e002685d08d0c70af35ff78ef3f73ecfcdb
[  659.058577] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/50fb19bd478e4bc220f42f01031be9ee603c4610
[  659.058736] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/37fb031c89a1c5759b11a5e113bb0d4a415d73d7
[  659.479035]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/451a78aec5529926d3894caa6397ebd8c5d87d18
[  659.618366] patching 4523 blocks to 4016
[  697.232625] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/451a78aec5529926d3894caa6397ebd8c5d87d18
[  698.215028] patching 8192 blocks to 8192
[  708.437552] patching 103 blocks to 6
[  708.448375] stashing 2 blocks to 3d1fc8c5decc02e22b5f8133f8b9e8b5f31c9826
[  708.448391]  writing 2 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3d1fc8c5decc02e22b5f8133f8b9e8b5f31c9826
[  708.619754]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9ede2539c05b03c2b1d67e477e3d10edd725c830
[  708.619857]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8298112540eccd5f29540910bd3020fd14760e14
[  708.620137]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/90b7452d163e8a84baa02b07be2b18aca7f0514a
[  708.620195]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/efa09f134df12d6b160284a77cdabdb26457e1bd
[  708.620589]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/98ae8d8f28a613f46386d37dfcd4d760c736d9d1
[  708.636544] stashing 512 overlapping blocks to 00587d78759cd6999051564c32c19cb8cb8b9263
[  708.636577] 251068416 bytes free on /cache (2097152 needed)
[  708.636629]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/00587d78759cd6999051564c32c19cb8cb8b9263
[  708.663107] patching 512 blocks to 512
[  708.716136] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/00587d78759cd6999051564c32c19cb8cb8b9263
[  708.765601] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/9ede2539c05b03c2b1d67e477e3d10edd725c830
[  708.765870] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8298112540eccd5f29540910bd3020fd14760e14
[  708.766084] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/90b7452d163e8a84baa02b07be2b18aca7f0514a
[  708.766271] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/efa09f134df12d6b160284a77cdabdb26457e1bd
[  708.766489] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/98ae8d8f28a613f46386d37dfcd4d760c736d9d1
[  708.860335]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3d1fc8c5decc02e22b5f8133f8b9e8b5f31c9826
[  708.860548]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/35243b975f7a45d23b806d19fd9e34ac90981855
[  708.861400]  loading /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/773058b03fd49b689383c8b1e864f1d052b52eca
[  708.908621] patching 1020 blocks to 1370
[  711.788944] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/3d1fc8c5decc02e22b5f8133f8b9e8b5f31c9826
[  711.789285] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/35243b975f7a45d23b806d19fd9e34ac90981855
[  711.789561] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/773058b03fd49b689383c8b1e864f1d052b52eca
[  711.840883] stashing 512 overlapping blocks to 614669598ab1233d2c4a776d82db4bdaa78692f1
[  711.840922] 254480384 bytes free on /cache (2097152 needed)
[  711.840965]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/614669598ab1233d2c4a776d82db4bdaa78692f1
[  711.867050] patching 512 blocks to 512
[  712.475797] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/614669598ab1233d2c4a776d82db4bdaa78692f1
[  712.610068] stashing 512 overlapping blocks to 77824a628a5fbde2139bb84c7ec74e72320ebaf3
[  712.610099] 254480384 bytes free on /cache (2097152 needed)
[  712.610154]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/77824a628a5fbde2139bb84c7ec74e72320ebaf3
[  712.635776] patching 512 blocks to 512
[  713.274026] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/77824a628a5fbde2139bb84c7ec74e72320ebaf3
[  713.408583] stashing 512 overlapping blocks to cf89fd500b39343485543efb489a6c6b8b78adf6
[  713.408614] 254480384 bytes free on /cache (2097152 needed)
[  713.408673]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cf89fd500b39343485543efb489a6c6b8b78adf6
[  713.435317] patching 512 blocks to 512
[  714.048528] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/cf89fd500b39343485543efb489a6c6b8b78adf6
[  714.156649] stashing 512 overlapping blocks to fe55712dd9447dca4e2af3a0167a700578235b54
[  714.156678] 254480384 bytes free on /cache (2097152 needed)
[  714.156737]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fe55712dd9447dca4e2af3a0167a700578235b54
[  714.183886] patching 512 blocks to 512
[  714.693044] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/fe55712dd9447dca4e2af3a0167a700578235b54
[  714.832925] stashing 512 overlapping blocks to ce300979016a750fb2d60ab3db4c5dafaa44643c
[  714.832955] 254480384 bytes free on /cache (2097152 needed)
[  714.833007]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce300979016a750fb2d60ab3db4c5dafaa44643c
[  714.858923] patching 512 blocks to 512
[  715.190597] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ce300979016a750fb2d60ab3db4c5dafaa44643c
[  715.323736] stashing 512 overlapping blocks to ed67debc5bb6adfdcc5b25f1d146a4162d98ce2d
[  715.323766] 254480384 bytes free on /cache (2097152 needed)
[  715.323818]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ed67debc5bb6adfdcc5b25f1d146a4162d98ce2d
[  715.349850] patching 512 blocks to 512
[  715.485251] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/ed67debc5bb6adfdcc5b25f1d146a4162d98ce2d
[  715.552809] stashing 512 overlapping blocks to 2d9704f49850b477e271f1d1a1f6ebf2104c6e15
[  715.552842] 254480384 bytes free on /cache (2097152 needed)
[  715.552895]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2d9704f49850b477e271f1d1a1f6ebf2104c6e15
[  715.578591] patching 512 blocks to 512
[  716.219829] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/2d9704f49850b477e271f1d1a1f6ebf2104c6e15
[  716.355163] stashing 512 overlapping blocks to 794caa016474bcafedaa2575025ec8d386496826
[  716.355198] 254480384 bytes free on /cache (2097152 needed)
[  716.355247]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/794caa016474bcafedaa2575025ec8d386496826
[  716.381375] patching 512 blocks to 512
[  717.007417] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/794caa016474bcafedaa2575025ec8d386496826
[  717.107543] stashing 512 overlapping blocks to 1fd9e413bf1d5bf3b7b394de32a3841f5613e5d7
[  717.107573] 254480384 bytes free on /cache (2097152 needed)
[  717.107628]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1fd9e413bf1d5bf3b7b394de32a3841f5613e5d7
[  717.148531] patching 512 blocks to 512
[  717.783244] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/1fd9e413bf1d5bf3b7b394de32a3841f5613e5d7
[  717.906774] stashing 512 overlapping blocks to 40e6f652034a11ef14fc08df5285a07cf591c64c
[  717.906804] 254480384 bytes free on /cache (2097152 needed)
[  717.906859]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/40e6f652034a11ef14fc08df5285a07cf591c64c
[  717.932539] patching 512 blocks to 512
[  718.566226] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/40e6f652034a11ef14fc08df5285a07cf591c64c
[  718.700561] stashing 512 overlapping blocks to 484da9e530f9682d909fd6a00d6e9223b8d04348
[  718.700593] 254480384 bytes free on /cache (2097152 needed)
[  718.700645]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/484da9e530f9682d909fd6a00d6e9223b8d04348
[  718.727233] patching 512 blocks to 512
[  718.966404] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/484da9e530f9682d909fd6a00d6e9223b8d04348
[  719.102368] stashing 512 overlapping blocks to 5d44a3a745079145d6b53a27ff19742f3d1c76c7
[  719.102403] 254480384 bytes free on /cache (2097152 needed)
[  719.102452]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5d44a3a745079145d6b53a27ff19742f3d1c76c7
[  719.128260] patching 512 blocks to 512
[  719.158786] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5d44a3a745079145d6b53a27ff19742f3d1c76c7
[  719.227566] stashing 512 overlapping blocks to 73fdfc48117a97b50bff84489699ff25c89c3afb
[  719.227601] 254480384 bytes free on /cache (2097152 needed)
[  719.227646]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/73fdfc48117a97b50bff84489699ff25c89c3afb
[  719.346751] patching 512 blocks to 512
[  719.677345] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/73fdfc48117a97b50bff84489699ff25c89c3afb
[  719.824705] stashing 512 overlapping blocks to 6e4ab9a217dc5972017d60826debe576911f2ec8
[  719.824736] 254480384 bytes free on /cache (2097152 needed)
[  719.824790]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6e4ab9a217dc5972017d60826debe576911f2ec8
[  719.850474] patching 512 blocks to 512
[  720.181493] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6e4ab9a217dc5972017d60826debe576911f2ec8
[  720.315634] stashing 512 overlapping blocks to c4f4128341175c8608571a81e0592221cef3ac39
[  720.315664] 254480384 bytes free on /cache (2097152 needed)
[  720.315719]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c4f4128341175c8608571a81e0592221cef3ac39
[  720.342352] patching 512 blocks to 512
[  720.373881] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/c4f4128341175c8608571a81e0592221cef3ac39
[  720.442044] stashing 512 overlapping blocks to e28e5c6ecdeb8ab37d4372208305c7383a442388
[  720.442076] 254480384 bytes free on /cache (2097152 needed)
[  720.442130]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e28e5c6ecdeb8ab37d4372208305c7383a442388
[  720.467921] patching 512 blocks to 512
[  720.928202] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/e28e5c6ecdeb8ab37d4372208305c7383a442388
[  721.059408] stashing 512 overlapping blocks to 7972f7d2c41c54107e1366b68aec73320f3631dd
[  721.059438] 254480384 bytes free on /cache (2097152 needed)
[  721.059492]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7972f7d2c41c54107e1366b68aec73320f3631dd
[  721.085506] patching 512 blocks to 512
[  721.734556] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7972f7d2c41c54107e1366b68aec73320f3631dd
[  721.865173] stashing 512 overlapping blocks to a10b643b1c80b015ff0d88eb9c72e772ef186638
[  721.865205] 254480384 bytes free on /cache (2097152 needed)
[  721.865258]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a10b643b1c80b015ff0d88eb9c72e772ef186638
[  721.891546] patching 512 blocks to 512
[  722.542580] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a10b643b1c80b015ff0d88eb9c72e772ef186638
[  722.673279] stashing 512 overlapping blocks to 585c697bb6e128da1a54d8def96f492450de520b
[  722.673310] 254480384 bytes free on /cache (2097152 needed)
[  722.673365]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/585c697bb6e128da1a54d8def96f492450de520b
[  722.699405] patching 512 blocks to 512
[  723.346875] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/585c697bb6e128da1a54d8def96f492450de520b
[  723.477459] stashing 512 overlapping blocks to 8e1cf7396e5fcd4a85f3c4598e2c64ddaccb3e54
[  723.477487] 254480384 bytes free on /cache (2097152 needed)
[  723.477543]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8e1cf7396e5fcd4a85f3c4598e2c64ddaccb3e54
[  723.503788] patching 512 blocks to 512
[  724.148495] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/8e1cf7396e5fcd4a85f3c4598e2c64ddaccb3e54
[  724.288222] stashing 512 overlapping blocks to 7e72ed31935afec1820653151d50c952172f0231
[  724.288254] 254480384 bytes free on /cache (2097152 needed)
[  724.288308]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7e72ed31935afec1820653151d50c952172f0231
[  724.314848] patching 512 blocks to 512
[  724.961886] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/7e72ed31935afec1820653151d50c952172f0231
[  725.102058] stashing 512 overlapping blocks to 48d5dc8ac055c1acc5c95f9ec530e46fec85cbdd
[  725.102096] 254480384 bytes free on /cache (2097152 needed)
[  725.102138]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/48d5dc8ac055c1acc5c95f9ec530e46fec85cbdd
[  725.127921] patching 512 blocks to 512
[  725.777021] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/48d5dc8ac055c1acc5c95f9ec530e46fec85cbdd
[  725.936194] stashing 512 overlapping blocks to 6060ee3b9a8ddd9a5d5afbf6657057eccc8e3c1d
[  725.936227] 254480384 bytes free on /cache (2097152 needed)
[  725.936280]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6060ee3b9a8ddd9a5d5afbf6657057eccc8e3c1d
[  726.159482] patching 512 blocks to 512
[  726.807185] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/6060ee3b9a8ddd9a5d5afbf6657057eccc8e3c1d
[  726.938156] stashing 512 overlapping blocks to 0b4f5b3eae620de2c80e3cec19bc873b76432c91
[  726.938186] 254480384 bytes free on /cache (2097152 needed)
[  726.938242]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0b4f5b3eae620de2c80e3cec19bc873b76432c91
[  726.964266] patching 512 blocks to 512
[  727.609967] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/0b4f5b3eae620de2c80e3cec19bc873b76432c91
[  727.740531] stashing 512 overlapping blocks to 5b3728c020dd5d788159e7dde1cf5c8e65ea70ba
[  727.740562] 254480384 bytes free on /cache (2097152 needed)
[  727.740613]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5b3728c020dd5d788159e7dde1cf5c8e65ea70ba
[  727.766300] patching 512 blocks to 512
[  728.414011] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/5b3728c020dd5d788159e7dde1cf5c8e65ea70ba
[  728.549115] patching 399 blocks to 528
[  729.295818] patching 9 blocks to 11
[  729.317644] patching 8 blocks to 9
[  729.337268] patching 15 blocks to 16
[  729.355979] patching 1 blocks to 2
[  729.375300] patching 1 blocks to 187
[  729.395080] patching 1 blocks to 17
[  729.421510] patching 1 blocks to 52
[  729.427738] patching 1 blocks to 2
[  729.452161] patching 1 blocks to 284
[  729.824557] patching 1 blocks to 1
[  729.825521] patching 1 blocks to 1
[  729.835059] patching 2 blocks to 2
[  729.867451] patching 1 blocks to 2
[  729.877937] patching 1 blocks to 2
[  729.970902] patching 1 blocks to 1057
[  730.108430] patching 1 blocks to 2
[  730.122461] patching 1 blocks to 54
[  730.178267] patching 1 blocks to 23
[  730.210216] patching 1 blocks to 24
[  730.214270] patching 3 blocks to 3
[  730.226679] patching 1 blocks to 15
[  730.249515] patching 1 blocks to 9
[  730.266178] patching 1 blocks to 5
[  730.279884] patching 1 blocks to 7
[  730.299079] patching 1 blocks to 54
[  730.357473] patching 1 blocks to 46
[  730.407507] patching 1 blocks to 1
[  730.434371] patching 10 blocks to 5
[  730.548618] patching 836 blocks to 1024
[  731.927637] stashing 512 overlapping blocks to f5ce1b8a76749ad071f4de484dfc737b8f37bd36
[  731.927718] 254480384 bytes free on /cache (2097152 needed)
[  731.927753]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f5ce1b8a76749ad071f4de484dfc737b8f37bd36
[  731.953993] patching 512 blocks to 512
[  732.599485] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f5ce1b8a76749ad071f4de484dfc737b8f37bd36
[  732.657356] patching 6 blocks to 5
[  733.629808] patching 6274 blocks to 7896
[  749.005630] stashing 512 overlapping blocks to bbac52ff1612a594f53839acee03a64435efb043
[  749.005668] 254480384 bytes free on /cache (2097152 needed)
[  749.005734]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bbac52ff1612a594f53839acee03a64435efb043
[  749.031711] patching 512 blocks to 512
[  749.677618] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/bbac52ff1612a594f53839acee03a64435efb043
[  749.737603] patching 8 blocks to 5
[  749.863913] patching 2442 blocks to 23
[  750.307295] patching 8430 blocks to 111
[  750.591529] patching 1319 blocks to 709
[  751.623729] stashing 512 overlapping blocks to 86090fa18e6c7a7e35d2edda3f6c707b15a0a88d
[  751.623767] 254480384 bytes free on /cache (2097152 needed)
[  751.623834]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/86090fa18e6c7a7e35d2edda3f6c707b15a0a88d
[  751.661192] patching 512 blocks to 512
[  752.311036] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/86090fa18e6c7a7e35d2edda3f6c707b15a0a88d
[  752.528680] patching 1297 blocks to 1530
[  759.227525]   moving 34 blocks
[  759.256710] patching 9 blocks to 10
[  760.366535] patching 7819 blocks to 9751
[  772.205191] stashing 9039 overlapping blocks to eb1e4f8a0072cc8f6b1b86bd39ac2ab9751185c9
[  772.205230] 254480384 bytes free on /cache (37023744 needed)
[  772.205292]  writing 9039 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/eb1e4f8a0072cc8f6b1b86bd39ac2ab9751185c9
[  772.782060] patching 9039 blocks to 5343
[  780.163996] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/eb1e4f8a0072cc8f6b1b86bd39ac2ab9751185c9
[  780.424625] stashing 512 overlapping blocks to 841b339de37b38a377c14177a1d48874f3f7c1aa
[  780.424663] 254480384 bytes free on /cache (2097152 needed)
[  780.424707]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/841b339de37b38a377c14177a1d48874f3f7c1aa
[  780.459374] patching 512 blocks to 512
[  781.107137] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/841b339de37b38a377c14177a1d48874f3f7c1aa
[  781.230887] patching 687 blocks to 429
[  783.094987] patching 26077 blocks to 237
[  783.807289] patching 4268 blocks to 3764
[  826.034483] patching 3873 blocks to 5881
[  832.779132] patching 1505 blocks to 1189
[  833.727899] patching 8 blocks to 5
[  833.788312] stashing 512 overlapping blocks to 69281737f88b9ae6d8894341a185b453a2227a6c
[  833.788350] 254480384 bytes free on /cache (2097152 needed)
[  833.788420]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69281737f88b9ae6d8894341a185b453a2227a6c
[  833.814397] patching 512 blocks to 512
[  834.460206] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/69281737f88b9ae6d8894341a185b453a2227a6c
[  834.533549] patching 156 blocks to 6
[  834.550847] patching 147 blocks to 6
[  834.567428] patching 108 blocks to 6
[  834.579351] patching 32 blocks to 5
[  834.590988] patching 2 blocks to 18
[  834.638495] patching 4 blocks to 5
[  834.725834] patching 151 blocks to 417
[  836.659965] patching 8192 blocks to 8192
[  844.670819] patching 963 blocks to 12
[  844.846860] patching 1439 blocks to 1435
[  846.438405] patching 126 blocks to 145
[  846.663477] patching 1618 blocks to 17
[  846.794549] patching 325 blocks to 1157
[  849.027849] patching 5689 blocks to 5940
[  865.451649] patching 2101 blocks to 1836
[  867.860003] patching 7 blocks to 7
[  867.908944] patching 724 blocks to 13
[  868.004273] patching 1655 blocks to 18
[  868.022695]   moving 1 blocks
[  868.023632]   moving 1 blocks
[  868.030480] patching 46 blocks to 48
[  868.214079] stashing 512 overlapping blocks to 456578616882ef3f9737df3f2591dbb4daffc226
[  868.214113] 254480384 bytes free on /cache (2097152 needed)
[  868.214184]  writing 512 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/456578616882ef3f9737df3f2591dbb4daffc226
[  868.263283] patching 512 blocks to 512
[  868.909763] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/456578616882ef3f9737df3f2591dbb4daffc226
[  868.985396] patching 131 blocks to 156
[  869.102382] patching 15 blocks to 5
[  869.123736] patching 224 blocks to 6
[  869.135074] patching 7 blocks to 5
[  869.535098] patching 3012 blocks to 3744
[  873.490839] patching 158 blocks to 7
[  873.990871] patching 3652 blocks to 4762
[  898.228758] patching 11 blocks to 14
[  898.295615] patching 225 blocks to 7
[  898.309840] patching 27 blocks to 23
[  898.342652] patching 51 blocks to 67
[  898.411064] patching 42 blocks to 104
[  898.518978] patching 17 blocks to 18
[  898.539665] patching 17 blocks to 18
[  898.561658] patching 9 blocks to 10
[  898.582886] patching 55 blocks to 58
[  898.630320] patching 5 blocks to 5
[  898.646564] patching 26 blocks to 31
[  898.678817] patching 11 blocks to 12
[  898.697943] patching 4 blocks to 4
[  898.764033] patching 408 blocks to 540
[  906.107048]   moving 18 blocks
[  906.111357] patching 6 blocks to 6
[  906.127289] patching 38 blocks to 5
[  906.160830] patching 78 blocks to 86
[  906.934749] patching 2909 blocks to 2824
[  910.254678] patching 152 blocks to 298
[  911.186307] patching 2173 blocks to 23
[  911.225609] patching 141 blocks to 148
[  911.371853] patching 195 blocks to 202
[  911.638989] patching 55 blocks to 203
[  911.845227] patching 5 blocks to 4
[  911.860332] patching 6 blocks to 8
[  911.886063] patching 41 blocks to 5
[  911.907873] patching 83 blocks to 6
[  911.919334] patching 12 blocks to 5
[  911.932383] patching 48 blocks to 5
[  911.943524] patching 10 blocks to 5
[  911.954798] patching 9 blocks to 5
[  911.965832] patching 14 blocks to 6
[  911.977964] patching 19 blocks to 15
[  911.996254] patching 9 blocks to 5
[  912.007822] patching 15 blocks to 5
[  912.020761] patching 36 blocks to 6
[  912.032176] patching 6 blocks to 5
[  912.043863] patching 6 blocks to 5
[  912.077198] patching 6 blocks to 5
[  912.089906] patching 44 blocks to 5
[  912.100899] patching 10 blocks to 5
[  912.113328] patching 36 blocks to 5
[  912.124653] patching 10 blocks to 5
[  912.135668] patching 15 blocks to 6
[  912.148592] patching 52 blocks to 6
[  912.159844] patching 7 blocks to 8
[  912.173620]  writing 2 blocks of new data
[  912.174870]   moving 1 blocks
[  912.175423]  writing 11 blocks of new data
[  912.180515]  writing 34 blocks of new data
[  912.187743]  writing 43 blocks of new data
[  912.196174] patching 16 blocks to 17
[  912.208427]  writing 11 blocks of new data
[  912.215576] patching 12 blocks to 12
[  912.229711]  writing 10 blocks of new data
[  912.231071]  writing 5 blocks of new data
[  912.236497] patching 15 blocks to 19
[  912.259496]  writing 24 blocks of new data
[  912.262984] patching 5 blocks to 5
[  912.275004] patching 1 blocks to 3
[  912.286304]  writing 7 blocks of new data
[  912.288543] patching 5 blocks to 6
[  912.300948] patching 5 blocks to 4
[  912.314436] patching 10 blocks to 10
[  912.333498] patching 5 blocks to 5
[  912.345602]  writing 28 blocks of new data
[  912.350613]  writing 8 blocks of new data
[  912.353235]  writing 8 blocks of new data
[  912.359634] patching 16 blocks to 17
[  912.373563] patching 12 blocks to 12
[  912.390517] patching 16 blocks to 17
[  912.446139]   moving 377 blocks
[  912.462369]  writing 11 blocks of new data
[  912.463571]  writing 6 blocks of new data
[  912.466425]  writing 1 blocks of new data
[  912.467017]  writing 8 blocks of new data
[  912.500287]   moving 263 blocks
[  912.514651] patching 14 blocks to 15
[  912.528250]  writing 1024 blocks of new data
[  912.673681]  writing 809 blocks of new data
[  912.812347] patching 125 blocks to 124
[  912.937213] patching 12 blocks to 12
[  912.969197] patching 41 blocks to 41
[  913.020624] patching 10 blocks to 5
[  913.035152] patching 28 blocks to 33
[  913.070673]  writing 3 blocks of new data
[  913.073560] patching 12 blocks to 12
[  913.085963]  writing 90 blocks of new data
[  913.100370]  writing 302 blocks of new data
[  913.143419] patching 42 blocks to 55
[  913.193542]  writing 5 blocks of new data
[  913.194639]  writing 1 blocks of new data
[  913.195233]  writing 460 blocks of new data
[  913.288447] patching 177 blocks to 105
[  913.399044] patching 4 blocks to 4
[  913.411966] patching 5 blocks to 5
[  913.423511]  writing 5 blocks of new data
[  913.542731] patching 1766 blocks to 752
[  914.627626] patching 75 blocks to 74
[  914.698605] patching 16 blocks to 17
[  914.711842] patching 5 blocks to 5
[  914.725129] patching 2 blocks to 3
[  914.734407]  writing 9 blocks of new data
[  914.739196]  writing 274 blocks of new data
[  914.779884] patching 12 blocks to 12
[  914.816003] patching 14 blocks to 15
[  914.879077] patching 276 blocks to 298
[  915.253165] patching 12 blocks to 12
[  915.299465] patching 10 blocks to 7
[  915.330825]  writing 33 blocks of new data
[  915.363268] patching 16 blocks to 17
[  915.401174] patching 1 blocks to 13
[  915.438809] patching 27 blocks to 30
[  915.493129] patching 14 blocks to 16
[  915.534063] patching 1 blocks to 6
[  915.568419]  writing 32 blocks of new data
[  915.598778]  writing 6 blocks of new data
[  915.626390] patching 17 blocks to 17
[  915.664949]  writing 9 blocks of new data
[  915.702141] patching 60 blocks to 70
[  915.789778] patching 13 blocks to 13
[  915.832735]  writing 100 blocks of new data
[  915.880881]  writing 4 blocks of new data
[  915.884369]  writing 800 blocks of new data
[  916.226066]  writing 48 blocks of new data
[  916.258728]  writing 80 blocks of new data
[  916.299667]  writing 9 blocks of new data
[  916.324940] patching 4 blocks to 7
[  916.622885] patching 2095 blocks to 2578
[  919.295238] patching 14 blocks to 15
[  919.307632]  writing 22 blocks of new data
[  919.312967]  writing 97 blocks of new data
[  919.329453]  writing 3 blocks of new data
[  919.332335]  writing 42 blocks of new data
[  919.337897]  writing 5 blocks of new data
[  919.339948] patching 1 blocks to 8
[  919.355003]  writing 4 blocks of new data
[  919.355990]  writing 11 blocks of new data
[  919.359950] patching 6 blocks to 5
[  919.374763] patching 8 blocks to 13
[  919.413879] patching 336 blocks to 60
[  919.475023]  writing 9 blocks of new data
[  919.480442] patching 1 blocks to 1
[  919.491661] patching 14 blocks to 15
[  919.506482] patching 5 blocks to 5
[  919.518962]  writing 22 blocks of new data
[  919.521037] patching 3 blocks to 3
[  919.532353] patching 1 blocks to 1
[  919.554754]   moving 87 blocks
[  919.563319] patching 12 blocks to 12
[  919.575816]  writing 27 blocks of new data
[  919.581526]  writing 39 blocks of new data
[  919.589121] patching 7 blocks to 8
[  919.603704]  writing 50 blocks of new data
[  919.610376]  writing 4 blocks of new data
[  919.613224]  writing 1 blocks of new data
[  919.615142] patching 7 blocks to 7
[  919.630238] patching 5 blocks to 4
[  919.643786] patching 12 blocks to 12
[  919.658695] patching 12 blocks to 12
[  919.670768]  writing 180 blocks of new data
[  919.700164]  writing 5 blocks of new data
[  919.703449] patching 14 blocks to 15
[  919.716618]  writing 4 blocks of new data
[  919.719604] patching 6 blocks to 9
[  919.973267]   moving 2168 blocks
[  920.047106]  writing 5 blocks of new data
[  920.048240]  writing 24 blocks of new data
[  920.062992] patching 70 blocks to 68
[  920.131759] patching 145 blocks to 69
[  920.178360] patching 14 blocks to 15
[  920.192055]  writing 16 blocks of new data
[  920.193799]  writing 5 blocks of new data
[  920.203972] patching 43 blocks to 57
[  920.255769] patching 12 blocks to 12
[  920.269934] patching 9 blocks to 10
[  920.286847]  writing 42 blocks of new data
[  920.293915]  writing 1 blocks of new data
[  920.296737] patching 12 blocks to 16
[  920.326084] patching 67 blocks to 72
[  920.381600]  writing 6 blocks of new data
[  920.382687]  writing 5 blocks of new data
[  920.383698]  writing 96 blocks of new data
[  920.405375] patching 6 blocks to 6
[  920.426836] patching 55 blocks to 80
[  920.467859] patching 19 blocks to 38
[  920.502187]  writing 4 blocks of new data
[  920.505316] patching 13 blocks to 10
[  920.523686] patching 25 blocks to 13
[  920.536951]  writing 26 blocks of new data
[  920.542142]  writing 63 blocks of new data
[  920.552345]  writing 204 blocks of new data
[  920.579808]  writing 1 blocks of new data
[  920.580335]  writing 24 blocks of new data
[  920.588178] patching 20 blocks to 16
[  920.609183]  writing 1 blocks of new data
[  920.609794]  writing 104 blocks of new data
[  920.638750]  writing 362 blocks of new data
[  920.693900]  writing 7 blocks of new data
[  920.694985]  writing 2 blocks of new data
[  920.695606]  writing 1 blocks of new data
[  920.700906] patching 14 blocks to 15
[  920.728218] patching 134 blocks to 103
[  920.826482]  writing 27 blocks of new data
[  920.833366] patching 5 blocks to 6
[  920.848862] patching 16 blocks to 17
[  920.863044] patching 14 blocks to 15
[  920.878581] patching 8 blocks to 8
[  920.896489] patching 7 blocks to 8
[  920.912221] patching 12 blocks to 12
[  920.929611]  writing 331 blocks of new data
[  920.984004] patching 10 blocks to 10
[  921.000141]  writing 40 blocks of new data
[  921.007067] patching 14 blocks to 14
[  921.027550] patching 14 blocks to 15
[  921.044888] patching 16 blocks to 17
[  921.061344] patching 19 blocks to 21
[  921.084436]  writing 1024 blocks of new data
[  921.289462]  writing 1024 blocks of new data
[  921.602303]  writing 1024 blocks of new data
[  921.838666]  writing 1024 blocks of new data
[  921.972564]  writing 510 blocks of new data
[  922.062804]  writing 5 blocks of new data
[  922.068652] patching 33 blocks to 32
[  922.094315] patching 6 blocks to 6
[  922.107830]  writing 5 blocks of new data
[  922.108871]  writing 18 blocks of new data
[  922.112207]  writing 59 blocks of new data
[  922.124493] patching 12 blocks to 12
[  922.138050] patching 7 blocks to 7
[  922.150718]  writing 13 blocks of new data
[  922.154128] patching 4 blocks to 4
[  922.165710]  writing 4 blocks of new data
[  922.175643] patching 41 blocks to 55
[  922.233202] patching 23 blocks to 36
[  922.272649] patching 16 blocks to 17
[  922.286690]  writing 6 blocks of new data
[  922.288322]  writing 29 blocks of new data
[  922.484913] patching 1670 blocks to 1740
[  923.852596] patching 16 blocks to 17
[  923.867176] patching 1 blocks to 1
[  923.877534] patching 5 blocks to 5
[  923.889437]  writing 66 blocks of new data
[  923.904699] patching 14 blocks to 15
[  923.917327]  writing 21 blocks of new data
[  923.923817] patching 12 blocks to 12
[  923.937554] patching 7 blocks to 7
[  923.948461] patching 1 blocks to 1
[  923.957778]  writing 55 blocks of new data
[  923.966799] patching 7 blocks to 7
[  923.980010] patching 10 blocks to 26
[  924.007606]  writing 26 blocks of new data
[  924.016259] patching 11 blocks to 15
[  924.070072]  writing 35 blocks of new data
[  924.126923]   moving 453 blocks
[  924.149340] patching 14 blocks to 15
[  924.208353] patching 391 blocks to 407
[  924.645804]  writing 20 blocks of new data
[  924.669725] patching 263 blocks to 140
[  924.820755] patching 2 blocks to 2
[  924.830811]  writing 8 blocks of new data
[  924.836859] patching 12 blocks to 12
[  924.848885]  writing 4 blocks of new data
[  924.851537] patching 12 blocks to 12
[  924.864553] patching 4 blocks to 4
[  924.875684]  writing 22 blocks of new data
[  924.879439]  writing 8 blocks of new data
[  924.884021]   moving 11 blocks
[  924.885103]  writing 12 blocks of new data
[  925.850797] patching 6236 blocks to 9583
[  936.464129]  writing 2 blocks of new data
[  936.466371] patching 1 blocks to 4
[  936.479960] patching 14 blocks to 15
[  936.503990] patching 110 blocks to 96
[  936.582312]  writing 126 blocks of new data
[  936.599731]  writing 30 blocks of new data
[  936.605022]  writing 5 blocks of new data
[  936.608314] patching 12 blocks to 12
[  936.622435] patching 4 blocks to 4
[  936.633751]  writing 186 blocks of new data
[  936.668103] patching 1 blocks to 134
[  936.840273] patching 16 blocks to 17
[  936.964591] patching 990 blocks to 1011
[  939.473592]  writing 61 blocks of new data
[  939.486279] patching 6 blocks to 8
[  939.501673]  writing 52 blocks of new data
[  939.509638]  writing 36 blocks of new data
[  939.519212] patching 5 blocks to 4
[  939.530895]  writing 17 blocks of new data
[  939.536804] patching 16 blocks to 17
[  939.551818] patching 5 blocks to 5
[  939.564801]  writing 13 blocks of new data
[  939.570817] patching 13 blocks to 24
[  939.649907]  writing 7 blocks of new data
[  939.654401] patching 24 blocks to 25
[  939.684469]  writing 36 blocks of new data
[  939.694147] patching 16 blocks to 17
[  939.713187] patching 7 blocks to 7
[  939.778935] stashing 162 overlapping blocks to a06f1fde20d7525299c73987373a595d43690b37
[  939.778967] 254480384 bytes free on /cache (663552 needed)
[  939.779037]  writing 162 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a06f1fde20d7525299c73987373a595d43690b37
[  939.792342] patching 162 blocks to 757
[  940.198160] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/a06f1fde20d7525299c73987373a595d43690b37
[  940.285334] patching 12 blocks to 12
[  940.324706] stashing 210 overlapping blocks to f7cf0a6dcecbe8dfbd4d065ee97be6b48089cd35
[  940.324741] 254480384 bytes free on /cache (860160 needed)
[  940.324792]  writing 210 blocks to /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f7cf0a6dcecbe8dfbd4d065ee97be6b48089cd35
[  940.339004] patching 210 blocks to 318
[  940.741879] deleting /cache/recovery/2bdde8504898ccfcd2c59f20bb8c9c25f73bb524/f7cf0a6dcecbe8dfbd4d065ee97be6b48089cd35
[  940.782338] patching 11 blocks to 11
[  940.801127] patching 4 blocks to 6
[  940.814915] patching 7 blocks to 8
[  940.831862] patching 25 blocks to 34
[  940.864732] patching 16 blocks to 17
[  940.878822]  writing 8 blocks of new data
[  940.882817]  writing 2 blocks of new data
[  940.884069] patching 1 blocks to 1
[  940.893556]  writing 39 blocks of new data
[  940.901874]  writing 31 blocks of new data
[  940.909975] patching 14 blocks to 15
[  941.076356] patching 929 blocks to 1646
[  944.936741] patching 16 blocks to 17
[  944.953254] patching 1 blocks to 1
[  944.967998] patching 12 blocks to 12
[  945.010744] patching 123 blocks to 353
[  945.374192] patching 19 blocks to 18
[  945.396294]  writing 1 blocks of new data
[  945.400616] patching 12 blocks to 12
[  945.784647] patching 2268 blocks to 2586
[  955.279934] patching 5 blocks to 6
[  955.292700]  writing 12 blocks of new data
[  955.295597]  writing 1024 blocks of new data
[  955.441676]  writing 1024 blocks of new data
[  955.582808]  writing 657 blocks of new data
[  955.690106] patching 4 blocks to 4
[  955.704810] patching 16 blocks to 17
[  955.719138]  writing 1024 blocks of new data
[  955.864525]  writing 1024 blocks of new data
[  956.194117]  writing 75 blocks of new data
[  956.239787] patching 19 blocks to 13
[  956.258935]  writing 4 blocks of new data
[  956.287986] patching 8 blocks to 9
[  956.327783] patching 25 blocks to 34
[  956.391431] patching 65 blocks to 21
[  956.440115] patching 16 blocks to 17
[  956.484095] patching 48 blocks to 73
[  956.559781] patching 4 blocks to 4
[  956.573755] patching 16 blocks to 17
[  956.587164]  writing 7 blocks of new data
[  956.590883] patching 16 blocks to 17
[  956.605484]  writing 87 blocks of new data
[  956.621885]  writing 5 blocks of new data
[  956.649489] patching 190 blocks to 252
[  956.888800] patching 1 blocks to 2
[  956.902610] patching 12 blocks to 12
[  956.915904]  writing 34 blocks of new data
[  956.925653] patching 16 blocks to 18
[  956.947315]  writing 39 blocks of new data
[  956.960662] patching 12 blocks to 12
[  956.979554] patching 1 blocks to 1
[  956.988417]  writing 80 blocks of new data
[  956.998836]  writing 5 blocks of new data
[  957.001782]  writing 30 blocks of new data
[  957.027600] patching 66 blocks to 199
[  957.146885] patching 7 blocks to 8
[  957.161196]  writing 18 blocks of new data
[  957.166681]  writing 5 blocks of new data
[  957.170111]  writing 26 blocks of new data
[  957.175276] patching 7 blocks to 9
[  957.193790] patching 56 blocks to 39
[  957.223775] patching 14 blocks to 15
[  957.236188]  writing 9 blocks of new data
[  957.239068]  writing 2 blocks of new data
[  957.241897] patching 14 blocks to 15
[  957.255890]  writing 14 blocks of new data
[  957.261097] patching 16 blocks to 17
[  957.284670] patching 78 blocks to 94
[  957.381144] patching 28 blocks to 5
[  957.394524]   moving 5 blocks
[  957.396896] patching 9 blocks to 9
[  957.416210] patching 28 blocks to 29
[  957.447490]   moving 1 blocks
[  957.448550] patching 1 blocks to 3
[  957.462446] patching 26 blocks to 30
[  957.495782]  writing 1 blocks of new data
[  957.496414]  writing 42 blocks of new data
[  957.509548] patching 43 blocks to 51
[  957.578219] patching 5 blocks to 342
[  957.777991] patching 12 blocks to 12
[  957.789757]  writing 762 blocks of new data
[  957.901777]  writing 6 blocks of new data
[  957.904791]  writing 12 blocks of new data
[  957.908638] patching 17 blocks to 17
[  957.923679] patching 6 blocks to 7
[  958.606301] patching 4824 blocks to 5678
[  962.929517]  writing 1024 blocks of new data
[  963.079330]  writing 1005 blocks of new data
[  963.219106] patching 1 blocks to 3
[  963.230332]  writing 47 blocks of new data
[  963.240725]  writing 38 blocks of new data
[  963.246925]  writing 4 blocks of new data
[  963.248215]  writing 5 blocks of new data
[  963.269854] patching 200 blocks to 130
[  963.381204] patching 56 blocks to 65
[  963.443168] patching 4 blocks to 4
[  963.454822]  writing 18 blocks of new data
[  963.459824] patching 14 blocks to 15
[  963.475425] patching 8 blocks to 8
[  963.491698]  writing 12 blocks of new data
[  963.496942] patching 12 blocks to 12
[  963.512893] patching 32 blocks to 32
[  963.550620] patching 16 blocks to 17
[  963.566154] patching 6 blocks to 8
[  963.580582]  writing 102 blocks of new data
[  963.598244]  writing 24 blocks of new data
[  963.604918] patching 5 blocks to 5
[  963.616829]  writing 29 blocks of new data
[  963.630846] patching 53 blocks to 64
[  963.700759]  writing 23 blocks of new data
[  963.707092] patching 16 blocks to 17
[  963.723980] patching 14 blocks to 15
[  963.763218] patching 285 blocks to 158
[  963.869399] patching 8 blocks to 9
[  963.882760]  writing 8 blocks of new data
[  963.890494] patching 32 blocks to 34
[  963.923561]  writing 242 blocks of new data
[  963.950341]  writing 14 blocks of new data
[  963.955161]  writing 5 blocks of new data
[  963.958255] patching 14 blocks to 15
[  963.972865] patching 11 blocks to 14
[  963.991581] patching 8 blocks to 9
[  964.010725] patching 1 blocks to 40
[  964.056931] patching 14 blocks to 15
[  964.069360]  writing 5 blocks of new data
[  964.073472] patching 5 blocks to 5
[  964.085489]  writing 68 blocks of new data
[  964.095859]  writing 4 blocks of new data
[  964.098566] patching 8 blocks to 9
[  964.114365] patching 1 blocks to 9
[  964.133935] patching 12 blocks to 12
[  964.147067]  writing 15 blocks of new data
[  964.172707] patching 201 blocks to 207
[  964.449966]   moving 278 blocks
[  964.465701] patching 12 blocks to 12
[  964.488560] patching 35 blocks to 58
[  964.540868] patching 16 blocks to 17
[  964.555169] patching 14 blocks to 15
[  964.571770] patching 14 blocks to 15
[  964.878604] patching 6222 blocks to 69
[  964.937052]  writing 4 blocks of new data
[  964.942190] patching 12 blocks to 12
[  964.960072] patching 16 blocks to 17
[  964.975469]  writing 31 blocks of new data
[  964.986512] patching 5 blocks to 5
[  964.999546] patching 5 blocks to 5
[  965.011791]  writing 368 blocks of new data
[  965.097710]  writing 1 blocks of new data
[  965.098360]  writing 41 blocks of new data
[  965.109925]  writing 38 blocks of new data
[  965.123946] patching 14 blocks to 15
[  965.268363] patching 1 blocks to 1936
[  965.382570]  writing 6 blocks of new data
[  965.386466] patching 16 blocks to 17
[  965.398732]  writing 5 blocks of new data
[  965.399788]  writing 25 blocks of new data
[  965.403032]  writing 9 blocks of new data
[  965.408252] patching 19 blocks to 19
[  965.430331] patching 16 blocks to 20
[  965.454100]  writing 774 blocks of new data
[  965.561154] patching 11 blocks to 14
[  965.581358] patching 12 blocks to 12
[  965.600483] patching 5 blocks to 6
[  965.615609] patching 12 blocks to 12
[  965.630391] patching 5 blocks to 5
[  965.644666] patching 14 blocks to 15
[  965.657158]  writing 33 blocks of new data
[  965.664935] patching 7 blocks to 7
[  965.681114] patching 16 blocks to 17
[  965.728056]   moving 275 blocks
[  965.740724]  writing 26 blocks of new data
[  965.765978] patching 16 blocks to 17
[  965.778910]   moving 2 blocks
[  965.779457]  writing 41 blocks of new data
[  965.787502]  writing 54 blocks of new data
[  965.796039]  writing 1024 blocks of new data
[  965.938701]  writing 1024 blocks of new data
[  966.070791]  writing 1024 blocks of new data
[  966.221175]  writing 1024 blocks of new data
[  966.363220]  writing 913 blocks of new data
[  966.494582]  writing 24 blocks of new data
[  966.500609] patching 5 blocks to 6
[  966.514901] patching 16 blocks to 17
[  966.547118] patching 125 blocks to 180
[  966.757564] patching 9 blocks to 9
[  966.774384]  writing 1024 blocks of new data
[  966.953603]  writing 912 blocks of new data
[  967.084604] patching 17 blocks to 17
[  967.099320]  writing 21 blocks of new data
[  967.104356]  writing 4 blocks of new data
[  967.105305]  writing 84 blocks of new data
[  967.120863] patching 8 blocks to 9
[  967.134867]  writing 21 blocks of new data
[  967.139907]  writing 253 blocks of new data
[  967.178148]  writing 12 blocks of new data
[  967.182249]  writing 14 blocks of new data
[  967.187986]  writing 35 blocks of new data
[  967.193629]  writing 45 blocks of new data
[  967.218194] patching 21 blocks to 23
[  967.241859]  writing 4 blocks of new data
[  967.262316] patching 215 blocks to 136
[  967.425140] patching 7 blocks to 7
[  967.451965] patching 58 blocks to 38
[  967.490688]  writing 36 blocks of new data
[  967.511111] patching 34 blocks to 35
[  967.541042]  writing 13 blocks of new data
[  967.546945] patching 2 blocks to 3
[  967.556622]  writing 31 blocks of new data
[  967.578038] patching 53 blocks to 37
[  967.616443]  writing 8 blocks of new data
[  967.617961]  writing 52 blocks of new data
[  967.631078] patching 8 blocks to 11
[  967.653028] patching 21 blocks to 5
[  967.663412]  writing 14 blocks of new data
[  967.667825]  writing 25 blocks of new data
[  967.672687] patching 1 blocks to 3
[  967.683792]  writing 534 blocks of new data
[  967.790813] patching 63 blocks to 30
[  967.820471]  writing 17 blocks of new data
[  967.823506]  writing 1024 blocks of new data
[  967.969818]  writing 1024 blocks of new data
[  968.103691]  writing 1024 blocks of new data
[  968.245269]  writing 1024 blocks of new data
[  968.390917]  writing 134 blocks of new data
[  968.421938] patching 16 blocks to 41
[  968.461940]  writing 61 blocks of new data
[  968.492349] patching 155 blocks to 182
[  968.733282]  writing 1 blocks of new data
[  968.737355]   moving 11 blocks
[  968.744621] patching 7 blocks to 8
[  968.764781] patching 14 blocks to 15
[  968.780112] patching 5 blocks to 5
[  968.792340]  writing 5 blocks of new data
[  968.799772] patching 20 blocks to 24
[  968.828193]  writing 50 blocks of new data
[  968.858447] patching 77 blocks to 31
[  968.893151] patching 12 blocks to 12
[  968.908211] patching 7 blocks to 7
[  968.922105]  writing 4 blocks of new data
[  968.922807]  writing 9 blocks of new data
[  968.927702] patching 16 blocks to 17
[  968.953057] patching 100 blocks to 112
[  969.054195] patching 17 blocks to 17
[  969.067438] patching 5 blocks to 4
[  969.081713] patching 16 blocks to 17
[  969.096143]  writing 8 blocks of new data
[  969.100708] patching 7 blocks to 24
[  969.158955]   moving 290 blocks
[  969.173146] patching 12 blocks to 12
[  969.187451] patching 17 blocks to 17
[  969.203264] patching 12 blocks to 12
[  969.218856] patching 16 blocks to 17
[  969.231090]  writing 230 blocks of new data
[  969.260223] patching 14 blocks to 15
[  969.273718] patching 5 blocks to 5
[  969.285935]  writing 27 blocks of new data
[  969.291890] patching 9 blocks to 14
[  969.310519]  writing 5 blocks of new data
[  969.319029] patching 43 blocks to 63
[  969.358900]  writing 427 blocks of new data
[  969.446907] patching 7 blocks to 7
[  969.457254]  writing 2 blocks of new data
[  969.458174]  writing 29 blocks of new data
[  969.465062] patching 14 blocks to 15
[  969.478543] patching 6 blocks to 5
[  969.490838]  writing 4 blocks of new data
[  969.494131] patching 31 blocks to 29
[  969.528403]  writing 20 blocks of new data
[  969.558047] patching 63 blocks to 321
[  969.863029]  writing 354 blocks of new data
[  969.923133]  writing 1 blocks of new data
[  969.925804] patching 11 blocks to 12
[  969.946542] patching 8 blocks to 9
[  969.958624]  writing 3 blocks of new data
[  969.961170] patching 13 blocks to 14
[  969.979248] patching 5 blocks to 5
[  969.991391]  writing 17 blocks of new data
[  969.998216] patching 14 blocks to 15
[  970.015937] patching 33 blocks to 34
[  970.054735] patching 16 blocks to 17
[  970.083703] patching 140 blocks to 109
[  970.183334]  writing 19 blocks of new data
[  970.186390]  writing 41 blocks of new data
[  970.197639] patching 16 blocks to 17
[  970.211570]  writing 264 blocks of new data
[  970.255737] patching 40 blocks to 46
[  970.320652] patching 149 blocks to 166
[  970.455906] patching 21 blocks to 24
[  970.483924]  writing 1024 blocks of new data
[  970.631589]  writing 1024 blocks of new data
[  970.753606]  writing 1024 blocks of new data
[  970.899020]  writing 1024 blocks of new data
[  971.044457]  writing 1024 blocks of new data
[  971.176628]  writing 1024 blocks of new data
[  971.314179]  writing 1024 blocks of new data
[  971.453131]  writing 1024 blocks of new data
[  971.575197]  writing 1024 blocks of new data
[  971.700470]  writing 1024 blocks of new data
[  971.852576]  writing 115 blocks of new data
[  971.878963] patching 14 blocks to 15
[  971.896127] patching 43 blocks to 5
[  971.909522] patching 21 blocks to 27
[  971.939746]  writing 260 blocks of new data
[  971.982475]  writing 4 blocks of new data
[  972.034620] patching 499 blocks to 425
[  972.373948]  writing 33 blocks of new data
[  972.384958]  writing 3 blocks of new data
[  972.386162]  writing 41 blocks of new data
[  972.394160]  writing 27 blocks of new data
[  972.547550] patching 1209 blocks to 1376
[  974.337727] patching 16 blocks to 17
[  974.351142] patching 7 blocks to 10
[  974.369255] patching 5 blocks to 6
[  974.391293] patching 84 blocks to 79
[  974.458938]  writing 1024 blocks of new data
[  974.623711]  writing 1024 blocks of new data
[  974.751692]  writing 813 blocks of new data
[  974.875562] patching 17 blocks to 17
[  974.890291] patching 16 blocks to 17
[  974.903895] patching 6 blocks to 6
[  974.927949] patching 12 blocks to 12
[  974.950585] patching 81 blocks to 81
[  975.021066]  writing 1024 blocks of new data
[  975.163545]  writing 244 blocks of new data
[  975.207873] patching 20 blocks to 21
[  975.231155]  writing 6 blocks of new data
[  975.234668] patching 14 blocks to 15
[  975.258216] patching 72 blocks to 100
[  975.372142]  writing 9 blocks of new data
[  975.377140] patching 14 blocks to 15
[  975.389527]  writing 2 blocks of new data
[  975.393111] patching 12 blocks to 12
[  975.405024]  writing 86 blocks of new data
[  975.419595]  writing 127 blocks of new data
[  975.442154]  writing 43 blocks of new data
[  975.448477]  writing 43 blocks of new data
[  975.458282] patching 12 blocks to 12
[  975.470474]  writing 33 blocks of new data
[  975.475291]  writing 22 blocks of new data
[  975.495180] patching 122 blocks to 122
[  975.610852] patching 21 blocks to 13
[  975.653761] patching 419 blocks to 104
[  975.763605]  writing 23 blocks of new data
[  975.767143]  writing 5 blocks of new data
[  975.768129]  writing 27 blocks of new data
[  975.772900]  writing 15 blocks of new data
[  975.779385] patching 14 blocks to 15
[  975.792678]  writing 25 blocks of new data
[  975.796408]  writing 80 blocks of new data
[  975.811618] patching 12 blocks to 12
[  975.825551] patching 12 blocks to 12
[  975.843967] patching 77 blocks to 6
[  975.874611] patching 144 blocks to 153
[  976.024037] patching 9 blocks to 8
[  976.042928] patching 14 blocks to 15
[  976.056688]  writing 20 blocks of new data
[  976.088833]   moving 240 blocks
[  976.107794] patching 40 blocks to 55
[  976.153748] patching 1 blocks to 1
[  976.165695] patching 19 blocks to 22
[  976.193844] patching 16 blocks to 17
[  976.213493] patching 41 blocks to 74
[  976.291817] patching 39 blocks to 44
[  976.333898]  writing 80 blocks of new data
[  976.351126] patching 23 blocks to 21
[  976.379527] patching 12 blocks to 12
[  976.395925] patching 31 blocks to 7
[  976.406542]  writing 14 blocks of new data
[  976.409439]  writing 14 blocks of new data
[  976.411867]  writing 34 blocks of new data
[  976.418587] patching 9 blocks to 10
[  976.435459] patching 8 blocks to 9
[  976.449879] patching 15 blocks to 17
[  976.467783]  writing 6 blocks of new data
[  976.473107] patching 43 blocks to 34
[  976.498857]  writing 18 blocks of new data
[  976.507409] patching 1 blocks to 43
[  976.512275]  writing 34 blocks of new data
[  976.518076]  writing 23 blocks of new data
[  976.525219] patching 16 blocks to 17
[  976.539160]  writing 110 blocks of new data
[  976.644550]   moving 795 blocks
[  976.675043] patching 2 blocks to 2
[  976.705809] patching 14 blocks to 15
[  976.723271] patching 45 blocks to 42
[  976.768635] patching 1 blocks to 13
[  976.833133] patching 30 blocks to 554
[  977.563075]  writing 73 blocks of new data
[  977.577081]  writing 6 blocks of new data
[  977.582892] patching 5 blocks to 4
[  977.597798] patching 12 blocks to 12
[  977.614731] patching 5 blocks to 6
[  977.629192] patching 11 blocks to 15
[  977.649765]  writing 11 blocks of new data
[  977.658897] patching 38 blocks to 57
[  977.712917]   zeroing 1024 blocks
[  977.753845]   zeroing 1024 blocks
[  977.793789]   zeroing 1024 blocks
[  977.853231]   zeroing 1024 blocks
[  977.897790]   zeroing 1024 blocks
[  977.941625]   zeroing 1024 blocks
[  977.983263]   zeroing 1024 blocks
[  978.025601]   zeroing 1024 blocks
[  978.067603]   zeroing 1024 blocks
[  978.108610]   zeroing 1024 blocks
[  978.166062]   zeroing 1024 blocks
[  978.205023]   zeroing 26 blocks
[  978.210660]   zeroing 186736 blocks
[  983.844843] wrote 674640 blocks; expected 674640
[  983.844879] stashed 75911 blocks
[  983.844885] max alloc needed was 121466880
[  983.844891] deleting stash 2bdde8504898ccfcd2c59f20bb8c9c25f73bb524
[  984.301121] system update is complete...
[  984.301224] Patching boot image...
[  984.673509] patch EMMC:/dev/block/bootdevice/by-name/boot:16777216:02a96aef194b97e0803da22abca04c31a2766ef7:16777216:f8143f9afbc9e1d276f9fe22f92b9de80b142a76: partition read matched size 16777216 SHA-1 02a96aef194b97e0803da22abca04c31a2766ef7
[  984.677870] 254484480 bytes free on /cache (16777216 needed)
[  984.677880] Using cache to copy content
[  988.220952] now f8143f9a
[  988.514665]   caches dropped
[  989.681042] verification read succeeded (attempt 1)
[  989.695770] system update is complete...
[  989.695799] updating sbl1 ...
[  989.705106] boot prop value is 7824900.sdhci 
[  989.705130] Storage type is emmc 
[  989.705791] Applying /tmp/sbl1.mbn of size 519296 onto sbl1 of size 524288
[  989.737700] wrote sbl1 partition
[  989.738020] updating rpm ...
[  989.742043] boot prop value is 7824900.sdhci 
[  989.742063] Storage type is emmc 
[  989.742154] Applying /tmp/rpm.mbn of size 262144 onto rpm of size 262144
[  989.760509] wrote rpm partition
[  989.761448] updating tz ...
[  989.780672] boot prop value is 7824900.sdhci 
[  989.780699] Storage type is emmc 
[  989.780795] Applying /tmp/tz.mbn of size 1835008 onto tz of size 1835008
[  989.882885] wrote tz partition
[  989.883499] updating devcfg ...
[  989.885170] boot prop value is 7824900.sdhci 
[  989.885183] Storage type is emmc 
[  989.885292] Applying /tmp/devcfg.mbn of size 65536 onto devcfg of size 65536
[  989.890944] wrote devcfg partition
[  989.891116] updating aboot ...
[  989.910644] boot prop value is 7824900.sdhci 
[  989.910668] Storage type is emmc 
[  989.910763] Applying /tmp/emmc_appsboot.mbn of size 1572864 onto aboot of size 1572864
[  990.006864] wrote aboot partition
[  990.007353] updating cmnlib ...
[  990.012030] boot prop value is 7824900.sdhci 
[  990.012090] Storage type is emmc 
[  990.012323] Applying /tmp/cmnlib.mbn of size 257152 onto cmnlib of size 262144
[  990.028860] wrote cmnlib partition
[  990.029080] updating cmnlib64 ...
[  990.033110] boot prop value is 7824900.sdhci 
[  990.033168] Storage type is emmc 
[  990.033400] Applying /tmp/cmnlib64.mbn of size 257152 onto cmnlib64 of size 262144
[  990.050903] wrote cmnlib64 partition
[  990.051158] updating keymaster ...
[  990.055935] boot prop value is 7824900.sdhci 
[  990.055961] Storage type is emmc 
[  990.056048] Applying /tmp/keymaster.mbn of size 257152 onto keymaster of size 262144
[  990.070351] wrote keymaster partition
[  990.070549] updating prov ...
[  990.073072] boot prop value is 7824900.sdhci 
[  990.073092] Storage type is emmc 
[  990.073196] Applying /tmp/prov.mbn of size 191616 onto prov of size 196608
[  990.085393] wrote prov partition
[  990.085577] Removing unneeded files from modem...
[  990.092053] Patching modem files...
[  990.104250] patch /modem/image/adsp.b00: now 42ae9e4a
[  990.119827] patch /modem/image/adsp.b01: now 13500067
[  990.136910] patch /modem/image/adsp.b02: now df804e86
[  990.173936] patch /modem/image/adsp.b03: now 4a95fdfd
[  990.661664] patch /modem/image/adsp.b04: now 7d0c8026
[  990.719682] patch /modem/image/adsp.b05: now d42015d8
[  990.738076] patch /modem/image/adsp.b06: now 700de424
[  990.756503] patch /modem/image/adsp.b07: now abf5c371
[  990.775558] patch /modem/image/adsp.b08: now b6e89027
[  990.793710] patch /modem/image/adsp.b09: now e8ccfc34
[  990.875858] patch /modem/image/adsp.b10: now f34ee78a
[  990.894679] patch /modem/image/adsp.b11: now d0ceca3e
[  990.923614] patch /modem/image/adsp.b12: now df93e9a2
[  990.966876] patch /modem/image/adsp.b13: now 18fcf0ea
[  990.980913] patch /modem/image/adsp.mdt: now 3f479433
[  990.998291] patch /modem/image/cmnlib.b00: now cc5fd5f7
[  991.011918] patch /modem/image/cmnlib.b01: now d4028244
[  991.041755] patch /modem/image/cmnlib.b02: now 5d7ab322
[  991.057415] patch /modem/image/cmnlib.b03: now eda9d76b
[  991.072465] patch /modem/image/cmnlib.b05: now ecbb8725
[  991.088606] patch /modem/image/cmnlib.mdt: now 21876b28
[  991.102187] patch /modem/image/cmnlib64.b00: now 27d0b3b5
[  991.118494] patch /modem/image/cmnlib64.b01: now 71b9ccb8
[  991.153964] patch /modem/image/cmnlib64.b02: now eba0bfcf
[  991.169595] patch /modem/image/cmnlib64.b03: now da43c695
[  991.184742] patch /modem/image/cmnlib64.b04: now 8845b964
[  991.199889] patch /modem/image/cmnlib64.b05: now 22cbd2ef
[  991.214559] patch /modem/image/cmnlib64.mdt: now 83d56c20
[  991.232335] patch /modem/image/cppf.b00: now 43d5238b
[  991.246728] patch /modem/image/cppf.b01: now 67d5b2d0
[  991.262259] patch /modem/image/cppf.b02: now b550f345
[  991.278807] patch /modem/image/cppf.b04: now 477e82a5
[  991.292374] patch /modem/image/cppf.b06: now 2283cb23
[  991.309258] patch /modem/image/cppf.mdt: now 869e08f4
[  991.324533] patch /modem/image/fpctzappfingerprint.b00: now b109ef50
[  991.338602] patch /modem/image/fpctzappfingerprint.b01: now a36e4b00
[  991.481911] patch /modem/image/fpctzappfingerprint.b02: now a9b655f4
[  991.506118] patch /modem/image/fpctzappfingerprint.b03: now 2e788825
[  991.523816] patch /modem/image/fpctzappfingerprint.b06: now eb45d761
[  991.538942] patch /modem/image/fpctzappfingerprint.mdt: now 3eb88568
[  991.566489] patch /modem/image/mba.mbn: now 8ee28c27
[  991.582495] patch /modem/image/modem.b00: now fc5d6ece
[  991.598707] patch /modem/image/modem.b01: now 983d841c
[  991.613655] patch /modem/image/modem.b02: now ffb079b8
[  991.640863] patch /modem/image/modem.b05: now 7c9fcfe4
[  991.690204] patch /modem/image/modem.b06: now b1537dbb
[  991.722248] patch /modem/image/modem.b07: now b059b192
[  991.810653] patch /modem/image/modem.b08: now 1685934f
[  991.937130] patch /modem/image/modem.b09: now 8907da85
[  993.483327] patch /modem/image/modem.b10: now 12f3ba87
[  993.803947] patch /modem/image/modem.b12: now becb9099
[  993.950036] patch /modem/image/modem.b13: now 5ca443ed
[  993.978298] patch /modem/image/modem.b16: now 1ad87f2d
[  997.458727] patch /modem/image/modem.b18: now b93c8c60
[  997.492134] patch /modem/image/modem.b19: now 0c6bdbb4
[  997.627290] patch /modem/image/modem.b20: now 30193ba7
[  997.647907] patch /modem/image/modem.mdt: now dc1edf24
[  997.661410] patch /modem/image/venus.b00: now 6d67b619
[  997.676205] patch /modem/image/venus.b01: now b71e50d2
[  997.770557] patch /modem/image/venus.b02: now a8ae4317
[  997.789387] patch /modem/image/venus.b03: now 3b02f43e
[  997.804400] patch /modem/image/venus.mdt: now a9dc0d2f
[  997.820567] patch /modem/image/wcnss.b00: now 491a93f2
[  997.837049] patch /modem/image/wcnss.b01: now 20810af8
[  997.851923] patch /modem/image/wcnss.b02: now 8e1f8408
[  998.229818] patch /modem/image/wcnss.b06: now 7c40d5ad
[  998.253573] patch /modem/image/wcnss.b10: now 042df472
[  998.277937] patch /modem/image/wcnss.b12: now 0e6d11be
[  998.293226] patch /modem/image/wcnss.mdt: now dd6770a1
[  998.311264] patch /modem/image/widevine.b00: now 0e2e8fa0
[  998.325567] patch /modem/image/widevine.b01: now 9a7409ab
[  998.371736] patch /modem/image/widevine.b02: now 5edd6964
[  998.388377] patch /modem/image/widevine.b03: now c5af68bb
[  998.406123] patch /modem/image/widevine.b04: now ac1375fc
[  998.419961] patch /modem/image/widevine.b05: now 7b05731e
[  998.437430] patch /modem/image/widevine.b06: now 078544bd
[  998.462212] patch /modem/image/widevine.mdt: now b2676812
[  998.465653] Unpacking new files in modem ...
[  998.470514] Extracted file "/modem/image/fpctzappfingerprint.b04"
[  998.477176] Extracted file "/modem/image/fpctzappfingerprint.b05"
[  998.556756] Extracted file "/modem/image/qdsp6m.qdb"
[  998.572434] Extracted file "/modem/image/modem.b17"
[  998.576298] Extracted file "/modem/image/cmnlib.b04"
[  998.579792] Extracted file "/modem/image/Ver_Info.txt"
[  998.579812] Extracted 6 file(s)
[  998.581020] Symlinks and permissions in modem ...
[  998.584815] Updating dsp ...
[  998.584841] Patching dsp image unconditionally...
[  998.584847] performing update
[  998.587206] blockimg version is 4
[  998.587221] maximum stash entries 0
[  998.587484] creating stash /cache/recovery/677d9ad4e05073580617d8de2a8bf397effe0443/
[  998.588826] 254480384 bytes free on /cache (0 needed)
[  998.588914]  writing 1024 blocks of new data
[  998.769805]  writing 1024 blocks of new data
[  998.910243]  writing 36 blocks of new data
[  998.918672]   zeroing 1024 blocks
[  998.971415]   zeroing 988 blocks
[  999.022247] wrote 4096 blocks; expected 4096
[  999.022336] stashed 0 blocks
[  999.022367] max alloc needed was 4096
[  999.022412] deleting stash 677d9ad4e05073580617d8de2a8bf397effe0443
[  999.030920] updating fsg ...
[  999.059130] boot prop value is 7824900.sdhci 
[  999.059162] Storage type is emmc 
[  999.059257] Applying /tmp/fsg.mbn of size 3096576 onto fsg of size 8388608
[  999.223906] wrote fsg partition
[  999.225573] Erasing modemst1 ...
[  999.225646] unknown volume for path [/cache]
[  999.225656] Unable to mount /cache!
[  999.225976] blk: partition "" size 1835008 not a multiple of io_buffer_size 524288
[  999.226048] blk: partition "" size 1835008 not a multiple of io_buffer_size 524288
[  999.226219] blk: partition "" size 21073920 not a multiple of io_buffer_size 524288
[  999.226322] blk: partition "" size 56883133440 not a multiple of io_buffer_size 524288
[  999.260056] Trying to access file /sys/block/mmcblk0/mmcblk0p27/start
[  999.260216] /dev/block/bootdevice/by-name/modemst1 starts at: 135135232
[  999.260244] Formatting partition /dev/block/bootdevice/by-name/modemst1 of length 2097152 starting at 135135232
[  999.281516] Format complete for partition 
[  999.282967] Erasing modemst2 ...
[  999.283018] unknown volume for path [/cache]
[  999.283039] Unable to mount /cache!
[  999.283108] Trying to access file /sys/block/mmcblk0/mmcblk0p28/start
[  999.283236] /dev/block/bootdevice/by-name/modemst2 starts at: 137232384
[  999.283265] Formatting partition /dev/block/bootdevice/by-name/modemst2 of length 2097152 starting at 137232384
[  999.283278] Aligning offset to 4194304 boundary by moving 1179648 bytes
[  999.322092] Format complete for partition 
[  999.324208] Patching oem image...
[  999.324254] 254484480 bytes free on /cache (204779520 needed)
[  999.324279] Patching oem image after verification.
[  999.324288] performing update
[  999.327542] blockimg version is 4
[  999.327570] maximum stash entries 0
[  999.327601] creating stash /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/
[  999.327815] 254480384 bytes free on /cache (204779520 needed)
[  999.327914]   zeroing 101860 blocks
[ 1003.075778] stashing 8192 overlapping blocks to e3deb15b4a9b2dee20028d1ceb55c6a2db762e0a
[ 1003.075818] 254480384 bytes free on /cache (33554432 needed)
[ 1003.075863]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/e3deb15b4a9b2dee20028d1ceb55c6a2db762e0a
[ 1003.786554] patching 8192 blocks to 8192
[ 1007.464756] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/e3deb15b4a9b2dee20028d1ceb55c6a2db762e0a
[ 1010.745661] stashing 49995 overlapping blocks to 3e9d890403a5c3696b650bf09adface51d03b49c
[ 1010.745699] 254480384 bytes free on /cache (204779520 needed)
[ 1010.745747]  writing 49995 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/3e9d890403a5c3696b650bf09adface51d03b49c
[ 1014.262217] patching 49995 blocks to 1930
[ 1014.497244] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/3e9d890403a5c3696b650bf09adface51d03b49c
[ 1015.513628] stashing 8192 overlapping blocks to 8d548480ef138e5a42ad62fd414b82e3a1edfc4c
[ 1015.513666] 254480384 bytes free on /cache (33554432 needed)
[ 1015.513710]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/8d548480ef138e5a42ad62fd414b82e3a1edfc4c
[ 1015.940953] patching 8192 blocks to 8192
[ 1022.623055] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/8d548480ef138e5a42ad62fd414b82e3a1edfc4c
[ 1023.937163] stashing 8192 overlapping blocks to c8fe82916d03b7f5a7f86052693e38a20903c782
[ 1023.937202] 254480384 bytes free on /cache (33554432 needed)
[ 1023.937250]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/c8fe82916d03b7f5a7f86052693e38a20903c782
[ 1024.371462] patching 8192 blocks to 8192
[ 1030.179230] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/c8fe82916d03b7f5a7f86052693e38a20903c782
[ 1031.429352] stashing 8192 overlapping blocks to 36c89ad4fbd1745b1f451ca2e6d7a27f828879a0
[ 1031.429389] 254480384 bytes free on /cache (33554432 needed)
[ 1031.429436]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/36c89ad4fbd1745b1f451ca2e6d7a27f828879a0
[ 1033.499793] patching 8192 blocks to 8192
[ 1041.104433] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/36c89ad4fbd1745b1f451ca2e6d7a27f828879a0
[ 1043.653891] stashing 8192 overlapping blocks to 1440c12a64131c32e6acb62aaca0bb6cb5a73c3d
[ 1043.653928] 254480384 bytes free on /cache (33554432 needed)
[ 1043.653980]  writing 8192 blocks to /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/1440c12a64131c32e6acb62aaca0bb6cb5a73c3d
[ 1044.398882] patching 8192 blocks to 8192
[ 1052.003750] deleting /cache/recovery/d42c8ee5b06118b9bb530644a3c2f4943bb98d8f/1440c12a64131c32e6acb62aaca0bb6cb5a73c3d
[ 1052.797591]   zeroing 1024 blocks
[ 1052.835569]   zeroing 1024 blocks
[ 1052.885101]   zeroing 1024 blocks
[ 1052.923711]   zeroing 1024 blocks
[ 1052.961566]   zeroing 1024 blocks
[ 1053.000128]   zeroing 512 blocks
[ 1053.035587]   zeroing 46226 blocks
[ 1054.756366] wrote 48522 blocks; expected 48522
[ 1054.756426] stashed 90955 blocks
[ 1054.756457] max alloc needed was 204779520
[ 1054.756499] deleting stash d42c8ee5b06118b9bb530644a3c2f4943bb98d8f
[ 1054.785092] Updating gpt_main0.bin...
[ 1054.786783] boot prop value is 7824900.sdhci 
[ 1054.786794] Storage type is emmc 
[ 1054.786802]
[ 1054.786808]  LBA SIZE for this device is : 512 bytes
[ 1054.787335] Changed ending_lba to 122142686
[ 1054.787344] growth partition is userdata
[ 1054.787391] Partition size(in blocks)  = Partition(/dev/block/mmcblk0) size : 62537072640 bytes 
[ 1054.787418] 122142720 for the partition /dev/block/mmcblk0
[ 1054.787444] Partition(/dev/block/mmcblk0) size : 62537072640 bytes 
[ 1054.797721] Writing header for backup partition 
[ 1054.802583] GPT upgrade successful
[ 1054.802660] Entered IsHardwareSecuredFn
[ 1054.802727] It is a Secure Hardware
[ 1054.802735] Leaving IsHardwareSecuredFn: t
[ 1054.802770] Erasing DDR..
[ 1054.802814] unknown volume for path [/cache]
[ 1054.802823] Unable to mount /cache!
[ 1054.802900] Trying to access file /sys/block/mmcblk0/mmcblk0p23/start
[ 1054.803000] /dev/block/bootdevice/by-name/DDR starts at: 133955584
[ 1054.803042] Formatting partition /dev/block/bootdevice/by-name/DDR of length 32768 starting at 133955584
[ 1054.803819] Format complete for partition 
[ 1054.804346] Erased DDR successfully..
[ 1055.043742]
[ 1055.086266] I:@/cache/recovery/block.map
[ 1055.086286] 1
[ 1055.086292] time_total: 1053
[ 1055.086297] retry: 0
[ 1055.086303] source_build: 18
[ 1055.086308] bytes_written_system: 2763325440
[ 1055.086314] bytes_stashed_system: 310931456
[ 1055.086319] bytes_written_dsp: 16777216
[ 1055.086324] bytes_stashed_dsp: 0
[ 1055.086330] bytes_written_oem: 198746112
[ 1055.086335] bytes_stashed_oem: 372551680
[ 1055.086340] uncrypt_time: 24
[ 1055.212582] Filesystem            1K-blocks    Used Available Use% Mounted on
[ 1055.212606] rootfs                  1735912    7976   1727936   1% /
[ 1055.212612] tmpfs                   1843412     408   1843004   1% /dev
[ 1055.212618] tmpfs                   1843412    1536   1841876   1% /tmp
[ 1055.212668] /dev/block/mmcblk0p52    253920     160    253760   1% /cache
[ 1055.212723] /dev/block/mmcblk0p53   3999372 2593664   1405708  65% /system
[ 1055.234664] 24K	/cache/recovery
[ 1055.235179] 4.0K	/cache/backup_stage
[ 1055.235189] 36K	/cache
[ 1055.264651] Usage of /system/bin/uncrypt:
[ 1055.264695] /system/bin/uncrypt [<package_path> <map_file>]  Uncrypt ota package.
[ 1055.264707] /system/bin/uncrypt --clear-bcb  Clear BCB data in misc partition.
[ 1055.264714] /system/bin/uncrypt --setup-bcb  Setup BCB data by command file.
[ 1055.266388] E qe : Failed to open /system/etc/qewl.jar (No such file or directory)
[ 1055.267088] qe 0/0
[ 1055.296190] I:Saving locale "en_US"
