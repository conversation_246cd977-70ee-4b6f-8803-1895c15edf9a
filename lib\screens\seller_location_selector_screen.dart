import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../services/location_service.dart';

/// SellerLocationSelectorScreen - Map-based location picker for sellers
/// 
/// This screen allows sellers to select their business location by dropping
/// a pin on a map. It provides accurate coordinate selection and address
/// resolution for delivery fee calculation purposes.
/// 
/// Features:
/// - Interactive Google Map with draggable marker
/// - Real-time address resolution
/// - Coordinate display with precision
/// - Save/Cancel functionality
/// - Integration with seller location service
class SellerLocationSelectorScreen extends StatefulWidget {
  final String sellerId;
  final double initialLatitude;
  final double initialLongitude;
  final String? initialAddress;

  const SellerLocationSelectorScreen({
    super.key,
    required this.sellerId,
    required this.initialLatitude,
    required this.initialLongitude,
    this.initialAddress,
  });

  @override
  State<SellerLocationSelectorScreen> createState() => _SellerLocationSelectorScreenState();
}

class _SellerLocationSelectorScreenState extends State<SellerLocationSelectorScreen> {
  final LocationService _locationService = LocationService();
  GoogleMapController? _mapController;
  
  late LatLng _selectedLocation;
  String? _selectedAddress;
  bool _isLoadingAddress = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _selectedLocation = LatLng(widget.initialLatitude, widget.initialLongitude);
    _selectedAddress = widget.initialAddress;
    
    // Load address for initial location if not provided
    if (_selectedAddress == null) {
      _loadAddressForLocation(_selectedLocation);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Shop Location'),
        backgroundColor: const Color(0xFF059669),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveLocation,
            child: _isSaving
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Map
          Expanded(
            child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: _selectedLocation,
                zoom: 16.0,
              ),
              onMapCreated: (GoogleMapController controller) {
                _mapController = controller;
              },
              onTap: _onMapTap,
              markers: {
                Marker(
                  markerId: const MarkerId('shop_location'),
                  position: _selectedLocation,
                  draggable: true,
                  onDragEnd: (LatLng location) {
                    setState(() {
                      _selectedLocation = location;
                    });
                    _loadAddressForLocation(location);
                  },
                  icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueGreen,
                  ),
                  infoWindow: InfoWindow(
                    title: 'Shop Location',
                    snippet: _selectedAddress ?? 'Loading address...',
                  ),
                ),
              },
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
              mapType: MapType.normal,
              zoomControlsEnabled: true,
            ),
          ),
          
          // Location info panel
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Selected Location',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                
                // Address
                Row(
                  children: [
                    const Icon(Icons.location_on, color: Color(0xFF059669), size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _isLoadingAddress
                          ? const Text(
                              'Loading address...',
                              style: TextStyle(color: Colors.grey),
                            )
                          : Text(
                              _selectedAddress ?? 'Address not available',
                              style: const TextStyle(fontSize: 14),
                            ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                
                // Coordinates
                Row(
                  children: [
                    const Icon(Icons.my_location, color: Color(0xFF059669), size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '${_selectedLocation.latitude.toStringAsFixed(6)}, ${_selectedLocation.longitude.toStringAsFixed(6)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Instructions
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF059669).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Color(0xFF059669), size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Tap on the map or drag the marker to select your exact shop location',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF059669),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Handle map tap to update location
  void _onMapTap(LatLng location) {
    setState(() {
      _selectedLocation = location;
    });
    _loadAddressForLocation(location);
  }

  /// Load address for the given location
  Future<void> _loadAddressForLocation(LatLng location) async {
    setState(() {
      _isLoadingAddress = true;
    });

    try {
      final address = await _locationService.reverseGeocode(
        location.latitude,
        location.longitude,
      );
      
      if (mounted) {
        setState(() {
          _selectedAddress = address ?? 'Address not available';
          _isLoadingAddress = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _selectedAddress = 'Failed to load address';
          _isLoadingAddress = false;
        });
      }
    }
  }

  /// Save the selected location
  Future<void> _saveLocation() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Return the selected location data
      final result = {
        'success': true,
        'latitude': _selectedLocation.latitude,
        'longitude': _selectedLocation.longitude,
        'address': _selectedAddress ?? 'Address not available',
      };

      Navigator.pop(context, result);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save location: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
