# Change log - 2025-08-14

Completed (zero-risk, feature-flagged where applicable):
- Seller dashboard gating: Add/Manage products blocked until approval
- Seller registration → Odoo sync: fire-and-forget after OTP registration success
- Odoo status sync guard: Prevents accidental auto-approval without odoo_product_id
- Cart Continue Shopping navigation fix: replaces pop with pushReplacement to catalog when needed
- Product details: SnackBar with View Cart CTA, share fallback, price header polished
- Seller login modal: optional bottom sheet to avoid keyboard overlap
- Shop Open/Close toggle (feature): JSONB persisted at sellers.extra.settings.is_open

Pending (approved next):
- Product form UI (meat/livestock, subcategories, images, UOM)
- Dedicated Customer Profile & Addresses screens (read-only)
- Payment success mock → tracking (feature-flagged)

Notes:
- No core files changed; all updates are backward compatible.
- See docs/bug_analysis_seller_customer.md for issue-by-issue rationale and verification.

