-- Fix Seller Location RLS Policies
-- This migration adds missing RLS policies for sellers table to allow
-- sellers to update their own location data (latitude, longitude, etc.)
--
-- Issue: Sellers cannot update their location data due to missing RLS policies
-- Solution: Add comprehensive RLS policies for sellers table operations

-- Enable RLS on sellers table (if not already enabled)
ALTER TABLE sellers ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Sellers can view their own profile" ON sellers;
DROP POLICY IF EXISTS "Sellers can update their own profile" ON sellers;
DROP POLICY IF EXISTS "Allow seller registration" ON sellers;
DROP POLICY IF EXISTS "Public can view approved sellers" ON sellers;

-- Policy 1: Allow seller registration (INSERT)
-- This allows new sellers to be created during registration
CREATE POLICY "Allow seller registration" ON sellers
FOR INSERT WITH CHECK (true);

-- Policy 2: Sellers can view their own profile (SELECT)
-- This allows sellers to read their own data
CREATE POLICY "Sellers can view their own profile" ON sellers
FOR SELECT USING (
    user_id = auth.uid()
);

-- Policy 3: Sellers can update their own profile (UPDATE)
-- This is the CRITICAL policy that was missing - allows sellers to update their location data
CREATE POLICY "Sellers can update their own profile" ON sellers
FOR UPDATE USING (
    user_id = auth.uid()
) WITH CHECK (
    user_id = auth.uid()
);

-- Policy 4: Public can view approved sellers (for customer browsing)
-- This allows customers to see approved sellers and their products
CREATE POLICY "Public can view approved sellers" ON sellers
FOR SELECT USING (
    approval_status = 'approved'
);

-- Policy 5: Service role has full access (for admin operations)
-- This allows admin operations and system processes
CREATE POLICY "Service role full access to sellers" ON sellers
FOR ALL TO service_role USING (true);

-- Add comments for documentation
COMMENT ON POLICY "Allow seller registration" ON sellers IS 
'Allows new seller registration during signup process';

COMMENT ON POLICY "Sellers can view their own profile" ON sellers IS 
'Allows sellers to read their own profile data including location fields';

COMMENT ON POLICY "Sellers can update their own profile" ON sellers IS 
'CRITICAL: Allows sellers to update their profile including location data (latitude, longitude, delivery_radius_km, location_verified, location_updated_at)';

COMMENT ON POLICY "Public can view approved sellers" ON sellers IS 
'Allows customers to browse approved sellers and their information';

COMMENT ON POLICY "Service role full access to sellers" ON sellers IS 
'Allows admin panel and system processes to manage seller data';

-- Verify the policies are created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'sellers' 
ORDER BY policyname;

-- Test the policies by attempting a sample update
-- (This will be commented out in production, but useful for testing)
/*
-- Test update (replace with actual seller ID and user ID for testing)
UPDATE sellers 
SET 
    latitude = 12.9716,
    longitude = 77.5946,
    delivery_radius_km = 10,
    location_verified = true,
    location_updated_at = NOW()
WHERE user_id = auth.uid();
*/
