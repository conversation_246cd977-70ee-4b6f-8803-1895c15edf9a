import 'package:flutter/material.dart';
import '../config/ui_flags.dart';

import '../supabase_service.dart';
import '../services/odoo_service.dart';
import '../services/auth_service.dart';
import '../services/seller_notification_service.dart';
import '../services/seller_status_sync_service.dart';
import 'product_management_screen.dart';
import 'seller_profile_screen.dart';
import 'seller_notifications_screen.dart';

class SellerDashboardScreen extends StatefulWidget {
  final Map<String, dynamic> seller;

  const SellerDashboardScreen({super.key, required this.seller});

  @override
  State<SellerDashboardScreen> createState() => _SellerDashboardScreenState();
}

class _SellerDashboardScreenState extends State<SellerDashboardScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  final OdooService _odooService = OdooService();
  final AuthService _authService = AuthService();
  final SellerNotificationService _notificationService =
      SellerNotificationService();
  final SellerStatusSyncService _statusSyncService = SellerStatusSyncService();

  bool _isLoading = true;
  Map<String, dynamic> _dashboardData = {};
  int _notificationCount = 0;
  bool _isSyncingStatus = false;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    _updateNotificationCount();
    _checkSellerApprovalStatus(); // Check approval status on dashboard load
  }

  Future<void> _logout() async {
    // Show confirmation dialog
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF059669),
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (shouldLogout == true && mounted) {
      try {
        // Clear the session using auth service
        await _authService.clearSession();

        // Also sign out from Supabase auth if needed
        await _supabaseService.signOut();
        print('🔐 Seller logged out successfully');

        // Navigate back to main screen (replace entire navigation stack)
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      } catch (e) {
        print('❌ Error during logout: $e');

        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Logout failed: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load seller's products
      final products = await _supabaseService.getMeatProducts(
        sellerId: widget.seller['id'],
        limit: 50,
      );

      // Load all orders to filter seller's orders
      final allOrders = await _supabaseService.getOrders(limit: 100);
      final sellerOrders = <Map<String, dynamic>>[];

      // Filter orders that contain seller's products
      for (final order in allOrders) {
        final orderItems = order['order_items'] as List? ?? [];
        bool hasSellerProduct = false;

        for (final item in orderItems) {
          final productId = item['product_id'];
          if (productId != null) {
            // Check if this product belongs to the seller
            final product = products.firstWhere(
              (p) => p['id'] == productId,
              orElse: () => {},
            );
            if (product.isNotEmpty) {
              hasSellerProduct = true;
              break;
            }
          }
        }

        if (hasSellerProduct) {
          sellerOrders.add(order);
        }
      }

      // Calculate comprehensive statistics
      final totalProducts = products.length;
      final activeProducts = products
          .where((p) => p['is_active'] == true)
          .length;
      final pendingProducts = products
          .where((p) => p['approval_status'] == 'pending')
          .length;
      final approvedProducts = products
          .where((p) => p['approval_status'] == 'approved')
          .length;

      final totalOrders = sellerOrders.length;
      final totalRevenue = sellerOrders.fold<double>(0.0, (sum, order) {
        return sum + (order['total_amount']?.toDouble() ?? 0.0);
      });

      final averageOrderValue = totalOrders > 0
          ? totalRevenue / totalOrders
          : 0.0;

      // Order status breakdown
      final confirmedOrders = sellerOrders
          .where((o) => o['status'] == 'confirmed')
          .length;
      final pendingOrders = sellerOrders
          .where((o) => o['status'] == 'pending')
          .length;
      final cancelledOrders = sellerOrders
          .where((o) => o['status'] == 'cancelled')
          .length;
      final deliveredOrders = sellerOrders
          .where((o) => o['status'] == 'delivered')
          .length;

      // Live orders (processing/confirmed)
      final liveOrders = sellerOrders
          .where(
            (o) => o['status'] == 'processing' || o['status'] == 'confirmed',
          )
          .toList();

      // Get unique customers
      final customerIds = sellerOrders.map((o) => o['customer_id']).toSet();
      final customerCount = customerIds.length;

      // Top customers (by order count)
      final customerOrderCounts = <String, int>{};
      for (final order in sellerOrders) {
        final customerId = order['customer_id'];
        customerOrderCounts[customerId] =
            (customerOrderCounts[customerId] ?? 0) + 1;
      }

      // Get customer details for top customers
      final topCustomers = <Map<String, dynamic>>[];
      final sortedCustomers = customerOrderCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      for (final entry in sortedCustomers.take(5)) {
        final customerOrder = sellerOrders.firstWhere(
          (o) => o['customer_id'] == entry.key,
          orElse: () => {},
        );
        if (customerOrder.isNotEmpty && customerOrder['customers'] != null) {
          topCustomers.add({
            'customer': customerOrder['customers'],
            'orderCount': entry.value,
            'totalSpent': sellerOrders
                .where((o) => o['customer_id'] == entry.key)
                .fold<double>(
                  0.0,
                  (sum, o) => sum + (o['total_amount']?.toDouble() ?? 0.0),
                ),
          });
        }
      }

      setState(() {
        _dashboardData = {
          'products': products,
          'orders': sellerOrders,
          'liveOrders': liveOrders,
          'topCustomers': topCustomers,
          'stats': {
            'totalRevenue': totalRevenue,
            'averageOrderValue': averageOrderValue,
            'totalOrders': totalOrders,
            'customerCount': customerCount,
            'totalProducts': totalProducts,
            'activeProducts': activeProducts,
            'pendingProducts': pendingProducts,
            'approvedProducts': approvedProducts,
            'confirmedOrders': confirmedOrders,
            'pendingOrders': pendingOrders,
            'cancelledOrders': cancelledOrders,
            'deliveredOrders': deliveredOrders,
          },
        };
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading dashboard data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Update notification count for badge display
  Future<void> _updateNotificationCount() async {
    try {
      final count = await _notificationService.getUnreadNotificationCount(
        widget.seller['id'],
      );
      if (mounted) {
        setState(() {
          _notificationCount = count;
        });
      }
    } catch (e) {
      // Silently handle notification count update errors
      print('Error updating notification count: $e');
    }
  }

  /// Navigate to notifications screen
  Future<void> _navigateToNotifications() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SellerNotificationsScreen(seller: widget.seller),
      ),
    );

    // Update notification count when returning from notifications screen
    if (result != null || mounted) {
      _updateNotificationCount();
    }
  }

  /// Check seller approval status from Odoo and update if changed
  Future<void> _checkSellerApprovalStatus() async {
    try {
      // Only check status for pending sellers or if it's been a while since last check
      final currentStatus = widget.seller['approval_status'];
      final odooSellerId = widget.seller['odoo_seller_id'];

      // Skip if seller is not in Odoo yet
      if (odooSellerId == null) {
        print('ℹ️ Seller not synced to Odoo yet, skipping status check');
        return;
      }

      // Skip if already approved (unless it's been more than a day)
      if (currentStatus == 'approved') {
        final updatedAt = widget.seller['updated_at'];
        if (updatedAt != null) {
          final lastUpdate = DateTime.parse(updatedAt);
          final hoursSinceUpdate = DateTime.now()
              .difference(lastUpdate)
              .inHours;
          if (hoursSinceUpdate < 24) {
            print('ℹ️ Seller recently approved, skipping status check');
            return;
          }
        }
      }

      print('🔄 Checking seller approval status from Odoo...');

      final result = await _statusSyncService.syncSellerStatus(
        widget.seller['id'],
        sellerName: widget.seller['seller_name'],
        showLogs: true,
      );

      if (result['success'] == true && result['status_changed'] == true) {
        final newStatus = result['current_status'];
        final previousStatus = result['previous_status'];

        print('✅ Seller status updated: $previousStatus → $newStatus');

        // Show status change notification to user
        if (mounted) {
          _showStatusChangeNotification(previousStatus, newStatus);

          // Reload dashboard data to reflect new status
          _loadDashboardData();
        }
      } else if (result['success'] == true) {
        print('ℹ️ Seller status unchanged: ${result['current_status']}');
      } else {
        print('⚠️ Status sync failed: ${result['error']}');
      }
    } catch (e) {
      print('❌ Error checking seller approval status: $e');
      // Don't show error to user for background status checks
    }
  }

  /// Manually sync seller status (called by user action)
  Future<void> _manualStatusSync() async {
    if (_isSyncingStatus) return; // Prevent multiple simultaneous syncs

    setState(() {
      _isSyncingStatus = true;
    });

    try {
      print('🔄 Manual seller status sync requested...');

      final result = await _statusSyncService.syncSellerStatus(
        widget.seller['id'],
        sellerName: widget.seller['seller_name'],
        showLogs: true,
      );

      if (mounted) {
        if (result['success'] == true) {
          if (result['status_changed'] == true) {
            final newStatus = result['current_status'];
            final previousStatus = result['previous_status'];

            _showStatusChangeNotification(previousStatus, newStatus);
            _loadDashboardData(); // Reload dashboard

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Status updated: $previousStatus → $newStatus'),
                backgroundColor: const Color(0xFF059669),
                duration: const Duration(seconds: 3),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Status is up to date: ${result['current_status']}',
                ),
                backgroundColor: const Color(0xFF3B82F6),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Sync failed: ${result['error'] ?? 'Unknown error'}',
              ),
              backgroundColor: const Color(0xFFDC2626),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      print('❌ Manual status sync error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to sync status. Please try again.'),
            backgroundColor: Color(0xFFDC2626),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncingStatus = false;
        });
      }
    }
  }

  /// Show status change notification dialog
  void _showStatusChangeNotification(String previousStatus, String newStatus) {
    if (!mounted) return;

    String title;
    String message;
    Color color;
    IconData icon;

    switch (newStatus) {
      case 'approved':
        title = '🎉 Congratulations!';
        message =
            'Your seller account has been approved! You can now start selling your products.';
        color = const Color(0xFF059669);
        icon = Icons.check_circle;
        break;
      case 'rejected':
        title = '❌ Account Rejected';
        message =
            'Your seller account has been rejected. Please contact support for more information.';
        color = const Color(0xFFDC2626);
        icon = Icons.cancel;
        break;
      case 'pending':
        title = '⏳ Status Updated';
        message = 'Your account status has been updated to pending review.';
        color = const Color(0xFFF59E0B);
        icon = Icons.hourglass_empty;
        break;
      default:
        title = '📋 Status Updated';
        message = 'Your account status has been updated to: $newStatus';
        color = const Color(0xFF3B82F6);
        icon = Icons.info;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(width: 12),
            Expanded(child: Text(title)),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFFECFDF5), // emerald-50
              const Color(0xFFDCFAE6), // green-100
            ],
          ),
        ),
        child: SafeArea(
          child: _isLoading ? _buildLoadingState() : _buildDashboard(),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF059669)),
      ),
    );
  }

  Widget _buildDashboard() {
    return CustomScrollView(
      slivers: [
        _buildAppBar(),
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              _buildWelcomeCard(),
              const SizedBox(height: 16),
              _buildMeatOverviewStats(),
              const SizedBox(height: 24),
              _buildLiveOrdersSection(),
              const SizedBox(height: 24),
              _buildOrderStatusOverview(),
              const SizedBox(height: 24),
              _buildQuickActions(),
              const SizedBox(height: 24),
              _buildInventorySection(),
              const SizedBox(height: 24),
              _buildTopCustomersSection(),
            ]),
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: const Color(0xFF059669),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Dashboard',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [const Color(0xFF059669), const Color(0xFF047857)],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _loadDashboardData,
        ),
        // Notifications Button with Badge
        Stack(
          children: [
            IconButton(
              icon: const Icon(
                Icons.notifications_outlined,
                color: Colors.white,
              ),
              onPressed: _navigateToNotifications,
              tooltip: 'Notifications',
            ),
            if (_notificationCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    '$_notificationCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        IconButton(
          icon: const Icon(Icons.logout, color: Colors.white),
          onPressed: _logout,
          tooltip: 'Logout',
        ),
      ],
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.9),
            Colors.green.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(
          width: 1,
          color: Colors.white.withValues(alpha: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF059669).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.store,
                  color: Color(0xFF059669),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back!',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                    Text(
                      widget.seller['seller_name'] ?? 'Seller',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getStatusColor(
                widget.seller['approval_status'],
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _getStatusColor(widget.seller['approval_status']),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(widget.seller['approval_status']),
                  color: _getStatusColor(widget.seller['approval_status']),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Status: ${_getStatusText(widget.seller['approval_status'])}',
                    style: TextStyle(
                      color: _getStatusColor(widget.seller['approval_status']),
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ),
                // Add status sync button for pending sellers or manual refresh
                if (widget.seller['approval_status'] == 'pending' ||
                    widget.seller['odoo_seller_id'] != null)
                  TextButton.icon(
                    onPressed: _isSyncingStatus ? null : _manualStatusSync,
                    icon: _isSyncingStatus
                        ? const SizedBox(
                            width: 12,
                            height: 12,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.refresh, size: 16),
                    label: Text(
                      _isSyncingStatus ? 'Syncing...' : 'Check Status',
                      style: const TextStyle(fontSize: 12),
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor: _getStatusColor(
                        widget.seller['approval_status'],
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
              ],
            ),
          ),
          if (UiFlags.enableSellerOpenCloseToggle)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Row(
                children: [
                  const Icon(
                    Icons.store_mall_directory,
                    size: 16,
                    color: Colors.black54,
                  ),
                  const SizedBox(width: 8),
                  const Text('Shop Open', style: TextStyle(fontSize: 14)),
                  const Spacer(),
                  Switch(
                    value:
                        (_dashboardData['seller_extra']?['settings']?['is_open'] ??
                            false) ==
                        true,
                    onChanged: (val) async {
                      try {
                        // Merge JSONB: extra.settings.is_open = val (client-side merge)
                        final currentExtra = Map<String, dynamic>.from(
                          (_dashboardData['seller_extra']
                                  as Map<String, dynamic>?) ??
                              {},
                        );
                        final settings = Map<String, dynamic>.from(
                          (currentExtra['settings'] as Map<String, dynamic>?) ??
                              {},
                        );
                        settings['is_open'] = val;
                        currentExtra['settings'] = settings;

                        final updated = await _supabaseService.updateSeller(
                          widget.seller['id'],
                          {'extra': currentExtra},
                        );

                        if (mounted) {
                          setState(() {
                            _dashboardData['seller_extra'] = updated['extra'];
                          });
                        }

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              val
                                  ? 'Shop marked as Open'
                                  : 'Shop marked as Closed',
                            ),
                            backgroundColor: const Color(0xFF059669),
                          ),
                        );
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Failed to update shop status: $e'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMeatOverviewStats() {
    final stats = _dashboardData['stats'] ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Meat Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'Total Revenue',
              '₹${(stats['totalRevenue'] ?? 0.0).toStringAsFixed(0)}',
              Icons.currency_rupee,
              const Color(0xFF059669),
            ),
            _buildStatCard(
              'Average Order Value',
              '₹${(stats['averageOrderValue'] ?? 0.0).toStringAsFixed(1)}',
              Icons.trending_up,
              const Color(0xFF10B981),
            ),
            _buildStatCard(
              'Total Orders',
              '${stats['totalOrders'] ?? 0}',
              Icons.shopping_cart_outlined,
              const Color(0xFF3B82F6),
            ),
            _buildStatCard(
              'Customer Count',
              '${stats['customerCount'] ?? 0}',
              Icons.people_outline,
              const Color(0xFF8B5CF6),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: 24),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'Add Product',
                Icons.add_box_outlined,
                const Color(0xFF059669),
                () {
                  final isApproved =
                      (widget.seller['approval_status']
                          ?.toString()
                          .toLowerCase() ==
                      'approved');
                  if (!isApproved) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Your account is pending approval. You can add products after approval.',
                        ),
                        backgroundColor: Colors.orange,
                      ),
                    );
                    return;
                  }
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) =>
                          ProductManagementScreen(seller: widget.seller),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                'View Orders',
                Icons.list_alt_outlined,
                const Color(0xFF3B82F6),
                () {
                  // TODO: Navigate to orders screen
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'Analytics',
                Icons.analytics_outlined,
                const Color(0xFF8B5CF6),
                () {
                  // TODO: Navigate to analytics screen
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                'Profile',
                Icons.person_outline,
                const Color(0xFFEF4444),
                () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          SellerProfileScreen(seller: widget.seller),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentProducts() {
    final products = _dashboardData['products'] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Products',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to all products
              },
              child: const Text(
                'View All',
                style: TextStyle(
                  color: Color(0xFF059669),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (products.isEmpty)
          _buildEmptyState(
            'No products yet',
            'Add your first product to get started',
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: products.length > 3 ? 3 : products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return _buildProductCard(product);
            },
          ),
      ],
    );
  }

  Widget _buildRecentOrders() {
    final orders = _dashboardData['orders'] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Recent Orders',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to all orders
              },
              child: const Text(
                'View All',
                style: TextStyle(
                  color: Color(0xFF059669),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (orders.isEmpty)
          _buildEmptyState(
            'No orders yet',
            'Orders will appear here once customers start buying',
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: orders.length > 3 ? 3 : orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return _buildOrderCard(order);
            },
          ),
      ],
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: const Color(0xFF059669).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.fastfood,
              color: Color(0xFF059669),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product['name'] ?? 'Product',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '₹${product['price'] ?? 0}/kg',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF059669),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      product['approval_status'],
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getStatusText(product['approval_status']),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getStatusColor(product['approval_status']),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order #${order['id'].toString().substring(0, 8)}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    order['status'],
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _getStatusText(order['status']),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getStatusColor(order['status']),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Customer: ${order['customers']?['full_name'] ?? 'Unknown'}',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 4),
          Text(
            'Total: ₹${order['total_amount'] ?? 0}',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF059669),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        children: [
          Icon(Icons.inbox_outlined, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
      case 'active':
      case 'delivered':
        return const Color(0xFF10B981);
      case 'pending':
      case 'processing':
        return const Color(0xFFF59E0B);
      case 'rejected':
      case 'cancelled':
        return const Color(0xFFEF4444);
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
      case 'active':
      case 'delivered':
        return Icons.check_circle;
      case 'pending':
      case 'processing':
        return Icons.access_time;
      case 'rejected':
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  String _getStatusText(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Pending';
      case 'rejected':
        return 'Rejected';
      case 'active':
        return 'Active';
      case 'delivered':
        return 'Delivered';
      case 'processing':
        return 'Processing';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status?.toUpperCase() ?? 'Unknown';
    }
  }

  Widget _buildLiveOrdersSection() {
    final liveOrders = _dashboardData['liveOrders'] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Live Orders in Progress',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        if (liveOrders.isEmpty)
          _buildEmptyState('No live orders', 'Active orders will appear here')
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: liveOrders.length > 3 ? 3 : liveOrders.length,
            itemBuilder: (context, index) {
              final order = liveOrders[index];
              return _buildLiveOrderCard(order);
            },
          ),
      ],
    );
  }

  Widget _buildLiveOrderCard(Map<String, dynamic> order) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF059669).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.local_shipping,
              color: Color(0xFF059669),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order #${order['id'].toString().substring(0, 8)}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Customer: ${order['customers']?['full_name'] ?? 'Unknown'}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(
                    order['status'],
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _getStatusText(order['status']),
                  style: TextStyle(
                    fontSize: 10,
                    color: _getStatusColor(order['status']),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '₹${order['total_amount'] ?? 0}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF059669),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatusOverview() {
    final stats = _dashboardData['stats'] ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Order Status Overview',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatusCard(
                '${stats['confirmedOrders'] ?? 0}',
                'Confirmed',
                const Color(0xFF10B981),
                Icons.check_circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatusCard(
                '${stats['pendingOrders'] ?? 0}',
                'Pending',
                const Color(0xFFF59E0B),
                Icons.access_time,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatusCard(
                '${stats['cancelledOrders'] ?? 0}',
                'Cancelled',
                const Color(0xFFEF4444),
                Icons.cancel,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusCard(
    String count,
    String label,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            count,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInventorySection() {
    final products = _dashboardData['products'] ?? [];
    final stats = _dashboardData['stats'] ?? {};

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Inventory & Product Intelligence',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Your Products (${stats['totalProducts'] ?? 0})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      final isApproved =
                          (widget.seller['approval_status']
                              ?.toString()
                              .toLowerCase() ==
                          'approved');
                      if (!isApproved) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Your account is pending approval. You can manage products after approval.',
                            ),
                            backgroundColor: Colors.orange,
                          ),
                        );
                        return;
                      }
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) =>
                              ProductManagementScreen(seller: widget.seller),
                        ),
                      );
                    },
                    child: const Text(
                      'Manage Products',
                      style: TextStyle(
                        color: Color(0xFF059669),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildInventoryStatCard(
                      'Active',
                      '${stats['activeProducts'] ?? 0}',
                      const Color(0xFF10B981),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildInventoryStatCard(
                      'Pending',
                      '${stats['pendingProducts'] ?? 0}',
                      const Color(0xFFF59E0B),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildInventoryStatCard(
                      'Approved',
                      '${stats['approvedProducts'] ?? 0}',
                      const Color(0xFF3B82F6),
                    ),
                  ),
                ],
              ),
              if (products.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                const Text(
                  'Recent Products',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                ...products
                    .take(3)
                    .map((product) => _buildInventoryProductCard(product)),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryStatCard(String label, String count, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Text(
            count,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryProductCard(Map<String, dynamic> product) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF059669).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(
              Icons.fastfood,
              color: Color(0xFF059669),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product['name'] ?? 'Product',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  '₹${product['price'] ?? 0}/kg',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF059669),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getStatusColor(
                product['approval_status'],
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              _getStatusText(product['approval_status']),
              style: TextStyle(
                fontSize: 10,
                color: _getStatusColor(product['approval_status']),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopCustomersSection() {
    final topCustomers = _dashboardData['topCustomers'] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Top Customers',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        if (topCustomers.isEmpty)
          _buildEmptyState(
            'No customers yet',
            'Customer data will appear here once you have orders',
          )
        else
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!, width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: topCustomers.length > 5 ? 5 : topCustomers.length,
              separatorBuilder: (context, index) =>
                  Divider(height: 1, color: Colors.grey[200]),
              itemBuilder: (context, index) {
                final customerData = topCustomers[index];
                return _buildTopCustomerCard(customerData, index + 1);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildTopCustomerCard(Map<String, dynamic> customerData, int rank) {
    final customer = customerData['customer'];
    final orderCount = customerData['orderCount'];
    final totalSpent = customerData['totalSpent'];

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: rank <= 3 ? const Color(0xFF059669) : Colors.grey[400],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                '$rank',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  customer['full_name'] ?? 'Customer',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  '$orderCount orders',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '₹${totalSpent.toStringAsFixed(0)}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF059669),
                ),
              ),
              Text(
                'Total Spent',
                style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
