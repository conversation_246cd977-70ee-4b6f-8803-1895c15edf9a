2025-08-23 10:32:50.922 [info] [main] Log level: Info
2025-08-23 10:32:50.922 [info] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-08-23 10:32:50.922 [info] [main] Using git "2.50.1.windows.1" from "C:\Program Files\Git\cmd\git.exe"
2025-08-23 10:32:50.922 [info] [Model][doInitialScan] Initial repository scan started
2025-08-23 10:32:50.922 [info] > git rev-parse --show-toplevel [304ms]
2025-08-23 10:32:50.938 [info] > git rev-parse --git-dir --git-common-dir --show-superproject-working-tree [935ms]
2025-08-23 10:32:50.959 [info] [Model][openRepository] Opened repository (path): c:\client
2025-08-23 10:32:50.959 [info] [Model][openRepository] Opened repository (real path): c:\client
2025-08-23 10:32:50.959 [info] [Model][openRepository] Opened repository (kind): repository
2025-08-23 10:32:52.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [895ms]
2025-08-23 10:32:52.162 [info] > git config --get commit.template [1168ms]
2025-08-23 10:32:52.192 [info] > git check-ignore -v -z --stdin [157ms]
2025-08-23 10:32:52.236 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [64ms]
2025-08-23 10:32:52.281 [info] > git status -z -uall [114ms]
2025-08-23 10:32:53.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [797ms]
2025-08-23 10:32:53.223 [info] > git config --get --local branch.main.vscode-merge-base [112ms]
2025-08-23 10:32:53.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/origin/main refs/remotes/origin/main [315ms]
2025-08-23 10:32:53.587 [info] > git config --get commit.template [436ms]
2025-08-23 10:32:54.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [712ms]
2025-08-23 10:32:54.690 [info] > git check-ignore -v -z --stdin [957ms]
2025-08-23 10:32:54.733 [info] > git fetch [3774ms]
2025-08-23 10:32:54.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [400ms] (cancelled)
2025-08-23 10:32:54.835 [info] > git status -z -uall [569ms] (cancelled)
2025-08-23 10:32:54.836 [info] > git rev-parse --show-toplevel [487ms]
2025-08-23 10:32:54.904 [info] > git check-ignore -v -z --stdin [86ms]
2025-08-23 10:32:54.957 [info] > git rev-parse --show-toplevel [114ms]
2025-08-23 10:32:54.970 [info] > git config --get commit.template [138ms]
2025-08-23 10:32:54.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [121ms]
2025-08-23 10:32:55.100 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [116ms]
2025-08-23 10:32:55.100 [info] > git rev-parse --show-toplevel [136ms]
2025-08-23 10:32:55.271 [info] > git status -z -uall [293ms]
2025-08-23 10:32:55.272 [info] > git rev-parse --show-toplevel [168ms]
2025-08-23 10:32:55.906 [info] > git rev-parse --show-toplevel [520ms]
2025-08-23 10:32:56.019 [info] > git rev-parse --show-toplevel [100ms]
2025-08-23 10:32:56.137 [info] > git rev-parse --show-toplevel [114ms]
2025-08-23 10:32:56.214 [info] > git rev-parse --show-toplevel [73ms]
2025-08-23 10:32:56.215 [info] fatal: this operation must be run in a work tree
2025-08-23 10:32:56.306 [info] > git rev-parse --show-toplevel [87ms]
2025-08-23 10:32:56.423 [info] > git rev-parse --show-toplevel [112ms]
2025-08-23 10:32:56.496 [info] > git rev-parse --show-toplevel [69ms]
2025-08-23 10:32:56.568 [info] > git rev-parse --show-toplevel [68ms]
2025-08-23 10:32:56.651 [info] > git rev-parse --show-toplevel [79ms]
2025-08-23 10:32:56.732 [info] > git rev-parse --show-toplevel [76ms]
2025-08-23 10:32:56.801 [info] > git rev-parse --show-toplevel [65ms]
2025-08-23 10:32:56.865 [info] > git rev-parse --show-toplevel [60ms]
2025-08-23 10:32:56.945 [info] > git rev-parse --show-toplevel [76ms]
2025-08-23 10:32:56.976 [info] > git check-ignore -v -z --stdin [84ms]
2025-08-23 10:32:57.021 [info] > git rev-parse --show-toplevel [72ms]
2025-08-23 10:32:57.125 [info] > git rev-parse --show-toplevel [100ms]
2025-08-23 10:32:57.225 [info] > git rev-parse --show-toplevel [89ms]
2025-08-23 10:32:57.314 [info] > git rev-parse --show-toplevel [84ms]
2025-08-23 10:32:57.438 [info] > git rev-parse --show-toplevel [120ms]
2025-08-23 10:32:57.535 [info] > git rev-parse --show-toplevel [93ms]
2025-08-23 10:32:57.619 [info] > git rev-parse --show-toplevel [79ms]
2025-08-23 10:32:57.707 [info] > git rev-parse --show-toplevel [83ms]
2025-08-23 10:32:57.790 [info] > git rev-parse --show-toplevel [77ms]
2025-08-23 10:32:57.865 [info] > git rev-parse --show-toplevel [69ms]
2025-08-23 10:32:57.868 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-08-23 10:33:09.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [391ms]
2025-08-23 10:33:11.851 [info] > git add -A -- . [3796ms]
2025-08-23 10:33:11.852 [info] warning: could not open directory 'android/app/src/main/java/.../com/': No such file or directory
warning: in the working copy of ' understanding_qwen.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agent-teams/team-all.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agent-teams/team-fullstack.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agent-teams/team-ide-minimal.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agent-teams/team-no-ui.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/analyst.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/architect.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/bmad-master.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/bmad-orchestrator.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/dev.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/pm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/po.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/qa.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/sm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/agents/ux-expert.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/checklists/architect-checklist.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/checklists/change-checklist.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/checklists/pm-checklist.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/checklists/po-master-checklist.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/checklists/story-dod-checklist.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/checklists/story-draft-checklist.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/core-config.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/data/bmad-kb.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/data/brainstorming-techniques.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/data/elicitation-methods.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/data/technical-preferences.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/data/test-levels-framework.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/data/test-priorities-matrix.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/enhanced-ide-development-workflow.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/install-manifest.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/advanced-elicitation.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/apply-qa-fixes.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/brownfield-create-epic.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/brownfield-create-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/correct-course.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/create-brownfield-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/create-deep-research-prompt.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/create-doc.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/create-next-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/document-project.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/execute-checklist.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/facilitate-brainstorming-session.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/generate-ai-frontend-prompt.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/index-docs.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/kb-mode-interaction.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/nfr-assess.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/qa-gate.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/review-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/risk-profile.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/shard-doc.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/test-design.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/trace-requirements.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/tasks/validate-next-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/architecture-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/brainstorming-output-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/brownfield-architecture-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/brownfield-prd-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/competitor-analysis-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/front-end-architecture-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/front-end-spec-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/fullstack-architecture-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/market-research-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/prd-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/project-brief-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/qa-gate-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/templates/story-tmpl.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/user-guide.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/utils/bmad-doc-template.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/utils/workflow-management.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/workflows/brownfield-fullstack.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/workflows/brownfield-service.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/workflows/brownfield-ui.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/workflows/greenfield-fullstack.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/workflows/greenfield-service.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/workflows/greenfield-ui.yaml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bmad-core/working-in-the-brownfield.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.bundle/config', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/analyst.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/architect.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/bmad-master.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/bmad-orchestrator.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/dev.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/pm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/po.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/qa.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/sm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/agents/ux-expert.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/advanced-elicitation.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/apply-qa-fixes.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/brownfield-create-epic.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/brownfield-create-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/correct-course.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/create-brownfield-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/create-deep-research-prompt.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/create-doc.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/create-next-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/document-project.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/execute-checklist.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/facilitate-brainstorming-session.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/generate-ai-frontend-prompt.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/index-docs.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/kb-mode-interaction.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/nfr-assess.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/qa-gate.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/review-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/risk-profile.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/shard-doc.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/test-design.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/trace-requirements.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.claude/commands/BMad/tasks/validate-next-story.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/01-bmad-master.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/02-bmad-orchestrator.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/03-pm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/04-analyst.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/05-architect.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/06-po.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/07-sm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/08-dev.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/09-qa.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.clinerules/10-ux-expert.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/analyst.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/architect.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/bmad-master.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/bmad-orchestrator.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/dev.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/pm.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/po.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/qa.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/sm.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.cursor/rules/bmad/ux-expert.mdc', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.env', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.eslintignore', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.eslintrc.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.gemini/bmad-method/GEMINI.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/COMMIT_EDITMSG', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/FETCH_HEAD', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/HEAD', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/ORIG_HEAD', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/config', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/description', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/applypatch-msg.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/commit-msg.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/fsmonitor-watchman.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/post-update.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/pre-applypatch.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/pre-commit.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/pre-merge-commit.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/pre-push.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/pre-rebase.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/pre-receive.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/prepare-commit-msg.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/push-to-checkout.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/sendemail-validate.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/hooks/update.sample', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/info/exclude', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/logs/HEAD', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/logs/refs/heads/main', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/logs/refs/remotes/origin/HEAD', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/logs/refs/remotes/origin/circleci-project-setup', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/logs/refs/remotes/origin/main', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/refs/heads/main', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/refs/remotes/origin/HEAD', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/refs/remotes/origin/circleci-project-setup', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.git_disabled/refs/remotes/origin/main', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.gitignore', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.kilocode/mcp.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.kilocodemodes', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.prettierrc.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.qwen/bmad-method/QWEN.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.roomodes', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/analyst.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/architect.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/bmad-master.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/bmad-orchestrator.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/dev.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/pm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/po.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/qa.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/sm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.trae/rules/ux-expert.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.vscode/google.gemini-cli-vscode-ide-companion-0.1.21/.vsixmanifest', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.vscode/google.gemini-cli-vscode-ide-companion-0.1.21/LICENSE.txt', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.vscode/google.gemini-cli-vscode-ide-companion-0.1.21/NOTICES.txt', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.vscode/google.gemini-cli-vscode-ide-companion-0.1.21/dist/extension.cjs', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.vscode/google.gemini-cli-vscode-ide-companion-0.1.21/dist/extension.cjs.map', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.vscode/google.gemini-cli-vscode-ide-companion-0.1.21/package.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.vscode/google.gemini-cli-vscode-ide-companion-0.1.21/readme.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.watchmanconfig', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/analyst.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/architect.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/bmad-master.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/bmad-orchestrator.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/dev.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/pm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/po.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/qa.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/sm.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '.windsurf/workflows/ux-expert.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'APK_BUILD_READY.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'App.tsx', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'Error-Fixes/deployment-errors-and-solutions.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'FINAL_VERIFICATION.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'Gemfile', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'Gemfile.lock', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'QUICK_START.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'README.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'README_OTP.md', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/.kotlin/errors/errors-1755816800419.log', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/.kotlin/errors/errors-1755816973653.log', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/.kotlin/errors/errors-1755838107332.log', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/.kotlin/errors/errors-1755839350035.log', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/.kotlin/errors/errors-1755840056843.log', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/.kotlin/errors/errors-1755840515038.log', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/.kotlin/errors/errors-1755849745517.log', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/build.gradle', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/google-services.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/proguard-rules.pro', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/src/debug/AndroidManifest.xml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/src/main/AndroidManifest.xml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/src/main/res/values/colors.xml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/src/main/res/values/strings.xml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/src/main/res/values/styles.xml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/app/src/main/res/xml/network_security_config.xml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/build.gradle', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/gradle.properties', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/gradle/wrapper/gradle-wrapper.properties', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/gradlew', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/gradlew.bat', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/link-assets-manifest.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/android/settings.gradle', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/app.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/.xcode.env', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/Podfile', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/Podfile.lock', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app.xcodeproj/project.pbxproj', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app.xcodeproj/xcshareddata/xcschemes/grocery_app.xcscheme', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app.xcworkspace/contents.xcworkspacedata', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app/AppDelegate.swift', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app/GoogleService-Info.plist', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app/Images.xcassets/AppIcon.appiconset/Contents.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app/Images.xcassets/Contents.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app/Info.plist', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app/LaunchScreen.storyboard', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/grocery_app/PrivacyInfo.xcprivacy', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/ios/link-assets-manifest.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/package-lock.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/package.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'SellerApp/tsconfig.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__mocks__/@react-native-async-storage/async-storage.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__mocks__/@react-native-community/geolocation.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__mocks__/fileMock.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__mocks__/react-native-gesture-handler.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__mocks__/react-native-reanimated-carousel.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__mocks__/react-native-safe-area-context.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__mocks__/react-native-screens.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__mocks__/styleMock.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__tests__/App.test.tsx', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__tests__/otp-components.test.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__tests__/otp-controllers.test.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of '__tests__/otp-services.test.js', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'android/app/build.gradle', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'android/app/google-services.json', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'android/app/proguard-rules.pro', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'android/app/src/debug/AndroidManifest.xml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'android/app/src/main/AndroidManifest.xml', LF will be replaced by CRLF the next time Git touches it
warning: in the working copy of 'android/app/src/main/assets/index.android.bundle', LF will be replaced by CRLF the next time Git touches it
error: open("android/app/src/main/java/.../.../MainApplication.java"): No such file or directory
error: unable to index file 'android/app/src/main/java/.../.../MainApplication.java'
fatal: adding files failed
2025-08-23 10:33:11.861 [info] > git config --get commit.template [2424ms]
2025-08-23 10:33:12.525 [info] > git config --get commit.template [335ms]
2025-08-23 10:33:12.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [170ms]
2025-08-23 10:33:12.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [116ms]
2025-08-23 10:33:13.479 [info] > git status -z -uall [607ms]
2025-08-23 10:33:15.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [294ms]
2025-08-23 10:33:15.569 [info] > git config --get commit.template [319ms]
2025-08-23 10:33:15.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [141ms]
2025-08-23 10:33:17.574 [info] > git status -z -uall [2001ms]
2025-08-23 10:33:22.713 [info] > git config --get commit.template [95ms]
2025-08-23 10:33:22.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [90ms]
2025-08-23 10:33:22.919 [info] > git status -z -uall [199ms]
2025-08-23 10:33:22.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [209ms]
2025-08-23 10:33:28.110 [info] > git config --get commit.template [138ms]
2025-08-23 10:33:28.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [132ms]
2025-08-23 10:33:28.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [80ms]
2025-08-23 10:33:28.256 [info] > git status -z -uall [139ms]
2025-08-23 10:33:33.378 [info] > git config --get commit.template [79ms]
2025-08-23 10:33:33.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [85ms]
2025-08-23 10:33:33.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [75ms]
2025-08-23 10:33:33.529 [info] > git status -z -uall [135ms]
2025-08-23 10:33:44.976 [info] > git config --get commit.template [215ms]
2025-08-23 10:33:44.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [210ms]
2025-08-23 10:33:45.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [115ms]
2025-08-23 10:33:45.287 [info] > git status -z -uall [301ms]
2025-08-23 10:33:50.427 [info] > git config --get commit.template [73ms]
2025-08-23 10:33:50.427 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [67ms]
2025-08-23 10:33:50.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [70ms]
2025-08-23 10:33:50.562 [info] > git status -z -uall [130ms]
2025-08-23 10:33:59.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [82ms]
2025-08-23 10:33:59.502 [info] > git config --get commit.template [91ms]
2025-08-23 10:33:59.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [80ms]
2025-08-23 10:33:59.665 [info] > git status -z -uall [158ms]
2025-08-23 10:34:04.761 [info] > git config --get commit.template [54ms]
2025-08-23 10:34:04.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [61ms]
2025-08-23 10:34:04.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [58ms]
2025-08-23 10:34:04.887 [info] > git status -z -uall [110ms]
2025-08-23 10:34:18.003 [info] > git config --get commit.template [100ms]
2025-08-23 10:34:18.011 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) --ignore-case refs/heads/main refs/remotes/main [103ms]
2025-08-23 10:34:18.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [114ms]
2025-08-23 10:34:18.225 [info] > git status -z -uall [208ms]
