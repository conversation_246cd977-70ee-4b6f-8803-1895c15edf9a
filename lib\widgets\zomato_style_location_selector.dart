import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/auto_location_service.dart';
import '../services/delivery_address_state.dart';
import '../services/location_service.dart';
import '../supabase_service.dart';
import '../screens/zomato_style_add_address_screen.dart';

/// ZomatoStyleLocationSelector - Modern location selection modal
///
/// This widget provides a clean, Zomato-inspired location selection experience with:
/// - Search bar for area/street name input
/// - "Use current location" button with GPS functionality
/// - Saved addresses list with labels and distance badges
/// - Clean Material 3 design with emerald theme
/// - Smooth animations and proper accessibility
class ZomatoStyleLocationSelector extends StatefulWidget {
  final String customerId;
  final String? initialAddress;
  final Function(String address, Map<String, dynamic>? locationData)?
  onLocationSelected;

  const ZomatoStyleLocationSelector({
    super.key,
    required this.customerId,
    this.initialAddress,
    this.onLocationSelected,
  });

  @override
  State<ZomatoStyleLocationSelector> createState() =>
      _ZomatoStyleLocationSelectorState();
}

class _ZomatoStyleLocationSelectorState
    extends State<ZomatoStyleLocationSelector>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final LocationService _locationService = LocationService();

  bool _isLoadingCurrentLocation = false;
  String? _currentLocationAddress;
  Map<String, dynamic>? _currentLocationData;
  List<Map<String, dynamic>> _savedAddresses = [];
  bool _isLoadingSavedAddresses = false;

  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadSavedAddresses();
    _searchController.text = widget.initialAddress ?? '';
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero)
        .animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    // Start animations
    _slideController.forward();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedAddresses() async {
    setState(() {
      _isLoadingSavedAddresses = true;
    });

    try {
      // Load saved addresses from Supabase
      final response = await SupabaseService().client
          .from('customer_addresses')
          .select('*')
          .eq('customer_id', widget.customerId)
          .order('is_default', ascending: false)
          .order('created_at', ascending: false);

      if (response != null) {
        setState(() {
          _savedAddresses = List<Map<String, dynamic>>.from(response);
        });
      }
    } catch (e) {
      print('❌ LOCATION_SELECTOR - Error loading saved addresses: $e');
    } finally {
      setState(() {
        _isLoadingSavedAddresses = false;
      });
    }
  }

  Future<void> _useCurrentLocation() async {
    setState(() {
      _isLoadingCurrentLocation = true;
    });

    try {
      final locationData = await AutoLocationService.getCurrentLocationOnce();

      if (locationData != null) {
        final address = await _locationService.reverseGeocode(
          locationData['latitude'],
          locationData['longitude'],
        );

        if (address != null && mounted) {
          setState(() {
            _currentLocationAddress = address;
            _currentLocationData = locationData;
          });

          // Auto-save as temporary current location
          DeliveryAddressState.setAddress(
            address,
            locationData: locationData,
            customerId: widget.customerId,
          );

          // Provide haptic feedback
          HapticFeedback.lightImpact();

          // Call callback and close modal
          widget.onLocationSelected?.call(address, locationData);
          Navigator.pop(context);
        }
      }
    } catch (e) {
      print('❌ LOCATION_SELECTOR - Error getting current location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unable to get current location. Please try again.'),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingCurrentLocation = false;
        });
      }
    }
  }

  void _selectSavedAddress(Map<String, dynamic> address) {
    final addressText = address['address'] as String;
    final locationData = {
      'latitude': address['latitude'],
      'longitude': address['longitude'],
      'place_id': address['place_id'],
    };

    // Update shared state
    DeliveryAddressState.setAddress(
      addressText,
      locationData: locationData,
      customerId: widget.customerId,
    );

    // Provide haptic feedback
    HapticFeedback.selectionClick();

    // Call callback and close modal
    widget.onLocationSelected?.call(addressText, locationData);
    Navigator.pop(context);
  }

  void _openAddAddressFlow() async {
    // Navigate to map-based address input screen
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) =>
            ZomatoStyleAddAddressScreen(customerId: widget.customerId),
      ),
    );

    if (result != null) {
      // Address was saved successfully, close this modal and return the result
      widget.onLocationSelected?.call(
        result['address'] as String,
        result['location_data'] as Map<String, dynamic>?,
      );
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              _buildSearchBar(),
              _buildUseCurrentLocationButton(),
              const Divider(height: 1),
              _buildSavedAddressesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.keyboard_arrow_down),
            iconSize: 28,
          ),
          const Expanded(
            child: Text(
              'Select a location',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search for area, street name...',
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          suffixIcon: IconButton(
            onPressed: () {
              // TODO: Implement voice search
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Voice search - Coming soon!'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            icon: const Icon(Icons.mic, color: Colors.grey),
          ),
          filled: true,
          fillColor: Colors.grey[100],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildUseCurrentLocationButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: _isLoadingCurrentLocation ? null : _useCurrentLocation,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.my_location,
                  color: Colors.green[700],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Use current location',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.green[800],
                      ),
                    ),
                    if (_isLoadingCurrentLocation)
                      Text(
                        'Getting your location...',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green[600],
                        ),
                      )
                    else if (_currentLocationAddress != null)
                      Text(
                        _currentLocationAddress!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )
                    else
                      Text(
                        'Enable location access to use this feature',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green[600],
                        ),
                      ),
                  ],
                ),
              ),
              if (_isLoadingCurrentLocation)
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation(Colors.green[600]),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSavedAddressesSection() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'SAVED ADDRESSES',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                    letterSpacing: 0.5,
                  ),
                ),
                TextButton.icon(
                  onPressed: _openAddAddressFlow,
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('Add Address'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.green[700],
                    textStyle: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _isLoadingSavedAddresses
                ? const Center(child: CircularProgressIndicator())
                : _savedAddresses.isEmpty
                ? _buildEmptyState()
                : _buildSavedAddressesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.location_on_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No saved addresses yet',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first address to get started',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _openAddAddressFlow,
            icon: const Icon(Icons.add),
            label: const Text('Add Address'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavedAddressesList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _savedAddresses.length,
      itemBuilder: (context, index) {
        final address = _savedAddresses[index];
        return _buildSavedAddressCard(address);
      },
    );
  }

  Widget _buildSavedAddressCard(Map<String, dynamic> address) {
    final label = address['label'] as String? ?? 'Other';
    final addressText = address['address'] as String;
    final isDefault = address['is_default'] as bool? ?? false;

    IconData labelIcon;
    Color labelColor;

    switch (label.toLowerCase()) {
      case 'home':
        labelIcon = Icons.home;
        labelColor = Colors.green[600]!;
        break;
      case 'work':
        labelIcon = Icons.work;
        labelColor = Colors.blue[600]!;
        break;
      default:
        labelIcon = Icons.location_on;
        labelColor = Colors.grey[600]!;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isDefault ? Colors.green[300]! : Colors.grey[200]!,
          width: isDefault ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: () => _selectSavedAddress(address),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: labelColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(labelIcon, color: labelColor, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          label,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (isDefault) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green[600],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'DEFAULT',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      addressText,
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '0 m', // TODO: Calculate actual distance
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Icon(
                    isDefault
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked,
                    color: isDefault ? Colors.green[600] : Colors.grey[400],
                    size: 20,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
