import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../services/location_service.dart';
import '../services/delivery_address_state.dart';

/// CustomerAddressFormScreen - Detailed address form inspired by Zomato
/// 
/// This screen provides a comprehensive address input form similar to Zomato's
/// address details screen. Features include:
/// - Map view with pin drop functionality
/// - Detailed address fields (house/flat, area, landmark)
/// - Receiver details (name, phone)
/// - Address type selection (Home, Work, Other)
/// - Distance and delivery time estimation
/// - Save address functionality
class CustomerAddressFormScreen extends StatefulWidget {
  final String customerId;
  final LatLng initialLocation;
  final String? initialAddress;
  final Map<String, dynamic>? existingAddress; // For editing

  const CustomerAddressFormScreen({
    super.key,
    required this.customerId,
    required this.initialLocation,
    this.initialAddress,
    this.existingAddress,
  });

  @override
  State<CustomerAddressFormScreen> createState() => _CustomerAddressFormScreenState();
}

class _CustomerAddressFormScreenState extends State<CustomerAddressFormScreen> {
  final LocationService _locationService = LocationService();
  final _formKey = GlobalKey<FormState>();
  
  // Controllers
  final _houseController = TextEditingController();
  final _areaController = TextEditingController();
  final _landmarkController = TextEditingController();
  final _receiverNameController = TextEditingController();
  final _receiverPhoneController = TextEditingController();
  
  // State
  GoogleMapController? _mapController;
  LatLng _selectedLocation = const LatLng(12.9716, 77.5946);
  String _selectedAddress = '';
  String _selectedAddressType = 'Home';
  bool _isLoadingAddress = false;
  bool _isSaving = false;
  String? _distanceInfo;
  String? _deliveryTimeInfo;

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.initialLocation;
    _selectedAddress = widget.initialAddress ?? '';
    
    // Pre-fill form if editing existing address
    if (widget.existingAddress != null) {
      _prefillForm();
    }
    
    _loadAddressForLocation(_selectedLocation);
    _calculateDeliveryInfo();
  }

  /// Pre-fill form with existing address data
  void _prefillForm() {
    final address = widget.existingAddress!;
    _houseController.text = address['house_details'] ?? '';
    _areaController.text = address['area'] ?? '';
    _landmarkController.text = address['landmark'] ?? '';
    _receiverNameController.text = address['receiver_name'] ?? '';
    _receiverPhoneController.text = address['receiver_phone'] ?? '';
    _selectedAddressType = address['type'] ?? 'Home';
  }

  /// Load address for given location
  Future<void> _loadAddressForLocation(LatLng location) async {
    setState(() {
      _isLoadingAddress = true;
    });

    try {
      final address = await _locationService.reverseGeocode(
        location.latitude,
        location.longitude,
      );

      if (mounted) {
        setState(() {
          _selectedAddress = address ?? 'Unknown location';
          _isLoadingAddress = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _selectedAddress = 'Unable to get address';
          _isLoadingAddress = false;
        });
      }
    }
  }

  /// Calculate delivery info (distance and time)
  Future<void> _calculateDeliveryInfo() async {
    try {
      // Simulate delivery calculation - in real app, this would use actual seller locations
      setState(() {
        _distanceInfo = '5.9 km away from your current location';
        _deliveryTimeInfo = '25-30 min';
      });
    } catch (e) {
      print('❌ ADDRESS_FORM - Error calculating delivery info: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildSaveButton(),
    );
  }

  /// Build app bar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        widget.existingAddress != null ? 'Edit Address' : 'Add Address',
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      backgroundColor: const Color(0xFF059669),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// Build main body
  Widget _buildBody() {
    return Column(
      children: [
        // Map section
        _buildMapSection(),
        
        // Form section
        Expanded(
          child: _buildFormSection(),
        ),
      ],
    );
  }

  /// Build map section
  Widget _buildMapSection() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Google Map
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: _selectedLocation,
              zoom: 16.0,
            ),
            onMapCreated: (GoogleMapController controller) {
              _mapController = controller;
            },
            onTap: _onMapTap,
            markers: {
              Marker(
                markerId: const MarkerId('delivery_location'),
                position: _selectedLocation,
                draggable: true,
                onDragEnd: (LatLng location) {
                  setState(() {
                    _selectedLocation = location;
                  });
                  _loadAddressForLocation(location);
                  _calculateDeliveryInfo();
                },
                icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueGreen,
                ),
              ),
            },
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
          ),
          
          // Instruction overlay
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Text(
                'Move pin to your exact delivery location',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          
          // Current location button
          Positioned(
            bottom: 16,
            right: 16,
            child: FloatingActionButton(
              mini: true,
              backgroundColor: const Color(0xFF059669),
              onPressed: _moveToCurrentLocation,
              child: const Icon(Icons.my_location, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  /// Build form section
  Widget _buildFormSection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Address display
            _buildAddressDisplay(),
            const SizedBox(height: 20),
            
            // Delivery details
            if (_distanceInfo != null) _buildDeliveryDetails(),
            const SizedBox(height: 20),
            
            // Additional address details
            _buildSectionTitle('Additional address details*'),
            const SizedBox(height: 12),
            _buildTextField(
              controller: _houseController,
              label: 'House/Flat/Floor No.',
              hint: 'E.g. Floor, House no.',
              isRequired: true,
            ),
            const SizedBox(height: 16),
            
            // Receiver details
            _buildSectionTitle('Receiver details for this address'),
            const SizedBox(height: 12),
            _buildTextField(
              controller: _receiverNameController,
              label: 'Receiver Name',
              hint: 'Enter receiver name',
              isRequired: true,
            ),
            const SizedBox(height: 12),
            _buildTextField(
              controller: _receiverPhoneController,
              label: 'Phone Number',
              hint: 'Enter phone number',
              keyboardType: TextInputType.phone,
              isRequired: true,
            ),
            const SizedBox(height: 20),
            
            // Address type selection
            _buildSectionTitle('Save address as'),
            const SizedBox(height: 12),
            _buildAddressTypeSelector(),
            
            const SizedBox(height: 100), // Space for save button
          ],
        ),
      ),
    );
  }

  /// Build address display
  Widget _buildAddressDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.red.shade600, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _selectedAddress.isNotEmpty ? _selectedAddress : 'Loading address...',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (_isLoadingAddress)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF059669)),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build delivery details
  Widget _buildDeliveryDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Column(
        children: [
          Text(
            _distanceInfo!,
            style: TextStyle(
              fontSize: 14,
              color: Colors.orange.shade800,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Use current location',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.orange.shade700,
                  decoration: TextDecoration.underline,
                ),
              ),
              Icon(
                Icons.chevron_right,
                size: 16,
                color: Colors.orange.shade700,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build section title
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
      ),
    );
  }

  /// Build text field
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    bool isRequired = false,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF059669), width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      validator: isRequired
          ? (value) {
              if (value == null || value.trim().isEmpty) {
                return '$label is required';
              }
              return null;
            }
          : null,
    );
  }

  /// Build address type selector
  Widget _buildAddressTypeSelector() {
    final types = [
      {'type': 'Home', 'icon': Icons.home, 'color': Colors.red.shade600},
      {'type': 'Work', 'icon': Icons.work, 'color': Colors.blue.shade600},
      {'type': 'Other', 'icon': Icons.location_on, 'color': Colors.grey.shade600},
    ];

    return Row(
      children: types.map((typeData) {
        final type = typeData['type'] as String;
        final icon = typeData['icon'] as IconData;
        final color = typeData['color'] as Color;
        final isSelected = _selectedAddressType == type;

        return Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedAddressType = type;
              });
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? color : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? color : Colors.grey.shade300,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    icon,
                    color: isSelected ? Colors.white : color,
                    size: 24,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    type,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.black87,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Build save button
  Widget _buildSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        height: 50,
        child: ElevatedButton(
          onPressed: _isSaving ? null : _saveAddress,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF059669),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          child: _isSaving
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  widget.existingAddress != null ? 'Update address' : 'Save address',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }

  /// Handle map tap
  void _onMapTap(LatLng location) {
    setState(() {
      _selectedLocation = location;
    });
    _loadAddressForLocation(location);
    _calculateDeliveryInfo();
  }

  /// Move to current location
  Future<void> _moveToCurrentLocation() async {
    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null && mounted) {
        final newLocation = LatLng(position.latitude, position.longitude);
        setState(() {
          _selectedLocation = newLocation;
        });
        
        _mapController?.animateCamera(
          CameraUpdate.newLatLng(newLocation),
        );
        
        _loadAddressForLocation(newLocation);
        _calculateDeliveryInfo();
      }
    } catch (e) {
      print('❌ ADDRESS_FORM - Error getting current location: $e');
    }
  }

  /// Save address
  Future<void> _saveAddress() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final addressData = {
        'address': _selectedAddress,
        'house_details': _houseController.text.trim(),
        'area': _areaController.text.trim(),
        'landmark': _landmarkController.text.trim(),
        'receiver_name': _receiverNameController.text.trim(),
        'receiver_phone': _receiverPhoneController.text.trim(),
        'type': _selectedAddressType,
        'latitude': _selectedLocation.latitude,
        'longitude': _selectedLocation.longitude,
      };

      // Return the address data to the previous screen
      Navigator.pop(context, addressData);
    } catch (e) {
      print('❌ ADDRESS_FORM - Error saving address: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save address: $e'),
          backgroundColor: Colors.red.shade600,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _houseController.dispose();
    _areaController.dispose();
    _landmarkController.dispose();
    _receiverNameController.dispose();
    _receiverPhoneController.dispose();
    super.dispose();
  }
}
