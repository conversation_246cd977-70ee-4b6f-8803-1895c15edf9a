<6>[    0.000000,0] Booting Linux on physical CPU 0x0
<6>[    0.000000,0] Initializing cgroup subsys cpu
<6>[    0.000000,0] Initializing cgroup subsys cpuacct
<5>[    0.000000,0] Linux version 3.18.71-perf-gfde333e (hudsoncm@ilclbld57) (gcc version 4.9.x 20150123 (prerelease) (GCC) ) #1 SMP PREEMPT Tue Aug 13 15:23:08 CDT 2019
<6>[    0.000000,0] CPU: ARMv7 Processor [410fd034] revision 4 (ARMv7), cr=10c0383d
<6>[    0.000000,0] CPU: PIPT / VIPT nonaliasing data cache, VIPT aliasing instruction cache
<6>[    0.000000,0] Machine model: sanders
<6>[    0.000000,0] Reserved memory: reserved region for node 'other_ext_region@0': base 0x84300000, size 37 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'modem_region@0': base 0x86c00000, size 106 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'adsp_fw_region@0': base 0x8d600000, size 17 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'wcnss_fw_region@0': base 0x8e700000, size 7 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'dfps_data_mem@90000000': base 0x90000000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'splash_region@0x90001000': base 0x90001000, size 19 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'ramoops_mem_region': base 0xef000000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'tzlog_bck_region': base 0xeefe4000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'wdog_cpuctx_region': base 0xeefe6000, size 0 MiB
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x84300000, size 37 MiB
<6>[    0.000000,0] Reserved memory: initialized node other_ext_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x86c00000, size 106 MiB
<6>[    0.000000,0] Reserved memory: initialized node modem_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x8d600000, size 17 MiB
<6>[    0.000000,0] Reserved memory: initialized node adsp_fw_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x8e700000, size 7 MiB
<6>[    0.000000,0] Reserved memory: initialized node wcnss_fw_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'venus_region@0' node: base 0x8f800000, size 8 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0x8f800000, size 8 MiB
<6>[    0.000000,0] Reserved memory: initialized node venus_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'secure_region@0' node: base 0xf6400000, size 152 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf6400000, size 152 MiB
<6>[    0.000000,0] Reserved memory: initialized node secure_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'qseecom_region@0' node: base 0xf5400000, size 16 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf5400000, size 16 MiB
<6>[    0.000000,0] Reserved memory: initialized node qseecom_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'adsp_region@0' node: base 0xf5000000, size 4 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf5000000, size 4 MiB
<6>[    0.000000,0] Reserved memory: initialized node adsp_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'gpu_region@0' node: base 0x8f000000, size 8 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0x8f000000, size 8 MiB
<6>[    0.000000,0] Reserved memory: initialized node gpu_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] cma: Reserved 16 MiB at 0xf4000000
<6>[    0.000000,0] Memory policy: Data cache writealloc
<7>[    0.000000,0] On node 0 totalpages: 940131
<7>[    0.000000,0] free_area_init_node: node 0, pgdat c15fef80, node_mem_map e73f9000
<7>[    0.000000,0]   Normal zone: 1316 pages used for memmap
<7>[    0.000000,0]   Normal zone: 0 pages reserved
<7>[    0.000000,0]   Normal zone: 168448 pages, LIFO batch:31
<7>[    0.000000,0]   HighMem zone: 6364 pages used for memmap
<7>[    0.000000,0]   HighMem zone: 771683 pages, LIFO batch:31
<6>[    0.000000,0] psci: probing for conduit method from DT.
<6>[    0.000000,0] psci: PSCIv1.0 detected in firmware.
<6>[    0.000000,0] psci: Using standard PSCI v0.2 function IDs
<4>[    0.000000,0] PERCPU: max_distance=0xb000 too large for vmalloc space 0x0
<6>[    0.000000,0] PERCPU: Embedded 11 pages/cpu @e72ee000 s14912 r8192 d21952 u45056
<7>[    0.000000,0] pcpu-alloc: s14912 r8192 d21952 u45056 alloc=11*4096
<7>[    0.000000,0] pcpu-alloc: [0] 0 [0] 1 [0] 2 [0] 3 [0] 4 [0] 5 [0] 6 [0] 7 
<4>[    0.000000,0] Built 1 zonelists in Zone order, mobility grouping on.  Total pages: 938815
<5>[    0.000000,0] Kernel command line: sched_enable_hmp=1 sched_enable_power_aware=1 console=null androidboot.hardware=qcom user_debug=30 msm_rtb.filter=0x237 ehci-hcd.park=3 androidboot.bootdevice=7824900.sdhci lpm_levels.sleep_disabled=1 vmalloc=350M buildvariant=user androidboot.emmc=true androidboot.serialno=ZY32286WPB androidboot.baseband=msm androidboot.mode=normal androidboot.device=sanders androidboot.hwrev=0x8400 androidboot.radio=INDIA androidboot.powerup_reason=0x00004000 androidboot.bootreason=reboot msm_poweroff.download_mode=0 androidboot.fsg-id= androidboot.wifimacaddr=A8:96:75:05:41:09,A8:96:75:05:41:0A androidboot.btmacaddr=A8:96:75:05:41:08 mdss_mdp.panel=1:dsi:0:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0 androidboot.bootloader=0xC212 androidboot.carrier=retin androidboot.poweroff_alarm=0 androidboot.hardware.sku=XT1804 androidboot.secure_hardware=1 androidboot.bl_state=1 androidboot.cid=0x32 androidboot.uid=C035992300000000000000000000 androidboot.write_protect=1 androidboot.ve<6>[    0.000000,0] PID hash table entries: 4096 (order: 2, 16384 bytes)
<6>[    0.000000,0] Dentry cache hash table entries: 131072 (order: 7, 524288 bytes)
<6>[    0.000000,0] Inode-cache hash table entries: 65536 (order: 6, 262144 bytes)
<4>[    0.000000,0] Memory: 3469276K/3760524K available (13312K kernel code, 1076K rwdata, 5704K rodata, 506K init, 1902K bss, 82352K reserved, 208896K cma-reserved, 2857356K highmem)
<5>[    0.000000,0] Virtual kernel memory layout:
<5>[    0.000000,0]     vector  : 0xffff0000 - 0xffff1000   (   4 kB)
<5>[    0.000000,0]     fixmap  : 0xffc00000 - 0xfff00000   (3072 kB)
<5>[    0.000000,0] 	   vmalloc : 0xe9200000 - 0xff000000   ( 350 MB)
<5>[    0.000000,0] 	   lowmem  : 0xc0000000 - 0xe9200000   ( 658 MB)
<5>[    0.000000,0]     pkmap   : 0xbfe00000 - 0xc0000000   (   2 MB)
<5>[    0.000000,0]     modules : 0xbf000000 - 0xbfe00000   (  14 MB)
<5>[    0.000000,0]       .text : 0xc0008000 - 0xc0e00000   (14304 kB)
<5>[    0.000000,0]       .init : 0xc1400000 - 0xc147ea40   ( 507 kB)
<5>[    0.000000,0]       .data : 0xc1500000 - 0xc160d324   (1077 kB)
<5>[    0.000000,0]        .bss : 0xc160d324 - 0xc17e8c68   (1903 kB)
<6>[    0.000000,0] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=8, Nodes=1
<6>[    0.000000,0] HMP scheduling enabled.
<6>[    0.000000,0] Preemptible hierarchical RCU implementation.
<6>[    0.000000,0] 	RCU dyntick-idle grace-period acceleration is enabled.
<4>[    0.000000,0] 
<4>[    0.000000,0] **********************************************************
<4>[    0.000000,0] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** trace_printk() being used. Allocating extra memory.  **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** This means that this is a DEBUG kernel and it is     **
<4>[    0.000000,0] ** unsafe for produciton use.                           **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** If you see this message and you are not debugging    **
<4>[    0.000000,0] ** the kernel, report this immediately to your vendor!  **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
<4>[    0.000000,0] **********************************************************
<6>[    0.000000,0] NR_IRQS:16 nr_irqs:16 16
<4>[    0.000000,0] mpm_init_irq_domain(): Cannot find irq controller for qcom,gpio-parent
<3>[    0.000000,0] MPM 1 irq mapping errored -517
<6>[    0.000000,0] 	Offload RCU callbacks from all CPUs
<6>[    0.000000,0] 	Offload RCU callbacks from CPUs: 0-7.
<6>[    0.000000,0] Architected cp15 and mmio timer(s) running at 19.20MHz (virt/virt).
<6>[    0.000006,0] sched_clock: 56 bits at 19MHz, resolution 52ns, wraps every 3579139424256ns
<6>[    0.000020,0] Switching to timer-based delay loop, resolution 52ns
<6>[    0.000035,0] Switched to clocksource arch_sys_counter
<6>[    0.000922,0] Calibrating delay loop (skipped), value calculated using timer frequency.. 38.00 BogoMIPS (lpj=64000)
<6>[    0.000938,0] pid_max: default: 32768 minimum: 301
<6>[    0.001023,0] Security Framework initialized
<6>[    0.001036,0] SELinux:  Initializing.
<7>[    0.001073,0] SELinux:  Starting in permissive mode
<6>[    0.001116,0] Mount-cache hash table entries: 2048 (order: 1, 8192 bytes)
<6>[    0.001128,0] Mountpoint-cache hash table entries: 2048 (order: 1, 8192 bytes)
<6>[    0.001855,0] Initializing cgroup subsys freezer
<6>[    0.001902,0] CPU: Testing write buffer coherency: ok
<3>[    0.002463,0] /cpus/cpu@0 missing clock-frequency property
<3>[    0.002478,0] /cpus/cpu@1 missing clock-frequency property
<3>[    0.002493,0] /cpus/cpu@2 missing clock-frequency property
<3>[    0.002510,0] /cpus/cpu@3 missing clock-frequency property
<3>[    0.002528,0] /cpus/cpu@100 missing clock-frequency property
<3>[    0.002549,0] /cpus/cpu@101 missing clock-frequency property
<3>[    0.002571,0] /cpus/cpu@102 missing clock-frequency property
<3>[    0.002594,0] /cpus/cpu@103 missing clock-frequency property
<6>[    0.002662,0] Setting up static identity map for 0x10d2ea88 - 0x10d2eae0
<4>[    0.002985,0] NOHZ: local_softirq_pending 02
<4>[    0.003388,0] NOHZ: local_softirq_pending 02
<6>[    0.011075,0] MSM Memory Dump base table set up
<6>[    0.011107,0] MSM Memory Dump apps data table set up
<6>[    0.011170,0] Configuring XPU violations to be fatal errors
<6>[    0.012411,0] cpu_clock_pwr_init: Power clocks configured
<4>[    0.017447,1] CPU1: Booted secondary processor
<4>[    0.022329,2] CPU2: Booted secondary processor
<4>[    0.027202,3] CPU3: Booted secondary processor
<4>[    0.032139,4] CPU4: Booted secondary processor
<4>[    0.037058,5] CPU5: Booted secondary processor
<4>[    0.041923,6] CPU6: Booted secondary processor
<4>[    0.046903,7] CPU7: Booted secondary processor
<6>[    0.047120,0] Brought up 8 CPUs
<6>[    0.047163,0] SMP: Total of 8 processors activated (307.00 BogoMIPS).
<6>[    0.047172,0] CPU: All CPU(s) started in SVC mode.
<6>[    0.056603,2] VFP support v0.3: implementor 41 architecture 3 part 40 variant 3 rev 4
<6>[    0.065978,2] pinctrl core: initialized pinctrl subsystem
<6>[    0.066430,2] regulator-dummy: no parameters
<6>[    0.143161,2] NET: Registered protocol family 16
<6>[    0.149370,2] DMA: preallocated 256 KiB pool for atomic coherent allocations
<4>[    0.150207,2] msm_pm_tz_boot_init: set warmboot address failed
<3>[    0.150232,2] scm_call failed: func id 0x2000101, ret: -1, syscall returns: 0x0, 0x0, 0x0
<6>[    0.163479,2] cpuidle: using governor ladder
<6>[    0.176816,2] cpuidle: using governor menu
<6>[    0.190140,2] cpuidle: using governor qcom
<6>[    0.196764,2] platform soc:qcom,kgsl-hyp: assigned reserved memory node gpu_region@0
<6>[    0.222148,2] msm_watchdog b017000.qcom,wdt: wdog absent resource not present
<6>[    0.222596,2] msm_watchdog b017000.qcom,wdt: MSM Watchdog Initialized
<6>[    0.227862,2] platform soc:qcom,adsprpc-mem: assigned reserved memory node adsp_region@0
<4>[    0.230093,2] irq: no irq domain found for /soc/pinctrl@1000000 !
<3>[    0.230634,2] spmi_pmic_arb 200f000.qcom,spmi: PMIC Arb Version-2 0x20010000
<3>[    0.231385,2] spmi_pmic_arb 200f000.qcom,spmi: non-zero irq-accumulator[0]:0x20000000
<3>[    0.238891,2] spmi spmi-0: of_spmi_register_devices: invalid sid on /soc/qcom,spmi@200f000/qcom,pm8950@0
<6>[    0.239363,2] platform 4080000.qcom,mss: assigned reserved memory node modem_region@0
<6>[    0.239789,2] platform c200000.qcom,lpass: assigned reserved memory node adsp_fw_region@0
<6>[    0.240022,2] platform 1de0000.qcom,venus: assigned reserved memory node venus_region@0
<6>[    0.240603,2] platform a21b000.qcom,pronto: assigned reserved memory node wcnss_fw_region@0
<6>[    0.242300,2] apc_mem_acc_corner: 0 <--> 0 mV 
<6>[    0.243130,2] gfx_mem_acc_corner: 0 <--> 0 mV 
<6>[    0.255662,2] persistent_ram: persistent_ram: paddr: ef000000, vaddr: e9280000, buf size = 0x1fff4
<6>[    0.255687,2] persistent_ram: persistent_ram: paddr: ef020000, vaddr: e9300000, buf size = 0x3fff4
<6>[    0.257670,2] persistent_ram: persistent_ram: paddr: ef060000, vaddr: e9262000, buf size = 0x7f4
<6>[    0.258687,2] console [pstore-1] enabled
<6>[    0.258698,2] pstore: Registered ramoops as persistent store backend
<6>[    0.258711,2] ramoops: attached 0x80000@0xef000000, ecc: 0/0
<6>[    0.260224,2] hw-breakpoint: found 5 (+1 reserved) breakpoint and 4 watchpoint registers.
<6>[    0.260237,2] hw-breakpoint: maximum watchpoint size is 8 bytes.
<4>[    0.262254,2] __of_mpm_init(): MPM driver mapping exists
<4>[    0.263571,2] msm_rpm_glink_dt_parse: qcom,rpm-glink compatible not matches
<6>[    0.263585,2] msm_rpm_dev_probe: APSS-RPM communication over SMD
<6>[    0.263598,2] smd_open() before smd_init()
<3>[    0.265354,2] msm_mpm_dev_probe(): Cannot get clk resource for XO: -517
<3>[    0.271054,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.271230,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.271388,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.277527,2] GFX_LDO: msm_gfx_ldo_parse_dt: Unable to parse CX parameters rc=-517
<3>[    0.277548,2] GFX_LDO: msm_gfx_ldo_probe: Unable to pasrse dt rc=-517
<6>[    0.279077,2] pm8953_s5: 400 <--> 1140 mV at 870 mV normal idle 
<6>[    0.279404,2] pm8953_s5_avs_limit: 400 <--> 1140 mV 
<6>[    0.279572,2] spm_regulator_probe: name=pm8953_s5, range=LV, voltage=870000 uV, mode=AUTO, step rate=1200 uV/us
<6>[    0.287762,2] msm_thermal:vdd_restriction_reg_init Defer regulator vdd-dig probe
<3>[    0.287784,2] msm_thermal:probe_vdd_rstr Err regulator init. err:-517. KTM continues.
<6>[    0.287804,2] msm-thermal soc:qcom,msm-thermal: probe_vdd_rstr:Failed reading node=/soc/qcom,msm-thermal, key=qcom,max-freq-level. err=-517. KTM continues
<3>[    0.287820,2] msm_thermal:msm_thermal_dev_probe Failed reading node=/soc/qcom,msm-thermal, key=qcom,online-hotplug-core. err:-517
<6>[    0.289248,2] sps:sps is ready.
<6>[    0.292859,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Speed bin: 2 PVS Version: 0
<3>[    0.293088,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Get vdd-mx regulator!!!
<4>[    0.293744,3] msm_rpm_glink_dt_parse: qcom,rpm-glink compatible not matches
<6>[    0.293759,3] msm_rpm_dev_probe: APSS-RPM communication over SMD
<6>[    0.294607,3] pm8953_s1: 870 <--> 1156 mV at 1000 mV normal idle 
<6>[    0.295419,3] pm8953_s2_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.295959,3] pm8953_s2_floor_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.296452,3] pm8953_s2_level_ao: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.297187,3] pm8953_s3: 1225 mV normal idle 
<6>[    0.297886,3] pm8953_s4: 1900 <--> 2050 mV at 1900 mV normal idle 
<6>[    0.298574,3] pm8953_s7_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.299095,3] pm8953_s7_level_ao: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.299622,3] pm8953_s7_level_so: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.300377,3] pm8953_l1: 1000 <--> 1100 mV at 1000 mV normal idle 
<6>[    0.301081,3] pm8953_l2: 1200 mV normal idle 
<6>[    0.301774,3] pm8953_l3: 925 mV normal idle 
<6>[    0.302477,3] pm8953_l5: 1800 mV normal idle 
<6>[    0.303516,3] pm8953_l6: 1800 mV normal idle 
<6>[    0.304220,3] pm8953_l7: 1800 <--> 1900 mV at 1800 mV normal idle 
<6>[    0.304757,3] pm8953_l7_ao: 1800 <--> 1900 mV at 1800 mV normal idle 
<6>[    0.305477,3] pm8953_l8: 2900 mV normal idle 
<6>[    0.306195,3] pm8953_l9: 3000 <--> 3300 mV at 3000 mV normal idle 
<6>[    0.307671,3] pm8953_l10: 2850 mV normal idle 
<6>[    0.308398,3] pm8953_l11: 2950 mV normal idle 
<6>[    0.309125,3] pm8953_l12: 1800 <--> 2950 mV at 1800 mV normal idle 
<6>[    0.309858,3] pm8953_l13: 3125 mV normal idle 
<6>[    0.310621,3] pm8953_l16: 1800 mV normal idle 
<6>[    0.311312,3] pm8953_l17: 2800 mV normal idle 
<6>[    0.312004,3] pm8953_l19: 1200 <--> 1350 mV at 1200 mV normal idle 
<6>[    0.312682,3] pm8953_l22: 2800 mV normal idle 
<6>[    0.313402,3] pm8953_l23: 1200 mV normal idle 
<3>[    0.313886,3] msm_mpm_dev_probe(): Cannot get clk resource for XO: -517
<6>[    0.314230,3] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 1: target-volt = 580000 uV
<6>[    0.314245,3] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 2: target-volt = 650000 uV
<6>[    0.314258,3] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 3: target-volt = 720000 uV
<6>[    0.314276,3] GFX_LDO: msm_gfx_ldo_adjust_init_voltage: adjusted the open-loop voltage[1] 580000 -> 615000
<6>[    0.314289,3] GFX_LDO: msm_gfx_ldo_adjust_init_voltage: adjusted the open-loop voltage[2] 650000 -> 675000
<6>[    0.314304,3] GFX_LDO: msm_gfx_ldo_voltage_init: LDO-mode fuse disabled by default
<6>[    0.314611,3] msm_gfx_ldo: 0 <--> 0 mV at 0 mV 
<6>[    0.315425,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: speed bin = 2
<6>[    0.315440,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: CPR fusing revision = 3
<6>[    0.315453,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: foundry id = 2
<6>[    0.315466,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: CPR misc fuse value = 0
<6>[    0.315506,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: Voltage boost fuse config = 0 boost = disable
<6>[    0.315648,3] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused   LowSVS: open-loop= 625000 uV
<6>[    0.315662,3] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused      SVS: open-loop= 700000 uV
<6>[    0.315674,3] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused      NOM: open-loop= 815000 uV
<6>[    0.315686,3] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused TURBO_L1: open-loop= 915000 uV
<6>[    0.315767,3] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused   LowSVS: quot[ 7]= 442
<6>[    0.315782,3] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused      SVS: quot[ 7]= 567, quot_offset[ 7]= 120
<6>[    0.315796,3] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused      NOM: quot[ 7]= 791, quot_offset[ 7]= 220
<6>[    0.315810,3] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused TURBO_L1: quot[ 7]= 978, quot_offset[ 7]= 185
<6>[    0.316188,3] cpr4_apss_init_aging: apc: sensor 6 aging init quotient diff = 12, aging RO scale = 2800 QUOT/V
<6>[    0.316369,3] cpr3_regulator_init_ctrl: apc: Default CPR mode = HW closed-loop
<6>[    0.316519,3] apc_corner: 0 <--> 0 mV at 0 mV 
<6>[    0.318121,3] msm_thermal:sensor_mgr_init_threshold threshold id already initialized
<6>[    0.318815,3] msm_thermal:vdd_restriction_reg_init Defer vdd rstr freq init.
<6>[    0.321936,3] qcom,gcc-8953 1800000.qcom,gcc: Venus speed bin: 2
<4>[    0.343581,3] branch_clk_handoff: gcc_usb_phy_cfg_ahb_clk clock is enabled in HW
<4>[    0.343600,3] branch_clk_handoff: even though ENABLE_BIT is not set
<6>[    0.345575,3] qcom,gcc-8953 1800000.qcom,gcc: Registered GCC clocks
<6>[    0.345778,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Speed bin: 2 PVS Version: 0
<3>[    0.348323,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,dfs-sid-c0
<3>[    0.348341,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No DFS SID tables found for Cluster-0
<3>[    0.348357,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,link-sid-c0
<3>[    0.348372,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No Link SID tables found for Cluster-0
<3>[    0.348388,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,lmh-sid-c0
<3>[    0.348403,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No LMH SID tables found for Cluster-0
<3>[    0.348413,3] ramp_lmh_sid: Use Default LMH SID
<3>[    0.348423,3] ramp_dfs_sid: Use Default DFS SID
<3>[    0.348432,3] ramp_link_sid: Use Default Link SID
<3>[    0.348486,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,dfs-sid-c1
<3>[    0.348500,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No DFS SID tables found for Cluster-1
<3>[    0.348516,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,link-sid-c1
<3>[    0.348530,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No Link SID tables found for Cluster-1
<3>[    0.348545,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,lmh-sid-c1
<3>[    0.348559,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No LMH SID tables found for Cluster-1
<3>[    0.348569,3] ramp_lmh_sid: Use Default LMH SID
<3>[    0.348579,3] ramp_dfs_sid: Use Default DFS SID
<3>[    0.348588,3] ramp_link_sid: Use Default Link SID
<6>[    0.348650,3] clock_rcgwr_init: RCGwR  Init Completed
<6>[    0.349075,3] populate_opp_table: clock-cpu-8953: OPP tables populated (cpu 3 and 7)
<6>[    0.349088,3] print_opp_table: clock_cpu: a53 C0: OPP voltage for 652800000: 1
<6>[    0.349099,3] print_opp_table: clock_cpu: a53 C0: OPP voltage for 2016000000: 7
<6>[    0.349109,3] print_opp_table: clock_cpu: a53 C1: OPP voltage for 652800000: 1
<6>[    0.349120,3] print_opp_table: clock_cpu: a53 C2: OPP voltage for 2016000000: 7
<6>[    0.351189,2] gcc-gfx-8953 1800000.qcom,gcc-gfx: Registered GCC GFX clocks.
<3>[    0.409978,2] msm_cpuss_dump soc:cpuss_dump: Couldn't get memory for dumping
<3>[    0.410006,2] msm_cpuss_dump soc:cpuss_dump: Couldn't get memory for dumping
<6>[    0.413753,2] KPI: Bootloader start count = 71994
<6>[    0.413772,2] KPI: Bootloader end count = 105803
<6>[    0.413782,2] KPI: Bootloader display count = 3152786707
<6>[    0.413791,2] KPI: Bootloader load kernel count = 6376
<6>[    0.413802,2] KPI: Kernel MPM timestamp = 169266
<6>[    0.413811,2] KPI: Kernel MPM Clock frequency = 32768
<6>[    0.413838,2] socinfo_print: v0.10, id=293, ver=1.1, raw_id=70, raw_ver=1, hw_plat=8, hw_plat_ver=65536
<6>[    0.413838,2]  accessory_chip=0, hw_plat_subtype=0, pmic_model=65558, pmic_die_revision=65536 foundry_id=3 serial_number=597243328
<6>[    0.415075,2] dummy_vreg: no parameters
<6>[    0.415397,2] vci_fci: no parameters
<5>[    0.416806,2] SCSI subsystem initialized
<6>[    0.417685,2] usbcore: registered new interface driver usbfs
<6>[    0.417760,2] usbcore: registered new interface driver hub
<6>[    0.417995,2] usbcore: registered new device driver usb
<6>[    0.419109,2] i2c-msm-v2 78b6000.i2c: probing driver i2c-msm-v2
<3>[    0.419443,2] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.419458,2] i2c-msm-v2 78b6000.i2c: msm_bus_scale_register_client(mstr-id:86):0 (not a problem)
<6>[    0.420939,2] i2c-msm-v2 78b7000.i2c: probing driver i2c-msm-v2
<3>[    0.421178,2] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.421193,2] i2c-msm-v2 78b7000.i2c: msm_bus_scale_register_client(mstr-id:86):0 (not a problem)
<6>[    0.421419,0] i2c-msm-v2 78b7000.i2c: irq:50 when no active transfer
<6>[    0.422096,2] i2c-msm-v2 7af5000.i2c: probing driver i2c-msm-v2
<3>[    0.422299,2] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.422312,2] i2c-msm-v2 7af5000.i2c: msm_bus_scale_register_client(mstr-id:84):0 (not a problem)
<6>[    0.423792,2] i2c-msm-v2 7af7000.i2c: probing driver i2c-msm-v2
<3>[    0.424015,2] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.424030,2] i2c-msm-v2 7af7000.i2c: msm_bus_scale_register_client(mstr-id:84):0 (not a problem)
<6>[    0.425545,0] media: Linux media interface: v0.10
<6>[    0.425620,0] Linux video capture interface: v2.00
<6>[    0.425720,0] EDAC MC: Ver: 3.0.0
<6>[    0.500315,3] cpufreq: driver msm up and running
<6>[    0.500750,3] platform soc:qcom,ion:qcom,ion-heap@8: assigned reserved memory node secure_region@0
<6>[    0.500918,3] platform soc:qcom,ion:qcom,ion-heap@27: assigned reserved memory node qseecom_region@0
<6>[    0.501119,3] ION heap system created
<6>[    0.501229,3] ION heap mm created at 0xf6400000 with size 9800000
<6>[    0.501239,3] ION heap qsecom created at 0xf5400000 with size 1000000
<3>[    0.501818,3] msm_bus_fabric_init_driver
<6>[    0.511144,3] qcom,qpnp-power-on qpnp-power-on-1: PMIC@SID0 Power-on reason: Triggered from Hard Reset and 'warm' boot
<6>[    0.511168,3] qcom,qpnp-power-on qpnp-power-on-1: PMIC@SID0: Power-off reason: Triggered from PS_HOLD (PS_HOLD/MSM controlled shutdown)
<6>[    0.511324,3] input: qpnp_pon as /devices/virtual/input/input0
<6>[    0.511666,3] pon_spare_reg: no parameters
<6>[    0.511727,3] qcom,qpnp-power-on qpnp-power-on-13: No PON config. specified
<6>[    0.511777,3] qcom,qpnp-power-on qpnp-power-on-13: PMIC@SID2 Power-on reason: Triggered from PON1 (secondary PMIC) and 'warm' boot
<6>[    0.511794,3] qcom,qpnp-power-on qpnp-power-on-13: PMIC@SID2: Power-off reason: Triggered from PS_HOLD (PS_HOLD/MSM controlled shutdown)
<6>[    0.511963,3] PMIC@SID0: (null) v1.0 options: 2, 2, 0, 0
<6>[    0.512055,3] PMIC@SID2: PMI8950 v2.0 options: 0, 0, 0, 0
<3>[    0.512924,3] ipa ipa2_uc_state_check:296 uC interface not initialized
<3>[    0.512939,3] ipa ipa_sps_irq_control_all:942 EP (2) not allocated.
<3>[    0.512946,3] ipa ipa_sps_irq_control_all:942 EP (5) not allocated.
<6>[    0.514312,5] sps:BAM 0x07904000 is registered.
<6>[    0.514788,5] sps:BAM 0x07904000 (va:0xe97c0000) enabled: ver:0x27, number of pipes:20
<6>[    0.517613,5] IPA driver initialization was successful.
<6>[    0.518681,5] gdsc_venus: no parameters
<6>[    0.518905,5] gdsc_mdss: no parameters
<6>[    0.519203,5] gdsc_jpeg: no parameters
<6>[    0.519554,5] gdsc_vfe: no parameters
<6>[    0.519904,5] gdsc_vfe1: no parameters
<6>[    0.520123,5] gdsc_cpp: no parameters
<6>[    0.520274,5] gdsc_oxili_gx: no parameters
<6>[    0.520322,5] gdsc_oxili_gx: supplied by msm_gfx_ldo
<6>[    0.520490,5] gdsc_venus_core0: fast normal 
<6>[    0.520649,5] gdsc_oxili_cx: no parameters
<6>[    0.520776,5] gdsc_usb30: no parameters
<6>[    0.521740,5] mdss_pll_probe: MDSS pll label = MDSS DSI 0 PLL
<6>[    0.521747,5] mdss_pll_probe: mdss_pll_probe: label=MDSS DSI 0 PLL PLL SSC enabled
<4>[    0.521764,5] mdss_pll_util_parse_dt_supply: : error reading ulp load. rc=-22
<6>[    0.522244,5] dsi_pll_clock_register_8996: Registered DSI PLL ndx=0 clocks successfully
<6>[    0.522264,5] mdss_pll_probe: MDSS pll label = MDSS DSI 1 PLL
<6>[    0.522270,5] mdss_pll_probe: mdss_pll_probe: label=MDSS DSI 1 PLL PLL SSC enabled
<4>[    0.522283,5] mdss_pll_util_parse_dt_supply: : error reading ulp load. rc=-22
<3>[    0.523382,5] pll_is_pll_locked_8996: DSI PLL ndx=1 status=0 failed to Lock
<6>[    0.523720,5] dsi_pll_clock_register_8996: Registered DSI PLL ndx=1 clocks successfully
<6>[    0.524155,5] msm_iommu 1e00000.qcom,iommu: device apps_iommu (model: 500) mapped at e9b80000, with 21 ctx banks
<6>[    0.528905,5] msm_iommu_ctx 1e20000.qcom,iommu-ctx: context adsp_elf using bank 0
<6>[    0.529025,5] msm_iommu_ctx 1e21000.qcom,iommu-ctx: context adsp_sec_pixel using bank 1
<6>[    0.529142,5] msm_iommu_ctx 1e22000.qcom,iommu-ctx: context mdp_1 using bank 2
<6>[    0.529260,5] msm_iommu_ctx 1e23000.qcom,iommu-ctx: context venus_fw using bank 3
<6>[    0.529378,5] msm_iommu_ctx 1e24000.qcom,iommu-ctx: context venus_sec_non_pixel using bank 4
<6>[    0.529500,5] msm_iommu_ctx 1e25000.qcom,iommu-ctx: context venus_sec_bitstream using bank 5
<6>[    0.529618,5] msm_iommu_ctx 1e26000.qcom,iommu-ctx: context venus_sec_pixel using bank 6
<6>[    0.529761,5] msm_iommu_ctx 1e28000.qcom,iommu-ctx: context pronto_pil using bank 8
<6>[    0.529904,5] msm_iommu_ctx 1e29000.qcom,iommu-ctx: context q6 using bank 9
<6>[    0.530056,5] msm_iommu_ctx 1e2a000.qcom,iommu-ctx: context periph_rpm using bank 10
<6>[    0.530201,5] msm_iommu_ctx 1e2b000.qcom,iommu-ctx: context lpass using bank 11
<6>[    0.530341,5] msm_iommu_ctx 1e2f000.qcom,iommu-ctx: context adsp_io using bank 15
<6>[    0.530482,5] msm_iommu_ctx 1e30000.qcom,iommu-ctx: context adsp_opendsp using bank 16
<6>[    0.530620,5] msm_iommu_ctx 1e31000.qcom,iommu-ctx: context adsp_shared using bank 17
<6>[    0.530759,5] msm_iommu_ctx 1e32000.qcom,iommu-ctx: context cpp using bank 18
<6>[    0.530898,5] msm_iommu_ctx 1e33000.qcom,iommu-ctx: context jpeg_enc0 using bank 19
<6>[    0.531049,5] msm_iommu_ctx 1e34000.qcom,iommu-ctx: context vfe using bank 20
<6>[    0.531188,5] msm_iommu_ctx 1e35000.qcom,iommu-ctx: context mdp_0 using bank 21
<6>[    0.531328,5] msm_iommu_ctx 1e36000.qcom,iommu-ctx: context venus_ns using bank 22
<6>[    0.531469,5] msm_iommu_ctx 1e38000.qcom,iommu-ctx: context ipa using bank 24
<6>[    0.531608,5] msm_iommu_ctx 1e37000.qcom,iommu-ctx: context access_control using bank 23
<6>[    0.533417,5] arm-smmu 1c40000.arm,smmu-kgsl: regulator defer delay 80
<6>[    0.535007,5] Advanced Linux Sound Architecture Driver Initialized.
<6>[    0.535654,5] Bluetooth: e6e05ed8
<6>[    0.535673,5] NET: Registered protocol family 31
<6>[    0.535678,5] Bluetooth: e6e05ed8
<6>[    0.535686,5] Bluetooth: e6e05ed0Bluetooth: e6e05ec0
<6>[    0.535715,5] Bluetooth: e6e05ec0<6>[    0.535947,5] cfg80211: Calling CRDA to update world regulatory domain
<6>[    0.535964,5] cfg80211: World regulatory domain updated:
<6>[    0.535969,5] cfg80211:  DFS Master region: unset
<6>[    0.535973,5] cfg80211:   (start_freq - end_freq @ bandwidth), (max_antenna_gain, max_eirp), (dfs_cac_time)
<6>[    0.535981,5] cfg80211:   (2402000 KHz - 2472000 KHz @ 40000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.535987,5] cfg80211:   (2457000 KHz - 2482000 KHz @ 40000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.535993,5] cfg80211:   (2474000 KHz - 2494000 KHz @ 20000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.535999,5] cfg80211:   (5170000 KHz - 5250000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.536005,5] cfg80211:   (5250000 KHz - 5330000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.536010,5] cfg80211:   (5490000 KHz - 5710000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.536016,5] cfg80211:   (5735000 KHz - 5835000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.536022,5] cfg80211:   (57240000 KHz - 63720000 KHz @ 2160000 KHz), (N/A, 0 mBm), (N/A)
<6>[    0.536348,1] ibb_reg: 4600 <--> 6000 mV at 5500 mV 
<6>[    0.536569,1] lab_reg: 4600 <--> 6000 mV at 5500 mV 
<6>[    0.538314,5] Switched to clocksource arch_sys_counter
<6>[    0.564426,5] NET: Registered protocol family 2
<6>[    0.564767,5] TCP established hash table entries: 8192 (order: 3, 32768 bytes)
<6>[    0.564804,5] TCP bind hash table entries: 8192 (order: 4, 65536 bytes)
<6>[    0.564863,5] TCP: Hash tables configured (established 8192 bind 8192)
<6>[    0.564887,5] TCP: reno registered
<6>[    0.564895,5] UDP hash table entries: 512 (order: 2, 16384 bytes)
<6>[    0.564913,5] UDP-Lite hash table entries: 512 (order: 2, 16384 bytes)
<6>[    0.565019,5] NET: Registered protocol family 1
<6>[    0.566056,5] gcc-mdss-8953 1800000.qcom,gcc-mdss: Registered GCC MDSS clocks.
<6>[    0.566508,5] Trying to unpack rootfs image as initramfs...
<6>[    0.698777,5] Freeing initrd memory: 6880K
<6>[    0.701084,5] hw perfevents: enabled with ARMv8 Cortex-A53 PMU driver, 7 counters available
<6>[    0.704139,5] futex hash table entries: 2048 (order: 5, 131072 bytes)
<6>[    0.704212,5] audit: initializing netlink subsys (disabled)
<5>[    0.704244,5] audit: type=2000 audit(0.703:1): initialized
<4>[    0.704537,5] vmscan: error setting kswapd cpu affinity mask
<5>[    0.707836,5] VFS: Disk quotas dquot_6.5.2
<4>[    0.707916,5] Dquot-cache hash table entries: 1024 (order 0, 4096 bytes)
<6>[    0.708724,5] exFAT: Version 1.2.9
<6>[    0.709144,5] Registering sdcardfs 0.1
<6>[    0.709249,5] fuse init (API version 7.23)
<7>[    0.709548,5] SELinux:  Registering netfilter hooks
<6>[    0.711128,5] bounce: pool size: 64 pages
<6>[    0.711210,5] Block layer SCSI generic (bsg) driver version 0.4 loaded (major 246)
<6>[    0.711220,5] io scheduler noop registered
<6>[    0.711227,5] io scheduler deadline registered
<6>[    0.711245,5] io scheduler cfq registered (default)
<3>[    0.714269,5] msm_dss_get_res_byname: 'vbif_nrt_phys' resource not found
<3>[    0.714278,5] mdss_mdp_probe+0x1a0/0x10d8->msm_dss_ioremap_byname: 'vbif_nrt_phys' msm_dss_get_res_byname failed
<3>[    0.714705,5] mdss_mdp_irq_clk_register: unable to get clk: lut_clk
<3>[    0.715199,5] No change in context(0==0), skip
<6>[    0.715902,5] mdss_mdp_pipe_addr_setup: type:0 ftchid:-1 xinid:0 num:0 rect:0 ndx:0x1 prio:0
<6>[    0.715920,5] mdss_mdp_pipe_addr_setup: type:1 ftchid:-1 xinid:1 num:3 rect:0 ndx:0x8 prio:1
<6>[    0.715926,5] mdss_mdp_pipe_addr_setup: type:1 ftchid:-1 xinid:5 num:4 rect:0 ndx:0x10 prio:2
<6>[    0.715942,5] mdss_mdp_pipe_addr_setup: type:2 ftchid:-1 xinid:2 num:6 rect:0 ndx:0x40 prio:3
<6>[    0.715957,5] mdss_mdp_pipe_addr_setup: type:3 ftchid:-1 xinid:7 num:10 rect:0 ndx:0x400 prio:0
<3>[    0.715969,5] mdss_mdp_parse_dt_handler: Error from prop qcom,mdss-pipe-sw-reset-off : u32 array read
<3>[    0.716069,5] mdss_mdp_parse_dt_handler: Error from prop qcom,mdss-ib-factor-overlap : u32 array read
<6>[    0.716288,5] xlog_status: enable:0, panic:1, dump:2
<6>[    0.716820,5] mdss_mdp_probe: mdss version = 0x10100000, bootloader display is on, num 1, intf_sel=0x00000100
<3>[    0.718247,5] mdss_smmu_util_parse_dt_clock: clocks are not defined
<6>[    0.718272,5] mdss_smmu_probe: iommu v2 domain[0] mapping and clk register successful!
<3>[    0.718290,5] mdss_smmu_util_parse_dt_clock: clocks are not defined
<6>[    0.718299,5] mdss_smmu_probe: iommu v2 domain[2] mapping and clk register successful!
<4>[    0.719285,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.719299,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.719311,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<6>[    0.719760,5] mdss_dsi_ctrl_probe: DSI Ctrl name = MDSS DSI CTRL->0
<6>[    0.720157,5] mdss_panel_parse_panel_config_dt: BL: panel=mipi_mot_vid_djn_1080p_550, manufacture_id(0xDA)= 0x1A controller_ver(0xDB)= 0xD5 controller_drv_ver(0XDC)= 0x45, full=0x000000000045D51A
<6>[    0.720166,5] mdss_dsi_find_panel_of_node: cmdline:0:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0 panel_name:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0
<6>[    0.720214,5] mdss_dsi_panel_init: Panel Name = mipi_mot_vid_djn_1080p_550
<6>[    0.720373,5] mdss_dsi_panel_timing_from_dt: found new timing "qcom,mdss_dsi_mot_djn_550_1080p_vid_v0" (e6e05788)
<3>[    0.720392,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-post-panel-on-command
<3>[    0.720401,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-timing-switch-command
<4>[    0.720407,5] mdss_dsi_panel_get_dsc_cfg_np: cannot find dsc config node:
<3>[    0.720520,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-idle-on-command
<3>[    0.720529,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-idle-off-command
<6>[    0.720558,5] mdss_dsi_parse_panel_features: ulps feature disabled
<6>[    0.720566,5] mdss_dsi_parse_panel_features: ulps during suspend feature disabled
<6>[    0.720573,5] mdss_dsi_parse_dms_config: dynamic switch feature enabled: 0
<3>[    0.720658,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-on
<3>[    0.720666,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-off
<6>[    0.720709,5] mdss_panel_parse_param_prop: HBM feature enabled with 2 dt cmds
<6>[    0.720714,5] mdss_panel_parse_param_prop: HBM type = 1
<6>[    0.720750,5] mdss_panel_parse_param_prop: CABC feature enabled with 3 dt cmds
<3>[    0.720759,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-on
<3>[    0.720767,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-off
<4>[    0.720786,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.720796,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.720807,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<3>[    0.720974,5] mdss_dsi_parse_gpio_params:4125, TE gpio not specified
<6>[    0.720980,5] mdss_dsi_parse_gpio_params: bklt_en gpio not specified
<3>[    0.721016,5] msm_dss_get_res_byname: 'dsi_phy_regulator' resource not found
<3>[    0.721025,5] mdss_dsi_retrieve_ctrl_resources+0x124/0x1b8->msm_dss_ioremap_byname: 'dsi_phy_regulator' msm_dss_get_res_byname failed
<6>[    0.721032,5] mdss_dsi_retrieve_ctrl_resources: ctrl_base=e9782000 ctrl_size=400 phy_base=e9790400 phy_size=580
<6>[    0.721104,5] dsi_panel_device_register: Continuous splash enabled
<6>[    0.721282,5] mdss_register_panel: adding framebuffer device 1a94000.qcom,mdss_dsi_ctrl0
<6>[    0.722637,5] mdss_dsi_ctrl_probe: Dsi Ctrl->0 initialized, DSI rev:0x10040002, PHY rev:0x2
<6>[    0.722755,5] mdss_dsi_status_init: DSI status check interval:8000
<6>[    0.723404,5] mdss_register_panel: adding framebuffer device soc:qcom,mdss_wb_panel
<6>[    0.723819,5] mdss_fb_probe: fb0: split_mode:0 left:0 right:0
<6>[    0.724235,5] mdss_fb_register: FrameBuffer[0] 1080x1920 registered successfully!
<6>[    0.724506,5] mdss_fb_probe: fb1: split_mode:0 left:0 right:0
<6>[    0.724582,5] mdss_fb_register: FrameBuffer[1] 640x640 registered successfully!
<3>[    0.724666,5] mdss_mdp_splash_parse_dt: splash mem child node is not present
<6>[    0.724687,5] anx7805 anx7805_init: anx7805_init
<6>[    0.724712,1] anx7805 anx7805_init_async: anx7805_init_async
<3>[    0.726641,5] IPC_RTR: msm_ipc_router_smd_driver_register Already driver registered IPCRTR
<3>[    0.726661,5] IPC_RTR: msm_ipc_router_smd_driver_register Already driver registered IPCRTR
<6>[    0.730089,5] In memshare_probe, Memshare probe success
<5>[    0.731507,5] msm_rpm_log_probe: OK
<6>[    0.732340,5] subsys-pil-tz soc:qcom,kgsl-hyp: for a506_zap segments only will be dumped.
<6>[    0.733853,5] subsys-pil-tz 1de0000.qcom,venus: for venus segments only will be dumped.
<6>[    0.735727,5] mmi_unit_info (SMEM) for modem: version = 0x03, device = 'sanders', radio = 0x0, radio_str = 'INDIA', system_rev = 0x8400, system_serial = 0xc035992300000000, machine = 'Qualcomm Technologies, Inc. MSM ', barcode = 'ZY32286WPB', baseband = '', carrier = 'retin', pu_reason = 0x00004000
<3>[    0.735756,5] ACPU Bin is not available.
<6>[    0.735801,5] mmi_storage_info :eMMC: 64GB SAMSUNG RC14MB FV=0000000000000007
<6>[    0.736174,5] msm_serial_hs module loaded
<6>[    0.744319,7] platform 1c40000.qcom,kgsl-iommu:gfx3d_secure: assigned reserved memory node secure_region@0
<6>[    0.749068,7] brd: module loaded
<6>[    0.750550,7] loop: module loaded
<6>[    0.750811,7] zram: Added device: zram0
<6>[    0.751106,7] QSEECOM: qseecom_probe: qseecom.qsee_version = 0x1001000
<4>[    0.751137,7] QSEECOM: qseecom_retrieve_ce_data: Device does not support PFE
<6>[    0.751145,7] QSEECOM: qseecom_probe: qseecom clocks handled by other subsystem
<4>[    0.751152,7] QSEECOM: qseecom_probe: qsee reentrancy support phase is not defined, setting to default 0
<4>[    0.751600,7] QSEECOM: qseecom_probe: qseecom.whitelist_support = 1
<6>[    0.752954,7] alsa-to-h2w soc:alsa_to_h2w: alsa_to_h2w_probe success
<4>[    0.753577,7] i2c-core: driver [tabla-i2c-core] using legacy suspend method
<4>[    0.753582,7] i2c-core: driver [tabla-i2c-core] using legacy resume method
<4>[    0.753648,7] i2c-core: driver [wcd9xxx-i2c-core] using legacy suspend method
<4>[    0.753652,7] i2c-core: driver [wcd9xxx-i2c-core] using legacy resume method
<4>[    0.753716,7] i2c-core: driver [tasha-i2c-core] using legacy suspend method
<4>[    0.753720,7] i2c-core: driver [tasha-i2c-core] using legacy resume method
<6>[    0.753948,7] Loading pn544 driver
<6>[    0.754058,7] nfc: succeed in obtaining nfc_clk from msm pmic
<4>[    0.754228,7] 5-0028 supply vdd not found, using dummy regulator
<6>[    0.754531,7] QCE50: __qce_get_device_tree_data: BAM Apps EE is not defined, setting to default 1
<6>[    0.755126,7] qce 720000.qcedev: Qualcomm Crypto 5.3.3 device found @0x720000
<6>[    0.755135,7] qce 720000.qcedev: CE device = 0x0
<6>[    0.755135,7] IO base, CE = 0xe9b40000
<6>[    0.755135,7] Consumer (IN) PIPE 2,    Producer (OUT) PIPE 3
<6>[    0.755135,7] IO base BAM = 0x00000000
<6>[    0.755135,7] BAM IRQ 59
<6>[    0.755135,7] Engines Availability = 0x2010853
<6>[    0.755279,7] sps:BAM 0x00704000 is registered.
<6>[    0.755427,7] sps:BAM 0x00704000 (va:0xea840000) enabled: ver:0x27, number of pipes:8
<6>[    0.755616,7] QCE50: qce_sps_init:  Qualcomm MSM CE-BAM at 0x0000000000704000 irq 59
<6>[    0.758328,6] QCE50: __qce_get_device_tree_data: BAM Apps EE is not defined, setting to default 1
<6>[    0.759188,6] qcrypto 720000.qcrypto: Qualcomm Crypto 5.3.3 device found @0x720000
<6>[    0.759197,6] qcrypto 720000.qcrypto: CE device = 0x0
<6>[    0.759197,6] IO base, CE = 0xea880000
<6>[    0.759197,6] Consumer (IN) PIPE 4,    Producer (OUT) PIPE 5
<6>[    0.759197,6] IO base BAM = 0x00000000
<6>[    0.759197,6] BAM IRQ 59
<6>[    0.759197,6] Engines Availability = 0x2010853
<6>[    0.759457,6] QCE50: qce_sps_init:  Qualcomm MSM CE-BAM at 0x0000000000704000 irq 59
<6>[    0.761655,6] qcrypto 720000.qcrypto: qcrypto-ecb-aes
<6>[    0.761725,6] qcrypto 720000.qcrypto: qcrypto-cbc-aes
<6>[    0.761795,6] qcrypto 720000.qcrypto: qcrypto-ctr-aes
<6>[    0.761864,6] qcrypto 720000.qcrypto: qcrypto-ecb-des
<6>[    0.761934,6] qcrypto 720000.qcrypto: qcrypto-cbc-des
<6>[    0.762005,6] qcrypto 720000.qcrypto: qcrypto-ecb-3des
<6>[    0.762075,6] qcrypto 720000.qcrypto: qcrypto-cbc-3des
<6>[    0.762144,6] qcrypto 720000.qcrypto: qcrypto-xts-aes
<6>[    0.762214,6] qcrypto 720000.qcrypto: qcrypto-sha1
<6>[    0.762284,6] qcrypto 720000.qcrypto: qcrypto-sha256
<6>[    0.762355,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-aes
<6>[    0.762426,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-des
<6>[    0.762498,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-3des
<6>[    0.762571,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-aes
<6>[    0.762647,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-des
<6>[    0.762717,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-3des
<6>[    0.762787,6] qcrypto 720000.qcrypto: qcrypto-hmac-sha1
<6>[    0.762858,6] qcrypto 720000.qcrypto: qcrypto-hmac-sha256
<6>[    0.762928,6] qcrypto 720000.qcrypto: qcrypto-aes-ccm
<6>[    0.763000,6] qcrypto 720000.qcrypto: qcrypto-rfc4309-aes-ccm
<3>[    0.763767,6] qcom_ice_get_device_tree_data: No vdd-hba-supply regulator, assuming not needed
<6>[    0.763865,6] ICE IRQ = 60
<6>[    0.764576,6] SCSI Media Changer driver v0.25 
<3>[    0.765974,6] spi_qsd 7af8000.spi: init_resources: unable to get core_clk
<3>[    0.766730,6] sps: BAM device 0x07884000 is not registered yet.
<6>[    0.766874,6] sps:BAM 0x07884000 is registered.
<6>[    0.767388,6] sps:BAM 0x07884000 (va:0xe9b20000) enabled: ver:0x19, number of pipes:12
<6>[    0.768101,6] tun: Universal TUN/TAP device driver, 1.6
<6>[    0.768107,6] tun: (C) 1999-2004 Max Krasnyansky <<EMAIL>>
<6>[    0.768161,6] PPP generic driver version 2.4.2
<6>[    0.768228,6] PPP BSD Compression module registered
<6>[    0.768235,6] PPP Deflate Compression module registered
<6>[    0.768253,6] PPP MPPE Compression module registered
<6>[    0.768262,6] NET: Registered protocol family 24
<6>[    0.768866,6] wcnss_wlan probed in built-in mode
<6>[    0.769487,6] pegasus: v0.9.3 (2013/04/25), Pegasus/Pegasus II USB Ethernet driver
<6>[    0.769552,6] usbcore: registered new interface driver pegasus
<6>[    0.769592,6] usbcore: registered new interface driver asix
<6>[    0.769622,6] usbcore: registered new interface driver ax88179_178a
<6>[    0.769651,6] usbcore: registered new interface driver cdc_ether
<6>[    0.769681,6] usbcore: registered new interface driver net1080
<6>[    0.769711,6] usbcore: registered new interface driver cdc_subset
<6>[    0.769741,6] usbcore: registered new interface driver zaurus
<6>[    0.769772,6] usbcore: registered new interface driver MOSCHIP usb-ethernet driver
<6>[    0.769897,6] usbcore: registered new interface driver cdc_ncm
<3>[    0.771192,6] scm_call failed: func id 0x2000c16, ret: -1, syscall returns: 0x0, 0x0, 0x0
<6>[    0.771198,6] hyp_assign_table: Failed to assign memory protection, ret = -5
<3>[    0.771205,6] msm_sharedmem: setup_shared_ram_perms: hyp_assign_phys failed IPA=0x0160xf4500000 size=1572864 err=-5
<6>[    0.771284,6] msm_sharedmem: msm_sharedmem_probe: Device created for client 'rmtfs'
<6>[    0.772985,6] msm_sharedmem: sharedmem_register_qmi: qmi init successful
<3>[    0.774850,6] msm-dwc3 7000000.ssusb: unable to get dbm device
<6>[    0.775831,6] ehci_hcd: USB 2.0 'Enhanced' Host Controller (EHCI) Driver
<6>[    0.775839,6] ehci-msm: Qualcomm On-Chip EHCI Host Controller
<6>[    0.776109,6] usbcore: registered new interface driver cdc_acm
<6>[    0.776114,6] cdc_acm: USB Abstract Control Model driver for USB modems and ISDN adapters
<6>[    0.776156,6] usbcore: registered new interface driver usb-storage
<6>[    0.776182,6] usbcore: registered new interface driver ums-alauda
<6>[    0.776208,6] usbcore: registered new interface driver ums-cypress
<6>[    0.776236,6] usbcore: registered new interface driver ums-datafab
<6>[    0.776263,6] usbcore: registered new interface driver ums-freecom
<6>[    0.776291,6] usbcore: registered new interface driver ums-isd200
<6>[    0.776317,6] usbcore: registered new interface driver ums-jumpshot
<6>[    0.776344,6] usbcore: registered new interface driver ums-karma
<6>[    0.776370,6] usbcore: registered new interface driver ums-onetouch
<6>[    0.776398,6] usbcore: registered new interface driver ums-sddr09
<6>[    0.776425,6] usbcore: registered new interface driver ums-sddr55
<6>[    0.776451,6] usbcore: registered new interface driver ums-usbat
<6>[    0.776516,6] usbcore: registered new interface driver usbserial
<6>[    0.776548,6] usbcore: registered new interface driver usb_ehset_test
<6>[    0.777025,6] gbridge_init: gbridge_init successs.
<6>[    0.777248,6] mousedev: PS/2 mouse device common for all mice
<6>[    0.777388,6] usbcore: registered new interface driver xpad
<6>[    0.777477,6] ft5x06_ts 3-0038: processing modifier config_modifier-charger[0]
<5>[    0.777483,6] using charger detection
<6>[    0.777581,6] ft5x06_ts 3-0038: processing modifier config_modifier-fps[1]
<5>[    0.777586,6] sing fingerprint sensor detection
<5>[    0.777592,6] using touch clip area in fps-active
<6>[    0.777714,6] input: ft5x06_ts as /devices/soc/78b7000.i2c/i2c-3/3-0038/input/input1
<3>[    1.003576,6] i2c-msm-v2 78b7000.i2c: msm_bus_scale_register_client(mstr-id:86):0xe (ok)
<6>[    1.003782,6] ft5x06_ts 3-0038: Device ID = 0x54
<6>[    1.003898,6] assigned minor 56
<6>[    1.004013,6] ft5x06_ts 3-0038: Create proc entry success
<6>[    1.004168,6] ft5x06_ts 3-0038: report rate = 110Hz
<6>[    1.004765,6] ft5x06_ts 3-0038: Firmware version = 6.0.0
<6>[    1.004915,6] vendor id 0x04 panel supplier is biel
<6>[    1.005091,6] ft5x06_ts 3-0038: Firmware id = 0x0001
<3>[    1.005168,6] ft5x06_ts 3-0038: Failed to register fps_notifier: -19
<3>[    1.005628,6] [NVT-ts] nvt_driver_init 1865: start
<6>[    1.005656,6] nvt_driver_init: finished
<6>[    1.006175,6] input: hbtp_vm as /devices/virtual/input/input2
<3>[    1.006947,6] fpc1020 spi8.0: Unable to read wakelock time
<6>[    1.007057,6] input: fpc1020 as /devices/virtual/input/input3
<6>[    1.007105,6] fpc1020 spi8.0: fpc1020_probe: ok
<6>[    1.007128,6] Driver ltr559 init.
<3>[    1.140228,0] i2c-msm-v2 7af7000.i2c: msm_bus_scale_register_client(mstr-id:84):0xf (ok)
<4>[    1.140436,0] ltr559_check_chip_id read the  LTR559_MANUFAC_ID is 0x5
<6>[    1.154642,0] ltr559_gpio_irq: INT No. 254
<6>[    1.154750,0] input: ltr559-ps as /devices/soc/7af7000.i2c/i2c-7/7-0023/input/input4
<4>[    1.154802,0] ltr559_probe input device success.
<6>[    1.155422,0] qcom,qpnp-rtc qpnp-rtc-8: rtc core: registered qpnp_rtc as rtc0
<6>[    1.155556,0] i2c /dev entries driver
<3>[    1.161458,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.163345,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.164039,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.164611,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.166374,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.167451,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.168501,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<5>[    1.168965,4] msm_actuator_platform_probe:2086 No valid actuator GPIOs data
<5>[    1.169046,4] msm_actuator_platform_probe:2086 No valid actuator GPIOs data
<3>[    1.169626,4] msm_eeprom_platform_probe failed 2029
<3>[    1.169990,4] msm_eeprom_platform_probe failed 2029
<3>[    1.170333,4] msm_eeprom_platform_probe failed 2029
<3>[    1.170967,4] msm_flash_get_pmic_source_info:1000 alternate current: read failed
<3>[    1.170974,4] msm_flash_get_pmic_source_info:1020 alternate max-current: read failed
<3>[    1.170980,4] msm_flash_get_pmic_source_info:1040 alternate duration: read failed
<3>[    1.171013,4] msm_flash_get_pmic_source_info:1000 alternate current: read failed
<3>[    1.171019,4] msm_flash_get_pmic_source_info:1020 alternate max-current: read failed
<3>[    1.171025,4] msm_flash_get_pmic_source_info:1040 alternate duration: read failed
<3>[    1.171058,4] msm_flash_get_pmic_source_info:1110 alternate current: read failed
<3>[    1.171063,4] msm_flash_get_pmic_source_info:1130 alternate current: read failed
<3>[    1.171096,4] msm_flash_get_pmic_source_info:1110 alternate current: read failed
<3>[    1.171101,4] msm_flash_get_pmic_source_info:1130 alternate current: read failed
<5>[    1.171107,4] msm_flash_get_dt_data:1203 No valid flash GPIOs data
<3>[    1.171113,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.171813,4] adp1660 i2c_add_driver success
<6>[    1.177694,4] MSM-CPP cpp_init_hardware:1005 CPP HW Version: 0x40030003
<3>[    1.177703,4] MSM-CPP cpp_init_hardware:1023 stream_cnt:0
<3>[    1.178939,5] CAM-SOC msm_camera_get_reg_base:787 err: mem resource vfe_fuse not found
<3>[    1.178945,5] CAM-SOC msm_camera_get_res_size:830 err: mem resource vfe_fuse not found
<3>[    1.179984,5] CAM-SOC msm_camera_get_reg_base:787 err: mem resource vfe_fuse not found
<3>[    1.179989,5] CAM-SOC msm_camera_get_res_size:830 err: mem resource vfe_fuse not found
<3>[    1.186872,5] __msm_jpeg_init:1537] Jpeg Device id 0
<6>[    1.188472,5] usbcore: registered new interface driver uvcvideo
<6>[    1.188478,5] USB Video Class driver (1.1.1)
<6>[    1.189052,5] FG: fg_check_ima_exception: Initial ima_err_sts=0 ima_exp_sts=0 ima_hw_sts=cc
<6>[    1.189271,5] FG: fg_empty_soc_irq_handler: triggered 0x22
<3>[    1.190351,5] FG: fg_power_get_property: ESR Bad: -22 mOhm
<3>[    1.190593,5] FG: fg_power_get_property: ESR Bad: -22 mOhm
<6>[    1.190637,5] FG: fg_probe: FG Probe success - FG Revision DIG:3.1 ANA:1.2 PMIC subtype=17
<3>[    1.191737,4] unable to find DT imem DLOAD mode node
<3>[    1.191992,4] unable to find DT imem EDLOAD mode node
<4>[    1.192980,6] thermal thermal_zone1: failed to read out thermal zone 1
<4>[    1.193136,6] thermal thermal_zone2: failed to read out thermal zone 2
<4>[    1.193283,6] thermal thermal_zone3: failed to read out thermal zone 3
<4>[    1.193461,6] thermal thermal_zone4: failed to read out thermal zone 4
<3>[    1.193918,6] qpnp_vadc_read: no vadc_chg_vote found
<3>[    1.193924,6] qpnp_vadc_get_temp: VADC read error with -22
<4>[    1.193930,6] thermal thermal_zone5: failed to read out thermal zone 5
<6>[    1.215666,6] device-mapper: uevent: version 1.0.3
<6>[    1.215805,6] device-mapper: ioctl: 4.28.0-ioctl (2014-09-17) initialised: <EMAIL>
<6>[    1.215878,6] device-mapper: req-crypt: dm-req-crypt successfully initalized.
<6>[    1.215878,6] 
<6>[    1.216513,6] sdhci: Secure Digital Host Controller Interface driver
<6>[    1.216517,6] sdhci: Copyright(c) Pierre Ossman
<6>[    1.216525,6] sdhci-pltfm: SDHCI platform and OF driver helper
<6>[    1.218614,0] qcom_ice_get_pdevice: found ice device c3a9bf00
<6>[    1.218621,0] qcom_ice_get_pdevice: matching platform device e5830000
<6>[    1.222402,0] qcom_ice 7803000.sdcc1ice: QC ICE 2.1.44 device found @0xe99a0000
<6>[    1.222753,0] sdhci_msm 7824900.sdhci: No vmmc regulator found
<6>[    1.222760,0] sdhci_msm 7824900.sdhci: No vqmmc regulator found
<6>[    1.223062,0] mmc0: SDHCI controller on 7824900.sdhci [7824900.sdhci] using 32-bit ADMA in CMDQ mode
<4>[    1.253732,0] sdhci_msm 7864900.sdhci: sdhci_msm_probe: ICE device is not enabled
<6>[    1.268307,0] sdhci_msm 7864900.sdhci: No vmmc regulator found
<6>[    1.268314,0] sdhci_msm 7864900.sdhci: No vqmmc regulator found
<6>[    1.268612,0] mmc1: SDHCI controller on 7864900.sdhci [7864900.sdhci] using 32-bit ADMA in legacy mode
<6>[    1.289468,1] mmc0: Out-of-interrupt timeout is 50[ms]
<6>[    1.289474,1] mmc0: BKOPS_EN equals 0x2
<6>[    1.289479,1] mmc0: eMMC FW version: 0x07
<6>[    1.289483,1] mmc0: CMDQ supported: depth: 16
<6>[    1.289488,1] mmc0: cache barrier support 0 flush policy 0
<6>[    1.299104,1] cmdq_host_alloc_tdl: desc_size: 512 data_sz: 126976 slot-sz: 16
<6>[    1.299267,1] mmc0: CMDQ enabled on card
<6>[    1.299276,1] mmc0: new HS400 MMC card at address 0001
<6>[    1.299526,1] sdhci_msm_pm_qos_cpu_init (): voted for group #0 (mask=0xf) latency=2
<6>[    1.299533,1] sdhci_msm_pm_qos_cpu_init (): voted for group #1 (mask=0xf0) latency=2
<6>[    1.299629,1] mmcblk0: mmc0:0001 RC14MB 58.2 GiB 
<6>[    1.299714,1] mmcblk0rpmb: mmc0:0001 RC14MB partition 3 4.00 MiB
<6>[    1.300342,2] qcom,leds-atc leds-atc-20: atc_leds_probe success
<6>[    1.300486,2] hidraw: raw HID events driver (C) Jiri Kosina
<6>[    1.300705,0] tz_log 8600720.tz-log: Hyp log service is not supported
<6>[    1.300872,2] usbcore: registered new interface driver usbhid
<6>[    1.300878,2] usbhid: USB HID core driver
<6>[    1.301243,1]  mmcblk0: p1 p2 p3 p4 p5 p6 p7 p8 p9 p10 p11 p12 p13 p14 p15 p16 p17 p18 p19 p20 p21 p22 p23 p24 p25 p26 p27 p28 p29 p30 p31 p32 p33 p34 p35 p36 p37 p38 p39 p40 p41 p42 p43 p44 p45 p46 p47 p48 p49 p50 p51 p52 p53 p54
<6>[    1.301244,2] ashmem: initialized
<6>[    1.301605,3] qpnp_coincell_charger_show_state: enabled=Y, voltage=3200 mV, resistance=2100 ohm
<6>[    1.304304,3] bimc-bwmon 408000.qcom,cpu-bwmon: BW HWmon governor registered.
<3>[    1.305800,3] devfreq soc:qcom,cpubw: Couldn't update frequency transition information.
<3>[    1.305926,3] devfreq soc:qcom,mincpubw: Couldn't update frequency transition information.
<3>[    1.307466,3] sensors-ssc soc:qcom,msm-ssc-sensors: msm_ssc_sensors_dt_parse: get qdsp timer cntpct hi offset fail
<6>[    1.307475,3] sensors-ssc soc:qcom,msm-ssc-sensors: slpi_loader_init_sysfs: Could not parse dt
<6>[    1.307824,3] usbcore: registered new interface driver snd-usb-audio
<6>[    1.311102,3] cs35l35 7-0040: Cirrus Logic CS35L35 (35a35), Revision: 00
<6>[    1.322888,4] msm-pcm-lpa soc:qcom,msm-pcm-lpa: msm_pcm_probe: dev name soc:qcom,msm-pcm-lpa
<6>[    1.327187,4] u32 classifier
<6>[    1.327192,4]     Actions configured
<6>[    1.327218,4] Netfilter messages via NETLINK v0.30.
<6>[    1.327257,4] nf_conntrack version 0.5.0 (16384 buckets, 65536 max)
<6>[    1.327503,4] ctnetlink v0.93: registering with nfnetlink.
<6>[    1.327963,4] xt_time: kernel timezone is -0000
<6>[    1.328182,4] ip_tables: (C) 2000-2006 Netfilter Core Team
<6>[    1.328293,4] arp_tables: (C) 2002 David S. Miller
<6>[    1.328330,4] TCP: cubic registered
<6>[    1.328336,4] Initializing XFRM netlink socket
<6>[    1.328567,4] NET: Registered protocol family 10
<6>[    1.329234,5] mip6: Mobile IPv6
<6>[    1.329253,5] ip6_tables: (C) 2000-2006 Netfilter Core Team
<6>[    1.329361,5] sit: IPv6 over IPv4 tunneling driver
<6>[    1.329687,5] NET: Registered protocol family 17
<6>[    1.329703,5] NET: Registered protocol family 15
<6>[    1.329734,5] bridge: automatic filtering via arp/ip/ip6tables has been deprecated. Update your scripts to load br_netfilter if you need this.
<6>[    1.329743,5] Ebtables v2.0 registered
<6>[    1.329844,5] Bluetooth: e6e05eb0
<6>[    1.329854,5] Bluetooth: e6e05ea8Bluetooth: e6e05ec0
<6>[    1.329889,5] Bluetooth: e6e05ea0Bluetooth: e6e05ea0
<6>[    1.329902,5] Bluetooth: e6e05e98Bluetooth: e6e05ed8
<6>[    1.329915,5] Bluetooth: e6e05ed8<6>[    1.329951,5] l2tp_core: L2TP core driver, V2.0
<6>[    1.329963,5] l2tp_ppp: PPPoL2TP kernel driver, V2.0
<6>[    1.329970,5] l2tp_ip: L2TP IP encapsulation support (L2TPv3)
<6>[    1.329987,5] l2tp_netlink: L2TP netlink interface
<6>[    1.330007,5] l2tp_eth: L2TP ethernet pseudowire support (L2TPv3)
<6>[    1.330023,5] l2tp_debugfs: L2TP debugfs support
<6>[    1.330030,5] l2tp_ip6: L2TP IP encapsulation support for IPv6 (L2TPv3)
<6>[    1.330505,5] NET: Registered protocol family 27
<6>[    1.334134,7] subsys-pil-tz a21b000.qcom,pronto: for wcnss segments only will be dumped.
<6>[    1.335766,7] pil-q6v5-mss 4080000.qcom,mss: for modem segments only will be dumped.
<6>[    1.337188,7] msm-dwc3 7000000.ssusb: unable to read dcp-max-current, using define value
<6>[    1.337535,7] ft5x06_ts 3-0038: unset chg state
<6>[    1.337553,7] ft5x06_ts 3-0038: ps present state not change
<6>[    1.338659,7] sps:BAM 0x07104000 is registered.
<3>[    1.341599,7] qpnp-smbcharger qpnp-smbcharger-17: node /soc/qcom,spmi@200f000/qcom,pmi8950@2/qcom,qpnp-smbcharger IO resource absent!
<3>[    1.341724,7] qpnp-smbcharger qpnp-smbcharger-17: length=8
<3>[    1.341732,7] qpnp-smbcharger qpnp-smbcharger-17: num parallel charge entries=8
<6>[    1.341820,7] smbcharger_charger_otg: no parameters
<6>[    1.342465,7] FG: fg_vbat_est_check: vbat(3625009),est-vbat(3707558),diff(82549),threshold(300000)
<6>[    1.365244,7] msm-dwc3 7000000.ssusb: Avail curr from USB = 1500
<3>[    1.365530,7] qpnp-smbcharger qpnp-smbcharger-17: Turbo Charger Detected!
<6>[    1.365998,7] FG: fg_vbat_est_check: vbat(3625009),est-vbat(3707558),diff(82549),threshold(300000)
<3>[    1.368935,5] qpnp-smbcharger qpnp-smbcharger-17: node /soc/qcom,spmi@200f000/qcom,pmi8950@2/qcom,qpnp-smbcharger IO resource absent!
<6>[    1.370127,5] qpnp-smbcharger qpnp-smbcharger-17: SMBCHG successfully probe Charger version=SCHG_LITE Revision DIG:0.0 ANA:0.1 batt=1 dc=0 usb=1
<3>[    1.371892,7] qpnp-smbcharger qpnp-smbcharger-17: Battery Temp State: Unknown -> Cool at -2C
<5>[    1.372348,4] Registering SWP/SWPB emulation handler
<6>[    1.372648,4] registered taskstats version 1
<6>[    1.377097,6] fastrpc soc:qcom,adsprpc-mem: for adsp_rh segments only will be dumped.
<1>[    1.378455,6] drv260x: drv260x_init success
<6>[    1.378881,2] utags (utags_probe): Done [config]
<6>[    1.378909,2] utags (utags_dt_init): backup storage path not provided
<6>[    1.379118,2] utags (utags_probe): Done [hw]
<6>[    1.379723,2] RNDIS_IPA module is loaded.
<6>[    1.380153,2] file system registered
<6>[    1.380197,2] mbim_init: initialize 1 instances
<6>[    1.380244,2] mbim_init: Initialized 1 ports
<6>[    1.381294,2] rndis_qc_init: initialize rndis QC instance
<6>[    1.381463,2] Number of LUNs=8
<6>[    1.381471,2] Mass Storage Function, version: 2009/09/11
<6>[    1.381479,2] LUN: removable file: (no medium)
<6>[    1.381491,2] Number of LUNs=1
<6>[    1.381530,2] LUN: removable file: (no medium)
<6>[    1.381535,2] Number of LUNs=1
<6>[    1.382199,2] android_usb gadget: android_usb ready
<6>[    1.383483,5] input: gpio-keys as /devices/soc/soc:gpio_keys/input/input5
<4>[    1.383835,5] i2c-core: driver [stmvl53l0] using legacy resume method
<6>[    1.384329,5] qcom,qpnp-rtc qpnp-rtc-8: setting system clock to 1970-01-01 00:09:13 UTC (553)
<3>[    1.385825,5] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    1.385853,5] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    1.385859,5] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    1.385864,5] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    1.385869,5] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    1.385874,5] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    1.385879,5] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    1.385884,5] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    1.385931,5] msm_thermal:devmgr_client_hotplug_update Hotplug task is not initialized
<6>[    1.386949,6] msm-core initialized without polling period
<3>[    1.389448,6] parse_cpu_levels: idx 1 276
<3>[    1.389457,6] calculate_residency: residency < 0 for LPM
<3>[    1.389572,6] parse_cpu_levels: idx 1 286
<3>[    1.389578,6] calculate_residency: residency < 0 for LPM
<3>[    1.392546,6] qcom,qpnp-flash-led qpnp-flash-led-23: Unable to acquire pinctrl
<6>[    1.394242,6] rmnet_ipa started initialization
<6>[    1.394248,6] IPA SSR support = True
<6>[    1.394252,6] IPA ipa-loaduC = True
<6>[    1.394257,6] IPA SG support = True
<3>[    1.396119,6] ipa ipa_sps_irq_control_all:942 EP (5) not allocated.
<3>[    1.396129,6] ipa ipa2_uc_state_check:301 uC is not loaded
<6>[    1.397129,6] rmnet_ipa completed initialization
<6>[    1.399529,6] qcom,cc-debug-8953 1874000.qcom,cc-debug: Registered Debug Mux successfully
<6>[    1.407973,6] msm8952-asoc-wcd c051000.sound: default codec configured
<3>[    1.411197,6] msm8952-asoc-wcd c051000.sound: ASoC: platform (null) not registered
<3>[    1.411238,6] msm8952-asoc-wcd c051000.sound: snd_soc_register_card failed (-517)
<6>[    1.412378,6] apc_mem_acc_corner: disabling
<6>[    1.412385,6] gfx_mem_acc_corner: disabling
<6>[    1.412427,6] vci_fci: disabling
<6>[    1.412466,6] regulator_proxy_consumer_remove_all: removing regulator proxy consumer requests
<6>[    1.412504,6] clock_late_init: Removing enables held for handed-off clocks
<6>[    1.416403,6] ALSA device list:
<6>[    1.416408,6]   No soundcards found.
<3>[    1.416477,6] Warning: unable to open an initial console.
<6>[    1.416837,7] ft5x06_ts 3-0038: ps present state not change
<6>[    1.417133,7] ft5x06_ts 3-0038: set chg state
<6>[    1.451443,6] Freeing unused kernel memory: 504K
<14>[    1.452994,6] init: init first stage started!
<14>[    1.453032,6] init: First stage mount skipped (recovery mode)
<14>[    1.453242,6] init: Using Android DT directory /proc/device-tree/firmware/android/
<14>[    1.453304,6] init: Skipped setting INIT_AVB_VERSION (not vbmeta compatible)
<14>[    1.453322,6] init: Loading SELinux policy
<7>[    1.458429,6] SELinux: 2048 avtab hash slots, 29509 rules.
<7>[    1.470614,6] SELinux: 2048 avtab hash slots, 29509 rules.
<7>[    1.470633,6] SELinux:  1 users, 2 roles, 2214 types, 0 bools, 1 sens, 1024 cats
<7>[    1.470640,6] SELinux:  93 classes, 29509 rules
<6>[    1.470887,4] FG: fg_vbat_est_check: vbat(3625009),est-vbat(3707558),diff(82549),threshold(300000)
<7>[    1.473975,6] SELinux:  Completing initialization.
<7>[    1.473980,6] SELinux:  Setting up existing superblocks.
<7>[    1.473994,6] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<7>[    1.474015,6] SELinux: initialized (dev rootfs, type rootfs), uses genfs_contexts
<7>[    1.474185,6] SELinux: initialized (dev bdev, type bdev), not configured for labeling
<7>[    1.474198,6] SELinux: initialized (dev proc, type proc), uses genfs_contexts
<7>[    1.474223,6] SELinux: initialized (dev debugfs, type debugfs), uses genfs_contexts
<4>[    1.483412,6] bcl_peripheral:bcl_poll_vbat_high Vbat reached high clear trip. vbat:3781440
<3>[    1.483437,6] bcl_peripheral:bcl_poll_ibat_low Invalid ibat state 1
<7>[    1.498462,4] SELinux: initialized (dev sockfs, type sockfs), uses task SIDs
<7>[    1.498481,4] SELinux: initialized (dev tracefs, type tracefs), uses genfs_contexts
<7>[    1.532666,4] SELinux: initialized (dev pipefs, type pipefs), uses task SIDs
<7>[    1.532680,4] SELinux: initialized (dev anon_inodefs, type anon_inodefs), not configured for labeling
<7>[    1.532687,4] SELinux: initialized (dev aio, type aio), not configured for labeling
<7>[    1.532696,4] SELinux: initialized (dev devpts, type devpts), uses transition SIDs
<7>[    1.532715,4] SELinux: initialized (dev configfs, type configfs), uses genfs_contexts
<7>[    1.532728,4] SELinux: initialized (dev selinuxfs, type selinuxfs), uses genfs_contexts
<7>[    1.532786,4] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<7>[    1.532820,4] SELinux: initialized (dev sysfs, type sysfs), uses genfs_contexts
<5>[    1.541951,4] audit: type=1403 audit(553.653:2): policy loaded auid=4294967295 ses=4294967295
<14>[    1.542184,4] selinux: SELinux: Loaded policy from /sepolicy
<14>[    1.542184,4] 
<5>[    1.542395,4] audit: type=1404 audit(553.653:3): enforcing=1 old_enforcing=0 auid=4294967295 ses=4294967295
<14>[    1.559289,4] selinux: SELinux: Loaded file_contexts
<14>[    1.559289,4] 
<5>[    1.560299,4] random: init urandom read with 88 bits of entropy available
<14>[    1.561121,4] init: init second stage started!
<14>[    1.569875,4] init: Using Android DT directory /proc/device-tree/firmware/android/
<14>[    1.576177,4] selinux: SELinux: Loaded file_contexts
<14>[    1.576177,4] 
<14>[    1.577930,4] selinux: SELinux: Loaded property_contexts from /plat_property_contexts & /nonplat_property_contexts.
<14>[    1.577930,4] 
<14>[    1.577949,4] init: Running restorecon...
<11>[    1.585424,4] selinux: SELinux:  Could not stat /dev/block: No such file or directory.
<11>[    1.585424,4] 
<11>[    1.585795,4] init: waitid failed: No child processes
<12>[    1.585842,4] init: Couldn't load property file: Unable to open '/system/etc/prop.default': No such file or directory: No such file or directory
<12>[    1.586303,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586328,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586352,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586376,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586400,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586423,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586447,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586473,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586496,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586519,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586542,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586566,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586591,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586615,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586638,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586661,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586685,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586727,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586752,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586775,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586798,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586821,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586844,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586867,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586890,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586913,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586937,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586960,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.586985,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587008,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587032,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587055,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587078,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587101,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587124,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587147,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587170,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587193,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587217,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587240,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587263,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587286,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.587312,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<11>[    1.589115,4] init: property_set("ro.cutoff_voltage_mv", "3400") failed: property already set
<11>[    1.589848,4] init: property_set("ro.opengles.version", "196610") failed: property already set
<11>[    1.590375,4] init: property_set("ro.carrier", "unknown") failed: property already set
<12>[    1.590858,4] init: Couldn't load property file: Unable to open '/odm/default.prop': No such file or directory: No such file or directory
<12>[    1.590920,4] init: Couldn't load property file: Unable to open '/vendor/default.prop': No such file or directory: No such file or directory
<14>[    1.591421,4] init: Created socket '/dev/socket/property_service', mode 666, user 0, group 0
<14>[    1.591542,4] init: Parsing file /init.rc...
<14>[    1.591645,4] init: Added '/init.recovery.qcom.rc' to import list
<14>[    1.591981,4] init: Parsing file /init.recovery.qcom.rc...
<14>[    1.592111,4] init: Parsing file /system/etc/init...
<11>[    1.592133,4] init: Unable to open '/system/etc/init': No such file or directory
<14>[    1.592156,4] init: Parsing file /vendor/etc/init...
<11>[    1.592178,4] init: Unable to open '/vendor/etc/init': No such file or directory
<14>[    1.592196,4] init: Parsing file /odm/etc/init...
<11>[    1.592216,4] init: Unable to open '/odm/etc/init': No such file or directory
<14>[    1.592308,4] init: processing action (early-init) from (/init.rc:3)
<14>[    1.592368,4] init: starting service 'ueventd'...
<5>[    1.592816,4] audit: type=1400 audit(553.703:4): avc:  denied  { create } for  uid=0 pid=1 comm="init" name="cgroup.procs" scontext=u:r:init:s0 tcontext=u:object_r:rootfs:s0 tclass=file permissive=0
<11>[    1.592883,4] init: Failed to write '413' to /acct/uid_0/pid_413/cgroup.procs: Permission denied
<11>[    1.592902,4] init: createProcessGroup(0, 413) failed for service 'ueventd': Permission denied
<14>[    1.592976,4] init: processing action (wait_for_coldboot_done) from (<Builtin Action>:0)
<14>[    1.595333,5] ueventd: ueventd started!
<14>[    1.595385,5] ueventd: Parsing file /ueventd.rc...
<11>[    1.595688,5] ueventd: /ueventd.rc: 66: invalid gid 'qcom_diag'
<14>[    1.596165,5] ueventd: Parsing file /vendor/ueventd.rc...
<11>[    1.596190,5] ueventd: Unable to open '/vendor/ueventd.rc': No such file or directory
<14>[    1.596208,5] ueventd: Parsing file /odm/ueventd.rc...
<11>[    1.596228,5] ueventd: Unable to open '/odm/ueventd.rc': No such file or directory
<14>[    1.596297,5] ueventd: Parsing file /ueventd.qcom.rc...
<11>[    1.596319,5] ueventd: Unable to open '/ueventd.qcom.rc': No such file or directory
<14>[    1.601678,5] selinux: SELinux: Loaded file_contexts
<14>[    1.601678,5] 
<14>[    1.733700,5] selinux: SELinux: Loaded file_contexts
<14>[    1.733700,5] 
<14>[    1.733792,4] selinux: SELinux: Loaded file_contexts
<14>[    1.733792,4] 
<14>[    1.733807,7] selinux: SELinux: Loaded file_contexts
<14>[    1.733807,7] 
<14>[    1.733963,1] selinux: SELinux: Loaded file_contexts
<14>[    1.733963,1] 
<14>[    1.734233,3] selinux: SELinux: Loaded file_contexts
<14>[    1.734233,3] 
<14>[    1.734583,6] selinux: SELinux: Loaded file_contexts
<14>[    1.734583,6] 
<14>[    1.741031,2] selinux: SELinux: Loaded file_contexts
<14>[    1.741031,2] 
<14>[    1.746774,0] selinux: SELinux: Loaded file_contexts
<14>[    1.746774,0] 
<14>[    1.747593,7] selinux: SELinux: Loaded file_contexts
<14>[    1.747593,7] 
<6>[    1.937607,7] FG: fg_vbat_est_check: vbat(3625009),est-vbat(3707558),diff(82549),threshold(300000)
<14>[    3.094232,4] ueventd: Coldboot took 1.492 seconds
<14>[    3.094684,3] init: Command 'wait_for_coldboot_done' action=wait_for_coldboot_done (<Builtin Action>:0) returned 0 took 1501ms.
<14>[    3.094723,3] init: processing action (mix_hwrng_into_linux_rng) from (<Builtin Action>:0)
<14>[    3.095250,3] init: Mixed 512 bytes from /dev/hw_random into /dev/urandom
<14>[    3.095278,3] init: processing action (set_mmap_rnd_bits) from (<Builtin Action>:0)
<14>[    3.095300,3] init: processing action (set_kptr_restrict) from (<Builtin Action>:0)
<14>[    3.095569,3] init: processing action (keychord_init) from (<Builtin Action>:0)
<14>[    3.095596,3] init: processing action (console_init) from (<Builtin Action>:0)
<14>[    3.095643,3] init: processing action (init) from (/init.rc:9)
<7>[    3.096215,3] SELinux: initialized (dev cgroup, type cgroup), uses genfs_contexts
<7>[    3.098065,3] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<14>[    3.098341,3] init: processing action (init) from (/init.recovery.qcom.rc:28)
<11>[    3.098384,3] init: Unable to open '/sys/class/backlight/panel0-backlight/brightness': No such file or directory
<14>[    3.099426,3] init: processing action (mix_hwrng_into_linux_rng) from (<Builtin Action>:0)
<14>[    3.099918,3] init: Mixed 512 bytes from /dev/hw_random into /dev/urandom
<14>[    3.099949,3] init: processing action (late-init) from (/init.rc:66)
<14>[    3.099993,3] init: processing action (queue_property_triggers) from (<Builtin Action>:0)
<14>[    3.100024,3] init: processing action (fs) from (/init.rc:36)
<7>[    3.101237,3] SELinux: initialized (dev functionfs, type functionfs), uses genfs_contexts
<3>[    3.101335,3] enable_store: android_usb: already disabled
<14>[    3.101809,3] init: processing action (load_system_props_action) from (/init.rc:59)
<12>[    3.101908,3] init: HW descriptor status=2
<6>[    3.101918,3] utags (reload_write): [init] (pid 1) [hw] 1
<12>[    3.229027,0] init: Sent HW descriptor reload command rc=2
<11>[    3.229081,0] init: File /vendor/etc/vhw.xml not found
<12>[    3.229130,0] init: Couldn't load property file: Unable to open '/system/build.prop': No such file or directory: No such file or directory
<12>[    3.229155,0] init: Couldn't load property file: Unable to open '/odm/build.prop': No such file or directory: No such file or directory
<12>[    3.229180,0] init: Couldn't load property file: Unable to open '/vendor/build.prop': No such file or directory: No such file or directory
<12>[    3.229204,0] init: Couldn't load property file: Unable to open '/factory/factory.prop': No such file or directory: No such file or directory
<14>[    3.230655,0] init: Command 'load_system_props' action=load_system_props_action (/init.rc:60) returned 0 took 128ms.
<14>[    3.230687,0] init: processing action (firmware_mounts_complete) from (/init.rc:62)
<14>[    3.230731,0] init: processing action (boot) from (/init.rc:51)
<14>[    3.231170,0] init: starting service 'charger'...
<14>[    3.231851,0] init: starting service 'recovery'...
<14>[    3.232466,0] init: processing action (enable_property_trigger) from (<Builtin Action>:0)
<12>[    3.235936,4] healthd: battery l=10 v=3584 t=37.5 h=2 st=3 c=544 fc=0 cc=0 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<5>[    3.239457,5] audit: type=1400 audit(555.350:5): avc:  denied  { read } for  uid=0 pid=424 comm="recovery" name="u:object_r:sf_lcd_density_prop:s0" dev="tmpfs" ino=16442 scontext=u:r:recovery:s0 tcontext=u:object_r:sf_lcd_density_prop:s0 tclass=file permissive=0
<6>[    3.286824,1] input input5: gpio-keys report volume_up [0x73] type 0x1 state Off
<5>[    3.287110,1] audit: type=1400 audit(555.400:6): avc:  denied  { write } for  uid=0 pid=424 comm="recovery" name="brightness" dev="sysfs" ino=22073 scontext=u:r:recovery:s0 tcontext=u:object_r:sysfs_graphics:s0 tclass=file permissive=0
<4>[    3.303736,0] irq 21, desc: e5d40840, depth: 0, count: 0, unhandled: 0
<4>[    3.303748,0] ->handle_irq():  c03f6c44, msm_gpio_irq_handler+0x0/0x118
<4>[    3.303755,0] ->irq_data.chip(): c1531158, gic_chip+0x0/0x74
<4>[    3.303756,0] ->action():   (null)
<4>[    3.303758,0]    IRQ_NOPROBE set
<4>[    3.303758,0]  IRQ_NOREQUEST set
<4>[    3.303759,0]   IRQ_NOTHREAD set
<6>[    3.304071,6] mdss_dsi_on[0]+.
<6>[    3.530237,4] ft5x06_ts 3-0038: set chg state
<6>[    4.370133,7] msm-dwc3 7000000.ssusb: Avail curr from USB = 1800
<6>[    4.370178,7] ft5x06_ts 3-0038: ps present state not change
<12>[    4.371093,4] healthd: battery l=10 v=3584 t=37.5 h=2 st=3 c=544 fc=0 cc=0 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<12>[    4.371779,4] healthd: battery l=10 v=3584 t=37.5 h=2 st=3 c=544 fc=0 cc=0 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<3>[    4.375504,7] qpnp-smbcharger qpnp-smbcharger-17: Battery Temp State: Cool -> Good at 37C
<6>[    4.381467,1] EXT4-fs (mmcblk0p52): mounted filesystem with ordered data mode. Opts: 
<7>[    4.381492,1] SELinux: initialized (dev mmcblk0p52, type ext4), uses xattr
<6>[    4.384130,3] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0x301c04c9)
<3>[    4.384133,3] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 1th superblock
<6>[    4.384375,3] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xc6a3ab26)
<3>[    4.384377,3] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 2th superblock
<6>[    4.384382,3] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0x301c04c9)
<3>[    4.384384,3] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 1th superblock
<6>[    4.384386,3] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xc6a3ab26)
<3>[    4.384388,3] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 2th superblock
<3>[    4.384751,3] EXT4-fs (mmcblk0p54): VFS: Can't find ext4 filesystem
<12>[    4.386633,2] healthd: battery l=10 v=3645 t=37.5 h=2 st=2 c=177 fc=0 cc=0 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<6>[    4.484497,7] FG: fg_vbat_est_check: vbat(3645761),est-vbat(3772255),diff(126494),threshold(300000)
<12>[    4.485421,2] healthd: battery l=10 v=3645 t=37.5 h=2 st=2 c=177 fc=0 cc=0 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<5>[    5.088873,0] random: nonblocking pool is initialized
<3>[    5.213483,4] FG: fg_get_mmi_battid: Battsn unused
<4>[    5.213501,4] qcom,qpnp-fg qpnp-fg-18: Default Serial Number SB18C15119
<4>[    5.213511,4] qcom,qpnp-fg qpnp-fg-18: Battery Match Found using default qcom,hg30-alt
<6>[    5.218628,4] FG: fg_batt_profile_init: Battery profiles same, using default
<6>[    5.221674,4] FG: populate_system_data: cutoff_voltage = 3199901, nom_cap_uah = 3021000 p1p2 = 33, p2p3 = 5
<6>[    5.221723,4] FG: fg_batt_profile_init: Battery SOC: 11, V: 3645761uV
<3>[    5.221880,4] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    5.221887,4] msm_thermal:devmgr_client_hotplug_update Hotplug task is not initialized
<3>[    5.221894,4] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    5.221896,4] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    5.221898,4] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    5.221900,4] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    5.221902,4] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    5.221904,4] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<3>[    5.221906,4] msm_thermal:devmgr_client_cpufreq_update Frequency mitigation task is not initialized
<6>[    5.222889,4] FG: fg_cap_learning_check: SW_CC_SOC based learning init_CC_SOC=27383950
<6>[    5.222902,4] FG: fg_vbat_est_check: vbat(3645761),est-vbat(3772255),diff(126494),threshold(300000)
<12>[    5.222980,2] healthd: battery l=11 v=3645 t=37.5 h=2 st=2 c=177 fc=3021000 cc=0 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<12>[    5.223935,2] healthd: battery l=11 v=3645 t=37.5 h=2 st=2 c=177 fc=3021000 cc=0 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<6>[   22.635482,1] EXT4-fs (mmcblk0p52): mounted filesystem with ordered data mode. Opts: 
<7>[   22.635549,1] SELinux: initialized (dev mmcblk0p52, type ext4), uses xattr
<6>[   23.243949,0] EXT4-fs (mmcblk0p52): mounted filesystem with ordered data mode. Opts: 
<7>[   23.243965,0] SELinux: initialized (dev mmcblk0p52, type ext4), uses xattr
