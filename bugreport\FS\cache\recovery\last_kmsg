<6>[    0.000000,0] Booting Linux on physical CPU 0x0
<6>[    0.000000,0] Initializing cgroup subsys cpu
<6>[    0.000000,0] Initializing cgroup subsys cpuacct
<5>[    0.000000,0] Linux version 3.18.71-perf-gfde333e (hudsoncm@ilclbld57) (gcc version 4.9.x 20150123 (prerelease) (GCC) ) #1 SMP PREEMPT Tue Aug 13 15:23:08 CDT 2019
<6>[    0.000000,0] CPU: ARMv7 Processor [410fd034] revision 4 (ARMv7), cr=10c0383d
<6>[    0.000000,0] CPU: PIPT / VIPT nonaliasing data cache, VIPT aliasing instruction cache
<6>[    0.000000,0] Machine model: sanders
<6>[    0.000000,0] Reserved memory: reserved region for node 'other_ext_region@0': base 0x84300000, size 37 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'modem_region@0': base 0x86c00000, size 106 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'adsp_fw_region@0': base 0x8d600000, size 17 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'wcnss_fw_region@0': base 0x8e700000, size 7 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'dfps_data_mem@90000000': base 0x90000000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'splash_region@0x90001000': base 0x90001000, size 19 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'ramoops_mem_region': base 0xef000000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'tzlog_bck_region': base 0xeefe4000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'wdog_cpuctx_region': base 0xeefe6000, size 0 MiB
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x84300000, size 37 MiB
<6>[    0.000000,0] Reserved memory: initialized node other_ext_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x86c00000, size 106 MiB
<6>[    0.000000,0] Reserved memory: initialized node modem_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x8d600000, size 17 MiB
<6>[    0.000000,0] Reserved memory: initialized node adsp_fw_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x8e700000, size 7 MiB
<6>[    0.000000,0] Reserved memory: initialized node wcnss_fw_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'venus_region@0' node: base 0x8f800000, size 8 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0x8f800000, size 8 MiB
<6>[    0.000000,0] Reserved memory: initialized node venus_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'secure_region@0' node: base 0xf6400000, size 152 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf6400000, size 152 MiB
<6>[    0.000000,0] Reserved memory: initialized node secure_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'qseecom_region@0' node: base 0xf5400000, size 16 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf5400000, size 16 MiB
<6>[    0.000000,0] Reserved memory: initialized node qseecom_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'adsp_region@0' node: base 0xf5000000, size 4 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf5000000, size 4 MiB
<6>[    0.000000,0] Reserved memory: initialized node adsp_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'gpu_region@0' node: base 0x8f000000, size 8 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0x8f000000, size 8 MiB
<6>[    0.000000,0] Reserved memory: initialized node gpu_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] cma: Reserved 16 MiB at 0xf4000000
<6>[    0.000000,0] Memory policy: Data cache writealloc
<7>[    0.000000,0] On node 0 totalpages: 940131
<7>[    0.000000,0] free_area_init_node: node 0, pgdat c15fef80, node_mem_map e73f9000
<7>[    0.000000,0]   Normal zone: 1316 pages used for memmap
<7>[    0.000000,0]   Normal zone: 0 pages reserved
<7>[    0.000000,0]   Normal zone: 168448 pages, LIFO batch:31
<7>[    0.000000,0]   HighMem zone: 6364 pages used for memmap
<7>[    0.000000,0]   HighMem zone: 771683 pages, LIFO batch:31
<6>[    0.000000,0] psci: probing for conduit method from DT.
<6>[    0.000000,0] psci: PSCIv1.0 detected in firmware.
<6>[    0.000000,0] psci: Using standard PSCI v0.2 function IDs
<4>[    0.000000,0] PERCPU: max_distance=0xb000 too large for vmalloc space 0x0
<6>[    0.000000,0] PERCPU: Embedded 11 pages/cpu @e72ee000 s14912 r8192 d21952 u45056
<7>[    0.000000,0] pcpu-alloc: s14912 r8192 d21952 u45056 alloc=11*4096
<7>[    0.000000,0] pcpu-alloc: [0] 0 [0] 1 [0] 2 [0] 3 [0] 4 [0] 5 [0] 6 [0] 7 
<4>[    0.000000,0] Built 1 zonelists in Zone order, mobility grouping on.  Total pages: 938815
<5>[    0.000000,0] Kernel command line: sched_enable_hmp=1 sched_enable_power_aware=1 console=null androidboot.hardware=qcom user_debug=30 msm_rtb.filter=0x237 ehci-hcd.park=3 androidboot.bootdevice=7824900.sdhci lpm_levels.sleep_disabled=1 vmalloc=350M buildvariant=user androidboot.emmc=true androidboot.serialno=ZY32286WPB androidboot.baseband=msm androidboot.mode=normal androidboot.device=sanders androidboot.hwrev=0x8400 androidboot.radio=INDIA androidboot.powerup_reason=0x00004000 androidboot.bootreason=reboot msm_poweroff.download_mode=0 androidboot.fsg-id= androidboot.wifimacaddr=A8:96:75:05:41:09,A8:96:75:05:41:0A androidboot.btmacaddr=A8:96:75:05:41:08 mdss_mdp.panel=1:dsi:0:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0 androidboot.bootloader=0xC212 androidboot.carrier=retin androidboot.poweroff_alarm=0 androidboot.hardware.sku=XT1804 androidboot.secure_hardware=1 androidboot.bl_state=1 androidboot.cid=0x32 androidboot.uid=C035992300000000000000000000 androidboot.write_protect=1 androidboot.ve<6>[    0.000000,0] PID hash table entries: 4096 (order: 2, 16384 bytes)
<6>[    0.000000,0] Dentry cache hash table entries: 131072 (order: 7, 524288 bytes)
<6>[    0.000000,0] Inode-cache hash table entries: 65536 (order: 6, 262144 bytes)
<4>[    0.000000,0] Memory: 3469276K/3760524K available (13312K kernel code, 1076K rwdata, 5704K rodata, 506K init, 1902K bss, 82352K reserved, 208896K cma-reserved, 2857356K highmem)
<5>[    0.000000,0] Virtual kernel memory layout:
<5>[    0.000000,0]     vector  : 0xffff0000 - 0xffff1000   (   4 kB)
<5>[    0.000000,0]     fixmap  : 0xffc00000 - 0xfff00000   (3072 kB)
<5>[    0.000000,0] 	   vmalloc : 0xe9200000 - 0xff000000   ( 350 MB)
<5>[    0.000000,0] 	   lowmem  : 0xc0000000 - 0xe9200000   ( 658 MB)
<5>[    0.000000,0]     pkmap   : 0xbfe00000 - 0xc0000000   (   2 MB)
<5>[    0.000000,0]     modules : 0xbf000000 - 0xbfe00000   (  14 MB)
<5>[    0.000000,0]       .text : 0xc0008000 - 0xc0e00000   (14304 kB)
<5>[    0.000000,0]       .init : 0xc1400000 - 0xc147ea40   ( 507 kB)
<5>[    0.000000,0]       .data : 0xc1500000 - 0xc160d324   (1077 kB)
<5>[    0.000000,0]        .bss : 0xc160d324 - 0xc17e8c68   (1903 kB)
<6>[    0.000000,0] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=8, Nodes=1
<6>[    0.000000,0] HMP scheduling enabled.
<6>[    0.000000,0] Preemptible hierarchical RCU implementation.
<6>[    0.000000,0] 	RCU dyntick-idle grace-period acceleration is enabled.
<4>[    0.000000,0] 
<4>[    0.000000,0] **********************************************************
<4>[    0.000000,0] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** trace_printk() being used. Allocating extra memory.  **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** This means that this is a DEBUG kernel and it is     **
<4>[    0.000000,0] ** unsafe for produciton use.                           **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** If you see this message and you are not debugging    **
<4>[    0.000000,0] ** the kernel, report this immediately to your vendor!  **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
<4>[    0.000000,0] **********************************************************
<6>[    0.000000,0] NR_IRQS:16 nr_irqs:16 16
<4>[    0.000000,0] mpm_init_irq_domain(): Cannot find irq controller for qcom,gpio-parent
<3>[    0.000000,0] MPM 1 irq mapping errored -517
<6>[    0.000000,0] 	Offload RCU callbacks from all CPUs
<6>[    0.000000,0] 	Offload RCU callbacks from CPUs: 0-7.
<6>[    0.000000,0] Architected cp15 and mmio timer(s) running at 19.20MHz (virt/virt).
<6>[    0.000006,0] sched_clock: 56 bits at 19MHz, resolution 52ns, wraps every 3579139424256ns
<6>[    0.000020,0] Switching to timer-based delay loop, resolution 52ns
<6>[    0.000035,0] Switched to clocksource arch_sys_counter
<6>[    0.000922,0] Calibrating delay loop (skipped), value calculated using timer frequency.. 38.00 BogoMIPS (lpj=64000)
<6>[    0.000937,0] pid_max: default: 32768 minimum: 301
<6>[    0.001019,0] Security Framework initialized
<6>[    0.001034,0] SELinux:  Initializing.
<7>[    0.001070,0] SELinux:  Starting in permissive mode
<6>[    0.001113,0] Mount-cache hash table entries: 2048 (order: 1, 8192 bytes)
<6>[    0.001124,0] Mountpoint-cache hash table entries: 2048 (order: 1, 8192 bytes)
<6>[    0.001851,0] Initializing cgroup subsys freezer
<6>[    0.001898,0] CPU: Testing write buffer coherency: ok
<3>[    0.002459,0] /cpus/cpu@0 missing clock-frequency property
<3>[    0.002474,0] /cpus/cpu@1 missing clock-frequency property
<3>[    0.002489,0] /cpus/cpu@2 missing clock-frequency property
<3>[    0.002506,0] /cpus/cpu@3 missing clock-frequency property
<3>[    0.002524,0] /cpus/cpu@100 missing clock-frequency property
<3>[    0.002545,0] /cpus/cpu@101 missing clock-frequency property
<3>[    0.002566,0] /cpus/cpu@102 missing clock-frequency property
<3>[    0.002589,0] /cpus/cpu@103 missing clock-frequency property
<6>[    0.002657,0] Setting up static identity map for 0x10d2ea88 - 0x10d2eae0
<4>[    0.002987,0] NOHZ: local_softirq_pending 02
<4>[    0.003388,0] NOHZ: local_softirq_pending 02
<6>[    0.011066,0] MSM Memory Dump base table set up
<6>[    0.011098,0] MSM Memory Dump apps data table set up
<6>[    0.011162,0] Configuring XPU violations to be fatal errors
<6>[    0.012378,0] cpu_clock_pwr_init: Power clocks configured
<4>[    0.017396,1] CPU1: Booted secondary processor
<4>[    0.022301,2] CPU2: Booted secondary processor
<4>[    0.027174,3] CPU3: Booted secondary processor
<4>[    0.032132,4] CPU4: Booted secondary processor
<4>[    0.037045,5] CPU5: Booted secondary processor
<4>[    0.041929,6] CPU6: Booted secondary processor
<4>[    0.046903,7] CPU7: Booted secondary processor
<6>[    0.047120,0] Brought up 8 CPUs
<6>[    0.047164,0] SMP: Total of 8 processors activated (307.00 BogoMIPS).
<6>[    0.047174,0] CPU: All CPU(s) started in SVC mode.
<6>[    0.056615,2] VFP support v0.3: implementor 41 architecture 3 part 40 variant 3 rev 4
<6>[    0.066013,2] pinctrl core: initialized pinctrl subsystem
<6>[    0.066458,2] regulator-dummy: no parameters
<6>[    0.143178,2] NET: Registered protocol family 16
<6>[    0.149442,2] DMA: preallocated 256 KiB pool for atomic coherent allocations
<4>[    0.150282,2] msm_pm_tz_boot_init: set warmboot address failed
<3>[    0.150308,2] scm_call failed: func id 0x2000101, ret: -1, syscall returns: 0x0, 0x0, 0x0
<6>[    0.163471,2] cpuidle: using governor ladder
<6>[    0.176802,2] cpuidle: using governor menu
<6>[    0.190131,2] cpuidle: using governor qcom
<6>[    0.196734,2] platform soc:qcom,kgsl-hyp: assigned reserved memory node gpu_region@0
<6>[    0.222135,2] msm_watchdog b017000.qcom,wdt: wdog absent resource not present
<6>[    0.222584,2] msm_watchdog b017000.qcom,wdt: MSM Watchdog Initialized
<6>[    0.227842,2] platform soc:qcom,adsprpc-mem: assigned reserved memory node adsp_region@0
<4>[    0.230072,2] irq: no irq domain found for /soc/pinctrl@1000000 !
<3>[    0.230612,2] spmi_pmic_arb 200f000.qcom,spmi: PMIC Arb Version-2 0x20010000
<3>[    0.231363,2] spmi_pmic_arb 200f000.qcom,spmi: non-zero irq-accumulator[0]:0x20000000
<3>[    0.238855,2] spmi spmi-0: of_spmi_register_devices: invalid sid on /soc/qcom,spmi@200f000/qcom,pm8950@0
<6>[    0.239324,2] platform 4080000.qcom,mss: assigned reserved memory node modem_region@0
<6>[    0.239749,2] platform c200000.qcom,lpass: assigned reserved memory node adsp_fw_region@0
<6>[    0.239981,2] platform 1de0000.qcom,venus: assigned reserved memory node venus_region@0
<6>[    0.240563,2] platform a21b000.qcom,pronto: assigned reserved memory node wcnss_fw_region@0
<6>[    0.242264,2] apc_mem_acc_corner: 0 <--> 0 mV 
<6>[    0.243103,2] gfx_mem_acc_corner: 0 <--> 0 mV 
<6>[    0.255630,2] persistent_ram: persistent_ram: paddr: ef000000, vaddr: e9280000, buf size = 0x1fff4
<6>[    0.255654,2] persistent_ram: persistent_ram: paddr: ef020000, vaddr: e9300000, buf size = 0x3fff4
<6>[    0.257415,2] persistent_ram: persistent_ram: paddr: ef060000, vaddr: e9262000, buf size = 0x7f4
<6>[    0.258432,2] console [pstore-1] enabled
<6>[    0.258443,2] pstore: Registered ramoops as persistent store backend
<6>[    0.258456,2] ramoops: attached 0x80000@0xef000000, ecc: 0/0
<6>[    0.259944,2] hw-breakpoint: found 5 (+1 reserved) breakpoint and 4 watchpoint registers.
<6>[    0.259957,2] hw-breakpoint: maximum watchpoint size is 8 bytes.
<4>[    0.262001,2] __of_mpm_init(): MPM driver mapping exists
<4>[    0.263280,2] msm_rpm_glink_dt_parse: qcom,rpm-glink compatible not matches
<6>[    0.263293,2] msm_rpm_dev_probe: APSS-RPM communication over SMD
<6>[    0.263306,2] smd_open() before smd_init()
<3>[    0.265106,2] msm_mpm_dev_probe(): Cannot get clk resource for XO: -517
<3>[    0.270736,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.270913,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.271073,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.277181,1] GFX_LDO: msm_gfx_ldo_parse_dt: Unable to parse CX parameters rc=-517
<3>[    0.277202,1] GFX_LDO: msm_gfx_ldo_probe: Unable to pasrse dt rc=-517
<6>[    0.278721,1] pm8953_s5: 400 <--> 1140 mV at 870 mV normal idle 
<6>[    0.279071,1] pm8953_s5_avs_limit: 400 <--> 1140 mV 
<6>[    0.279220,1] spm_regulator_probe: name=pm8953_s5, range=LV, voltage=870000 uV, mode=AUTO, step rate=1200 uV/us
<6>[    0.287351,1] msm_thermal:vdd_restriction_reg_init Defer regulator vdd-dig probe
<3>[    0.287372,1] msm_thermal:probe_vdd_rstr Err regulator init. err:-517. KTM continues.
<6>[    0.287392,1] msm-thermal soc:qcom,msm-thermal: probe_vdd_rstr:Failed reading node=/soc/qcom,msm-thermal, key=qcom,max-freq-level. err=-517. KTM continues
<3>[    0.287407,1] msm_thermal:msm_thermal_dev_probe Failed reading node=/soc/qcom,msm-thermal, key=qcom,online-hotplug-core. err:-517
<6>[    0.288858,1] sps:sps is ready.
<6>[    0.292461,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Speed bin: 2 PVS Version: 0
<3>[    0.292703,1] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Get vdd-mx regulator!!!
<4>[    0.293301,2] msm_rpm_glink_dt_parse: qcom,rpm-glink compatible not matches
<6>[    0.293316,2] msm_rpm_dev_probe: APSS-RPM communication over SMD
<6>[    0.294260,0] pm8953_s1: 870 <--> 1156 mV at 1000 mV normal idle 
<6>[    0.295039,0] pm8953_s2_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.295583,0] pm8953_s2_floor_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.296087,0] pm8953_s2_level_ao: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.296806,0] pm8953_s3: 1225 mV normal idle 
<6>[    0.297499,0] pm8953_s4: 1900 <--> 2050 mV at 1900 mV normal idle 
<6>[    0.298218,0] pm8953_s7_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.298721,0] pm8953_s7_level_ao: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.299240,0] pm8953_s7_level_so: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.299925,0] pm8953_l1: 1000 <--> 1100 mV at 1000 mV normal idle 
<6>[    0.300665,0] pm8953_l2: 1200 mV normal idle 
<6>[    0.301364,0] pm8953_l3: 925 mV normal idle 
<6>[    0.302054,0] pm8953_l5: 1800 mV normal idle 
<6>[    0.303114,1] pm8953_l6: 1800 mV normal idle 
<6>[    0.303885,1] pm8953_l7: 1800 <--> 1900 mV at 1800 mV normal idle 
<6>[    0.304407,1] pm8953_l7_ao: 1800 <--> 1900 mV at 1800 mV normal idle 
<6>[    0.305121,1] pm8953_l8: 2900 mV normal idle 
<6>[    0.305838,1] pm8953_l9: 3000 <--> 3300 mV at 3000 mV normal idle 
<6>[    0.307265,1] pm8953_l10: 2850 mV normal idle 
<6>[    0.307989,1] pm8953_l11: 2950 mV normal idle 
<6>[    0.308712,1] pm8953_l12: 1800 <--> 2950 mV at 1800 mV normal idle 
<6>[    0.309459,1] pm8953_l13: 3125 mV normal idle 
<6>[    0.310229,1] pm8953_l16: 1800 mV normal idle 
<6>[    0.310918,1] pm8953_l17: 2800 mV normal idle 
<6>[    0.311608,1] pm8953_l19: 1200 <--> 1350 mV at 1200 mV normal idle 
<6>[    0.312297,1] pm8953_l22: 2800 mV normal idle 
<6>[    0.312995,1] pm8953_l23: 1200 mV normal idle 
<3>[    0.313514,1] msm_mpm_dev_probe(): Cannot get clk resource for XO: -517
<6>[    0.313861,1] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 1: target-volt = 580000 uV
<6>[    0.313875,1] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 2: target-volt = 650000 uV
<6>[    0.313889,1] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 3: target-volt = 720000 uV
<6>[    0.313906,1] GFX_LDO: msm_gfx_ldo_adjust_init_voltage: adjusted the open-loop voltage[1] 580000 -> 615000
<6>[    0.313920,1] GFX_LDO: msm_gfx_ldo_adjust_init_voltage: adjusted the open-loop voltage[2] 650000 -> 675000
<6>[    0.313934,1] GFX_LDO: msm_gfx_ldo_voltage_init: LDO-mode fuse disabled by default
<6>[    0.314236,1] msm_gfx_ldo: 0 <--> 0 mV at 0 mV 
<6>[    0.315062,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: speed bin = 2
<6>[    0.315077,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: CPR fusing revision = 3
<6>[    0.315090,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: foundry id = 2
<6>[    0.315103,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: CPR misc fuse value = 0
<6>[    0.315143,1] cpr4_msm8953_apss_read_fuse_data: apc_corner: Voltage boost fuse config = 0 boost = disable
<6>[    0.315278,1] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused   LowSVS: open-loop= 625000 uV
<6>[    0.315291,1] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused      SVS: open-loop= 700000 uV
<6>[    0.315303,1] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused      NOM: open-loop= 815000 uV
<6>[    0.315316,1] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused TURBO_L1: open-loop= 915000 uV
<6>[    0.315397,1] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused   LowSVS: quot[ 7]= 442
<6>[    0.315411,1] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused      SVS: quot[ 7]= 567, quot_offset[ 7]= 120
<6>[    0.315425,1] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused      NOM: quot[ 7]= 791, quot_offset[ 7]= 220
<6>[    0.315440,1] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused TURBO_L1: quot[ 7]= 978, quot_offset[ 7]= 185
<6>[    0.315810,1] cpr4_apss_init_aging: apc: sensor 6 aging init quotient diff = 12, aging RO scale = 2800 QUOT/V
<6>[    0.315992,1] cpr3_regulator_init_ctrl: apc: Default CPR mode = HW closed-loop
<6>[    0.316142,1] apc_corner: 0 <--> 0 mV at 0 mV 
<6>[    0.317755,2] msm_thermal:sensor_mgr_init_threshold threshold id already initialized
<6>[    0.318455,2] msm_thermal:vdd_restriction_reg_init Defer vdd rstr freq init.
<6>[    0.321558,2] qcom,gcc-8953 1800000.qcom,gcc: Venus speed bin: 2
<4>[    0.343152,2] branch_clk_handoff: gcc_usb_phy_cfg_ahb_clk clock is enabled in HW
<4>[    0.343171,2] branch_clk_handoff: even though ENABLE_BIT is not set
<6>[    0.345179,2] qcom,gcc-8953 1800000.qcom,gcc: Registered GCC clocks
<6>[    0.345387,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Speed bin: 2 PVS Version: 0
<3>[    0.347928,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,dfs-sid-c0
<3>[    0.347945,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No DFS SID tables found for Cluster-0
<3>[    0.347961,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,link-sid-c0
<3>[    0.347976,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No Link SID tables found for Cluster-0
<3>[    0.347993,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,lmh-sid-c0
<3>[    0.348007,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No LMH SID tables found for Cluster-0
<3>[    0.348018,2] ramp_lmh_sid: Use Default LMH SID
<3>[    0.348027,2] ramp_dfs_sid: Use Default DFS SID
<3>[    0.348037,2] ramp_link_sid: Use Default Link SID
<3>[    0.348090,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,dfs-sid-c1
<3>[    0.348105,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No DFS SID tables found for Cluster-1
<3>[    0.348120,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,link-sid-c1
<3>[    0.348134,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No Link SID tables found for Cluster-1
<3>[    0.348150,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,lmh-sid-c1
<3>[    0.348164,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No LMH SID tables found for Cluster-1
<3>[    0.348174,2] ramp_lmh_sid: Use Default LMH SID
<3>[    0.348183,2] ramp_dfs_sid: Use Default DFS SID
<3>[    0.348193,2] ramp_link_sid: Use Default Link SID
<6>[    0.348266,2] clock_rcgwr_init: RCGwR  Init Completed
<6>[    0.348682,2] populate_opp_table: clock-cpu-8953: OPP tables populated (cpu 3 and 7)
<6>[    0.348695,2] print_opp_table: clock_cpu: a53 C0: OPP voltage for 652800000: 1
<6>[    0.348706,2] print_opp_table: clock_cpu: a53 C0: OPP voltage for 2016000000: 7
<6>[    0.348716,2] print_opp_table: clock_cpu: a53 C1: OPP voltage for 652800000: 1
<6>[    0.348727,2] print_opp_table: clock_cpu: a53 C2: OPP voltage for 2016000000: 7
<6>[    0.350838,1] gcc-gfx-8953 1800000.qcom,gcc-gfx: Registered GCC GFX clocks.
<3>[    0.410081,1] msm_cpuss_dump soc:cpuss_dump: Couldn't get memory for dumping
<3>[    0.410110,1] msm_cpuss_dump soc:cpuss_dump: Couldn't get memory for dumping
<6>[    0.413772,1] KPI: Bootloader start count = 73260
<6>[    0.413790,1] KPI: Bootloader end count = 108342
<6>[    0.413800,1] KPI: Bootloader display count = 3144342802
<6>[    0.413810,1] KPI: Bootloader load kernel count = 7749
<6>[    0.413820,1] KPI: Kernel MPM timestamp = 171874
<6>[    0.413829,1] KPI: Kernel MPM Clock frequency = 32768
<6>[    0.413856,1] socinfo_print: v0.10, id=293, ver=1.1, raw_id=70, raw_ver=1, hw_plat=8, hw_plat_ver=65536
<6>[    0.413856,1]  accessory_chip=0, hw_plat_subtype=0, pmic_model=65558, pmic_die_revision=65536 foundry_id=3 serial_number=597243328
<6>[    0.415092,1] dummy_vreg: no parameters
<6>[    0.415421,1] vci_fci: no parameters
<5>[    0.416811,1] SCSI subsystem initialized
<6>[    0.417694,1] usbcore: registered new interface driver usbfs
<6>[    0.417773,1] usbcore: registered new interface driver hub
<6>[    0.418042,3] usbcore: registered new device driver usb
<6>[    0.419184,3] i2c-msm-v2 78b6000.i2c: probing driver i2c-msm-v2
<3>[    0.419519,3] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.419534,3] i2c-msm-v2 78b6000.i2c: msm_bus_scale_register_client(mstr-id:86):0 (not a problem)
<6>[    0.421024,3] i2c-msm-v2 78b7000.i2c: probing driver i2c-msm-v2
<3>[    0.421272,3] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.421287,3] i2c-msm-v2 78b7000.i2c: msm_bus_scale_register_client(mstr-id:86):0 (not a problem)
<6>[    0.421512,0] i2c-msm-v2 78b7000.i2c: irq:50 when no active transfer
<6>[    0.422195,3] i2c-msm-v2 7af5000.i2c: probing driver i2c-msm-v2
<3>[    0.422396,3] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.422409,3] i2c-msm-v2 7af5000.i2c: msm_bus_scale_register_client(mstr-id:84):0 (not a problem)
<6>[    0.423863,3] i2c-msm-v2 7af7000.i2c: probing driver i2c-msm-v2
<3>[    0.424084,3] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.424098,3] i2c-msm-v2 7af7000.i2c: msm_bus_scale_register_client(mstr-id:84):0 (not a problem)
<6>[    0.425614,3] media: Linux media interface: v0.10
<6>[    0.425688,3] Linux video capture interface: v2.00
<6>[    0.425787,3] EDAC MC: Ver: 3.0.0
<6>[    0.526958,3] cpufreq: driver msm up and running
<6>[    0.527370,3] platform soc:qcom,ion:qcom,ion-heap@8: assigned reserved memory node secure_region@0
<6>[    0.527544,3] platform soc:qcom,ion:qcom,ion-heap@27: assigned reserved memory node qseecom_region@0
<6>[    0.527743,3] ION heap system created
<6>[    0.527851,3] ION heap mm created at 0xf6400000 with size 9800000
<6>[    0.527861,3] ION heap qsecom created at 0xf5400000 with size 1000000
<3>[    0.528434,3] msm_bus_fabric_init_driver
<6>[    0.537767,3] qcom,qpnp-power-on qpnp-power-on-1: PMIC@SID0 Power-on reason: Triggered from Hard Reset and 'warm' boot
<6>[    0.537791,3] qcom,qpnp-power-on qpnp-power-on-1: PMIC@SID0: Power-off reason: Triggered from PS_HOLD (PS_HOLD/MSM controlled shutdown)
<6>[    0.537941,3] input: qpnp_pon as /devices/virtual/input/input0
<6>[    0.538282,3] pon_spare_reg: no parameters
<6>[    0.538343,3] qcom,qpnp-power-on qpnp-power-on-13: No PON config. specified
<6>[    0.538392,3] qcom,qpnp-power-on qpnp-power-on-13: PMIC@SID2 Power-on reason: Triggered from PON1 (secondary PMIC) and 'warm' boot
<6>[    0.538408,3] qcom,qpnp-power-on qpnp-power-on-13: PMIC@SID2: Power-off reason: Triggered from PS_HOLD (PS_HOLD/MSM controlled shutdown)
<6>[    0.538567,3] PMIC@SID0: (null) v1.0 options: 2, 2, 0, 0
<6>[    0.538657,3] PMIC@SID2: PMI8950 v2.0 options: 0, 0, 0, 0
<3>[    0.539538,3] ipa ipa2_uc_state_check:296 uC interface not initialized
<3>[    0.539553,3] ipa ipa_sps_irq_control_all:942 EP (2) not allocated.
<3>[    0.539559,3] ipa ipa_sps_irq_control_all:942 EP (5) not allocated.
<6>[    0.540894,3] sps:BAM 0x07904000 is registered.
<6>[    0.541357,3] sps:BAM 0x07904000 (va:0xe97c0000) enabled: ver:0x27, number of pipes:20
<6>[    0.544338,5] IPA driver initialization was successful.
<6>[    0.545488,5] gdsc_venus: no parameters
<6>[    0.545706,5] gdsc_mdss: no parameters
<6>[    0.546004,5] gdsc_jpeg: no parameters
<6>[    0.546359,5] gdsc_vfe: no parameters
<6>[    0.546720,5] gdsc_vfe1: no parameters
<6>[    0.546925,5] gdsc_cpp: no parameters
<6>[    0.547072,5] gdsc_oxili_gx: no parameters
<6>[    0.547119,5] gdsc_oxili_gx: supplied by msm_gfx_ldo
<6>[    0.547286,5] gdsc_venus_core0: fast normal 
<6>[    0.547440,5] gdsc_oxili_cx: no parameters
<6>[    0.547572,5] gdsc_usb30: no parameters
<6>[    0.548470,5] mdss_pll_probe: MDSS pll label = MDSS DSI 0 PLL
<6>[    0.548477,5] mdss_pll_probe: mdss_pll_probe: label=MDSS DSI 0 PLL PLL SSC enabled
<4>[    0.548493,5] mdss_pll_util_parse_dt_supply: : error reading ulp load. rc=-22
<6>[    0.548979,5] dsi_pll_clock_register_8996: Registered DSI PLL ndx=0 clocks successfully
<6>[    0.549000,5] mdss_pll_probe: MDSS pll label = MDSS DSI 1 PLL
<6>[    0.549006,5] mdss_pll_probe: mdss_pll_probe: label=MDSS DSI 1 PLL PLL SSC enabled
<4>[    0.549019,5] mdss_pll_util_parse_dt_supply: : error reading ulp load. rc=-22
<3>[    0.550109,5] pll_is_pll_locked_8996: DSI PLL ndx=1 status=0 failed to Lock
<6>[    0.550441,5] dsi_pll_clock_register_8996: Registered DSI PLL ndx=1 clocks successfully
<6>[    0.550872,5] msm_iommu 1e00000.qcom,iommu: device apps_iommu (model: 500) mapped at e9b80000, with 21 ctx banks
<6>[    0.555610,5] msm_iommu_ctx 1e20000.qcom,iommu-ctx: context adsp_elf using bank 0
<6>[    0.555735,5] msm_iommu_ctx 1e21000.qcom,iommu-ctx: context adsp_sec_pixel using bank 1
<6>[    0.555852,5] msm_iommu_ctx 1e22000.qcom,iommu-ctx: context mdp_1 using bank 2
<6>[    0.555969,5] msm_iommu_ctx 1e23000.qcom,iommu-ctx: context venus_fw using bank 3
<6>[    0.556089,5] msm_iommu_ctx 1e24000.qcom,iommu-ctx: context venus_sec_non_pixel using bank 4
<6>[    0.556206,5] msm_iommu_ctx 1e25000.qcom,iommu-ctx: context venus_sec_bitstream using bank 5
<6>[    0.556324,5] msm_iommu_ctx 1e26000.qcom,iommu-ctx: context venus_sec_pixel using bank 6
<6>[    0.556467,5] msm_iommu_ctx 1e28000.qcom,iommu-ctx: context pronto_pil using bank 8
<6>[    0.556612,5] msm_iommu_ctx 1e29000.qcom,iommu-ctx: context q6 using bank 9
<6>[    0.556757,5] msm_iommu_ctx 1e2a000.qcom,iommu-ctx: context periph_rpm using bank 10
<6>[    0.556896,5] msm_iommu_ctx 1e2b000.qcom,iommu-ctx: context lpass using bank 11
<6>[    0.557041,5] msm_iommu_ctx 1e2f000.qcom,iommu-ctx: context adsp_io using bank 15
<6>[    0.557180,5] msm_iommu_ctx 1e30000.qcom,iommu-ctx: context adsp_opendsp using bank 16
<6>[    0.557319,5] msm_iommu_ctx 1e31000.qcom,iommu-ctx: context adsp_shared using bank 17
<6>[    0.557459,5] msm_iommu_ctx 1e32000.qcom,iommu-ctx: context cpp using bank 18
<6>[    0.557602,5] msm_iommu_ctx 1e33000.qcom,iommu-ctx: context jpeg_enc0 using bank 19
<6>[    0.557747,5] msm_iommu_ctx 1e34000.qcom,iommu-ctx: context vfe using bank 20
<6>[    0.557889,5] msm_iommu_ctx 1e35000.qcom,iommu-ctx: context mdp_0 using bank 21
<6>[    0.558030,5] msm_iommu_ctx 1e36000.qcom,iommu-ctx: context venus_ns using bank 22
<6>[    0.558170,5] msm_iommu_ctx 1e38000.qcom,iommu-ctx: context ipa using bank 24
<6>[    0.558310,5] msm_iommu_ctx 1e37000.qcom,iommu-ctx: context access_control using bank 23
<6>[    0.560065,5] arm-smmu 1c40000.arm,smmu-kgsl: regulator defer delay 80
<6>[    0.561660,5] Advanced Linux Sound Architecture Driver Initialized.
<6>[    0.562309,5] Bluetooth: e6e05ed8
<6>[    0.562328,5] NET: Registered protocol family 31
<6>[    0.562334,5] Bluetooth: e6e05ed8
<6>[    0.562341,5] Bluetooth: e6e05ed0Bluetooth: e6e05ec0
<6>[    0.562372,5] Bluetooth: e6e05ec0<6>[    0.562612,5] cfg80211: Calling CRDA to update world regulatory domain
<6>[    0.562627,5] cfg80211: World regulatory domain updated:
<6>[    0.562632,5] cfg80211:  DFS Master region: unset
<6>[    0.562636,5] cfg80211:   (start_freq - end_freq @ bandwidth), (max_antenna_gain, max_eirp), (dfs_cac_time)
<6>[    0.562644,5] cfg80211:   (2402000 KHz - 2472000 KHz @ 40000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.562650,5] cfg80211:   (2457000 KHz - 2482000 KHz @ 40000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.562656,5] cfg80211:   (2474000 KHz - 2494000 KHz @ 20000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.562661,5] cfg80211:   (5170000 KHz - 5250000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.562667,5] cfg80211:   (5250000 KHz - 5330000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.562673,5] cfg80211:   (5490000 KHz - 5710000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.562679,5] cfg80211:   (5735000 KHz - 5835000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.562684,5] cfg80211:   (57240000 KHz - 63720000 KHz @ 2160000 KHz), (N/A, 0 mBm), (N/A)
<6>[    0.563021,1] ibb_reg: 4600 <--> 6000 mV at 5500 mV 
<6>[    0.563248,1] lab_reg: 4600 <--> 6000 mV at 5500 mV 
<6>[    0.564984,6] Switched to clocksource arch_sys_counter
<6>[    0.591193,6] NET: Registered protocol family 2
<6>[    0.591522,6] TCP established hash table entries: 8192 (order: 3, 32768 bytes)
<6>[    0.591559,6] TCP bind hash table entries: 8192 (order: 4, 65536 bytes)
<6>[    0.591617,6] TCP: Hash tables configured (established 8192 bind 8192)
<6>[    0.591644,6] TCP: reno registered
<6>[    0.591652,6] UDP hash table entries: 512 (order: 2, 16384 bytes)
<6>[    0.591669,6] UDP-Lite hash table entries: 512 (order: 2, 16384 bytes)
<6>[    0.591775,6] NET: Registered protocol family 1
<6>[    0.592855,4] gcc-mdss-8953 1800000.qcom,gcc-mdss: Registered GCC MDSS clocks.
<6>[    0.593328,4] Trying to unpack rootfs image as initramfs...
<6>[    0.725822,4] Freeing initrd memory: 6880K
<6>[    0.728147,4] hw perfevents: enabled with ARMv8 Cortex-A53 PMU driver, 7 counters available
<6>[    0.731212,5] futex hash table entries: 2048 (order: 5, 131072 bytes)
<6>[    0.731281,5] audit: initializing netlink subsys (disabled)
<5>[    0.731315,5] audit: type=2000 audit(0.730:1): initialized
<4>[    0.731616,5] vmscan: error setting kswapd cpu affinity mask
<5>[    0.734907,5] VFS: Disk quotas dquot_6.5.2
<4>[    0.734986,5] Dquot-cache hash table entries: 1024 (order 0, 4096 bytes)
<6>[    0.735791,5] exFAT: Version 1.2.9
<6>[    0.736212,5] Registering sdcardfs 0.1
<6>[    0.736319,5] fuse init (API version 7.23)
<7>[    0.736622,5] SELinux:  Registering netfilter hooks
<6>[    0.738191,5] bounce: pool size: 64 pages
<6>[    0.738270,5] Block layer SCSI generic (bsg) driver version 0.4 loaded (major 246)
<6>[    0.738280,5] io scheduler noop registered
<6>[    0.738287,5] io scheduler deadline registered
<6>[    0.738304,5] io scheduler cfq registered (default)
<3>[    0.741332,5] msm_dss_get_res_byname: 'vbif_nrt_phys' resource not found
<3>[    0.741342,5] mdss_mdp_probe+0x1a0/0x10d8->msm_dss_ioremap_byname: 'vbif_nrt_phys' msm_dss_get_res_byname failed
<3>[    0.741765,5] mdss_mdp_irq_clk_register: unable to get clk: lut_clk
<3>[    0.742272,5] No change in context(0==0), skip
<6>[    0.742976,5] mdss_mdp_pipe_addr_setup: type:0 ftchid:-1 xinid:0 num:0 rect:0 ndx:0x1 prio:0
<6>[    0.742994,5] mdss_mdp_pipe_addr_setup: type:1 ftchid:-1 xinid:1 num:3 rect:0 ndx:0x8 prio:1
<6>[    0.743000,5] mdss_mdp_pipe_addr_setup: type:1 ftchid:-1 xinid:5 num:4 rect:0 ndx:0x10 prio:2
<6>[    0.743015,5] mdss_mdp_pipe_addr_setup: type:2 ftchid:-1 xinid:2 num:6 rect:0 ndx:0x40 prio:3
<6>[    0.743030,5] mdss_mdp_pipe_addr_setup: type:3 ftchid:-1 xinid:7 num:10 rect:0 ndx:0x400 prio:0
<3>[    0.743042,5] mdss_mdp_parse_dt_handler: Error from prop qcom,mdss-pipe-sw-reset-off : u32 array read
<3>[    0.743141,5] mdss_mdp_parse_dt_handler: Error from prop qcom,mdss-ib-factor-overlap : u32 array read
<6>[    0.743357,5] xlog_status: enable:0, panic:1, dump:2
<6>[    0.743897,5] mdss_mdp_probe: mdss version = 0x10100000, bootloader display is on, num 1, intf_sel=0x00000100
<3>[    0.745305,5] mdss_smmu_util_parse_dt_clock: clocks are not defined
<6>[    0.745329,5] mdss_smmu_probe: iommu v2 domain[0] mapping and clk register successful!
<3>[    0.745351,5] mdss_smmu_util_parse_dt_clock: clocks are not defined
<6>[    0.745360,5] mdss_smmu_probe: iommu v2 domain[2] mapping and clk register successful!
<4>[    0.746348,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.746361,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.746373,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<6>[    0.746848,5] mdss_dsi_ctrl_probe: DSI Ctrl name = MDSS DSI CTRL->0
<6>[    0.747229,5] mdss_panel_parse_panel_config_dt: BL: panel=mipi_mot_vid_djn_1080p_550, manufacture_id(0xDA)= 0x1A controller_ver(0xDB)= 0xD5 controller_drv_ver(0XDC)= 0x45, full=0x000000000045D51A
<6>[    0.747238,5] mdss_dsi_find_panel_of_node: cmdline:0:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0 panel_name:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0
<6>[    0.747284,5] mdss_dsi_panel_init: Panel Name = mipi_mot_vid_djn_1080p_550
<6>[    0.747443,5] mdss_dsi_panel_timing_from_dt: found new timing "qcom,mdss_dsi_mot_djn_550_1080p_vid_v0" (e6e05788)
<3>[    0.747461,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-post-panel-on-command
<3>[    0.747471,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-timing-switch-command
<4>[    0.747476,5] mdss_dsi_panel_get_dsc_cfg_np: cannot find dsc config node:
<3>[    0.747589,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-idle-on-command
<3>[    0.747598,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-idle-off-command
<6>[    0.747627,5] mdss_dsi_parse_panel_features: ulps feature disabled
<6>[    0.747635,5] mdss_dsi_parse_panel_features: ulps during suspend feature disabled
<6>[    0.747642,5] mdss_dsi_parse_dms_config: dynamic switch feature enabled: 0
<3>[    0.747726,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-on
<3>[    0.747735,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-off
<6>[    0.747778,5] mdss_panel_parse_param_prop: HBM feature enabled with 2 dt cmds
<6>[    0.747783,5] mdss_panel_parse_param_prop: HBM type = 1
<6>[    0.747818,5] mdss_panel_parse_param_prop: CABC feature enabled with 3 dt cmds
<3>[    0.747827,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-on
<3>[    0.747836,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-off
<4>[    0.747855,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.747865,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.747876,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<3>[    0.748033,5] mdss_dsi_parse_gpio_params:4125, TE gpio not specified
<6>[    0.748038,5] mdss_dsi_parse_gpio_params: bklt_en gpio not specified
<3>[    0.748072,5] msm_dss_get_res_byname: 'dsi_phy_regulator' resource not found
<3>[    0.748082,5] mdss_dsi_retrieve_ctrl_resources+0x124/0x1b8->msm_dss_ioremap_byname: 'dsi_phy_regulator' msm_dss_get_res_byname failed
<6>[    0.748088,5] mdss_dsi_retrieve_ctrl_resources: ctrl_base=e9782000 ctrl_size=400 phy_base=e9790400 phy_size=580
<6>[    0.748158,5] dsi_panel_device_register: Continuous splash enabled
<6>[    0.748333,5] mdss_register_panel: adding framebuffer device 1a94000.qcom,mdss_dsi_ctrl0
<6>[    0.749707,5] mdss_dsi_ctrl_probe: Dsi Ctrl->0 initialized, DSI rev:0x10040002, PHY rev:0x2
<6>[    0.749822,5] mdss_dsi_status_init: DSI status check interval:8000
<6>[    0.750470,5] mdss_register_panel: adding framebuffer device soc:qcom,mdss_wb_panel
<6>[    0.750884,5] mdss_fb_probe: fb0: split_mode:0 left:0 right:0
<6>[    0.751292,5] mdss_fb_register: FrameBuffer[0] 1080x1920 registered successfully!
<6>[    0.751566,5] mdss_fb_probe: fb1: split_mode:0 left:0 right:0
<6>[    0.751641,5] mdss_fb_register: FrameBuffer[1] 640x640 registered successfully!
<3>[    0.751724,5] mdss_mdp_splash_parse_dt: splash mem child node is not present
<6>[    0.751744,5] anx7805 anx7805_init: anx7805_init
<6>[    0.751770,1] anx7805 anx7805_init_async: anx7805_init_async
<3>[    0.753725,5] IPC_RTR: msm_ipc_router_smd_driver_register Already driver registered IPCRTR
<3>[    0.753745,5] IPC_RTR: msm_ipc_router_smd_driver_register Already driver registered IPCRTR
<6>[    0.757609,5] In memshare_probe, Memshare probe success
<5>[    0.759026,5] msm_rpm_log_probe: OK
<6>[    0.759867,5] subsys-pil-tz soc:qcom,kgsl-hyp: for a506_zap segments only will be dumped.
<6>[    0.761399,5] subsys-pil-tz 1de0000.qcom,venus: for venus segments only will be dumped.
<6>[    0.763269,5] mmi_unit_info (SMEM) for modem: version = 0x03, device = 'sanders', radio = 0x0, radio_str = 'INDIA', system_rev = 0x8400, system_serial = 0xc035992300000000, machine = 'Qualcomm Technologies, Inc. MSM ', barcode = 'ZY32286WPB', baseband = '', carrier = 'retin', pu_reason = 0x00004000
<3>[    0.763299,5] ACPU Bin is not available.
<6>[    0.763344,5] mmi_storage_info :eMMC: 64GB SAMSUNG RC14MB FV=0000000000000007
<6>[    0.763745,5] msm_serial_hs module loaded
<6>[    0.771845,6] platform 1c40000.qcom,kgsl-iommu:gfx3d_secure: assigned reserved memory node secure_region@0
<6>[    0.776570,6] brd: module loaded
<6>[    0.778063,6] loop: module loaded
<6>[    0.778329,6] zram: Added device: zram0
<6>[    0.778624,6] QSEECOM: qseecom_probe: qseecom.qsee_version = 0x1001000
<4>[    0.778647,6] QSEECOM: qseecom_retrieve_ce_data: Device does not support PFE
<6>[    0.778656,6] QSEECOM: qseecom_probe: qseecom clocks handled by other subsystem
<4>[    0.778663,6] QSEECOM: qseecom_probe: qsee reentrancy support phase is not defined, setting to default 0
<4>[    0.779114,6] QSEECOM: qseecom_probe: qseecom.whitelist_support = 1
<6>[    0.780491,6] alsa-to-h2w soc:alsa_to_h2w: alsa_to_h2w_probe success
<4>[    0.781103,6] i2c-core: driver [tabla-i2c-core] using legacy suspend method
<4>[    0.781108,6] i2c-core: driver [tabla-i2c-core] using legacy resume method
<4>[    0.781171,6] i2c-core: driver [wcd9xxx-i2c-core] using legacy suspend method
<4>[    0.781176,6] i2c-core: driver [wcd9xxx-i2c-core] using legacy resume method
<4>[    0.781239,6] i2c-core: driver [tasha-i2c-core] using legacy suspend method
<4>[    0.781244,6] i2c-core: driver [tasha-i2c-core] using legacy resume method
<6>[    0.781475,6] Loading pn544 driver
<6>[    0.781583,6] nfc: succeed in obtaining nfc_clk from msm pmic
<4>[    0.781745,6] 5-0028 supply vdd not found, using dummy regulator
<6>[    0.782050,6] QCE50: __qce_get_device_tree_data: BAM Apps EE is not defined, setting to default 1
<6>[    0.782560,6] qce 720000.qcedev: Qualcomm Crypto 5.3.3 device found @0x720000
<6>[    0.782570,6] qce 720000.qcedev: CE device = 0x0
<6>[    0.782570,6] IO base, CE = 0xe9b40000
<6>[    0.782570,6] Consumer (IN) PIPE 2,    Producer (OUT) PIPE 3
<6>[    0.782570,6] IO base BAM = 0x00000000
<6>[    0.782570,6] BAM IRQ 59
<6>[    0.782570,6] Engines Availability = 0x2010853
<6>[    0.782728,6] sps:BAM 0x00704000 is registered.
<6>[    0.782889,6] sps:BAM 0x00704000 (va:0xea840000) enabled: ver:0x27, number of pipes:8
<6>[    0.783076,6] QCE50: qce_sps_init:  Qualcomm MSM CE-BAM at 0x0000000000704000 irq 59
<6>[    0.785747,6] QCE50: __qce_get_device_tree_data: BAM Apps EE is not defined, setting to default 1
<6>[    0.786586,6] qcrypto 720000.qcrypto: Qualcomm Crypto 5.3.3 device found @0x720000
<6>[    0.786594,6] qcrypto 720000.qcrypto: CE device = 0x0
<6>[    0.786594,6] IO base, CE = 0xea880000
<6>[    0.786594,6] Consumer (IN) PIPE 4,    Producer (OUT) PIPE 5
<6>[    0.786594,6] IO base BAM = 0x00000000
<6>[    0.786594,6] BAM IRQ 59
<6>[    0.786594,6] Engines Availability = 0x2010853
<6>[    0.786866,6] QCE50: qce_sps_init:  Qualcomm MSM CE-BAM at 0x0000000000704000 irq 59
<6>[    0.789025,6] qcrypto 720000.qcrypto: qcrypto-ecb-aes
<6>[    0.789096,6] qcrypto 720000.qcrypto: qcrypto-cbc-aes
<6>[    0.789167,6] qcrypto 720000.qcrypto: qcrypto-ctr-aes
<6>[    0.789237,6] qcrypto 720000.qcrypto: qcrypto-ecb-des
<6>[    0.789310,6] qcrypto 720000.qcrypto: qcrypto-cbc-des
<6>[    0.789380,6] qcrypto 720000.qcrypto: qcrypto-ecb-3des
<6>[    0.789450,6] qcrypto 720000.qcrypto: qcrypto-cbc-3des
<6>[    0.789525,6] qcrypto 720000.qcrypto: qcrypto-xts-aes
<6>[    0.789596,6] qcrypto 720000.qcrypto: qcrypto-sha1
<6>[    0.789666,6] qcrypto 720000.qcrypto: qcrypto-sha256
<6>[    0.789738,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-aes
<6>[    0.789809,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-des
<6>[    0.789881,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-3des
<6>[    0.789954,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-aes
<6>[    0.790026,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-des
<6>[    0.790113,6] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-3des
<6>[    0.790186,6] qcrypto 720000.qcrypto: qcrypto-hmac-sha1
<6>[    0.790258,6] qcrypto 720000.qcrypto: qcrypto-hmac-sha256
<6>[    0.790331,6] qcrypto 720000.qcrypto: qcrypto-aes-ccm
<6>[    0.790403,6] qcrypto 720000.qcrypto: qcrypto-rfc4309-aes-ccm
<3>[    0.791091,6] qcom_ice_get_device_tree_data: No vdd-hba-supply regulator, assuming not needed
<6>[    0.791185,6] ICE IRQ = 60
<6>[    0.791894,6] SCSI Media Changer driver v0.25 
<3>[    0.793286,6] spi_qsd 7af8000.spi: init_resources: unable to get core_clk
<3>[    0.794070,6] sps: BAM device 0x07884000 is not registered yet.
<6>[    0.794209,6] sps:BAM 0x07884000 is registered.
<6>[    0.794720,6] sps:BAM 0x07884000 (va:0xe9b20000) enabled: ver:0x19, number of pipes:12
<6>[    0.795385,6] tun: Universal TUN/TAP device driver, 1.6
<6>[    0.795390,6] tun: (C) 1999-2004 Max Krasnyansky <<EMAIL>>
<6>[    0.795444,6] PPP generic driver version 2.4.2
<6>[    0.795511,6] PPP BSD Compression module registered
<6>[    0.795518,6] PPP Deflate Compression module registered
<6>[    0.795536,6] PPP MPPE Compression module registered
<6>[    0.795545,6] NET: Registered protocol family 24
<6>[    0.796139,6] wcnss_wlan probed in built-in mode
<6>[    0.796778,6] pegasus: v0.9.3 (2013/04/25), Pegasus/Pegasus II USB Ethernet driver
<6>[    0.796842,6] usbcore: registered new interface driver pegasus
<6>[    0.796879,6] usbcore: registered new interface driver asix
<6>[    0.796909,6] usbcore: registered new interface driver ax88179_178a
<6>[    0.796940,6] usbcore: registered new interface driver cdc_ether
<6>[    0.796970,6] usbcore: registered new interface driver net1080
<6>[    0.797000,6] usbcore: registered new interface driver cdc_subset
<6>[    0.797029,6] usbcore: registered new interface driver zaurus
<6>[    0.797060,6] usbcore: registered new interface driver MOSCHIP usb-ethernet driver
<6>[    0.797185,6] usbcore: registered new interface driver cdc_ncm
<6>[    0.797505,6] msm_sharedmem: sharedmem_register_qmi: qmi init successful
<3>[    0.798781,6] scm_call failed: func id 0x2000c16, ret: -1, syscall returns: 0x0, 0x0, 0x0
<6>[    0.798788,6] hyp_assign_table: Failed to assign memory protection, ret = -5
<3>[    0.798795,6] msm_sharedmem: setup_shared_ram_perms: hyp_assign_phys failed IPA=0x0160xf4500000 size=1572864 err=-5
<6>[    0.798875,6] msm_sharedmem: msm_sharedmem_probe: Device created for client 'rmtfs'
<3>[    0.802450,6] msm-dwc3 7000000.ssusb: unable to get dbm device
<6>[    0.803444,6] ehci_hcd: USB 2.0 'Enhanced' Host Controller (EHCI) Driver
<6>[    0.803452,6] ehci-msm: Qualcomm On-Chip EHCI Host Controller
<6>[    0.803717,6] usbcore: registered new interface driver cdc_acm
<6>[    0.803722,6] cdc_acm: USB Abstract Control Model driver for USB modems and ISDN adapters
<6>[    0.803765,6] usbcore: registered new interface driver usb-storage
<6>[    0.803792,6] usbcore: registered new interface driver ums-alauda
<6>[    0.803818,6] usbcore: registered new interface driver ums-cypress
<6>[    0.803845,6] usbcore: registered new interface driver ums-datafab
<6>[    0.803871,6] usbcore: registered new interface driver ums-freecom
<6>[    0.803897,6] usbcore: registered new interface driver ums-isd200
<6>[    0.803923,6] usbcore: registered new interface driver ums-jumpshot
<6>[    0.803949,6] usbcore: registered new interface driver ums-karma
<6>[    0.803976,6] usbcore: registered new interface driver ums-onetouch
<6>[    0.804002,6] usbcore: registered new interface driver ums-sddr09
<6>[    0.804029,6] usbcore: registered new interface driver ums-sddr55
<6>[    0.804055,6] usbcore: registered new interface driver ums-usbat
<6>[    0.804125,6] usbcore: registered new interface driver usbserial
<6>[    0.804157,6] usbcore: registered new interface driver usb_ehset_test
<6>[    0.804628,6] gbridge_init: gbridge_init successs.
<6>[    0.804858,6] mousedev: PS/2 mouse device common for all mice
<6>[    0.805004,6] usbcore: registered new interface driver xpad
<6>[    0.805092,6] ft5x06_ts 3-0038: processing modifier config_modifier-charger[0]
<5>[    0.805098,6] using charger detection
<6>[    0.805197,6] ft5x06_ts 3-0038: processing modifier config_modifier-fps[1]
<5>[    0.805202,6] sing fingerprint sensor detection
<5>[    0.805209,6] using touch clip area in fps-active
<6>[    0.805333,6] input: ft5x06_ts as /devices/soc/78b7000.i2c/i2c-3/3-0038/input/input1
<3>[    1.033574,6] i2c-msm-v2 78b7000.i2c: msm_bus_scale_register_client(mstr-id:86):0xe (ok)
<6>[    1.033781,6] ft5x06_ts 3-0038: Device ID = 0x54
<6>[    1.033892,6] assigned minor 56
<6>[    1.034006,6] ft5x06_ts 3-0038: Create proc entry success
<6>[    1.034161,6] ft5x06_ts 3-0038: report rate = 110Hz
<6>[    1.034759,6] ft5x06_ts 3-0038: Firmware version = 6.0.0
<6>[    1.034909,6] vendor id 0x04 panel supplier is biel
<6>[    1.035083,6] ft5x06_ts 3-0038: Firmware id = 0x0001
<3>[    1.035158,6] ft5x06_ts 3-0038: Failed to register fps_notifier: -19
<3>[    1.035624,6] [NVT-ts] nvt_driver_init 1865: start
<6>[    1.035652,6] nvt_driver_init: finished
<6>[    1.036168,6] input: hbtp_vm as /devices/virtual/input/input2
<3>[    1.036936,6] fpc1020 spi8.0: Unable to read wakelock time
<6>[    1.037042,6] input: fpc1020 as /devices/virtual/input/input3
<6>[    1.037093,6] fpc1020 spi8.0: fpc1020_probe: ok
<6>[    1.037117,6] Driver ltr559 init.
<3>[    1.170232,0] i2c-msm-v2 7af7000.i2c: msm_bus_scale_register_client(mstr-id:84):0xf (ok)
<4>[    1.170441,0] ltr559_check_chip_id read the  LTR559_MANUFAC_ID is 0x5
<6>[    1.184644,0] ltr559_gpio_irq: INT No. 254
<6>[    1.184750,0] input: ltr559-ps as /devices/soc/7af7000.i2c/i2c-7/7-0023/input/input4
<4>[    1.184800,0] ltr559_probe input device success.
<6>[    1.185413,0] qcom,qpnp-rtc qpnp-rtc-8: rtc core: registered qpnp_rtc as rtc0
<6>[    1.185547,0] i2c /dev entries driver
<3>[    1.191488,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.193356,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.194030,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.194596,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.196343,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.197418,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.198468,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<5>[    1.198934,4] msm_actuator_platform_probe:2086 No valid actuator GPIOs data
<5>[    1.199014,4] msm_actuator_platform_probe:2086 No valid actuator GPIOs data
<3>[    1.199599,4] msm_eeprom_platform_probe failed 2029
<3>[    1.199961,4] msm_eeprom_platform_probe failed 2029
<3>[    1.200309,4] msm_eeprom_platform_probe failed 2029
<3>[    1.200943,4] msm_flash_get_pmic_source_info:1000 alternate current: read failed
<3>[    1.200949,4] msm_flash_get_pmic_source_info:1020 alternate max-current: read failed
<3>[    1.200955,4] msm_flash_get_pmic_source_info:1040 alternate duration: read failed
<3>[    1.200988,4] msm_flash_get_pmic_source_info:1000 alternate current: read failed
<3>[    1.200994,4] msm_flash_get_pmic_source_info:1020 alternate max-current: read failed
<3>[    1.201000,4] msm_flash_get_pmic_source_info:1040 alternate duration: read failed
<3>[    1.201033,4] msm_flash_get_pmic_source_info:1110 alternate current: read failed
<3>[    1.201039,4] msm_flash_get_pmic_source_info:1130 alternate current: read failed
<3>[    1.201071,4] msm_flash_get_pmic_source_info:1110 alternate current: read failed
<3>[    1.201077,4] msm_flash_get_pmic_source_info:1130 alternate current: read failed
<5>[    1.201083,4] msm_flash_get_dt_data:1203 No valid flash GPIOs data
<3>[    1.201088,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.201792,4] adp1660 i2c_add_driver success
<6>[    1.207663,4] MSM-CPP cpp_init_hardware:1005 CPP HW Version: 0x40030003
<3>[    1.207672,4] MSM-CPP cpp_init_hardware:1023 stream_cnt:0
<3>[    1.208898,5] CAM-SOC msm_camera_get_reg_base:787 err: mem resource vfe_fuse not found
<3>[    1.208904,5] CAM-SOC msm_camera_get_res_size:830 err: mem resource vfe_fuse not found
<3>[    1.209952,5] CAM-SOC msm_camera_get_reg_base:787 err: mem resource vfe_fuse not found
<3>[    1.209958,5] CAM-SOC msm_camera_get_res_size:830 err: mem resource vfe_fuse not found
<3>[    1.216855,5] __msm_jpeg_init:1537] Jpeg Device id 0
<6>[    1.218454,5] usbcore: registered new interface driver uvcvideo
<6>[    1.218460,5] USB Video Class driver (1.1.1)
<6>[    1.219035,5] FG: fg_check_ima_exception: Initial ima_err_sts=0 ima_exp_sts=0 ima_hw_sts=66
<6>[    1.219255,5] FG: fg_empty_soc_irq_handler: triggered 0x20
<3>[    1.220287,5] FG: fg_power_get_property: ESR Bad: -22 mOhm
<3>[    1.220526,5] FG: fg_power_get_property: ESR Bad: -22 mOhm
<6>[    1.220570,5] FG: fg_probe: FG Probe success - FG Revision DIG:3.1 ANA:1.2 PMIC subtype=17
<3>[    1.222090,5] unable to find DT imem DLOAD mode node
<3>[    1.222469,5] unable to find DT imem EDLOAD mode node
<4>[    1.225075,4] thermal thermal_zone1: failed to read out thermal zone 1
<4>[    1.225235,6] thermal thermal_zone2: failed to read out thermal zone 2
<4>[    1.225388,6] thermal thermal_zone3: failed to read out thermal zone 3
<4>[    1.225543,6] thermal thermal_zone4: failed to read out thermal zone 4
<3>[    1.225998,6] qpnp_vadc_read: no vadc_chg_vote found
<3>[    1.226003,6] qpnp_vadc_get_temp: VADC read error with -22
<4>[    1.226009,6] thermal thermal_zone5: failed to read out thermal zone 5
<6>[    1.247734,6] device-mapper: uevent: version 1.0.3
<6>[    1.247864,6] device-mapper: ioctl: 4.28.0-ioctl (2014-09-17) initialised: <EMAIL>
<6>[    1.247937,6] device-mapper: req-crypt: dm-req-crypt successfully initalized.
<6>[    1.247937,6] 
<6>[    1.248575,6] sdhci: Secure Digital Host Controller Interface driver
<6>[    1.248580,6] sdhci: Copyright(c) Pierre Ossman
<6>[    1.248588,6] sdhci-pltfm: SDHCI platform and OF driver helper
<6>[    1.250690,1] qcom_ice_get_pdevice: found ice device e5a58540
<6>[    1.250697,1] qcom_ice_get_pdevice: matching platform device e5830000
<6>[    1.254499,1] qcom_ice 7803000.sdcc1ice: QC ICE 2.1.44 device found @0xe99a0000
<6>[    1.254846,1] sdhci_msm 7824900.sdhci: No vmmc regulator found
<6>[    1.254853,1] sdhci_msm 7824900.sdhci: No vqmmc regulator found
<6>[    1.255149,1] mmc0: SDHCI controller on 7824900.sdhci [7824900.sdhci] using 32-bit ADMA in CMDQ mode
<4>[    1.287075,1] sdhci_msm 7864900.sdhci: sdhci_msm_probe: ICE device is not enabled
<6>[    1.301402,1] sdhci_msm 7864900.sdhci: No vmmc regulator found
<6>[    1.301409,1] sdhci_msm 7864900.sdhci: No vqmmc regulator found
<6>[    1.301728,1] mmc1: SDHCI controller on 7864900.sdhci [7864900.sdhci] using 32-bit ADMA in legacy mode
<6>[    1.322799,0] mmc0: Out-of-interrupt timeout is 50[ms]
<6>[    1.322805,0] mmc0: BKOPS_EN equals 0x2
<6>[    1.322810,0] mmc0: eMMC FW version: 0x07
<6>[    1.322815,0] mmc0: CMDQ supported: depth: 16
<6>[    1.322820,0] mmc0: cache barrier support 0 flush policy 0
<6>[    1.332429,0] cmdq_host_alloc_tdl: desc_size: 512 data_sz: 126976 slot-sz: 16
<6>[    1.332598,0] mmc0: CMDQ enabled on card
<6>[    1.332608,0] mmc0: new HS400 MMC card at address 0001
<6>[    1.332867,0] sdhci_msm_pm_qos_cpu_init (): voted for group #0 (mask=0xf) latency=2
<6>[    1.332875,0] sdhci_msm_pm_qos_cpu_init (): voted for group #1 (mask=0xf0) latency=2
<6>[    1.332980,0] mmcblk0: mmc0:0001 RC14MB 58.2 GiB 
<6>[    1.333064,0] mmcblk0rpmb: mmc0:0001 RC14MB partition 3 4.00 MiB
<6>[    1.333682,0] qcom,leds-atc leds-atc-20: atc_leds_probe success
<6>[    1.333817,0] hidraw: raw HID events driver (C) Jiri Kosina
<6>[    1.333998,1] tz_log 8600720.tz-log: Hyp log service is not supported
<6>[    1.334207,0] usbcore: registered new interface driver usbhid
<6>[    1.334211,0] usbhid: USB HID core driver
<6>[    1.334664,0] ashmem: initialized
<6>[    1.334965,1]  mmcblk0: p1 p2 p3 p4 p5 p6 p7 p8 p9 p10 p11 p12 p13 p14 p15 p16 p17 p18 p19 p20 p21 p22 p23 p24 p25 p26 p27 p28 p29 p30 p31 p32 p33 p34 p35 p36 p37 p38 p39 p40 p41 p42 p43 p44 p45 p46 p47 p48 p49 p50 p51 p52 p53 p54
<6>[    1.335064,0] qpnp_coincell_charger_show_state: enabled=Y, voltage=3200 mV, resistance=2100 ohm
<6>[    1.339001,0] bimc-bwmon 408000.qcom,cpu-bwmon: BW HWmon governor registered.
<3>[    1.340673,0] devfreq soc:qcom,cpubw: Couldn't update frequency transition information.
<3>[    1.340792,0] devfreq soc:qcom,mincpubw: Couldn't update frequency transition information.
<3>[    1.342297,0] sensors-ssc soc:qcom,msm-ssc-sensors: msm_ssc_sensors_dt_parse: get qdsp timer cntpct hi offset fail
<6>[    1.342306,0] sensors-ssc soc:qcom,msm-ssc-sensors: slpi_loader_init_sysfs: Could not parse dt
<6>[    1.342652,0] usbcore: registered new interface driver snd-usb-audio
<6>[    1.346795,4] cs35l35 7-0040: Cirrus Logic CS35L35 (35a35), Revision: 00
<6>[    1.358307,4] msm-pcm-lpa soc:qcom,msm-pcm-lpa: msm_pcm_probe: dev name soc:qcom,msm-pcm-lpa
<6>[    1.362580,4] u32 classifier
<6>[    1.362585,4]     Actions configured
<6>[    1.362611,4] Netfilter messages via NETLINK v0.30.
<6>[    1.362649,4] nf_conntrack version 0.5.0 (16384 buckets, 65536 max)
<6>[    1.362897,4] ctnetlink v0.93: registering with nfnetlink.
<6>[    1.363351,4] xt_time: kernel timezone is -0000
<6>[    1.363593,4] ip_tables: (C) 2000-2006 Netfilter Core Team
<6>[    1.363705,4] arp_tables: (C) 2002 David S. Miller
<6>[    1.363739,4] TCP: cubic registered
<6>[    1.363745,4] Initializing XFRM netlink socket
<6>[    1.363977,4] NET: Registered protocol family 10
<6>[    1.364646,5] mip6: Mobile IPv6
<6>[    1.364665,5] ip6_tables: (C) 2000-2006 Netfilter Core Team
<6>[    1.364764,5] sit: IPv6 over IPv4 tunneling driver
<6>[    1.365081,5] NET: Registered protocol family 17
<6>[    1.365097,5] NET: Registered protocol family 15
<6>[    1.365129,5] bridge: automatic filtering via arp/ip/ip6tables has been deprecated. Update your scripts to load br_netfilter if you need this.
<6>[    1.365137,5] Ebtables v2.0 registered
<6>[    1.365234,5] Bluetooth: e6e05eb0
<6>[    1.365244,5] Bluetooth: e6e05ea8Bluetooth: e6e05ec0
<6>[    1.365267,5] Bluetooth: e6e05ea0Bluetooth: e6e05ea0
<6>[    1.365279,5] Bluetooth: e6e05e98Bluetooth: e6e05ed8
<6>[    1.365293,5] Bluetooth: e6e05ed8<6>[    1.365333,5] l2tp_core: L2TP core driver, V2.0
<6>[    1.365345,5] l2tp_ppp: PPPoL2TP kernel driver, V2.0
<6>[    1.365352,5] l2tp_ip: L2TP IP encapsulation support (L2TPv3)
<6>[    1.365369,5] l2tp_netlink: L2TP netlink interface
<6>[    1.365390,5] l2tp_eth: L2TP ethernet pseudowire support (L2TPv3)
<6>[    1.365405,5] l2tp_debugfs: L2TP debugfs support
<6>[    1.365412,5] l2tp_ip6: L2TP IP encapsulation support for IPv6 (L2TPv3)
<6>[    1.365928,5] NET: Registered protocol family 27
<6>[    1.369661,2] subsys-pil-tz a21b000.qcom,pronto: for wcnss segments only will be dumped.
<6>[    1.371444,2] pil-q6v5-mss 4080000.qcom,mss: for modem segments only will be dumped.
<6>[    1.372930,2] msm-dwc3 7000000.ssusb: unable to read dcp-max-current, using define value
<6>[    1.373233,2] ft5x06_ts 3-0038: unset chg state
<6>[    1.373252,2] ft5x06_ts 3-0038: ps present state not change
<6>[    1.374679,2] sps:BAM 0x07104000 is registered.
<3>[    1.377621,2] qpnp-smbcharger qpnp-smbcharger-17: node /soc/qcom,spmi@200f000/qcom,pmi8950@2/qcom,qpnp-smbcharger IO resource absent!
<3>[    1.377753,2] qpnp-smbcharger qpnp-smbcharger-17: length=8
<3>[    1.377761,2] qpnp-smbcharger qpnp-smbcharger-17: num parallel charge entries=8
<6>[    1.377853,2] smbcharger_charger_otg: no parameters
<6>[    1.378508,2] FG: fg_vbat_est_check: vbat(3825508),est-vbat(3827034),diff(1526),threshold(300000)
<6>[    1.401915,2] msm-dwc3 7000000.ssusb: Avail curr from USB = 1500
<3>[    1.402207,2] qpnp-smbcharger qpnp-smbcharger-17: Turbo Charger Detected!
<6>[    1.402667,2] FG: fg_vbat_est_check: vbat(3825508),est-vbat(3827034),diff(1526),threshold(300000)
<3>[    1.407574,0] qpnp-smbcharger qpnp-smbcharger-17: node /soc/qcom,spmi@200f000/qcom,pmi8950@2/qcom,qpnp-smbcharger IO resource absent!
<3>[    1.407689,2] qpnp-smbcharger qpnp-smbcharger-17: Battery Temp State: Unknown -> Cool at -2C
<6>[    1.408891,0] qpnp-smbcharger qpnp-smbcharger-17: SMBCHG successfully probe Charger version=SCHG_LITE Revision DIG:0.0 ANA:0.1 batt=1 dc=0 usb=1
<5>[    1.411172,5] Registering SWP/SWPB emulation handler
<6>[    1.411448,5] registered taskstats version 1
<6>[    1.415493,5] fastrpc soc:qcom,adsprpc-mem: for adsp_rh segments only will be dumped.
<1>[    1.416683,5] drv260x: drv260x_init success
<6>[    1.417109,5] utags (utags_probe): Done [config]
<6>[    1.417133,5] utags (utags_dt_init): backup storage path not provided
<6>[    1.417325,5] utags (utags_probe): Done [hw]
<6>[    1.417904,5] RNDIS_IPA module is loaded.
<6>[    1.418247,5] file system registered
<6>[    1.418290,5] mbim_init: initialize 1 instances
<6>[    1.418336,5] mbim_init: Initialized 1 ports
<6>[    1.419362,5] rndis_qc_init: initialize rndis QC instance
<6>[    1.419533,5] Number of LUNs=8
<6>[    1.419541,5] Mass Storage Function, version: 2009/09/11
<6>[    1.419548,5] LUN: removable file: (no medium)
<6>[    1.419559,5] Number of LUNs=1
<6>[    1.419597,5] LUN: removable file: (no medium)
<6>[    1.419601,5] Number of LUNs=1
<6>[    1.420280,5] android_usb gadget: android_usb ready
<6>[    1.421435,5] input: gpio-keys as /devices/soc/soc:gpio_keys/input/input5
<4>[    1.421756,5] i2c-core: driver [stmvl53l0] using legacy resume method
<6>[    1.422232,5] qcom,qpnp-rtc qpnp-rtc-8: setting system clock to 1970-01-01 00:32:40 UTC (1960)
<6>[    1.424767,6] msm-core initialized without polling period
<3>[    1.427316,6] parse_cpu_levels: idx 1 276
<3>[    1.427326,6] calculate_residency: residency < 0 for LPM
<3>[    1.427441,6] parse_cpu_levels: idx 1 286
<3>[    1.427447,6] calculate_residency: residency < 0 for LPM
<3>[    1.430395,6] qcom,qpnp-flash-led qpnp-flash-led-23: Unable to acquire pinctrl
<6>[    1.432082,6] rmnet_ipa started initialization
<6>[    1.432089,6] IPA SSR support = True
<6>[    1.432093,6] IPA ipa-loaduC = True
<6>[    1.432097,6] IPA SG support = True
<3>[    1.433933,6] ipa ipa_sps_irq_control_all:942 EP (5) not allocated.
<3>[    1.433942,6] ipa ipa2_uc_state_check:301 uC is not loaded
<6>[    1.434942,6] rmnet_ipa completed initialization
<6>[    1.437386,6] qcom,cc-debug-8953 1874000.qcom,cc-debug: Registered Debug Mux successfully
<6>[    1.445820,6] msm8952-asoc-wcd c051000.sound: default codec configured
<3>[    1.449045,6] msm8952-asoc-wcd c051000.sound: ASoC: platform (null) not registered
<3>[    1.449089,6] msm8952-asoc-wcd c051000.sound: snd_soc_register_card failed (-517)
<6>[    1.450265,6] apc_mem_acc_corner: disabling
<6>[    1.450272,6] gfx_mem_acc_corner: disabling
<6>[    1.450311,6] vci_fci: disabling
<6>[    1.450350,6] regulator_proxy_consumer_remove_all: removing regulator proxy consumer requests
<6>[    1.450389,6] clock_late_init: Removing enables held for handed-off clocks
<6>[    1.453507,2] ft5x06_ts 3-0038: ps present state not change
<6>[    1.453805,2] ft5x06_ts 3-0038: set chg state
<6>[    1.454282,6] ALSA device list:
<6>[    1.454287,6]   No soundcards found.
<3>[    1.454359,6] Warning: unable to open an initial console.
<6>[    1.490317,6] Freeing unused kernel memory: 504K
<14>[    1.491855,6] init: init first stage started!
<14>[    1.491891,6] init: First stage mount skipped (recovery mode)
<14>[    1.492104,6] init: Using Android DT directory /proc/device-tree/firmware/android/
<14>[    1.492167,6] init: Skipped setting INIT_AVB_VERSION (not vbmeta compatible)
<14>[    1.492185,6] init: Loading SELinux policy
<7>[    1.497238,6] SELinux: 2048 avtab hash slots, 29509 rules.
<6>[    1.506828,4] FG: fg_vbat_est_check: vbat(3825508),est-vbat(3827034),diff(1526),threshold(300000)
<7>[    1.509431,6] SELinux: 2048 avtab hash slots, 29509 rules.
<7>[    1.509451,6] SELinux:  1 users, 2 roles, 2214 types, 0 bools, 1 sens, 1024 cats
<7>[    1.509458,6] SELinux:  93 classes, 29509 rules
<7>[    1.512757,6] SELinux:  Completing initialization.
<7>[    1.512762,6] SELinux:  Setting up existing superblocks.
<7>[    1.512777,6] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<7>[    1.512798,6] SELinux: initialized (dev rootfs, type rootfs), uses genfs_contexts
<7>[    1.512967,6] SELinux: initialized (dev bdev, type bdev), not configured for labeling
<7>[    1.512982,6] SELinux: initialized (dev proc, type proc), uses genfs_contexts
<7>[    1.513006,6] SELinux: initialized (dev debugfs, type debugfs), uses genfs_contexts
<4>[    1.523408,6] bcl_peripheral:bcl_poll_vbat_high Vbat reached high clear trip. vbat:3856320
<3>[    1.523432,6] bcl_peripheral:bcl_poll_ibat_low Invalid ibat state 1
<7>[    1.537232,4] SELinux: initialized (dev sockfs, type sockfs), uses task SIDs
<7>[    1.537249,4] SELinux: initialized (dev tracefs, type tracefs), uses genfs_contexts
<7>[    1.570842,4] SELinux: initialized (dev pipefs, type pipefs), uses task SIDs
<7>[    1.570853,4] SELinux: initialized (dev anon_inodefs, type anon_inodefs), not configured for labeling
<7>[    1.570860,4] SELinux: initialized (dev aio, type aio), not configured for labeling
<7>[    1.570869,4] SELinux: initialized (dev devpts, type devpts), uses transition SIDs
<7>[    1.570888,4] SELinux: initialized (dev configfs, type configfs), uses genfs_contexts
<7>[    1.570901,4] SELinux: initialized (dev selinuxfs, type selinuxfs), uses genfs_contexts
<7>[    1.570959,4] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<7>[    1.570993,4] SELinux: initialized (dev sysfs, type sysfs), uses genfs_contexts
<5>[    1.581999,4] audit: type=1403 audit(1960.656:2): policy loaded auid=4294967295 ses=4294967295
<14>[    1.582224,4] selinux: SELinux: Loaded policy from /sepolicy
<14>[    1.582224,4] 
<5>[    1.582438,4] audit: type=1404 audit(1960.656:3): enforcing=1 old_enforcing=0 auid=4294967295 ses=4294967295
<14>[    1.605955,4] selinux: SELinux: Loaded file_contexts
<14>[    1.605955,4] 
<5>[    1.606960,4] random: init urandom read with 86 bits of entropy available
<14>[    1.607783,4] init: init second stage started!
<14>[    1.616535,4] init: Using Android DT directory /proc/device-tree/firmware/android/
<14>[    1.622869,4] selinux: SELinux: Loaded file_contexts
<14>[    1.622869,4] 
<14>[    1.624611,4] selinux: SELinux: Loaded property_contexts from /plat_property_contexts & /nonplat_property_contexts.
<14>[    1.624611,4] 
<14>[    1.624629,4] init: Running restorecon...
<11>[    1.632052,4] selinux: SELinux:  Could not stat /dev/block: No such file or directory.
<11>[    1.632052,4] 
<11>[    1.632416,4] init: waitid failed: No child processes
<12>[    1.632465,4] init: Couldn't load property file: Unable to open '/system/etc/prop.default': No such file or directory: No such file or directory
<12>[    1.632911,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.632938,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.632962,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.632985,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633009,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633033,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633056,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633079,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633103,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633127,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633150,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633175,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633198,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633221,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633244,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633268,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633291,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633314,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633337,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633360,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633405,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633429,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633452,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633475,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633499,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633521,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633545,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633568,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633591,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633614,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633637,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633663,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633688,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633711,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633734,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633757,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633780,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633804,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633827,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633850,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633872,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633895,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.633918,4] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<11>[    1.635711,4] init: property_set("ro.cutoff_voltage_mv", "3400") failed: property already set
<11>[    1.636438,4] init: property_set("ro.opengles.version", "196610") failed: property already set
<11>[    1.636975,4] init: property_set("ro.carrier", "unknown") failed: property already set
<12>[    1.637456,4] init: Couldn't load property file: Unable to open '/odm/default.prop': No such file or directory: No such file or directory
<12>[    1.637515,4] init: Couldn't load property file: Unable to open '/vendor/default.prop': No such file or directory: No such file or directory
<14>[    1.638014,4] init: Created socket '/dev/socket/property_service', mode 666, user 0, group 0
<14>[    1.638135,4] init: Parsing file /init.rc...
<14>[    1.638235,4] init: Added '/init.recovery.qcom.rc' to import list
<14>[    1.638569,4] init: Parsing file /init.recovery.qcom.rc...
<14>[    1.638698,4] init: Parsing file /system/etc/init...
<11>[    1.638720,4] init: Unable to open '/system/etc/init': No such file or directory
<14>[    1.638742,4] init: Parsing file /vendor/etc/init...
<11>[    1.638766,4] init: Unable to open '/vendor/etc/init': No such file or directory
<14>[    1.638785,4] init: Parsing file /odm/etc/init...
<11>[    1.638805,4] init: Unable to open '/odm/etc/init': No such file or directory
<14>[    1.638900,4] init: processing action (early-init) from (/init.rc:3)
<14>[    1.638959,4] init: starting service 'ueventd'...
<5>[    1.639409,4] audit: type=1400 audit(1960.713:4): avc:  denied  { create } for  uid=0 pid=1 comm="init" name="cgroup.procs" scontext=u:r:init:s0 tcontext=u:object_r:rootfs:s0 tclass=file permissive=0
<11>[    1.639474,4] init: Failed to write '412' to /acct/uid_0/pid_412/cgroup.procs: Permission denied
<11>[    1.639493,4] init: createProcessGroup(0, 412) failed for service 'ueventd': Permission denied
<14>[    1.639566,4] init: processing action (wait_for_coldboot_done) from (<Builtin Action>:0)
<14>[    1.641916,5] ueventd: ueventd started!
<14>[    1.641967,5] ueventd: Parsing file /ueventd.rc...
<11>[    1.642276,5] ueventd: /ueventd.rc: 66: invalid gid 'qcom_diag'
<14>[    1.642757,5] ueventd: Parsing file /vendor/ueventd.rc...
<11>[    1.642780,5] ueventd: Unable to open '/vendor/ueventd.rc': No such file or directory
<14>[    1.642798,5] ueventd: Parsing file /odm/ueventd.rc...
<11>[    1.642819,5] ueventd: Unable to open '/odm/ueventd.rc': No such file or directory
<14>[    1.642891,5] ueventd: Parsing file /ueventd.qcom.rc...
<11>[    1.642912,5] ueventd: Unable to open '/ueventd.qcom.rc': No such file or directory
<14>[    1.648225,5] selinux: SELinux: Loaded file_contexts
<14>[    1.648225,5] 
<14>[    1.779762,4] selinux: SELinux: Loaded file_contexts
<14>[    1.779762,4] 
<14>[    1.779763,5] selinux: SELinux: Loaded file_contexts
<14>[    1.779763,5] 
<14>[    1.779765,7] selinux: SELinux: Loaded file_contexts
<14>[    1.779765,7] 
<14>[    1.779766,6] selinux: SELinux: Loaded file_contexts
<14>[    1.779766,6] 
<14>[    1.780113,1] selinux: SELinux: Loaded file_contexts
<14>[    1.780113,1] 
<14>[    1.780274,3] selinux: SELinux: Loaded file_contexts
<14>[    1.780274,3] 
<14>[    1.786517,7] selinux: SELinux: Loaded file_contexts
<14>[    1.786517,7] 
<14>[    1.786901,2] selinux: SELinux: Loaded file_contexts
<14>[    1.786901,2] 
<14>[    1.786923,0] selinux: SELinux: Loaded file_contexts
<14>[    1.786923,0] 
<6>[    1.994837,4] FG: fg_vbat_est_check: vbat(3825508),est-vbat(3827034),diff(1526),threshold(300000)
<14>[    3.128407,5] ueventd: Coldboot took 1.479 seconds
<14>[    3.131275,0] init: Command 'wait_for_coldboot_done' action=wait_for_coldboot_done (<Builtin Action>:0) returned 0 took 1491ms.
<14>[    3.131316,0] init: processing action (mix_hwrng_into_linux_rng) from (<Builtin Action>:0)
<14>[    3.131847,0] init: Mixed 512 bytes from /dev/hw_random into /dev/urandom
<14>[    3.131876,0] init: processing action (set_mmap_rnd_bits) from (<Builtin Action>:0)
<14>[    3.131903,0] init: processing action (set_kptr_restrict) from (<Builtin Action>:0)
<14>[    3.132174,0] init: processing action (keychord_init) from (<Builtin Action>:0)
<14>[    3.132203,0] init: processing action (console_init) from (<Builtin Action>:0)
<14>[    3.132251,0] init: processing action (init) from (/init.rc:9)
<7>[    3.132821,0] SELinux: initialized (dev cgroup, type cgroup), uses genfs_contexts
<7>[    3.134689,0] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<14>[    3.134963,0] init: processing action (init) from (/init.recovery.qcom.rc:28)
<11>[    3.135005,0] init: Unable to open '/sys/class/backlight/panel0-backlight/brightness': No such file or directory
<14>[    3.136077,0] init: processing action (mix_hwrng_into_linux_rng) from (<Builtin Action>:0)
<14>[    3.136568,0] init: Mixed 512 bytes from /dev/hw_random into /dev/urandom
<14>[    3.136599,0] init: processing action (late-init) from (/init.rc:66)
<14>[    3.136642,0] init: processing action (queue_property_triggers) from (<Builtin Action>:0)
<14>[    3.136672,0] init: processing action (fs) from (/init.rc:36)
<7>[    3.137901,0] SELinux: initialized (dev functionfs, type functionfs), uses genfs_contexts
<3>[    3.138001,0] enable_store: android_usb: already disabled
<14>[    3.138469,0] init: processing action (load_system_props_action) from (/init.rc:59)
<12>[    3.138569,0] init: HW descriptor status=2
<6>[    3.138579,0] utags (reload_write): [init] (pid 1) [hw] 1
<12>[    3.262394,0] init: Sent HW descriptor reload command rc=2
<11>[    3.262447,0] init: File /vendor/etc/vhw.xml not found
<12>[    3.262495,0] init: Couldn't load property file: Unable to open '/system/build.prop': No such file or directory: No such file or directory
<12>[    3.262520,0] init: Couldn't load property file: Unable to open '/odm/build.prop': No such file or directory: No such file or directory
<12>[    3.262546,0] init: Couldn't load property file: Unable to open '/vendor/build.prop': No such file or directory: No such file or directory
<12>[    3.262569,0] init: Couldn't load property file: Unable to open '/factory/factory.prop': No such file or directory: No such file or directory
<14>[    3.264077,0] init: Command 'load_system_props' action=load_system_props_action (/init.rc:60) returned 0 took 125ms.
<14>[    3.264109,0] init: processing action (firmware_mounts_complete) from (/init.rc:62)
<14>[    3.264152,0] init: processing action (boot) from (/init.rc:51)
<14>[    3.264553,0] init: starting service 'charger'...
<14>[    3.265249,0] init: starting service 'recovery'...
<14>[    3.265944,0] init: processing action (enable_property_trigger) from (<Builtin Action>:0)
<12>[    3.269300,4] healthd: battery l=30 v=3750 t=40.7 h=2 st=3 c=536 fc=0 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<5>[    3.272920,5] audit: type=1400 audit(1962.346:5): avc:  denied  { read } for  uid=0 pid=425 comm="recovery" name="u:object_r:sf_lcd_density_prop:s0" dev="tmpfs" ino=14672 scontext=u:r:recovery:s0 tcontext=u:object_r:sf_lcd_density_prop:s0 tclass=file permissive=0
<6>[    3.273285,5] input input5: gpio-keys report volume_up [0x73] type 0x1 state Off
<5>[    3.313741,0] audit: type=1400 audit(1962.390:6): avc:  denied  { write } for  uid=0 pid=425 comm="recovery" name="brightness" dev="sysfs" ino=22073 scontext=u:r:recovery:s0 tcontext=u:object_r:sysfs_graphics:s0 tclass=file permissive=0
<4>[    3.330581,0] irq 21, desc: e5d40840, depth: 0, count: 0, unhandled: 0
<4>[    3.330593,0] ->handle_irq():  c03f6c44, msm_gpio_irq_handler+0x0/0x118
<4>[    3.330600,0] ->irq_data.chip(): c1531158, gic_chip+0x0/0x74
<4>[    3.330601,0] ->action():   (null)
<4>[    3.330602,0]    IRQ_NOPROBE set
<4>[    3.330603,0]  IRQ_NOREQUEST set
<4>[    3.330604,0]   IRQ_NOTHREAD set
<6>[    3.330916,4] mdss_dsi_on[0]+.
<6>[    3.556891,6] ft5x06_ts 3-0038: set chg state
<6>[    4.407259,5] EXT4-fs (mmcblk0p52): mounted filesystem with ordered data mode. Opts: 
<7>[    4.407285,5] SELinux: initialized (dev mmcblk0p52, type ext4), uses xattr
<6>[    4.409840,5] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xd192812d)
<3>[    4.409843,5] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 1th superblock
<6>[    4.410096,5] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xd8386d2e)
<3>[    4.410098,5] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 2th superblock
<6>[    4.410102,5] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xd192812d)
<3>[    4.410105,5] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 1th superblock
<6>[    4.410107,5] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xd8386d2e)
<3>[    4.410109,5] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 2th superblock
<6>[    4.410128,2] msm-dwc3 7000000.ssusb: Avail curr from USB = 1800
<6>[    4.410174,2] ft5x06_ts 3-0038: ps present state not change
<3>[    4.410492,5] EXT4-fs (mmcblk0p54): VFS: Can't find ext4 filesystem
<12>[    4.411030,4] healthd: battery l=30 v=3750 t=40.7 h=2 st=3 c=536 fc=0 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<12>[    4.411751,4] healthd: battery l=30 v=3750 t=40.7 h=2 st=3 c=536 fc=0 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<3>[    4.415461,2] qpnp-smbcharger qpnp-smbcharger-17: Battery Temp State: Cool -> Good at 40C
<12>[    4.426648,4] healthd: battery l=30 v=3754 t=40.7 h=2 st=2 c=378 fc=0 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<6>[    4.524581,4] FG: fg_vbat_est_check: vbat(3754860),est-vbat(3795906),diff(41046),threshold(300000)
<12>[    4.526503,0] healthd: battery l=30 v=3754 t=40.7 h=2 st=2 c=378 fc=0 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<5>[    5.214314,0] random: nonblocking pool is initialized
<3>[    5.246805,4] FG: fg_get_mmi_battid: Battsn unused
<4>[    5.246812,4] qcom,qpnp-fg qpnp-fg-18: Default Serial Number SB18C15119
<4>[    5.246818,4] qcom,qpnp-fg qpnp-fg-18: Battery Match Found using default qcom,hg30-alt
<6>[    5.251760,4] FG: fg_batt_profile_init: Battery profiles same, using default
<6>[    5.254821,4] FG: populate_system_data: cutoff_voltage = 3199901, nom_cap_uah = 3021000 p1p2 = 33, p2p3 = 5
<6>[    5.254884,4] FG: fg_batt_profile_init: Battery SOC: 31, V: 3754860uV
<6>[    5.255513,4] FG: fg_vbat_est_check: vbat(3754860),est-vbat(3795906),diff(41046),threshold(300000)
<12>[    5.257070,0] healthd: battery l=31 v=3754 t=40.7 h=2 st=2 c=378 fc=3021000 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<12>[    5.257717,0] healthd: battery l=31 v=3754 t=40.7 h=2 st=2 c=378 fc=3021000 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<6>[   10.553853,0] EXT4-fs (mmcblk0p52): mounted filesystem with ordered data mode. Opts: 
<7>[   10.553867,0] SELinux: initialized (dev mmcblk0p52, type ext4), uses xattr
<12>[   11.114008,0] healthd: battery l=31 v=3754 t=40.7 h=2 st=2 c=378 fc=3021000 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<12>[   11.114681,0] healthd: battery l=31 v=3754 t=40.7 h=2 st=2 c=378 fc=3021000 cc=1 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=a
<6>[   11.154560,3] EXT4-fs (mmcblk0p52): mounted filesystem with ordered data mode. Opts: 
<7>[   11.154575,3] SELinux: initialized (dev mmcblk0p52, type ext4), uses xattr
