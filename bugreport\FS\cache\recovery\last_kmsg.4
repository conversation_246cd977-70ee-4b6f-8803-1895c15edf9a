<6>[    0.000000,0] Booting Linux on physical CPU 0x0
<6>[    0.000000,0] Initializing cgroup subsys cpu
<6>[    0.000000,0] Initializing cgroup subsys cpuacct
<5>[    0.000000,0] Linux version 3.18.71-perf-g810c3e8 (hudsoncm@ilclbld35) (gcc version 4.9.x 20150123 (prerelease) (GCC) ) #1 SMP PREEMPT Fri Apr 26 14:05:39 CDT 2019
<6>[    0.000000,0] CPU: ARMv7 Processor [410fd034] revision 4 (ARMv7), cr=10c0383d
<6>[    0.000000,0] CPU: PIPT / VIPT nonaliasing data cache, VIPT aliasing instruction cache
<6>[    0.000000,0] Machine model: sanders
<6>[    0.000000,0] Reserved memory: reserved region for node 'other_ext_region@0': base 0x84300000, size 37 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'modem_region@0': base 0x86c00000, size 106 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'adsp_fw_region@0': base 0x8d600000, size 17 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'wcnss_fw_region@0': base 0x8e700000, size 7 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'dfps_data_mem@90000000': base 0x90000000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'splash_region@0x90001000': base 0x90001000, size 19 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'ramoops_mem_region': base 0xef000000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'tzlog_bck_region': base 0xeefe4000, size 0 MiB
<6>[    0.000000,0] Reserved memory: reserved region for node 'wdog_cpuctx_region': base 0xeefe6000, size 0 MiB
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x84300000, size 37 MiB
<6>[    0.000000,0] Reserved memory: initialized node other_ext_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x86c00000, size 106 MiB
<6>[    0.000000,0] Reserved memory: initialized node modem_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x8d600000, size 17 MiB
<6>[    0.000000,0] Reserved memory: initialized node adsp_fw_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Removed memory: created DMA memory pool at 0x8e700000, size 7 MiB
<6>[    0.000000,0] Reserved memory: initialized node wcnss_fw_region@0, compatible id removed-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'venus_region@0' node: base 0x8f800000, size 8 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0x8f800000, size 8 MiB
<6>[    0.000000,0] Reserved memory: initialized node venus_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'secure_region@0' node: base 0xf6400000, size 152 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf6400000, size 152 MiB
<6>[    0.000000,0] Reserved memory: initialized node secure_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'qseecom_region@0' node: base 0xf5400000, size 16 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf5400000, size 16 MiB
<6>[    0.000000,0] Reserved memory: initialized node qseecom_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'adsp_region@0' node: base 0xf5000000, size 4 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0xf5000000, size 4 MiB
<6>[    0.000000,0] Reserved memory: initialized node adsp_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] Reserved memory: allocated memory for 'gpu_region@0' node: base 0x8f000000, size 8 MiB
<6>[    0.000000,0] Reserved memory: created CMA memory pool at 0x8f000000, size 8 MiB
<6>[    0.000000,0] Reserved memory: initialized node gpu_region@0, compatible id shared-dma-pool
<6>[    0.000000,0] cma: Reserved 16 MiB at 0xf4000000
<6>[    0.000000,0] Memory policy: Data cache writealloc
<7>[    0.000000,0] On node 0 totalpages: 940131
<7>[    0.000000,0] free_area_init_node: node 0, pgdat c15fef80, node_mem_map e73f9000
<7>[    0.000000,0]   Normal zone: 1316 pages used for memmap
<7>[    0.000000,0]   Normal zone: 0 pages reserved
<7>[    0.000000,0]   Normal zone: 168448 pages, LIFO batch:31
<7>[    0.000000,0]   HighMem zone: 6364 pages used for memmap
<7>[    0.000000,0]   HighMem zone: 771683 pages, LIFO batch:31
<6>[    0.000000,0] psci: probing for conduit method from DT.
<6>[    0.000000,0] psci: PSCIv1.0 detected in firmware.
<6>[    0.000000,0] psci: Using standard PSCI v0.2 function IDs
<4>[    0.000000,0] PERCPU: max_distance=0xb000 too large for vmalloc space 0x0
<6>[    0.000000,0] PERCPU: Embedded 11 pages/cpu @e72ee000 s14912 r8192 d21952 u45056
<7>[    0.000000,0] pcpu-alloc: s14912 r8192 d21952 u45056 alloc=11*4096
<7>[    0.000000,0] pcpu-alloc: [0] 0 [0] 1 [0] 2 [0] 3 [0] 4 [0] 5 [0] 6 [0] 7 
<4>[    0.000000,0] Built 1 zonelists in Zone order, mobility grouping on.  Total pages: 938815
<5>[    0.000000,0] Kernel command line: sched_enable_hmp=1 sched_enable_power_aware=1 console=null androidboot.hardware=qcom user_debug=30 msm_rtb.filter=0x237 ehci-hcd.park=3 androidboot.bootdevice=7824900.sdhci lpm_levels.sleep_disabled=1 vmalloc=350M buildvariant=user androidboot.emmc=true androidboot.serialno=ZY32286WPB androidboot.baseband=msm androidboot.mode=normal androidboot.device=sanders androidboot.hwrev=0x8400 androidboot.radio=INDIA androidboot.powerup_reason=0x00004000 androidboot.bootreason=reboot msm_poweroff.download_mode=0 androidboot.fsg-id= androidboot.wifimacaddr=A8:96:75:05:41:09,A8:96:75:05:41:0A androidboot.btmacaddr=A8:96:75:05:41:08 mdss_mdp.panel=1:dsi:0:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0 androidboot.bootloader=0xC212 androidboot.carrier=retin androidboot.poweroff_alarm=0 androidboot.hardware.sku=XT1804 androidboot.secure_hardware=1 androidboot.bl_state=1 androidboot.cid=0x32 androidboot.uid=C035992300000000000000000000 androidboot.write_protect=1 androidboot.ve<6>[    0.000000,0] PID hash table entries: 4096 (order: 2, 16384 bytes)
<6>[    0.000000,0] Dentry cache hash table entries: 131072 (order: 7, 524288 bytes)
<6>[    0.000000,0] Inode-cache hash table entries: 65536 (order: 6, 262144 bytes)
<4>[    0.000000,0] Memory: 3469276K/3760524K available (13312K kernel code, 1076K rwdata, 5704K rodata, 506K init, 1902K bss, 82352K reserved, 208896K cma-reserved, 2857356K highmem)
<5>[    0.000000,0] Virtual kernel memory layout:
<5>[    0.000000,0]     vector  : 0xffff0000 - 0xffff1000   (   4 kB)
<5>[    0.000000,0]     fixmap  : 0xffc00000 - 0xfff00000   (3072 kB)
<5>[    0.000000,0] 	   vmalloc : 0xe9200000 - 0xff000000   ( 350 MB)
<5>[    0.000000,0] 	   lowmem  : 0xc0000000 - 0xe9200000   ( 658 MB)
<5>[    0.000000,0]     pkmap   : 0xbfe00000 - 0xc0000000   (   2 MB)
<5>[    0.000000,0]     modules : 0xbf000000 - 0xbfe00000   (  14 MB)
<5>[    0.000000,0]       .text : 0xc0008000 - 0xc0e00000   (14304 kB)
<5>[    0.000000,0]       .init : 0xc1400000 - 0xc147ea40   ( 507 kB)
<5>[    0.000000,0]       .data : 0xc1500000 - 0xc160d304   (1077 kB)
<5>[    0.000000,0]        .bss : 0xc160d304 - 0xc17e8c68   (1903 kB)
<6>[    0.000000,0] SLUB: HWalign=64, Order=0-3, MinObjects=0, CPUs=8, Nodes=1
<6>[    0.000000,0] HMP scheduling enabled.
<6>[    0.000000,0] Preemptible hierarchical RCU implementation.
<6>[    0.000000,0] 	RCU dyntick-idle grace-period acceleration is enabled.
<4>[    0.000000,0] 
<4>[    0.000000,0] **********************************************************
<4>[    0.000000,0] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** trace_printk() being used. Allocating extra memory.  **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** This means that this is a DEBUG kernel and it is     **
<4>[    0.000000,0] ** unsafe for produciton use.                           **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] ** If you see this message and you are not debugging    **
<4>[    0.000000,0] ** the kernel, report this immediately to your vendor!  **
<4>[    0.000000,0] **                                                      **
<4>[    0.000000,0] **   NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE NOTICE   **
<4>[    0.000000,0] **********************************************************
<6>[    0.000000,0] NR_IRQS:16 nr_irqs:16 16
<4>[    0.000000,0] mpm_init_irq_domain(): Cannot find irq controller for qcom,gpio-parent
<3>[    0.000000,0] MPM 1 irq mapping errored -517
<6>[    0.000000,0] 	Offload RCU callbacks from all CPUs
<6>[    0.000000,0] 	Offload RCU callbacks from CPUs: 0-7.
<6>[    0.000000,0] Architected cp15 and mmio timer(s) running at 19.20MHz (virt/virt).
<6>[    0.000006,0] sched_clock: 56 bits at 19MHz, resolution 52ns, wraps every 3579139424256ns
<6>[    0.000020,0] Switching to timer-based delay loop, resolution 52ns
<6>[    0.000035,0] Switched to clocksource arch_sys_counter
<6>[    0.000918,0] Calibrating delay loop (skipped), value calculated using timer frequency.. 38.00 BogoMIPS (lpj=64000)
<6>[    0.000933,0] pid_max: default: 32768 minimum: 301
<6>[    0.001017,0] Security Framework initialized
<6>[    0.001031,0] SELinux:  Initializing.
<7>[    0.001067,0] SELinux:  Starting in permissive mode
<6>[    0.001110,0] Mount-cache hash table entries: 2048 (order: 1, 8192 bytes)
<6>[    0.001122,0] Mountpoint-cache hash table entries: 2048 (order: 1, 8192 bytes)
<6>[    0.001846,0] Initializing cgroup subsys freezer
<6>[    0.001892,0] CPU: Testing write buffer coherency: ok
<3>[    0.002454,0] /cpus/cpu@0 missing clock-frequency property
<3>[    0.002469,0] /cpus/cpu@1 missing clock-frequency property
<3>[    0.002484,0] /cpus/cpu@2 missing clock-frequency property
<3>[    0.002501,0] /cpus/cpu@3 missing clock-frequency property
<3>[    0.002519,0] /cpus/cpu@100 missing clock-frequency property
<3>[    0.002539,0] /cpus/cpu@101 missing clock-frequency property
<3>[    0.002561,0] /cpus/cpu@102 missing clock-frequency property
<3>[    0.002584,0] /cpus/cpu@103 missing clock-frequency property
<6>[    0.002660,0] Setting up static identity map for 0x10d2e3d8 - 0x10d2e430
<4>[    0.002984,0] NOHZ: local_softirq_pending 02
<4>[    0.003387,0] NOHZ: local_softirq_pending 02
<6>[    0.011054,0] MSM Memory Dump base table set up
<6>[    0.011087,0] MSM Memory Dump apps data table set up
<6>[    0.011150,0] Configuring XPU violations to be fatal errors
<6>[    0.012419,0] cpu_clock_pwr_init: Power clocks configured
<4>[    0.017432,1] CPU1: Booted secondary processor
<4>[    0.022314,2] CPU2: Booted secondary processor
<4>[    0.027161,3] CPU3: Booted secondary processor
<4>[    0.032108,4] CPU4: Booted secondary processor
<4>[    0.037029,5] CPU5: Booted secondary processor
<4>[    0.041866,6] CPU6: Booted secondary processor
<4>[    0.046817,7] CPU7: Booted secondary processor
<6>[    0.047035,0] Brought up 8 CPUs
<6>[    0.047084,0] SMP: Total of 8 processors activated (307.00 BogoMIPS).
<6>[    0.047093,0] CPU: All CPU(s) started in SVC mode.
<6>[    0.056516,2] VFP support v0.3: implementor 41 architecture 3 part 40 variant 3 rev 4
<6>[    0.065928,2] pinctrl core: initialized pinctrl subsystem
<6>[    0.066365,2] regulator-dummy: no parameters
<6>[    0.142483,2] NET: Registered protocol family 16
<6>[    0.148748,2] DMA: preallocated 256 KiB pool for atomic coherent allocations
<4>[    0.149559,2] msm_pm_tz_boot_init: set warmboot address failed
<3>[    0.149585,2] scm_call failed: func id 0x2000101, ret: -1, syscall returns: 0x0, 0x0, 0x0
<6>[    0.160138,2] cpuidle: using governor ladder
<6>[    0.173468,2] cpuidle: using governor menu
<6>[    0.186799,2] cpuidle: using governor qcom
<6>[    0.193368,2] platform soc:qcom,kgsl-hyp: assigned reserved memory node gpu_region@0
<6>[    0.218680,2] msm_watchdog b017000.qcom,wdt: wdog absent resource not present
<6>[    0.219133,2] msm_watchdog b017000.qcom,wdt: MSM Watchdog Initialized
<6>[    0.224370,2] platform soc:qcom,adsprpc-mem: assigned reserved memory node adsp_region@0
<4>[    0.226567,2] irq: no irq domain found for /soc/pinctrl@1000000 !
<3>[    0.227132,2] spmi_pmic_arb 200f000.qcom,spmi: PMIC Arb Version-2 0x20010000
<3>[    0.227882,2] spmi_pmic_arb 200f000.qcom,spmi: non-zero irq-accumulator[0]:0x20000000
<3>[    0.235310,2] spmi spmi-0: of_spmi_register_devices: invalid sid on /soc/qcom,spmi@200f000/qcom,pm8950@0
<6>[    0.235784,2] platform 4080000.qcom,mss: assigned reserved memory node modem_region@0
<6>[    0.236207,2] platform c200000.qcom,lpass: assigned reserved memory node adsp_fw_region@0
<6>[    0.236437,2] platform 1de0000.qcom,venus: assigned reserved memory node venus_region@0
<6>[    0.237010,2] platform a21b000.qcom,pronto: assigned reserved memory node wcnss_fw_region@0
<6>[    0.238703,2] apc_mem_acc_corner: 0 <--> 0 mV 
<6>[    0.239533,2] gfx_mem_acc_corner: 0 <--> 0 mV 
<6>[    0.252012,2] persistent_ram: persistent_ram: paddr: ef000000, vaddr: e9280000, buf size = 0x1fff4
<6>[    0.252037,2] persistent_ram: persistent_ram: paddr: ef020000, vaddr: e9300000, buf size = 0x3fff4
<6>[    0.255373,2] persistent_ram: persistent_ram: paddr: ef060000, vaddr: e9262000, buf size = 0x7f4
<6>[    0.256381,2] console [pstore-1] enabled
<6>[    0.256391,2] pstore: Registered ramoops as persistent store backend
<6>[    0.256404,2] ramoops: attached 0x80000@0xef000000, ecc: 0/0
<6>[    0.257914,2] hw-breakpoint: found 5 (+1 reserved) breakpoint and 4 watchpoint registers.
<6>[    0.257929,2] hw-breakpoint: maximum watchpoint size is 8 bytes.
<4>[    0.259946,2] __of_mpm_init(): MPM driver mapping exists
<4>[    0.261259,2] msm_rpm_glink_dt_parse: qcom,rpm-glink compatible not matches
<6>[    0.261274,2] msm_rpm_dev_probe: APSS-RPM communication over SMD
<6>[    0.261287,2] smd_open() before smd_init()
<3>[    0.263017,2] msm_mpm_dev_probe(): Cannot get clk resource for XO: -517
<3>[    0.268681,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.268859,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.269018,2] smd_channel_probe_now: allocation table not initialized
<3>[    0.275091,2] GFX_LDO: msm_gfx_ldo_parse_dt: Unable to parse CX parameters rc=-517
<3>[    0.275112,2] GFX_LDO: msm_gfx_ldo_probe: Unable to pasrse dt rc=-517
<6>[    0.276617,2] pm8953_s5: 400 <--> 1140 mV at 870 mV normal idle 
<6>[    0.276989,2] pm8953_s5_avs_limit: 400 <--> 1140 mV 
<6>[    0.277155,2] spm_regulator_probe: name=pm8953_s5, range=LV, voltage=870000 uV, mode=AUTO, step rate=1200 uV/us
<6>[    0.285225,2] msm_thermal:vdd_restriction_reg_init Defer regulator vdd-dig probe
<3>[    0.285247,2] msm_thermal:probe_vdd_rstr Err regulator init. err:-517. KTM continues.
<6>[    0.285267,2] msm-thermal soc:qcom,msm-thermal: probe_vdd_rstr:Failed reading node=/soc/qcom,msm-thermal, key=qcom,max-freq-level. err=-517. KTM continues
<3>[    0.285282,2] msm_thermal:msm_thermal_dev_probe Failed reading node=/soc/qcom,msm-thermal, key=qcom,online-hotplug-core. err:-517
<6>[    0.286754,2] sps:sps is ready.
<6>[    0.290369,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Speed bin: 2 PVS Version: 0
<3>[    0.290615,2] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Get vdd-mx regulator!!!
<4>[    0.291262,3] msm_rpm_glink_dt_parse: qcom,rpm-glink compatible not matches
<6>[    0.291278,3] msm_rpm_dev_probe: APSS-RPM communication over SMD
<6>[    0.292127,3] pm8953_s1: 870 <--> 1156 mV at 1000 mV normal idle 
<6>[    0.292934,3] pm8953_s2_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.293510,3] pm8953_s2_floor_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.294000,3] pm8953_s2_level_ao: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.294679,3] pm8953_s3: 1225 mV normal idle 
<6>[    0.295373,3] pm8953_s4: 1900 <--> 2050 mV at 1900 mV normal idle 
<6>[    0.296057,3] pm8953_s7_level: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.296573,3] pm8953_s7_level_ao: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.297155,3] pm8953_s7_level_so: 0 <--> 0 mV at 0 mV normal idle 
<6>[    0.297841,3] pm8953_l1: 1000 <--> 1100 mV at 1000 mV normal idle 
<6>[    0.298540,3] pm8953_l2: 1200 mV normal idle 
<6>[    0.299226,3] pm8953_l3: 925 mV normal idle 
<6>[    0.299924,3] pm8953_l5: 1800 mV normal idle 
<6>[    0.300986,3] pm8953_l6: 1800 mV normal idle 
<6>[    0.301691,3] pm8953_l7: 1800 <--> 1900 mV at 1800 mV normal idle 
<6>[    0.302225,3] pm8953_l7_ao: 1800 <--> 1900 mV at 1800 mV normal idle 
<6>[    0.302942,3] pm8953_l8: 2900 mV normal idle 
<6>[    0.303687,3] pm8953_l9: 3000 <--> 3300 mV at 3000 mV normal idle 
<6>[    0.305115,3] pm8953_l10: 2850 mV normal idle 
<6>[    0.305838,3] pm8953_l11: 2950 mV normal idle 
<6>[    0.306561,3] pm8953_l12: 1800 <--> 2950 mV at 1800 mV normal idle 
<6>[    0.307327,3] pm8953_l13: 3125 mV normal idle 
<6>[    0.308053,3] pm8953_l16: 1800 mV normal idle 
<6>[    0.308741,3] pm8953_l17: 2800 mV normal idle 
<6>[    0.309430,3] pm8953_l19: 1200 <--> 1350 mV at 1200 mV normal idle 
<6>[    0.310133,3] pm8953_l22: 2800 mV normal idle 
<6>[    0.310830,3] pm8953_l23: 1200 mV normal idle 
<3>[    0.311313,3] msm_mpm_dev_probe(): Cannot get clk resource for XO: -517
<6>[    0.311653,3] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 1: target-volt = 580000 uV
<6>[    0.311668,3] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 2: target-volt = 650000 uV
<6>[    0.311681,3] GFX_LDO: msm_gfx_ldo_voltage_init: LDO corner 3: target-volt = 720000 uV
<6>[    0.311699,3] GFX_LDO: msm_gfx_ldo_adjust_init_voltage: adjusted the open-loop voltage[1] 580000 -> 615000
<6>[    0.311712,3] GFX_LDO: msm_gfx_ldo_adjust_init_voltage: adjusted the open-loop voltage[2] 650000 -> 675000
<6>[    0.311726,3] GFX_LDO: msm_gfx_ldo_voltage_init: LDO-mode fuse disabled by default
<6>[    0.312032,3] msm_gfx_ldo: 0 <--> 0 mV at 0 mV 
<6>[    0.312836,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: speed bin = 2
<6>[    0.312852,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: CPR fusing revision = 3
<6>[    0.312865,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: foundry id = 2
<6>[    0.312878,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: CPR misc fuse value = 0
<6>[    0.312919,3] cpr4_msm8953_apss_read_fuse_data: apc_corner: Voltage boost fuse config = 0 boost = disable
<6>[    0.313062,3] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused   LowSVS: open-loop= 625000 uV
<6>[    0.313075,3] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused      SVS: open-loop= 700000 uV
<6>[    0.313087,3] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused      NOM: open-loop= 815000 uV
<6>[    0.313099,3] cpr4_msm8953_apss_calculate_open_loop_voltages: apc_corner: fused TURBO_L1: open-loop= 915000 uV
<6>[    0.313179,3] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused   LowSVS: quot[ 7]= 442
<6>[    0.313193,3] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused      SVS: quot[ 7]= 567, quot_offset[ 7]= 120
<6>[    0.313208,3] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused      NOM: quot[ 7]= 791, quot_offset[ 7]= 220
<6>[    0.313221,3] cpr4_msm8953_apss_calculate_target_quotients: apc_corner: fused TURBO_L1: quot[ 7]= 978, quot_offset[ 7]= 185
<6>[    0.313625,3] cpr4_apss_init_aging: apc: sensor 6 aging init quotient diff = 12, aging RO scale = 2800 QUOT/V
<6>[    0.313807,3] cpr3_regulator_init_ctrl: apc: Default CPR mode = HW closed-loop
<6>[    0.313959,3] apc_corner: 0 <--> 0 mV at 0 mV 
<6>[    0.315480,3] msm_thermal:sensor_mgr_init_threshold threshold id already initialized
<6>[    0.316176,3] msm_thermal:vdd_restriction_reg_init Defer vdd rstr freq init.
<6>[    0.319301,3] qcom,gcc-8953 1800000.qcom,gcc: Venus speed bin: 2
<4>[    0.340754,3] branch_clk_handoff: gcc_usb_phy_cfg_ahb_clk clock is enabled in HW
<4>[    0.340774,3] branch_clk_handoff: even though ENABLE_BIT is not set
<6>[    0.342735,3] qcom,gcc-8953 1800000.qcom,gcc: Registered GCC clocks
<6>[    0.342935,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: Speed bin: 2 PVS Version: 0
<3>[    0.345479,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,dfs-sid-c0
<3>[    0.345497,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No DFS SID tables found for Cluster-0
<3>[    0.345514,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,link-sid-c0
<3>[    0.345528,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No Link SID tables found for Cluster-0
<3>[    0.345544,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,lmh-sid-c0
<3>[    0.345558,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No LMH SID tables found for Cluster-0
<3>[    0.345569,3] ramp_lmh_sid: Use Default LMH SID
<3>[    0.345579,3] ramp_dfs_sid: Use Default DFS SID
<3>[    0.345588,3] ramp_link_sid: Use Default Link SID
<3>[    0.345642,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,dfs-sid-c1
<3>[    0.345657,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No DFS SID tables found for Cluster-1
<3>[    0.345672,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,link-sid-c1
<3>[    0.345686,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No Link SID tables found for Cluster-1
<3>[    0.345702,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: missing qcom,lmh-sid-c1
<3>[    0.345716,3] cpu-clock-8953 b114000.qcom,cpu-clock-8953: No LMH SID tables found for Cluster-1
<3>[    0.345726,3] ramp_lmh_sid: Use Default LMH SID
<3>[    0.345735,3] ramp_dfs_sid: Use Default DFS SID
<3>[    0.345744,3] ramp_link_sid: Use Default Link SID
<6>[    0.345806,3] clock_rcgwr_init: RCGwR  Init Completed
<6>[    0.346218,3] populate_opp_table: clock-cpu-8953: OPP tables populated (cpu 3 and 7)
<6>[    0.346232,3] print_opp_table: clock_cpu: a53 C0: OPP voltage for 652800000: 1
<6>[    0.346243,3] print_opp_table: clock_cpu: a53 C0: OPP voltage for 2016000000: 7
<6>[    0.346254,3] print_opp_table: clock_cpu: a53 C1: OPP voltage for 652800000: 1
<6>[    0.346263,3] print_opp_table: clock_cpu: a53 C2: OPP voltage for 2016000000: 7
<6>[    0.348311,2] gcc-gfx-8953 1800000.qcom,gcc-gfx: Registered GCC GFX clocks.
<3>[    0.406595,2] msm_cpuss_dump soc:cpuss_dump: Couldn't get memory for dumping
<3>[    0.406624,2] msm_cpuss_dump soc:cpuss_dump: Couldn't get memory for dumping
<6>[    0.410338,2] KPI: Bootloader start count = 71987
<6>[    0.410357,2] KPI: Bootloader end count = 101172
<6>[    0.410367,2] KPI: Bootloader display count = 3153771795
<6>[    0.410376,2] KPI: Bootloader load kernel count = 2047
<6>[    0.410387,2] KPI: Kernel MPM timestamp = 164488
<6>[    0.410395,2] KPI: Kernel MPM Clock frequency = 32768
<6>[    0.410423,2] socinfo_print: v0.10, id=293, ver=1.1, raw_id=70, raw_ver=1, hw_plat=8, hw_plat_ver=65536
<6>[    0.410423,2]  accessory_chip=0, hw_plat_subtype=0, pmic_model=65558, pmic_die_revision=65536 foundry_id=3 serial_number=597243328
<6>[    0.411684,2] dummy_vreg: no parameters
<6>[    0.411976,2] vci_fci: no parameters
<5>[    0.413343,2] SCSI subsystem initialized
<6>[    0.414258,2] usbcore: registered new interface driver usbfs
<6>[    0.414333,2] usbcore: registered new interface driver hub
<6>[    0.414586,2] usbcore: registered new device driver usb
<6>[    0.415706,2] i2c-msm-v2 78b6000.i2c: probing driver i2c-msm-v2
<3>[    0.416037,2] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.416052,2] i2c-msm-v2 78b6000.i2c: msm_bus_scale_register_client(mstr-id:86):0 (not a problem)
<6>[    0.417564,0] i2c-msm-v2 78b7000.i2c: probing driver i2c-msm-v2
<3>[    0.417807,0] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.417821,0] i2c-msm-v2 78b7000.i2c: msm_bus_scale_register_client(mstr-id:86):0 (not a problem)
<6>[    0.418048,0] i2c-msm-v2 78b7000.i2c: irq:50 when no active transfer
<6>[    0.418772,1] i2c-msm-v2 7af5000.i2c: probing driver i2c-msm-v2
<3>[    0.418979,1] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.418993,1] i2c-msm-v2 7af5000.i2c: msm_bus_scale_register_client(mstr-id:84):0 (not a problem)
<6>[    0.420512,0] i2c-msm-v2 7af7000.i2c: probing driver i2c-msm-v2
<3>[    0.420744,0] AXI: msm_bus_scale_register_client(): msm_bus_scale_register_client: Bus driver not ready.
<6>[    0.420757,0] i2c-msm-v2 7af7000.i2c: msm_bus_scale_register_client(mstr-id:84):0 (not a problem)
<6>[    0.422301,0] media: Linux media interface: v0.10
<6>[    0.422375,0] Linux video capture interface: v2.00
<6>[    0.422463,0] EDAC MC: Ver: 3.0.0
<6>[    0.510295,1] cpufreq: driver msm up and running
<6>[    0.510704,1] platform soc:qcom,ion:qcom,ion-heap@8: assigned reserved memory node secure_region@0
<6>[    0.510877,1] platform soc:qcom,ion:qcom,ion-heap@27: assigned reserved memory node qseecom_region@0
<6>[    0.511067,1] ION heap system created
<6>[    0.511169,1] ION heap mm created at 0xf6400000 with size 9800000
<6>[    0.511179,1] ION heap qsecom created at 0xf5400000 with size 1000000
<3>[    0.511749,1] msm_bus_fabric_init_driver
<6>[    0.521075,1] qcom,qpnp-power-on qpnp-power-on-1: PMIC@SID0 Power-on reason: Triggered from PON1 (secondary PMIC) and 'warm' boot
<6>[    0.521099,1] qcom,qpnp-power-on qpnp-power-on-1: PMIC@SID0: Power-off reason: Triggered from PS_HOLD (PS_HOLD/MSM controlled shutdown)
<6>[    0.521249,1] input: qpnp_pon as /devices/virtual/input/input0
<6>[    0.521590,1] pon_spare_reg: no parameters
<6>[    0.521652,1] qcom,qpnp-power-on qpnp-power-on-13: No PON config. specified
<6>[    0.521702,1] qcom,qpnp-power-on qpnp-power-on-13: PMIC@SID2 Power-on reason: Triggered from PON1 (secondary PMIC) and 'warm' boot
<6>[    0.521718,1] qcom,qpnp-power-on qpnp-power-on-13: PMIC@SID2: Power-off reason: Triggered from PS_HOLD (PS_HOLD/MSM controlled shutdown)
<6>[    0.521884,1] PMIC@SID0: (null) v1.0 options: 2, 2, 0, 0
<6>[    0.521975,1] PMIC@SID2: PMI8950 v2.0 options: 0, 0, 0, 0
<3>[    0.522839,1] ipa ipa2_uc_state_check:296 uC interface not initialized
<3>[    0.522853,1] ipa ipa_sps_irq_control_all:942 EP (2) not allocated.
<3>[    0.522860,1] ipa ipa_sps_irq_control_all:942 EP (5) not allocated.
<6>[    0.524209,5] sps:BAM 0x07904000 is registered.
<6>[    0.524669,5] sps:BAM 0x07904000 (va:0xe97c0000) enabled: ver:0x27, number of pipes:20
<6>[    0.527476,5] IPA driver initialization was successful.
<6>[    0.528542,5] gdsc_venus: no parameters
<6>[    0.528765,5] gdsc_mdss: no parameters
<6>[    0.529060,5] gdsc_jpeg: no parameters
<6>[    0.529409,5] gdsc_vfe: no parameters
<6>[    0.529758,5] gdsc_vfe1: no parameters
<6>[    0.529956,5] gdsc_cpp: no parameters
<6>[    0.530126,5] gdsc_oxili_gx: no parameters
<6>[    0.530175,5] gdsc_oxili_gx: supplied by msm_gfx_ldo
<6>[    0.530340,5] gdsc_venus_core0: fast normal 
<6>[    0.530499,5] gdsc_oxili_cx: no parameters
<6>[    0.530625,5] gdsc_usb30: no parameters
<6>[    0.531585,5] mdss_pll_probe: MDSS pll label = MDSS DSI 0 PLL
<6>[    0.531592,5] mdss_pll_probe: mdss_pll_probe: label=MDSS DSI 0 PLL PLL SSC enabled
<4>[    0.531608,5] mdss_pll_util_parse_dt_supply: : error reading ulp load. rc=-22
<6>[    0.532089,5] dsi_pll_clock_register_8996: Registered DSI PLL ndx=0 clocks successfully
<6>[    0.532110,5] mdss_pll_probe: MDSS pll label = MDSS DSI 1 PLL
<6>[    0.532115,5] mdss_pll_probe: mdss_pll_probe: label=MDSS DSI 1 PLL PLL SSC enabled
<4>[    0.532129,5] mdss_pll_util_parse_dt_supply: : error reading ulp load. rc=-22
<3>[    0.533217,5] pll_is_pll_locked_8996: DSI PLL ndx=1 status=0 failed to Lock
<6>[    0.533566,5] dsi_pll_clock_register_8996: Registered DSI PLL ndx=1 clocks successfully
<6>[    0.534000,5] msm_iommu 1e00000.qcom,iommu: device apps_iommu (model: 500) mapped at e9b80000, with 21 ctx banks
<6>[    0.538735,5] msm_iommu_ctx 1e20000.qcom,iommu-ctx: context adsp_elf using bank 0
<6>[    0.538854,5] msm_iommu_ctx 1e21000.qcom,iommu-ctx: context adsp_sec_pixel using bank 1
<6>[    0.538971,5] msm_iommu_ctx 1e22000.qcom,iommu-ctx: context mdp_1 using bank 2
<6>[    0.539091,5] msm_iommu_ctx 1e23000.qcom,iommu-ctx: context venus_fw using bank 3
<6>[    0.539209,5] msm_iommu_ctx 1e24000.qcom,iommu-ctx: context venus_sec_non_pixel using bank 4
<6>[    0.539332,5] msm_iommu_ctx 1e25000.qcom,iommu-ctx: context venus_sec_bitstream using bank 5
<6>[    0.539450,5] msm_iommu_ctx 1e26000.qcom,iommu-ctx: context venus_sec_pixel using bank 6
<6>[    0.539591,5] msm_iommu_ctx 1e28000.qcom,iommu-ctx: context pronto_pil using bank 8
<6>[    0.539731,5] msm_iommu_ctx 1e29000.qcom,iommu-ctx: context q6 using bank 9
<6>[    0.539869,5] msm_iommu_ctx 1e2a000.qcom,iommu-ctx: context periph_rpm using bank 10
<6>[    0.540008,5] msm_iommu_ctx 1e2b000.qcom,iommu-ctx: context lpass using bank 11
<6>[    0.540164,5] msm_iommu_ctx 1e2f000.qcom,iommu-ctx: context adsp_io using bank 15
<6>[    0.540306,5] msm_iommu_ctx 1e30000.qcom,iommu-ctx: context adsp_opendsp using bank 16
<6>[    0.540447,5] msm_iommu_ctx 1e31000.qcom,iommu-ctx: context adsp_shared using bank 17
<6>[    0.540586,5] msm_iommu_ctx 1e32000.qcom,iommu-ctx: context cpp using bank 18
<6>[    0.540728,5] msm_iommu_ctx 1e33000.qcom,iommu-ctx: context jpeg_enc0 using bank 19
<6>[    0.540877,5] msm_iommu_ctx 1e34000.qcom,iommu-ctx: context vfe using bank 20
<6>[    0.541017,5] msm_iommu_ctx 1e35000.qcom,iommu-ctx: context mdp_0 using bank 21
<6>[    0.541156,5] msm_iommu_ctx 1e36000.qcom,iommu-ctx: context venus_ns using bank 22
<6>[    0.541296,5] msm_iommu_ctx 1e38000.qcom,iommu-ctx: context ipa using bank 24
<6>[    0.541436,5] msm_iommu_ctx 1e37000.qcom,iommu-ctx: context access_control using bank 23
<6>[    0.543181,5] arm-smmu 1c40000.arm,smmu-kgsl: regulator defer delay 80
<6>[    0.544781,5] Advanced Linux Sound Architecture Driver Initialized.
<6>[    0.545431,5] Bluetooth: e6e05ed8
<6>[    0.545450,5] NET: Registered protocol family 31
<6>[    0.545455,5] Bluetooth: e6e05ed8
<6>[    0.545463,5] Bluetooth: e6e05ed0Bluetooth: e6e05ec0
<6>[    0.545492,5] Bluetooth: e6e05ec0<6>[    0.545721,5] cfg80211: Calling CRDA to update world regulatory domain
<6>[    0.545737,5] cfg80211: World regulatory domain updated:
<6>[    0.545742,5] cfg80211:  DFS Master region: unset
<6>[    0.545746,5] cfg80211:   (start_freq - end_freq @ bandwidth), (max_antenna_gain, max_eirp), (dfs_cac_time)
<6>[    0.545754,5] cfg80211:   (2402000 KHz - 2472000 KHz @ 40000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.545759,5] cfg80211:   (2457000 KHz - 2482000 KHz @ 40000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.545765,5] cfg80211:   (2474000 KHz - 2494000 KHz @ 20000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.545770,5] cfg80211:   (5170000 KHz - 5250000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.545776,5] cfg80211:   (5250000 KHz - 5330000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.545782,5] cfg80211:   (5490000 KHz - 5710000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.545788,5] cfg80211:   (5735000 KHz - 5835000 KHz @ 80000 KHz), (N/A, 2000 mBm), (N/A)
<6>[    0.545793,5] cfg80211:   (57240000 KHz - 63720000 KHz @ 2160000 KHz), (N/A, 0 mBm), (N/A)
<6>[    0.546098,6] ibb_reg: 4600 <--> 6000 mV at 5500 mV 
<6>[    0.546319,6] lab_reg: 4600 <--> 6000 mV at 5500 mV 
<6>[    0.548054,5] Switched to clocksource arch_sys_counter
<6>[    0.573976,5] NET: Registered protocol family 2
<6>[    0.574316,5] TCP established hash table entries: 8192 (order: 3, 32768 bytes)
<6>[    0.574354,5] TCP bind hash table entries: 8192 (order: 4, 65536 bytes)
<6>[    0.574412,5] TCP: Hash tables configured (established 8192 bind 8192)
<6>[    0.574438,5] TCP: reno registered
<6>[    0.574445,5] UDP hash table entries: 512 (order: 2, 16384 bytes)
<6>[    0.574462,5] UDP-Lite hash table entries: 512 (order: 2, 16384 bytes)
<6>[    0.574569,5] NET: Registered protocol family 1
<6>[    0.575604,5] gcc-mdss-8953 1800000.qcom,gcc-mdss: Registered GCC MDSS clocks.
<6>[    0.576056,5] Trying to unpack rootfs image as initramfs...
<6>[    0.708418,5] Freeing initrd memory: 6880K
<6>[    0.710749,5] hw perfevents: enabled with ARMv8 Cortex-A53 PMU driver, 7 counters available
<6>[    0.713823,5] futex hash table entries: 2048 (order: 5, 131072 bytes)
<6>[    0.713894,5] audit: initializing netlink subsys (disabled)
<5>[    0.713928,5] audit: type=2000 audit(0.713:1): initialized
<4>[    0.714220,5] vmscan: error setting kswapd cpu affinity mask
<5>[    0.717532,5] VFS: Disk quotas dquot_6.5.2
<4>[    0.717612,5] Dquot-cache hash table entries: 1024 (order 0, 4096 bytes)
<6>[    0.718415,5] exFAT: Version 1.2.9
<6>[    0.718834,5] Registering sdcardfs 0.1
<6>[    0.718946,5] fuse init (API version 7.23)
<7>[    0.719244,5] SELinux:  Registering netfilter hooks
<6>[    0.720808,5] bounce: pool size: 64 pages
<6>[    0.720890,5] Block layer SCSI generic (bsg) driver version 0.4 loaded (major 246)
<6>[    0.720899,5] io scheduler noop registered
<6>[    0.720907,5] io scheduler deadline registered
<6>[    0.720923,5] io scheduler cfq registered (default)
<3>[    0.723948,5] msm_dss_get_res_byname: 'vbif_nrt_phys' resource not found
<3>[    0.723958,5] mdss_mdp_probe+0x1a0/0x10d8->msm_dss_ioremap_byname: 'vbif_nrt_phys' msm_dss_get_res_byname failed
<3>[    0.724400,5] mdss_mdp_irq_clk_register: unable to get clk: lut_clk
<3>[    0.724886,5] No change in context(0==0), skip
<6>[    0.725584,5] mdss_mdp_pipe_addr_setup: type:0 ftchid:-1 xinid:0 num:0 rect:0 ndx:0x1 prio:0
<6>[    0.725601,5] mdss_mdp_pipe_addr_setup: type:1 ftchid:-1 xinid:1 num:3 rect:0 ndx:0x8 prio:1
<6>[    0.725607,5] mdss_mdp_pipe_addr_setup: type:1 ftchid:-1 xinid:5 num:4 rect:0 ndx:0x10 prio:2
<6>[    0.725624,5] mdss_mdp_pipe_addr_setup: type:2 ftchid:-1 xinid:2 num:6 rect:0 ndx:0x40 prio:3
<6>[    0.725639,5] mdss_mdp_pipe_addr_setup: type:3 ftchid:-1 xinid:7 num:10 rect:0 ndx:0x400 prio:0
<3>[    0.725650,5] mdss_mdp_parse_dt_handler: Error from prop qcom,mdss-pipe-sw-reset-off : u32 array read
<3>[    0.725752,5] mdss_mdp_parse_dt_handler: Error from prop qcom,mdss-ib-factor-overlap : u32 array read
<6>[    0.725971,5] xlog_status: enable:0, panic:1, dump:2
<6>[    0.726489,5] mdss_mdp_probe: mdss version = 0x10100000, bootloader display is on, num 1, intf_sel=0x00000100
<3>[    0.727912,5] mdss_smmu_util_parse_dt_clock: clocks are not defined
<6>[    0.727938,5] mdss_smmu_probe: iommu v2 domain[0] mapping and clk register successful!
<3>[    0.727956,5] mdss_smmu_util_parse_dt_clock: clocks are not defined
<6>[    0.727966,5] mdss_smmu_probe: iommu v2 domain[2] mapping and clk register successful!
<4>[    0.728951,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.728965,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.728977,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<6>[    0.729426,5] mdss_dsi_ctrl_probe: DSI Ctrl name = MDSS DSI CTRL->0
<6>[    0.729806,5] mdss_panel_parse_panel_config_dt: BL: panel=mipi_mot_vid_djn_1080p_550, manufacture_id(0xDA)= 0x1A controller_ver(0xDB)= 0xD5 controller_drv_ver(0XDC)= 0x45, full=0x000000000045D51A
<6>[    0.729815,5] mdss_dsi_find_panel_of_node: cmdline:0:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0 panel_name:qcom,mdss_dsi_mot_djn_550_1080p_vid_v0
<6>[    0.729859,5] mdss_dsi_panel_init: Panel Name = mipi_mot_vid_djn_1080p_550
<6>[    0.730023,5] mdss_dsi_panel_timing_from_dt: found new timing "qcom,mdss_dsi_mot_djn_550_1080p_vid_v0" (e6e05788)
<3>[    0.730057,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-post-panel-on-command
<3>[    0.730066,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-timing-switch-command
<4>[    0.730072,5] mdss_dsi_panel_get_dsc_cfg_np: cannot find dsc config node:
<3>[    0.730189,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-idle-on-command
<3>[    0.730198,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-idle-off-command
<6>[    0.730228,5] mdss_dsi_parse_panel_features: ulps feature disabled
<6>[    0.730235,5] mdss_dsi_parse_panel_features: ulps during suspend feature disabled
<6>[    0.730243,5] mdss_dsi_parse_dms_config: dynamic switch feature enabled: 0
<3>[    0.730330,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-on
<3>[    0.730339,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-off
<6>[    0.730382,5] mdss_panel_parse_param_prop: HBM feature enabled with 2 dt cmds
<6>[    0.730387,5] mdss_panel_parse_param_prop: HBM type = 1
<6>[    0.730422,5] mdss_panel_parse_param_prop: CABC feature enabled with 3 dt cmds
<3>[    0.730431,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-on
<3>[    0.730440,5] mdss_dsi_parse_dcs_cmds: failed, key=qcom,mdss-dsi-lp-mode-off
<4>[    0.730457,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.730468,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<4>[    0.730478,5] mdss_dsi_get_dt_vreg_data: error reading ulp load. rc=-22
<3>[    0.730646,5] mdss_dsi_parse_gpio_params:4125, TE gpio not specified
<6>[    0.730652,5] mdss_dsi_parse_gpio_params: bklt_en gpio not specified
<3>[    0.730688,5] msm_dss_get_res_byname: 'dsi_phy_regulator' resource not found
<3>[    0.730697,5] mdss_dsi_retrieve_ctrl_resources+0x124/0x1b8->msm_dss_ioremap_byname: 'dsi_phy_regulator' msm_dss_get_res_byname failed
<6>[    0.730704,5] mdss_dsi_retrieve_ctrl_resources: ctrl_base=e9782000 ctrl_size=400 phy_base=e9790400 phy_size=580
<6>[    0.730775,5] dsi_panel_device_register: Continuous splash enabled
<6>[    0.730951,5] mdss_register_panel: adding framebuffer device 1a94000.qcom,mdss_dsi_ctrl0
<6>[    0.732303,5] mdss_dsi_ctrl_probe: Dsi Ctrl->0 initialized, DSI rev:0x10040002, PHY rev:0x2
<6>[    0.732417,5] mdss_dsi_status_init: DSI status check interval:8000
<6>[    0.733045,5] mdss_register_panel: adding framebuffer device soc:qcom,mdss_wb_panel
<6>[    0.733475,5] mdss_fb_probe: fb0: split_mode:0 left:0 right:0
<6>[    0.733892,5] mdss_fb_register: FrameBuffer[0] 1080x1920 registered successfully!
<6>[    0.734163,5] mdss_fb_probe: fb1: split_mode:0 left:0 right:0
<6>[    0.734240,5] mdss_fb_register: FrameBuffer[1] 640x640 registered successfully!
<3>[    0.734325,5] mdss_mdp_splash_parse_dt: splash mem child node is not present
<6>[    0.734346,5] anx7805 anx7805_init: anx7805_init
<6>[    0.734369,0] anx7805 anx7805_init_async: anx7805_init_async
<3>[    0.736312,5] IPC_RTR: msm_ipc_router_smd_driver_register Already driver registered IPCRTR
<3>[    0.736332,5] IPC_RTR: msm_ipc_router_smd_driver_register Already driver registered IPCRTR
<6>[    0.740124,5] In memshare_probe, Memshare probe success
<5>[    0.741542,5] msm_rpm_log_probe: OK
<6>[    0.742382,5] subsys-pil-tz soc:qcom,kgsl-hyp: for a506_zap segments only will be dumped.
<6>[    0.743891,5] subsys-pil-tz 1de0000.qcom,venus: for venus segments only will be dumped.
<6>[    0.745772,5] mmi_unit_info (SMEM) for modem: version = 0x03, device = 'sanders', radio = 0x0, radio_str = 'INDIA', system_rev = 0x8400, system_serial = 0xc035992300000000, machine = 'Qualcomm Technologies, Inc. MSM ', barcode = 'ZY32286WPB', baseband = '', carrier = 'retin', pu_reason = 0x00004000
<3>[    0.745802,5] ACPU Bin is not available.
<6>[    0.745847,5] mmi_storage_info :eMMC: 64GB SAMSUNG RC14MB FV=0000000000000007
<6>[    0.746222,5] msm_serial_hs module loaded
<6>[    0.754374,5] platform 1c40000.qcom,kgsl-iommu:gfx3d_secure: assigned reserved memory node secure_region@0
<6>[    0.759128,5] brd: module loaded
<6>[    0.760610,5] loop: module loaded
<6>[    0.760873,5] zram: Added device: zram0
<6>[    0.761170,5] QSEECOM: qseecom_probe: qseecom.qsee_version = 0x1001000
<4>[    0.761193,5] QSEECOM: qseecom_retrieve_ce_data: Device does not support PFE
<6>[    0.761201,5] QSEECOM: qseecom_probe: qseecom clocks handled by other subsystem
<4>[    0.761208,5] QSEECOM: qseecom_probe: qsee reentrancy support phase is not defined, setting to default 0
<4>[    0.761662,5] QSEECOM: qseecom_probe: qseecom.whitelist_support = 1
<6>[    0.763027,5] alsa-to-h2w soc:alsa_to_h2w: alsa_to_h2w_probe success
<4>[    0.763657,5] i2c-core: driver [tabla-i2c-core] using legacy suspend method
<4>[    0.763662,5] i2c-core: driver [tabla-i2c-core] using legacy resume method
<4>[    0.763725,5] i2c-core: driver [wcd9xxx-i2c-core] using legacy suspend method
<4>[    0.763730,5] i2c-core: driver [wcd9xxx-i2c-core] using legacy resume method
<4>[    0.763797,5] i2c-core: driver [tasha-i2c-core] using legacy suspend method
<4>[    0.763802,5] i2c-core: driver [tasha-i2c-core] using legacy resume method
<6>[    0.764031,5] Loading pn544 driver
<6>[    0.764144,5] nfc: succeed in obtaining nfc_clk from msm pmic
<4>[    0.764307,5] 5-0028 supply vdd not found, using dummy regulator
<6>[    0.764608,5] QCE50: __qce_get_device_tree_data: BAM Apps EE is not defined, setting to default 1
<6>[    0.765124,5] qce 720000.qcedev: Qualcomm Crypto 5.3.3 device found @0x720000
<6>[    0.765133,5] qce 720000.qcedev: CE device = 0x0
<6>[    0.765133,5] IO base, CE = 0xe9b40000
<6>[    0.765133,5] Consumer (IN) PIPE 2,    Producer (OUT) PIPE 3
<6>[    0.765133,5] IO base BAM = 0x00000000
<6>[    0.765133,5] BAM IRQ 59
<6>[    0.765133,5] Engines Availability = 0x2010853
<6>[    0.765291,5] sps:BAM 0x00704000 is registered.
<6>[    0.765445,5] sps:BAM 0x00704000 (va:0xea840000) enabled: ver:0x27, number of pipes:8
<6>[    0.765637,5] QCE50: qce_sps_init:  Qualcomm MSM CE-BAM at 0x0000000000704000 irq 59
<6>[    0.768339,5] QCE50: __qce_get_device_tree_data: BAM Apps EE is not defined, setting to default 1
<6>[    0.769095,5] qcrypto 720000.qcrypto: Qualcomm Crypto 5.3.3 device found @0x720000
<6>[    0.769105,5] qcrypto 720000.qcrypto: CE device = 0x0
<6>[    0.769105,5] IO base, CE = 0xea880000
<6>[    0.769105,5] Consumer (IN) PIPE 4,    Producer (OUT) PIPE 5
<6>[    0.769105,5] IO base BAM = 0x00000000
<6>[    0.769105,5] BAM IRQ 59
<6>[    0.769105,5] Engines Availability = 0x2010853
<6>[    0.769360,5] QCE50: qce_sps_init:  Qualcomm MSM CE-BAM at 0x0000000000704000 irq 59
<6>[    0.771534,5] qcrypto 720000.qcrypto: qcrypto-ecb-aes
<6>[    0.771606,5] qcrypto 720000.qcrypto: qcrypto-cbc-aes
<6>[    0.771676,5] qcrypto 720000.qcrypto: qcrypto-ctr-aes
<6>[    0.771750,5] qcrypto 720000.qcrypto: qcrypto-ecb-des
<6>[    0.771820,5] qcrypto 720000.qcrypto: qcrypto-cbc-des
<6>[    0.771890,5] qcrypto 720000.qcrypto: qcrypto-ecb-3des
<6>[    0.771959,5] qcrypto 720000.qcrypto: qcrypto-cbc-3des
<6>[    0.772029,5] qcrypto 720000.qcrypto: qcrypto-xts-aes
<6>[    0.772100,5] qcrypto 720000.qcrypto: qcrypto-sha1
<6>[    0.772170,5] qcrypto 720000.qcrypto: qcrypto-sha256
<6>[    0.772243,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-aes
<6>[    0.772314,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-des
<6>[    0.772390,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha1-cbc-3des
<6>[    0.772461,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-aes
<6>[    0.772533,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-des
<6>[    0.772604,5] qcrypto 720000.qcrypto: qcrypto-aead-hmac-sha256-cbc-3des
<6>[    0.772675,5] qcrypto 720000.qcrypto: qcrypto-hmac-sha1
<6>[    0.772748,5] qcrypto 720000.qcrypto: qcrypto-hmac-sha256
<6>[    0.772819,5] qcrypto 720000.qcrypto: qcrypto-aes-ccm
<6>[    0.772891,5] qcrypto 720000.qcrypto: qcrypto-rfc4309-aes-ccm
<3>[    0.773627,5] qcom_ice_get_device_tree_data: No vdd-hba-supply regulator, assuming not needed
<6>[    0.773720,5] ICE IRQ = 60
<6>[    0.774427,5] SCSI Media Changer driver v0.25 
<3>[    0.775824,5] spi_qsd 7af8000.spi: init_resources: unable to get core_clk
<3>[    0.776562,5] sps: BAM device 0x07884000 is not registered yet.
<6>[    0.776725,5] sps:BAM 0x07884000 is registered.
<6>[    0.777230,5] sps:BAM 0x07884000 (va:0xe9b20000) enabled: ver:0x19, number of pipes:12
<6>[    0.777865,5] tun: Universal TUN/TAP device driver, 1.6
<6>[    0.777870,5] tun: (C) 1999-2004 Max Krasnyansky <<EMAIL>>
<6>[    0.777924,5] PPP generic driver version 2.4.2
<6>[    0.777998,5] PPP BSD Compression module registered
<6>[    0.778005,5] PPP Deflate Compression module registered
<6>[    0.778023,5] PPP MPPE Compression module registered
<6>[    0.778033,5] NET: Registered protocol family 24
<6>[    0.778627,5] wcnss_wlan probed in built-in mode
<6>[    0.779253,5] pegasus: v0.9.3 (2013/04/25), Pegasus/Pegasus II USB Ethernet driver
<6>[    0.779317,5] usbcore: registered new interface driver pegasus
<6>[    0.779357,5] usbcore: registered new interface driver asix
<6>[    0.779386,5] usbcore: registered new interface driver ax88179_178a
<6>[    0.779415,5] usbcore: registered new interface driver cdc_ether
<6>[    0.779445,5] usbcore: registered new interface driver net1080
<6>[    0.779474,5] usbcore: registered new interface driver cdc_subset
<6>[    0.779503,5] usbcore: registered new interface driver zaurus
<6>[    0.779534,5] usbcore: registered new interface driver MOSCHIP usb-ethernet driver
<6>[    0.779659,5] usbcore: registered new interface driver cdc_ncm
<3>[    0.780640,5] scm_call failed: func id 0x2000c16, ret: -1, syscall returns: 0x0, 0x0, 0x0
<6>[    0.780647,5] hyp_assign_table: Failed to assign memory protection, ret = -5
<3>[    0.780654,5] msm_sharedmem: setup_shared_ram_perms: hyp_assign_phys failed IPA=0x0160xf4500000 size=1572864 err=-5
<6>[    0.780738,5] msm_sharedmem: msm_sharedmem_probe: Device created for client 'rmtfs'
<6>[    0.782416,5] msm_sharedmem: sharedmem_register_qmi: qmi init successful
<3>[    0.784300,5] msm-dwc3 7000000.ssusb: unable to get dbm device
<6>[    0.785276,5] ehci_hcd: USB 2.0 'Enhanced' Host Controller (EHCI) Driver
<6>[    0.785284,5] ehci-msm: Qualcomm On-Chip EHCI Host Controller
<6>[    0.785554,5] usbcore: registered new interface driver cdc_acm
<6>[    0.785559,5] cdc_acm: USB Abstract Control Model driver for USB modems and ISDN adapters
<6>[    0.785603,5] usbcore: registered new interface driver usb-storage
<6>[    0.785630,5] usbcore: registered new interface driver ums-alauda
<6>[    0.785657,5] usbcore: registered new interface driver ums-cypress
<6>[    0.785682,5] usbcore: registered new interface driver ums-datafab
<6>[    0.785708,5] usbcore: registered new interface driver ums-freecom
<6>[    0.785735,5] usbcore: registered new interface driver ums-isd200
<6>[    0.785761,5] usbcore: registered new interface driver ums-jumpshot
<6>[    0.785787,5] usbcore: registered new interface driver ums-karma
<6>[    0.785813,5] usbcore: registered new interface driver ums-onetouch
<6>[    0.785842,5] usbcore: registered new interface driver ums-sddr09
<6>[    0.785868,5] usbcore: registered new interface driver ums-sddr55
<6>[    0.785894,5] usbcore: registered new interface driver ums-usbat
<6>[    0.785959,5] usbcore: registered new interface driver usbserial
<6>[    0.785991,5] usbcore: registered new interface driver usb_ehset_test
<6>[    0.786463,5] gbridge_init: gbridge_init successs.
<6>[    0.786690,5] mousedev: PS/2 mouse device common for all mice
<6>[    0.786846,5] usbcore: registered new interface driver xpad
<6>[    0.786934,5] ft5x06_ts 3-0038: processing modifier config_modifier-charger[0]
<5>[    0.786939,5] using charger detection
<6>[    0.787038,5] ft5x06_ts 3-0038: processing modifier config_modifier-fps[1]
<5>[    0.787043,5] sing fingerprint sensor detection
<5>[    0.787049,5] using touch clip area in fps-active
<6>[    0.787174,5] input: ft5x06_ts as /devices/soc/78b7000.i2c/i2c-3/3-0038/input/input1
<3>[    1.013575,5] i2c-msm-v2 78b7000.i2c: msm_bus_scale_register_client(mstr-id:86):0xe (ok)
<6>[    1.013779,5] ft5x06_ts 3-0038: Device ID = 0x54
<6>[    1.013891,5] assigned minor 56
<6>[    1.014010,5] ft5x06_ts 3-0038: Create proc entry success
<6>[    1.014164,5] ft5x06_ts 3-0038: report rate = 110Hz
<6>[    1.014759,5] ft5x06_ts 3-0038: Firmware version = 6.0.0
<6>[    1.014910,5] vendor id 0x04 panel supplier is biel
<6>[    1.015084,5] ft5x06_ts 3-0038: Firmware id = 0x0001
<3>[    1.015160,5] ft5x06_ts 3-0038: Failed to register fps_notifier: -19
<3>[    1.015627,5] [NVT-ts] nvt_driver_init 1865: start
<6>[    1.015655,5] nvt_driver_init: finished
<6>[    1.016178,5] input: hbtp_vm as /devices/virtual/input/input2
<3>[    1.016945,5] fpc1020 spi8.0: Unable to read wakelock time
<6>[    1.017055,5] input: fpc1020 as /devices/virtual/input/input3
<6>[    1.017102,5] fpc1020 spi8.0: fpc1020_probe: ok
<6>[    1.017125,5] Driver ltr559 init.
<3>[    1.150230,0] i2c-msm-v2 7af7000.i2c: msm_bus_scale_register_client(mstr-id:84):0xf (ok)
<4>[    1.150438,0] ltr559_check_chip_id read the  LTR559_MANUFAC_ID is 0x5
<6>[    1.164615,0] ltr559_gpio_irq: INT No. 254
<6>[    1.164724,0] input: ltr559-ps as /devices/soc/7af7000.i2c/i2c-7/7-0023/input/input4
<4>[    1.164774,0] ltr559_probe input device success.
<6>[    1.165389,0] qcom,qpnp-rtc qpnp-rtc-8: rtc core: registered qpnp_rtc as rtc0
<6>[    1.165524,0] i2c /dev entries driver
<3>[    1.171436,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.173327,0] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.174015,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.174585,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.176345,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.177419,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.178468,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<5>[    1.178932,4] msm_actuator_platform_probe:2086 No valid actuator GPIOs data
<5>[    1.179012,4] msm_actuator_platform_probe:2086 No valid actuator GPIOs data
<3>[    1.179595,4] msm_eeprom_platform_probe failed 2029
<3>[    1.179958,4] msm_eeprom_platform_probe failed 2029
<3>[    1.180300,4] msm_eeprom_platform_probe failed 2029
<3>[    1.180930,4] msm_flash_get_pmic_source_info:1000 alternate current: read failed
<3>[    1.180936,4] msm_flash_get_pmic_source_info:1020 alternate max-current: read failed
<3>[    1.180942,4] msm_flash_get_pmic_source_info:1040 alternate duration: read failed
<3>[    1.180974,4] msm_flash_get_pmic_source_info:1000 alternate current: read failed
<3>[    1.180980,4] msm_flash_get_pmic_source_info:1020 alternate max-current: read failed
<3>[    1.180985,4] msm_flash_get_pmic_source_info:1040 alternate duration: read failed
<3>[    1.181018,4] msm_flash_get_pmic_source_info:1110 alternate current: read failed
<3>[    1.181023,4] msm_flash_get_pmic_source_info:1130 alternate current: read failed
<3>[    1.181056,4] msm_flash_get_pmic_source_info:1110 alternate current: read failed
<3>[    1.181061,4] msm_flash_get_pmic_source_info:1130 alternate current: read failed
<5>[    1.181067,4] msm_flash_get_dt_data:1203 No valid flash GPIOs data
<3>[    1.181072,4] msm_camera_get_dt_vreg_data:1198 number of entries is 0 or not present in dts
<3>[    1.181772,4] adp1660 i2c_add_driver success
<6>[    1.187642,4] MSM-CPP cpp_init_hardware:1005 CPP HW Version: 0x40030003
<3>[    1.187651,4] MSM-CPP cpp_init_hardware:1023 stream_cnt:0
<3>[    1.188875,5] CAM-SOC msm_camera_get_reg_base:787 err: mem resource vfe_fuse not found
<3>[    1.188882,5] CAM-SOC msm_camera_get_res_size:830 err: mem resource vfe_fuse not found
<3>[    1.189923,5] CAM-SOC msm_camera_get_reg_base:787 err: mem resource vfe_fuse not found
<3>[    1.189929,5] CAM-SOC msm_camera_get_res_size:830 err: mem resource vfe_fuse not found
<3>[    1.196809,5] __msm_jpeg_init:1537] Jpeg Device id 0
<6>[    1.198396,5] usbcore: registered new interface driver uvcvideo
<6>[    1.198402,5] USB Video Class driver (1.1.1)
<6>[    1.198975,5] FG: fg_check_ima_exception: Initial ima_err_sts=0 ima_exp_sts=0 ima_hw_sts=22
<6>[    1.199205,5] FG: fg_empty_soc_irq_handler: triggered 0x20
<3>[    1.200238,5] FG: fg_power_get_property: ESR Bad: -22 mOhm
<3>[    1.200485,5] FG: fg_power_get_property: ESR Bad: -22 mOhm
<6>[    1.200536,5] FG: fg_probe: FG Probe success - FG Revision DIG:3.1 ANA:1.2 PMIC subtype=17
<3>[    1.202065,5] unable to find DT imem DLOAD mode node
<3>[    1.202437,5] unable to find DT imem EDLOAD mode node
<4>[    1.203960,5] thermal thermal_zone1: failed to read out thermal zone 1
<4>[    1.204327,6] thermal thermal_zone2: failed to read out thermal zone 2
<4>[    1.204485,6] thermal thermal_zone3: failed to read out thermal zone 3
<4>[    1.204638,6] thermal thermal_zone4: failed to read out thermal zone 4
<3>[    1.205084,6] qpnp_vadc_read: no vadc_chg_vote found
<3>[    1.205089,6] qpnp_vadc_get_temp: VADC read error with -22
<4>[    1.205096,6] thermal thermal_zone5: failed to read out thermal zone 5
<6>[    1.226750,6] device-mapper: uevent: version 1.0.3
<6>[    1.226881,6] device-mapper: ioctl: 4.28.0-ioctl (2014-09-17) initialised: <EMAIL>
<6>[    1.226964,6] device-mapper: req-crypt: dm-req-crypt successfully initalized.
<6>[    1.226964,6] 
<6>[    1.227601,6] sdhci: Secure Digital Host Controller Interface driver
<6>[    1.227605,6] sdhci: Copyright(c) Pierre Ossman
<6>[    1.227613,6] sdhci-pltfm: SDHCI platform and OF driver helper
<6>[    1.229706,1] qcom_ice_get_pdevice: found ice device c3a219c0
<6>[    1.229713,1] qcom_ice_get_pdevice: matching platform device e5830000
<6>[    1.233528,1] qcom_ice 7803000.sdcc1ice: QC ICE 2.1.44 device found @0xe99a0000
<6>[    1.233883,1] sdhci_msm 7824900.sdhci: No vmmc regulator found
<6>[    1.233889,1] sdhci_msm 7824900.sdhci: No vqmmc regulator found
<6>[    1.234188,1] mmc0: SDHCI controller on 7824900.sdhci [7824900.sdhci] using 32-bit ADMA in CMDQ mode
<4>[    1.267066,1] sdhci_msm 7864900.sdhci: sdhci_msm_probe: ICE device is not enabled
<6>[    1.281356,1] sdhci_msm 7864900.sdhci: No vmmc regulator found
<6>[    1.281363,1] sdhci_msm 7864900.sdhci: No vqmmc regulator found
<6>[    1.281671,1] mmc1: SDHCI controller on 7864900.sdhci [7864900.sdhci] using 32-bit ADMA in legacy mode
<6>[    1.302801,0] mmc0: Out-of-interrupt timeout is 50[ms]
<6>[    1.302807,0] mmc0: BKOPS_EN equals 0x2
<6>[    1.302812,0] mmc0: eMMC FW version: 0x07
<6>[    1.302817,0] mmc0: CMDQ supported: depth: 16
<6>[    1.302822,0] mmc0: cache barrier support 0 flush policy 0
<6>[    1.312430,0] cmdq_host_alloc_tdl: desc_size: 512 data_sz: 126976 slot-sz: 16
<6>[    1.312597,0] mmc0: CMDQ enabled on card
<6>[    1.312606,0] mmc0: new HS400 MMC card at address 0001
<6>[    1.312859,0] sdhci_msm_pm_qos_cpu_init (): voted for group #0 (mask=0xf) latency=2
<6>[    1.312867,0] sdhci_msm_pm_qos_cpu_init (): voted for group #1 (mask=0xf0) latency=2
<6>[    1.312957,0] mmcblk0: mmc0:0001 RC14MB 58.2 GiB 
<6>[    1.313041,0] mmcblk0rpmb: mmc0:0001 RC14MB partition 3 4.00 MiB
<6>[    1.313628,2] qcom,leds-atc leds-atc-20: atc_leds_probe success
<6>[    1.313735,2] hidraw: raw HID events driver (C) Jiri Kosina
<6>[    1.313929,1] tz_log 8600720.tz-log: Hyp log service is not supported
<6>[    1.314076,2] usbcore: registered new interface driver usbhid
<6>[    1.314080,2] usbhid: USB HID core driver
<6>[    1.314457,2] ashmem: initialized
<6>[    1.314595,0]  mmcblk0: p1 p2 p3 p4 p5 p6 p7 p8 p9 p10 p11 p12 p13 p14 p15 p16 p17 p18 p19 p20 p21 p22 p23 p24 p25 p26 p27 p28 p29 p30 p31 p32 p33 p34 p35 p36 p37 p38 p39 p40 p41 p42 p43 p44 p45 p46 p47 p48 p49 p50 p51 p52 p53 p54
<6>[    1.314809,2] qpnp_coincell_charger_show_state: enabled=Y, voltage=3200 mV, resistance=2100 ohm
<6>[    1.317648,2] bimc-bwmon 408000.qcom,cpu-bwmon: BW HWmon governor registered.
<3>[    1.319182,2] devfreq soc:qcom,cpubw: Couldn't update frequency transition information.
<3>[    1.319300,2] devfreq soc:qcom,mincpubw: Couldn't update frequency transition information.
<3>[    1.320843,2] sensors-ssc soc:qcom,msm-ssc-sensors: msm_ssc_sensors_dt_parse: get qdsp timer cntpct hi offset fail
<6>[    1.320852,2] sensors-ssc soc:qcom,msm-ssc-sensors: slpi_loader_init_sysfs: Could not parse dt
<6>[    1.321202,2] usbcore: registered new interface driver snd-usb-audio
<6>[    1.324542,5] cs35l35 7-0040: Cirrus Logic CS35L35 (35a35), Revision: 00
<6>[    1.335845,5] msm-pcm-lpa soc:qcom,msm-pcm-lpa: msm_pcm_probe: dev name soc:qcom,msm-pcm-lpa
<6>[    1.340132,5] u32 classifier
<6>[    1.340138,5]     Actions configured
<6>[    1.340164,5] Netfilter messages via NETLINK v0.30.
<6>[    1.340202,5] nf_conntrack version 0.5.0 (16384 buckets, 65536 max)
<6>[    1.340440,5] ctnetlink v0.93: registering with nfnetlink.
<6>[    1.340896,5] xt_time: kernel timezone is -0000
<6>[    1.341121,5] ip_tables: (C) 2000-2006 Netfilter Core Team
<6>[    1.341227,5] arp_tables: (C) 2002 David S. Miller
<6>[    1.341269,5] TCP: cubic registered
<6>[    1.341276,5] Initializing XFRM netlink socket
<6>[    1.341504,5] NET: Registered protocol family 10
<6>[    1.342141,5] mip6: Mobile IPv6
<6>[    1.342161,5] ip6_tables: (C) 2000-2006 Netfilter Core Team
<6>[    1.342263,5] sit: IPv6 over IPv4 tunneling driver
<6>[    1.342582,5] NET: Registered protocol family 17
<6>[    1.342599,5] NET: Registered protocol family 15
<6>[    1.342629,5] bridge: automatic filtering via arp/ip/ip6tables has been deprecated. Update your scripts to load br_netfilter if you need this.
<6>[    1.342638,5] Ebtables v2.0 registered
<6>[    1.342733,5] Bluetooth: e6e05eb0
<6>[    1.342742,5] Bluetooth: e6e05ea8Bluetooth: e6e05ec0
<6>[    1.342767,5] Bluetooth: e6e05ea0Bluetooth: e6e05ea0
<6>[    1.342779,5] Bluetooth: e6e05e98Bluetooth: e6e05ed8
<6>[    1.342792,5] Bluetooth: e6e05ed8<6>[    1.342827,5] l2tp_core: L2TP core driver, V2.0
<6>[    1.342840,5] l2tp_ppp: PPPoL2TP kernel driver, V2.0
<6>[    1.342847,5] l2tp_ip: L2TP IP encapsulation support (L2TPv3)
<6>[    1.342863,5] l2tp_netlink: L2TP netlink interface
<6>[    1.342884,5] l2tp_eth: L2TP ethernet pseudowire support (L2TPv3)
<6>[    1.342899,5] l2tp_debugfs: L2TP debugfs support
<6>[    1.342906,5] l2tp_ip6: L2TP IP encapsulation support for IPv6 (L2TPv3)
<6>[    1.343441,5] NET: Registered protocol family 27
<6>[    1.347167,7] subsys-pil-tz a21b000.qcom,pronto: for wcnss segments only will be dumped.
<6>[    1.348788,7] pil-q6v5-mss 4080000.qcom,mss: for modem segments only will be dumped.
<6>[    1.350186,7] msm-dwc3 7000000.ssusb: unable to read dcp-max-current, using define value
<6>[    1.350528,7] ft5x06_ts 3-0038: unset chg state
<6>[    1.350547,7] ft5x06_ts 3-0038: ps present state not change
<6>[    1.351648,7] sps:BAM 0x07104000 is registered.
<3>[    1.354586,7] qpnp-smbcharger qpnp-smbcharger-17: node /soc/qcom,spmi@200f000/qcom,pmi8950@2/qcom,qpnp-smbcharger IO resource absent!
<3>[    1.354714,7] qpnp-smbcharger qpnp-smbcharger-17: length=8
<3>[    1.354721,7] qpnp-smbcharger qpnp-smbcharger-17: num parallel charge entries=8
<6>[    1.354805,7] smbcharger_charger_otg: no parameters
<6>[    1.355436,7] FG: fg_vbat_est_check: vbat(3805214),est-vbat(3794686),diff(10528),threshold(300000)
<6>[    1.378169,7] FG: fg_vbat_est_check: vbat(3805214),est-vbat(3794686),diff(10528),threshold(300000)
<3>[    1.380673,5] qpnp-smbcharger qpnp-smbcharger-17: node /soc/qcom,spmi@200f000/qcom,pmi8950@2/qcom,qpnp-smbcharger IO resource absent!
<6>[    1.380726,5] ft5x06_ts 3-0038: ps present state not change
<6>[    1.381409,6] qpnp-smbcharger qpnp-smbcharger-17: SMBCHG successfully probe Charger version=SCHG_LITE Revision DIG:0.0 ANA:0.1 batt=1 dc=0 usb=0
<5>[    1.384958,5] Registering SWP/SWPB emulation handler
<6>[    1.385249,5] registered taskstats version 1
<3>[    1.386019,6] qpnp-smbcharger qpnp-smbcharger-17: Battery Temp State: Unknown -> Cool at -2C
<6>[    1.386186,6] ft5x06_ts 3-0038: ps present state not change
<6>[    1.389449,5] fastrpc soc:qcom,adsprpc-mem: for adsp_rh segments only will be dumped.
<1>[    1.390764,5] drv260x: drv260x_init success
<6>[    1.391191,5] utags (utags_probe): Done [config]
<6>[    1.391217,5] utags (utags_dt_init): backup storage path not provided
<6>[    1.391415,7] utags (utags_probe): Done [hw]
<6>[    1.391910,7] RNDIS_IPA module is loaded.
<6>[    1.392267,7] file system registered
<6>[    1.392313,7] mbim_init: initialize 1 instances
<6>[    1.392358,7] mbim_init: Initialized 1 ports
<6>[    1.393391,7] rndis_qc_init: initialize rndis QC instance
<6>[    1.393565,7] Number of LUNs=8
<6>[    1.393572,7] Mass Storage Function, version: 2009/09/11
<6>[    1.393579,7] LUN: removable file: (no medium)
<6>[    1.393591,7] Number of LUNs=1
<6>[    1.393630,7] LUN: removable file: (no medium)
<6>[    1.393634,7] Number of LUNs=1
<6>[    1.394291,7] android_usb gadget: android_usb ready
<6>[    1.395525,7] input: gpio-keys as /devices/soc/soc:gpio_keys/input/input5
<4>[    1.395845,7] i2c-core: driver [stmvl53l0] using legacy resume method
<6>[    1.396295,7] qcom,qpnp-rtc qpnp-rtc-8: setting system clock to 1970-12-23 07:05:57 UTC (30783957)
<6>[    1.398606,7] msm-core initialized without polling period
<3>[    1.401235,7] parse_cpu_levels: idx 1 276
<3>[    1.401246,7] calculate_residency: residency < 0 for LPM
<3>[    1.401361,7] parse_cpu_levels: idx 1 286
<3>[    1.401367,7] calculate_residency: residency < 0 for LPM
<3>[    1.404377,7] qcom,qpnp-flash-led qpnp-flash-led-23: Unable to acquire pinctrl
<6>[    1.406099,7] rmnet_ipa started initialization
<6>[    1.406106,7] IPA SSR support = True
<6>[    1.406111,7] IPA ipa-loaduC = True
<6>[    1.406116,7] IPA SG support = True
<3>[    1.407969,7] ipa ipa_sps_irq_control_all:942 EP (5) not allocated.
<3>[    1.407980,7] ipa ipa2_uc_state_check:301 uC is not loaded
<6>[    1.409528,6] msm-dwc3 7000000.ssusb: DWC3 in low power mode
<6>[    1.410505,7] rmnet_ipa completed initialization
<6>[    1.412947,7] qcom,cc-debug-8953 1874000.qcom,cc-debug: Registered Debug Mux successfully
<6>[    1.421440,7] msm8952-asoc-wcd c051000.sound: default codec configured
<3>[    1.424666,7] msm8952-asoc-wcd c051000.sound: ASoC: platform (null) not registered
<3>[    1.424707,7] msm8952-asoc-wcd c051000.sound: snd_soc_register_card failed (-517)
<6>[    1.425868,7] apc_mem_acc_corner: disabling
<6>[    1.425875,7] gfx_mem_acc_corner: disabling
<6>[    1.425916,7] vci_fci: disabling
<6>[    1.425954,7] regulator_proxy_consumer_remove_all: removing regulator proxy consumer requests
<6>[    1.425991,7] clock_late_init: Removing enables held for handed-off clocks
<6>[    1.429695,7] ALSA device list:
<6>[    1.429701,7]   No soundcards found.
<3>[    1.429767,7] Warning: unable to open an initial console.
<6>[    1.464423,7] Freeing unused kernel memory: 504K
<14>[    1.465967,7] init: init first stage started!
<14>[    1.466006,7] init: First stage mount skipped (recovery mode)
<14>[    1.466223,7] init: Using Android DT directory /proc/device-tree/firmware/android/
<14>[    1.466286,7] init: Skipped setting INIT_AVB_VERSION (not vbmeta compatible)
<14>[    1.466303,7] init: Loading SELinux policy
<7>[    1.471394,7] SELinux: 2048 avtab hash slots, 29509 rules.
<7>[    1.483587,7] SELinux: 2048 avtab hash slots, 29509 rules.
<7>[    1.483608,7] SELinux:  1 users, 2 roles, 2214 types, 0 bools, 1 sens, 1024 cats
<7>[    1.483614,7] SELinux:  93 classes, 29509 rules
<7>[    1.486890,7] SELinux:  Completing initialization.
<7>[    1.486898,7] SELinux:  Setting up existing superblocks.
<7>[    1.486912,7] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<7>[    1.486935,7] SELinux: initialized (dev rootfs, type rootfs), uses genfs_contexts
<7>[    1.487107,7] SELinux: initialized (dev bdev, type bdev), not configured for labeling
<7>[    1.487122,7] SELinux: initialized (dev proc, type proc), uses genfs_contexts
<7>[    1.487148,7] SELinux: initialized (dev debugfs, type debugfs), uses genfs_contexts
<4>[    1.496745,7] bcl_peripheral:bcl_poll_vbat_high Vbat reached high clear trip. vbat:3856320
<3>[    1.496772,7] bcl_peripheral:bcl_poll_ibat_low Invalid ibat state 1
<7>[    1.511325,7] SELinux: initialized (dev sockfs, type sockfs), uses task SIDs
<7>[    1.511343,7] SELinux: initialized (dev tracefs, type tracefs), uses genfs_contexts
<7>[    1.545193,7] SELinux: initialized (dev pipefs, type pipefs), uses task SIDs
<7>[    1.545206,7] SELinux: initialized (dev anon_inodefs, type anon_inodefs), not configured for labeling
<7>[    1.545213,7] SELinux: initialized (dev aio, type aio), not configured for labeling
<7>[    1.545222,7] SELinux: initialized (dev devpts, type devpts), uses transition SIDs
<7>[    1.545240,7] SELinux: initialized (dev configfs, type configfs), uses genfs_contexts
<7>[    1.545253,7] SELinux: initialized (dev selinuxfs, type selinuxfs), uses genfs_contexts
<7>[    1.545311,7] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<7>[    1.545345,7] SELinux: initialized (dev sysfs, type sysfs), uses genfs_contexts
<5>[    1.555331,7] audit: type=1403 audit(30783957.656:2): policy loaded auid=4294967295 ses=4294967295
<14>[    1.555575,7] selinux: SELinux: Loaded policy from /sepolicy
<14>[    1.555575,7] 
<5>[    1.555789,7] audit: type=1404 audit(30783957.656:3): enforcing=1 old_enforcing=0 auid=4294967295 ses=4294967295
<14>[    1.579302,7] selinux: SELinux: Loaded file_contexts
<14>[    1.579302,7] 
<5>[    1.580306,7] random: init urandom read with 86 bits of entropy available
<14>[    1.581119,7] init: init second stage started!
<14>[    1.589835,7] init: Using Android DT directory /proc/device-tree/firmware/android/
<14>[    1.596167,7] selinux: SELinux: Loaded file_contexts
<14>[    1.596167,7] 
<14>[    1.597913,7] selinux: SELinux: Loaded property_contexts from /plat_property_contexts & /nonplat_property_contexts.
<14>[    1.597913,7] 
<14>[    1.597932,7] init: Running restorecon...
<11>[    1.605373,7] selinux: SELinux:  Could not stat /dev/block: No such file or directory.
<11>[    1.605373,7] 
<11>[    1.605742,7] init: waitid failed: No child processes
<12>[    1.605786,7] init: Couldn't load property file: Unable to open '/system/etc/prop.default': No such file or directory: No such file or directory
<12>[    1.606241,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606266,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606289,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606313,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606336,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606360,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606383,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606406,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606429,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606455,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606478,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606501,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606525,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606548,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606571,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606595,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606619,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606642,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606665,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606688,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606731,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606755,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606778,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606802,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606825,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606848,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606871,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606894,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606917,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606940,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606963,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.606987,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607010,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607033,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607056,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607079,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607103,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607126,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607149,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607174,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607197,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607220,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<12>[    1.607243,7] init: Couldn't load property file: Unable to open '/oem/oem.prop': No such file or directory: No such file or directory
<11>[    1.609035,7] init: property_set("ro.cutoff_voltage_mv", "3400") failed: property already set
<11>[    1.609765,7] init: property_set("ro.opengles.version", "196610") failed: property already set
<11>[    1.610306,7] init: property_set("ro.carrier", "unknown") failed: property already set
<12>[    1.610787,7] init: Couldn't load property file: Unable to open '/odm/default.prop': No such file or directory: No such file or directory
<12>[    1.610852,7] init: Couldn't load property file: Unable to open '/vendor/default.prop': No such file or directory: No such file or directory
<14>[    1.611346,7] init: Created socket '/dev/socket/property_service', mode 666, user 0, group 0
<14>[    1.611467,7] init: Parsing file /init.rc...
<14>[    1.611569,7] init: Added '/init.recovery.qcom.rc' to import list
<14>[    1.611906,7] init: Parsing file /init.recovery.qcom.rc...
<14>[    1.612034,7] init: Parsing file /system/etc/init...
<11>[    1.612056,7] init: Unable to open '/system/etc/init': No such file or directory
<14>[    1.612077,7] init: Parsing file /vendor/etc/init...
<11>[    1.612099,7] init: Unable to open '/vendor/etc/init': No such file or directory
<14>[    1.612116,7] init: Parsing file /odm/etc/init...
<11>[    1.612136,7] init: Unable to open '/odm/etc/init': No such file or directory
<14>[    1.612231,7] init: processing action (early-init) from (/init.rc:3)
<14>[    1.612289,7] init: starting service 'ueventd'...
<5>[    1.612746,7] audit: type=1400 audit(30783957.713:4): avc:  denied  { create } for  uid=0 pid=1 comm="init" name="cgroup.procs" scontext=u:r:init:s0 tcontext=u:object_r:rootfs:s0 tclass=file permissive=0
<11>[    1.612815,7] init: Failed to write '409' to /acct/uid_0/pid_409/cgroup.procs: Permission denied
<11>[    1.612835,7] init: createProcessGroup(0, 409) failed for service 'ueventd': Permission denied
<14>[    1.612909,7] init: processing action (wait_for_coldboot_done) from (<Builtin Action>:0)
<14>[    1.615274,4] ueventd: ueventd started!
<14>[    1.615325,4] ueventd: Parsing file /ueventd.rc...
<11>[    1.615630,4] ueventd: /ueventd.rc: 66: invalid gid 'qcom_diag'
<14>[    1.616122,4] ueventd: Parsing file /vendor/ueventd.rc...
<11>[    1.616145,4] ueventd: Unable to open '/vendor/ueventd.rc': No such file or directory
<14>[    1.616163,4] ueventd: Parsing file /odm/ueventd.rc...
<11>[    1.616183,4] ueventd: Unable to open '/odm/ueventd.rc': No such file or directory
<14>[    1.616251,4] ueventd: Parsing file /ueventd.qcom.rc...
<11>[    1.616273,4] ueventd: Unable to open '/ueventd.qcom.rc': No such file or directory
<14>[    1.621605,4] selinux: SELinux: Loaded file_contexts
<14>[    1.621605,4] 
<14>[    1.751983,5] selinux: SELinux: Loaded file_contexts
<14>[    1.751983,5] 
<14>[    1.751984,7] selinux: SELinux: Loaded file_contexts
<14>[    1.751984,7] 
<14>[    1.751985,4] selinux: SELinux: Loaded file_contexts
<14>[    1.751985,4] 
<14>[    1.751996,6] selinux: SELinux: Loaded file_contexts
<14>[    1.751996,6] 
<14>[    1.752278,1] selinux: SELinux: Loaded file_contexts
<14>[    1.752278,1] 
<14>[    1.752458,3] selinux: SELinux: Loaded file_contexts
<14>[    1.752458,3] 
<14>[    1.759121,2] selinux: SELinux: Loaded file_contexts
<14>[    1.759121,2] 
<14>[    1.759122,0] selinux: SELinux: Loaded file_contexts
<14>[    1.759122,0] 
<14>[    1.766628,4] selinux: SELinux: Loaded file_contexts
<14>[    1.766628,4] 
<14>[    3.125719,4] ueventd: Coldboot took 1.503 seconds
<14>[    3.135578,0] init: Command 'wait_for_coldboot_done' action=wait_for_coldboot_done (<Builtin Action>:0) returned 0 took 1522ms.
<14>[    3.135617,0] init: processing action (mix_hwrng_into_linux_rng) from (<Builtin Action>:0)
<14>[    3.136765,0] init: Mixed 512 bytes from /dev/hw_random into /dev/urandom
<14>[    3.136794,0] init: processing action (set_mmap_rnd_bits) from (<Builtin Action>:0)
<14>[    3.136817,0] init: processing action (set_kptr_restrict) from (<Builtin Action>:0)
<14>[    3.137089,0] init: processing action (keychord_init) from (<Builtin Action>:0)
<14>[    3.137117,0] init: processing action (console_init) from (<Builtin Action>:0)
<14>[    3.137164,0] init: processing action (init) from (/init.rc:9)
<7>[    3.137735,0] SELinux: initialized (dev cgroup, type cgroup), uses genfs_contexts
<7>[    3.139531,0] SELinux: initialized (dev tmpfs, type tmpfs), uses transition SIDs
<14>[    3.139800,0] init: processing action (init) from (/init.recovery.qcom.rc:28)
<11>[    3.139843,0] init: Unable to open '/sys/class/backlight/panel0-backlight/brightness': No such file or directory
<14>[    3.140970,0] init: processing action (mix_hwrng_into_linux_rng) from (<Builtin Action>:0)
<14>[    3.142048,0] init: Mixed 512 bytes from /dev/hw_random into /dev/urandom
<14>[    3.142079,0] init: processing action (late-init) from (/init.rc:66)
<14>[    3.142123,0] init: processing action (queue_property_triggers) from (<Builtin Action>:0)
<14>[    3.142154,0] init: processing action (fs) from (/init.rc:36)
<7>[    3.143269,0] SELinux: initialized (dev functionfs, type functionfs), uses genfs_contexts
<3>[    3.143398,0] enable_store: android_usb: already disabled
<14>[    3.143872,0] init: processing action (load_system_props_action) from (/init.rc:59)
<12>[    3.143969,0] init: HW descriptor status=2
<6>[    3.143979,0] utags (reload_write): [init] (pid 1) [hw] 1
<12>[    3.246866,0] init: Sent HW descriptor reload command rc=2
<11>[    3.246916,0] init: File /vendor/etc/vhw.xml not found
<12>[    3.246963,0] init: Couldn't load property file: Unable to open '/system/build.prop': No such file or directory: No such file or directory
<12>[    3.246987,0] init: Couldn't load property file: Unable to open '/odm/build.prop': No such file or directory: No such file or directory
<12>[    3.247012,0] init: Couldn't load property file: Unable to open '/vendor/build.prop': No such file or directory: No such file or directory
<12>[    3.247036,0] init: Couldn't load property file: Unable to open '/factory/factory.prop': No such file or directory: No such file or directory
<14>[    3.248519,0] init: Command 'load_system_props' action=load_system_props_action (/init.rc:60) returned 0 took 104ms.
<14>[    3.248549,0] init: processing action (firmware_mounts_complete) from (/init.rc:62)
<14>[    3.248593,0] init: processing action (boot) from (/init.rc:51)
<14>[    3.248990,0] init: starting service 'charger'...
<14>[    3.249751,0] init: starting service 'recovery'...
<14>[    3.250396,0] init: processing action (enable_property_trigger) from (<Builtin Action>:0)
<12>[    3.253612,4] healthd: battery l=74 v=3885 t=29.5 h=2 st=3 c=479 fc=0 cc=112 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<5>[    3.257296,5] audit: type=1400 audit(30783959.360:5): avc:  denied  { read } for  uid=0 pid=422 comm="recovery" name="u:object_r:sf_lcd_density_prop:s0" dev="tmpfs" ino=16770 scontext=u:r:recovery:s0 tcontext=u:object_r:sf_lcd_density_prop:s0 tclass=file permissive=0
<6>[    3.313481,1] input input5: gpio-keys report volume_up [0x73] type 0x1 state Off
<5>[    3.313769,1] audit: type=1400 audit(30783959.416:6): avc:  denied  { write } for  uid=0 pid=422 comm="recovery" name="brightness" dev="sysfs" ino=22073 scontext=u:r:recovery:s0 tcontext=u:object_r:sysfs_graphics:s0 tclass=file permissive=0
<4>[    3.333571,0] irq 21, desc: e5d40840, depth: 0, count: 0, unhandled: 0
<4>[    3.333583,0] ->handle_irq():  c03f6ca4, msm_gpio_irq_handler+0x0/0x118
<4>[    3.333590,0] ->irq_data.chip(): c1531158, gic_chip+0x0/0x74
<4>[    3.333592,0] ->action():   (null)
<4>[    3.333594,0]    IRQ_NOPROBE set
<4>[    3.333595,0]  IRQ_NOREQUEST set
<4>[    3.333595,0]   IRQ_NOTHREAD set
<6>[    3.334019,7] mdss_dsi_on[0]+.
<5>[    4.321552,4] audit: type=1400 audit(30783960.423:7): avc:  denied  { search } for  uid=0 pid=422 comm="recovery" name="usb" dev="sysfs" ino=31589 scontext=u:r:recovery:s0 tcontext=u:object_r:sysfs_usb_supply:s0 tclass=dir permissive=0
<6>[    4.370212,3] EXT4-fs (mmcblk0p52): mounted filesystem with ordered data mode. Opts: 
<7>[    4.370237,3] SELinux: initialized (dev mmcblk0p52, type ext4), uses xattr
<5>[    5.195563,0] random: nonblocking pool is initialized
<3>[    5.226812,4] FG: fg_get_mmi_battid: Battsn unused
<4>[    5.226828,4] qcom,qpnp-fg qpnp-fg-18: Default Serial Number SB18C15119
<4>[    5.226837,4] qcom,qpnp-fg qpnp-fg-18: Battery Match Found using default qcom,hg30-alt
<6>[    5.231543,4] FG: fg_batt_profile_init: Battery profiles same, using default
<6>[    5.234611,4] FG: populate_system_data: cutoff_voltage = 3199901, nom_cap_uah = 3021000 p1p2 = 33, p2p3 = 5
<6>[    5.234665,4] FG: fg_batt_profile_init: Battery SOC: 74, V: 3885017uV
<6>[    5.234704,4] FG: fg_vbat_est_check: vbat(3885017),est-vbat(3918586),diff(33569),threshold(300000)
<12>[    5.236256,0] healthd: battery l=74 v=3885 t=29.5 h=2 st=3 c=479 fc=2202000 cc=112 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[    5.236791,0] healthd: battery l=74 v=3885 t=29.5 h=2 st=3 c=479 fc=2202000 cc=112 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<6>[    5.503795,5] EXT4-fs (mmcblk0p51): mounted filesystem with ordered data mode. Opts: 
<7>[    5.503881,5] SELinux: initialized (dev mmcblk0p51, type ext4), uses mountpoint labeling
<6>[    5.840788,5] EXT4-fs (mmcblk0p19): mounted filesystem with ordered data mode. Opts: 
<7>[    5.840811,5] SELinux: initialized (dev mmcblk0p19, type ext4), uses xattr
<3>[   61.497720,6] qpnp-smbcharger qpnp-smbcharger-17: Battery Temp State: Cool -> Good at 30C
<12>[   61.508980,0] healthd: battery l=74 v=3939 t=30.0 h=2 st=3 c=304 fc=2202000 cc=112 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[  121.547345,0] healthd: battery l=74 v=3943 t=30.0 h=2 st=3 c=310 fc=2202000 cc=112 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[  121.658536,0] healthd: battery l=74 v=3942 t=30.0 h=2 st=3 c=310 fc=2202000 cc=112 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<6>[  166.826594,4] update-binary (434): drop_caches: 3
<6>[  178.185597,0] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0x301c04c9)
<3>[  178.185600,0] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 1th superblock
<6>[  178.185786,0] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xc6a3ab26)
<3>[  178.185788,0] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 2th superblock
<6>[  178.185792,0] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0x301c04c9)
<3>[  178.185794,0] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 1th superblock
<6>[  178.185796,0] F2FS-fs (mmcblk0p54): Magic Mismatch, valid(0xf2f52010) - read(0xc6a3ab26)
<3>[  178.185798,0] F2FS-fs (mmcblk0p54): Can't find valid F2FS filesystem in 2th superblock
<3>[  178.186163,0] EXT4-fs (mmcblk0p54): VFS: Can't find ext4 filesystem
<12>[  181.663705,0] healthd: battery l=73 v=3931 t=30.2 h=2 st=3 c=331 fc=2202000 cc=112 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
<12>[  181.818584,0] healthd: battery l=73 v=3900 t=30.2 h=2 st=3 c=453 fc=2202000 cc=112 ml=-1 mst=1 mf=0 mt=0 mps=0 chg=
